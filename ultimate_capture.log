[14:51:19.321] Loading script mitm_url_capture.py
[14:51:19.324] HTTP(S) proxy listening at *:8888.
[14:54:56.051][127.0.0.1:53128] client connect
🌐 HTTP: GET http://connectivitycheck.gstatic.com/generate_204
[14:54:56.060][127.0.0.1:53129] client connect
[14:54:56.067][127.0.0.1:53128] server connect connectivitycheck.gstatic.com:80 (************:80)
[14:54:56.068][127.0.0.1:53129] server connect www.google.com:443 (***********:443)
   ✅ 204 No Content
127.0.0.1:53128: GET http://connectivitycheck.gstatic.com/generate_204
              << 204 No Content 0b
[14:54:56.271][127.0.0.1:53128] server disconnect connectivitycheck.gstatic.com:80 (************:80)
[14:54:56.271][127.0.0.1:53128] client disconnect
[14:54:59.020][127.0.0.1:53135] client connect
🌐 HTTP: GET http://www.google.com/gen_204
[14:54:59.029][127.0.0.1:53135] server connect www.google.com:80 (***********:80)
   ✅ 204 No Content
127.0.0.1:53135: GET http://www.google.com/gen_204
              << 204 No Content 0b
[14:54:59.576][127.0.0.1:53135] server disconnect www.google.com:80 (***********:80)
[14:54:59.577][127.0.0.1:53135] client disconnect
[14:54:59.578][127.0.0.1:53129] Client TLS handshake failed. The client does not trust the proxy's certificate for www.google.com (OpenSSL Error([('SSL routines', '', 'sslv3 alert certificate unknown')]))
[14:54:59.580][127.0.0.1:53129] client disconnect
[14:54:59.582][127.0.0.1:53129] server disconnect www.google.com:443 (***********:443)
[14:55:41.196][127.0.0.1:53184] client connect
[14:55:41.202][127.0.0.1:53184] server connect android.apis.google.com:443 (198.18.1.23:443)
[14:55:41.426][127.0.0.1:53184] Client TLS handshake failed. The client does not trust the proxy's certificate for android.apis.google.com (OpenSSL Error([('SSL routines', '', 'sslv3 alert certificate unknown')]))
[14:55:41.427][127.0.0.1:53184] client disconnect
[14:55:41.429][127.0.0.1:53184] server disconnect android.apis.google.com:443 (198.18.1.23:443)
[14:55:41.452][127.0.0.1:53188] client connect
[14:55:41.458][127.0.0.1:53188] server connect android.googleapis.com:443 (198.18.0.137:443)
[14:55:41.684][127.0.0.1:53188] Client TLS handshake failed. The client does not trust the proxy's certificate for android.googleapis.com (OpenSSL Error([('SSL routines', '', 'sslv3 alert certificate unknown')]))
[14:55:41.685][127.0.0.1:53188] client disconnect
[14:55:41.686][127.0.0.1:53188] server disconnect android.googleapis.com:443 (198.18.0.137:443)
[14:57:30.260][127.0.0.1:53306] client connect
[14:57:30.266][127.0.0.1:53307] client connect
[14:57:30.267][127.0.0.1:53306] server connect www.google.com:443 (***********:443)
🌐 HTTP: GET http://connectivitycheck.gstatic.com/generate_204
[14:57:30.274][127.0.0.1:53307] server connect connectivitycheck.gstatic.com:80 (************:80)
   ✅ 204 No Content
127.0.0.1:53307: GET http://connectivitycheck.gstatic.com/generate_204
              << 204 No Content 0b
[14:57:30.464][127.0.0.1:53307] server disconnect connectivitycheck.gstatic.com:80 (************:80)
[14:57:30.465][127.0.0.1:53307] client disconnect
[14:57:30.549][127.0.0.1:53306] Client TLS handshake failed. The client does not trust the proxy's certificate for www.google.com (OpenSSL Error([('SSL routines', '', 'sslv3 alert certificate unknown')]))
[14:57:30.551][127.0.0.1:53306] client disconnect
[14:57:30.552][127.0.0.1:53306] server disconnect www.google.com:443 (***********:443)
[14:57:30.555][127.0.0.1:53312] client connect
🌐 HTTP: GET http://www.google.com/gen_204
[14:57:30.561][127.0.0.1:53312] server connect www.google.com:80 (***********:80)
   ✅ 204 No Content
127.0.0.1:53312: GET http://www.google.com/gen_204
              << 204 No Content 0b
[14:57:30.809][127.0.0.1:53312] server disconnect www.google.com:80 (***********:80)
[14:57:30.811][127.0.0.1:53312] client disconnect
[14:58:40.183][127.0.0.1:53367] client connect
[14:58:40.190][127.0.0.1:53367] server connect www.google.com:443 (***********:443)
[14:58:40.192][127.0.0.1:53369] client connect
🌐 HTTP: GET http://connectivitycheck.gstatic.com/generate_204
[14:58:40.205][127.0.0.1:53369] server connect connectivitycheck.gstatic.com:80 (************:80)
   ✅ 204 No Content
127.0.0.1:53369: GET http://connectivitycheck.gstatic.com/generate_204
              << 204 No Content 0b
[14:58:40.950][127.0.0.1:53369] server disconnect connectivitycheck.gstatic.com:80 (************:80)
[14:58:40.951][127.0.0.1:53369] client disconnect
[14:58:43.149][127.0.0.1:53378] client connect
🌐 HTTP: GET http://www.google.com/gen_204
[14:58:43.154][127.0.0.1:53378] server connect www.google.com:80 (***********:80)
[14:58:43.465][127.0.0.1:53367] Client TLS handshake failed. The client does not trust the proxy's certificate for www.google.com (OpenSSL Error([('SSL routines', '', 'sslv3 alert certificate unknown')]))
[14:58:43.466][127.0.0.1:53367] client disconnect
[14:58:43.467][127.0.0.1:53367] server disconnect www.google.com:443 (***********:443)
   ✅ 204 No Content
127.0.0.1:53378: GET http://www.google.com/gen_204
              << 204 No Content 0b
[14:58:44.933][127.0.0.1:53378] server disconnect www.google.com:80 (***********:80)
[14:58:44.934][127.0.0.1:53378] client disconnect
[14:58:45.983][127.0.0.1:53386] client connect
[14:58:45.989][127.0.0.1:53386] server connect www.google.com:443 (***********:443)
[14:58:45.991][127.0.0.1:53388] client connect
🌐 HTTP: GET http://connectivitycheck.gstatic.com/generate_204
[14:58:45.998][127.0.0.1:53388] server connect connectivitycheck.gstatic.com:80 (************:80)
   ✅ 204 No Content
127.0.0.1:53388: GET http://connectivitycheck.gstatic.com/generate_204
              << 204 No Content 0b
[14:58:46.470][127.0.0.1:53388] server disconnect connectivitycheck.gstatic.com:80 (************:80)
[14:58:46.470][127.0.0.1:53388] client disconnect
[14:58:46.862][127.0.0.1:53386] Client TLS handshake failed. The client does not trust the proxy's certificate for www.google.com (OpenSSL Error([('SSL routines', '', 'sslv3 alert certificate unknown')]))
[14:58:46.864][127.0.0.1:53386] client disconnect
[14:58:46.866][127.0.0.1:53386] server disconnect www.google.com:443 (***********:443)
[14:58:46.872][127.0.0.1:53392] client connect
🌐 HTTP: GET http://play.googleapis.com/generate_204
[14:58:46.881][127.0.0.1:53392] server connect play.googleapis.com:80 (198.18.0.111:80)
   ✅ 204 No Content
127.0.0.1:53392: GET http://play.googleapis.com/generate_204
              << 204 No Content 0b
[14:58:49.191][127.0.0.1:53392] server disconnect play.googleapis.com:80 (198.18.0.111:80)
[14:58:49.192][127.0.0.1:53392] client disconnect
[14:58:51.233][127.0.0.1:53399] client connect
[14:58:51.238][127.0.0.1:53399] server connect www.google.com:443 (***********:443)
[14:58:51.239][127.0.0.1:53401] client connect
🌐 HTTP: GET http://connectivitycheck.gstatic.com/generate_204
[14:58:51.249][127.0.0.1:53401] server connect connectivitycheck.gstatic.com:80 (************:80)
   ✅ 204 No Content
127.0.0.1:53401: GET http://connectivitycheck.gstatic.com/generate_204
              << 204 No Content 0b
[14:58:51.649][127.0.0.1:53401] server disconnect connectivitycheck.gstatic.com:80 (************:80)
[14:58:51.650][127.0.0.1:53401] client disconnect
[14:58:51.923][127.0.0.1:53399] Client TLS handshake failed. The client does not trust the proxy's certificate for www.google.com (OpenSSL Error([('SSL routines', '', 'sslv3 alert certificate unknown')]))
[14:58:51.925][127.0.0.1:53399] client disconnect
[14:58:51.927][127.0.0.1:53399] server disconnect www.google.com:443 (***********:443)
[14:58:51.932][127.0.0.1:53406] client connect
🌐 HTTP: GET http://play.googleapis.com/generate_204
[14:58:51.937][127.0.0.1:53406] server connect play.googleapis.com:80 (198.18.0.111:80)
   ✅ 204 No Content
127.0.0.1:53406: GET http://play.googleapis.com/generate_204
              << 204 No Content 0b
[14:58:52.627][127.0.0.1:53406] server disconnect play.googleapis.com:80 (198.18.0.111:80)
[14:58:52.628][127.0.0.1:53406] client disconnect
[14:58:56.715][127.0.0.1:53412] client connect
🌐 HTTP: GET http://connectivitycheck.gstatic.com/generate_204
[14:58:56.723][127.0.0.1:53413] client connect
[14:58:56.727][127.0.0.1:53412] server connect connectivitycheck.gstatic.com:80 (************:80)
[14:58:56.730][127.0.0.1:53413] server connect www.google.com:443 (***********:443)
   ✅ 204 No Content
127.0.0.1:53412: GET http://connectivitycheck.gstatic.com/generate_204
              << 204 No Content 0b
[14:58:56.874][127.0.0.1:53412] server disconnect connectivitycheck.gstatic.com:80 (************:80)
[14:58:56.875][127.0.0.1:53412] client disconnect
[14:58:57.024][127.0.0.1:53413] Client TLS handshake failed. The client does not trust the proxy's certificate for www.google.com (OpenSSL Error([('SSL routines', '', 'sslv3 alert certificate unknown')]))
[14:58:57.025][127.0.0.1:53413] client disconnect
[14:58:57.027][127.0.0.1:53413] server disconnect www.google.com:443 (***********:443)
[14:58:57.032][127.0.0.1:53418] client connect
🌐 HTTP: GET http://www.google.com/gen_204
[14:58:57.038][127.0.0.1:53418] server connect www.google.com:80 (***********:80)
   ✅ 204 No Content
127.0.0.1:53418: GET http://www.google.com/gen_204
              << 204 No Content 0b
[14:58:57.321][127.0.0.1:53418] server disconnect www.google.com:80 (***********:80)
[14:58:57.322][127.0.0.1:53418] client disconnect
[14:59:05.408][127.0.0.1:53429] client connect
🌐 HTTP: GET http://connectivitycheck.gstatic.com/generate_204
[14:59:05.421][127.0.0.1:53430] client connect
[14:59:05.425][127.0.0.1:53429] server connect connectivitycheck.gstatic.com:80 (************:80)
[14:59:05.434][127.0.0.1:53430] server connect www.google.com:443 (***********:443)
   ✅ 204 No Content
127.0.0.1:53429: GET http://connectivitycheck.gstatic.com/generate_204
              << 204 No Content 0b
[14:59:05.581][127.0.0.1:53429] server disconnect connectivitycheck.gstatic.com:80 (************:80)
[14:59:05.582][127.0.0.1:53429] client disconnect
[14:59:08.345][127.0.0.1:53436] client connect
🌐 HTTP: GET http://play.googleapis.com/generate_204
[14:59:08.351][127.0.0.1:53436] server connect play.googleapis.com:80 (198.18.0.111:80)
   ✅ 204 No Content
127.0.0.1:53436: GET http://play.googleapis.com/generate_204
              << 204 No Content 0b
[14:59:08.583][127.0.0.1:53436] server disconnect play.googleapis.com:80 (198.18.0.111:80)
[14:59:08.583][127.0.0.1:53436] client disconnect
[14:59:11.457][127.0.0.1:53430] Client TLS handshake failed. The client does not trust the proxy's certificate for www.google.com (OpenSSL Error([('SSL routines', '', 'sslv3 alert certificate unknown')]))
[14:59:11.459][127.0.0.1:53430] client disconnect
[14:59:11.460][127.0.0.1:53430] server disconnect www.google.com:443 (***********:443)
[14:59:27.515][127.0.0.1:53455] client connect
[14:59:27.520][127.0.0.1:53455] server connect www.google.com:443 (***********:443)
[14:59:27.526][127.0.0.1:53457] client connect
🌐 HTTP: GET http://connectivitycheck.gstatic.com/generate_204
[14:59:27.534][127.0.0.1:53457] server connect connectivitycheck.gstatic.com:80 (************:80)
   ✅ 204 No Content
127.0.0.1:53457: GET http://connectivitycheck.gstatic.com/generate_204
              << 204 No Content 0b
[14:59:27.712][127.0.0.1:53457] server disconnect connectivitycheck.gstatic.com:80 (************:80)
[14:59:27.713][127.0.0.1:53457] client disconnect
[14:59:27.903][127.0.0.1:53455] Client TLS handshake failed. The client does not trust the proxy's certificate for www.google.com (OpenSSL Error([('SSL routines', '', 'sslv3 alert certificate unknown')]))
[14:59:27.904][127.0.0.1:53455] client disconnect
[14:59:27.904][127.0.0.1:53455] server disconnect www.google.com:443 (***********:443)
[14:59:27.909][127.0.0.1:53461] client connect
🌐 HTTP: GET http://www.google.com/gen_204
[14:59:27.914][127.0.0.1:53461] server connect www.google.com:80 (***********:80)
   ✅ 204 No Content
127.0.0.1:53461: GET http://www.google.com/gen_204
              << 204 No Content 0b
[14:59:28.427][127.0.0.1:53461] server disconnect www.google.com:80 (***********:80)
[14:59:28.427][127.0.0.1:53461] client disconnect
[14:59:55.689][127.0.0.1:53482] client connect
[14:59:55.694][127.0.0.1:53482] server connect play.googleapis.com:443 (198.18.0.111:443)
[14:59:55.951][127.0.0.1:53482] Client TLS handshake failed. The client does not trust the proxy's certificate for play.googleapis.com (OpenSSL Error([('SSL routines', '', 'sslv3 alert certificate unknown')]))
[14:59:55.952][127.0.0.1:53482] client disconnect
[14:59:55.953][127.0.0.1:53482] server disconnect play.googleapis.com:443 (198.18.0.111:443)
[15:00:00.500][127.0.0.1:53487] client connect
🌐 HTTP: GET http://connectivitycheck.gstatic.com/generate_204
[15:00:00.509][127.0.0.1:53488] client connect
[15:00:00.512][127.0.0.1:53487] server connect connectivitycheck.gstatic.com:80 (************:80)
[15:00:00.516][127.0.0.1:53488] server connect www.google.com:443 (***********:443)
   ✅ 204 No Content
127.0.0.1:53487: GET http://connectivitycheck.gstatic.com/generate_204
              << 204 No Content 0b
[15:00:00.625][127.0.0.1:53487] server disconnect connectivitycheck.gstatic.com:80 (************:80)
[15:00:00.627][127.0.0.1:53487] client disconnect
[15:00:00.740][127.0.0.1:53488] Client TLS handshake failed. The client does not trust the proxy's certificate for www.google.com (OpenSSL Error([('SSL routines', '', 'sslv3 alert certificate unknown')]))
[15:00:00.742][127.0.0.1:53488] client disconnect
[15:00:00.744][127.0.0.1:53488] server disconnect www.google.com:443 (***********:443)
[15:00:00.749][127.0.0.1:53493] client connect
🌐 HTTP: GET http://play.googleapis.com/generate_204
[15:00:00.755][127.0.0.1:53493] server connect play.googleapis.com:80 (198.18.0.111:80)
   ✅ 204 No Content
127.0.0.1:53493: GET http://play.googleapis.com/generate_204
              << 204 No Content 0b
[15:00:00.955][127.0.0.1:53493] server disconnect play.googleapis.com:80 (198.18.0.111:80)
[15:00:00.956][127.0.0.1:53493] client disconnect
[15:01:05.051][127.0.0.1:53571] client connect
🌐 HTTP: GET http://connectivitycheck.gstatic.com/generate_204
[15:01:05.071][127.0.0.1:53571] server connect connectivitycheck.gstatic.com:80 (************:80)
[15:01:05.078][127.0.0.1:53575] client connect
[15:01:05.091][127.0.0.1:53575] server connect www.google.com:443 (***********:443)
   ✅ 204 No Content
127.0.0.1:53571: GET http://connectivitycheck.gstatic.com/generate_204
              << 204 No Content 0b
[15:01:05.199][127.0.0.1:53571] server disconnect connectivitycheck.gstatic.com:80 (************:80)
[15:01:05.201][127.0.0.1:53571] client disconnect
[15:01:05.353][127.0.0.1:53575] Client TLS handshake failed. The client does not trust the proxy's certificate for www.google.com (OpenSSL Error([('SSL routines', '', 'sslv3 alert certificate unknown')]))
[15:01:05.356][127.0.0.1:53575] client disconnect
[15:01:05.361][127.0.0.1:53575] server disconnect www.google.com:443 (***********:443)
[15:01:05.377][127.0.0.1:53578] client connect
🌐 HTTP: GET http://www.google.com/gen_204
[15:01:05.399][127.0.0.1:53578] server connect www.google.com:80 (***********:80)
   ✅ 204 No Content
127.0.0.1:53578: GET http://www.google.com/gen_204
              << 204 No Content 0b
[15:01:05.685][127.0.0.1:53578] server disconnect www.google.com:80 (***********:80)
[15:01:05.686][127.0.0.1:53578] client disconnect
[15:03:13.747][127.0.0.1:53768] client connect
[15:03:13.756][127.0.0.1:53769] client connect
[15:03:13.757][127.0.0.1:53768] server connect www.google.com:443 (***********:443)
🌐 HTTP: GET http://connectivitycheck.gstatic.com/generate_204
[15:03:13.765][127.0.0.1:53769] server connect connectivitycheck.gstatic.com:80 (************:80)
   ✅ 204 No Content
127.0.0.1:53769: GET http://connectivitycheck.gstatic.com/generate_204
              << 204 No Content 0b
[15:03:13.869][127.0.0.1:53769] server disconnect connectivitycheck.gstatic.com:80 (************:80)
[15:03:13.870][127.0.0.1:53769] client disconnect
[15:03:15.298][127.0.0.1:53768] Server TLS handshake failed. connection closed
[15:03:15.299][127.0.0.1:53768] Unable to establish TLS connection with server (connection closed). Trying to establish TLS with client anyway. If you plan to redirect requests away from this server, consider setting `connection_strategy` to `lazy` to suppress early connections.
[15:03:15.305][127.0.0.1:53768] server disconnect www.google.com:443 (***********:443)
[15:03:15.308][127.0.0.1:53768] Client TLS handshake failed. The client does not trust the proxy's certificate for www.google.com (OpenSSL Error([('SSL routines', '', 'sslv3 alert certificate unknown')]))
[15:03:15.309][127.0.0.1:53768] client disconnect
[15:03:15.317][127.0.0.1:53782] client connect
🌐 HTTP: GET http://www.google.com/gen_204
[15:03:15.326][127.0.0.1:53782] server connect www.google.com:80 (***********:80)
   ✅ 204 No Content
127.0.0.1:53782: GET http://www.google.com/gen_204
              << 204 No Content 0b
[15:03:15.554][127.0.0.1:53782] server disconnect www.google.com:80 (***********:80)
[15:03:15.554][127.0.0.1:53782] client disconnect
[15:03:36.125][127.0.0.1:53810] client connect
[15:03:36.132][127.0.0.1:53810] server connect accounts.google.com:443 (198.18.0.136:443)
[15:03:36.444][127.0.0.1:53810] Client TLS handshake failed. The client does not trust the proxy's certificate for accounts.google.com (OpenSSL Error([('SSL routines', '', 'sslv3 alert certificate unknown')]))
[15:03:36.445][127.0.0.1:53810] client disconnect
[15:03:36.446][127.0.0.1:53810] server disconnect accounts.google.com:443 (198.18.0.136:443)
[15:04:36.417][127.0.0.1:53875] client connect
[15:04:36.424][127.0.0.1:53875] server connect update.googleapis.com:443 (198.18.0.139:443)
[15:04:36.616][127.0.0.1:53875] Client TLS handshake failed. The client does not trust the proxy's certificate for update.googleapis.com (OpenSSL Error([('SSL routines', '', 'sslv3 alert certificate unknown')]))
[15:04:36.618][127.0.0.1:53875] client disconnect
[15:04:36.620][127.0.0.1:53875] server disconnect update.googleapis.com:443 (198.18.0.139:443)
[15:04:36.625][127.0.0.1:53878] client connect
🌐 HTTP: POST http://update.googleapis.com/service/update2/json?cup2key=10:*********&cup2hreq=b8a91aa28af83a75a6b2ca881311ebac546b5a9841089c74dc049e118bb4bd77
[15:04:36.634][127.0.0.1:53878] server connect update.googleapis.com:80 (198.18.0.139:80)
   ✅ 200 OK
127.0.0.1:53878: POST http://update.googleapis.com/service/update2/json?cup2k…
              << 200 OK 491b
[15:04:59.735][127.0.0.1:53895] client connect
[15:04:59.742][127.0.0.1:53896] client connect
[15:04:59.744][127.0.0.1:53895] server connect www.google.com:443 (***********:443)
🌐 HTTP: GET http://connectivitycheck.gstatic.com/generate_204
[15:04:59.752][127.0.0.1:53896] server connect connectivitycheck.gstatic.com:80 (************:80)
   ✅ 204 No Content
127.0.0.1:53896: GET http://connectivitycheck.gstatic.com/generate_204
              << 204 No Content 0b
[15:04:59.837][127.0.0.1:53896] server disconnect connectivitycheck.gstatic.com:80 (************:80)
[15:04:59.837][127.0.0.1:53896] client disconnect
[15:05:00.041][127.0.0.1:53895] Client TLS handshake failed. The client does not trust the proxy's certificate for www.google.com (OpenSSL Error([('SSL routines', '', 'sslv3 alert certificate unknown')]))
[15:05:00.042][127.0.0.1:53895] client disconnect
[15:05:00.043][127.0.0.1:53895] server disconnect www.google.com:443 (***********:443)
[15:05:00.048][127.0.0.1:53901] client connect
🌐 HTTP: GET http://www.google.com/gen_204
[15:05:00.052][127.0.0.1:53901] server connect www.google.com:80 (***********:80)
127.0.0.1:53901: GET http://www.google.com/gen_204
 << Client disconnected.
[15:05:10.052][127.0.0.1:53901] client disconnect
[15:05:10.053][127.0.0.1:53901] server disconnect www.google.com:80 (***********:80)
[15:07:21.485][127.0.0.1:54151] client connect
[15:07:21.513][127.0.0.1:54151] server connect ce3e75d5.jpush.cn:443 (************:443)
[15:07:21.719][127.0.0.1:54151] Client TLS handshake failed. The client does not trust the proxy's certificate for ce3e75d5.jpush.cn (OpenSSL Error([('SSL routines', '', 'sslv3 alert certificate unknown')]))
[15:07:21.721][127.0.0.1:54151] client disconnect
[15:07:21.724][127.0.0.1:54151] server disconnect ce3e75d5.jpush.cn:443 (************:443)
[15:07:21.731][127.0.0.1:54154] client connect
[15:07:21.742][127.0.0.1:54154] server connect ce3e75d5-ipv6.jpush.cn:443 (************:443)
[15:07:21.928][127.0.0.1:54154] Client TLS handshake failed. The client does not trust the proxy's certificate for ce3e75d5-ipv6.jpush.cn (OpenSSL Error([('SSL routines', '', 'sslv3 alert certificate unknown')]))
[15:07:21.929][127.0.0.1:54154] client disconnect
[15:07:21.930][127.0.0.1:54154] server disconnect ce3e75d5-ipv6.jpush.cn:443 (************:443)
[15:07:31.664][127.0.0.1:54163] client connect
🌐 HTTP: GET http://connectivitycheck.gstatic.com/generate_204
[15:07:31.671][127.0.0.1:54164] client connect
[15:07:31.672][127.0.0.1:54163] server connect connectivitycheck.gstatic.com:80 (************:80)
[15:07:31.677][127.0.0.1:54164] server connect www.google.com:443 (***********:443)
   ✅ 204 No Content
127.0.0.1:54163: GET http://connectivitycheck.gstatic.com/generate_204
              << 204 No Content 0b
[15:07:31.799][127.0.0.1:54163] server disconnect connectivitycheck.gstatic.com:80 (************:80)
[15:07:31.800][127.0.0.1:54163] client disconnect
[15:07:31.916][127.0.0.1:54164] Client TLS handshake failed. The client does not trust the proxy's certificate for www.google.com (OpenSSL Error([('SSL routines', '', 'sslv3 alert certificate unknown')]))
[15:07:31.917][127.0.0.1:54164] client disconnect
[15:07:31.918][127.0.0.1:54164] server disconnect www.google.com:443 (***********:443)
[15:07:31.922][127.0.0.1:54169] client connect
🌐 HTTP: GET http://www.google.com/gen_204
[15:07:31.929][127.0.0.1:54169] server connect www.google.com:80 (***********:80)
   ✅ 204 No Content
127.0.0.1:54169: GET http://www.google.com/gen_204
              << 204 No Content 0b
[15:07:32.157][127.0.0.1:54169] server disconnect www.google.com:80 (***********:80)
[15:07:32.159][127.0.0.1:54169] client disconnect
[15:08:36.903][127.0.0.1:53878] server disconnect update.googleapis.com:80 (198.18.0.139:80)
[15:09:10.619][127.0.0.1:54315] client connect
🌐 HTTP: GET http://connectivitycheck.gstatic.com/generate_204
[15:09:10.627][127.0.0.1:54315] server connect connectivitycheck.gstatic.com:80 (************:80)
[15:09:10.631][127.0.0.1:54318] client connect
[15:09:10.637][127.0.0.1:54318] server connect www.google.com:443 (***********:443)
   ✅ 204 No Content
127.0.0.1:54315: GET http://connectivitycheck.gstatic.com/generate_204
              << 204 No Content 0b
[15:09:10.878][127.0.0.1:54315] server disconnect connectivitycheck.gstatic.com:80 (************:80)
[15:09:10.879][127.0.0.1:54315] client disconnect
[15:09:11.232][127.0.0.1:54318] Client TLS handshake failed. The client does not trust the proxy's certificate for www.google.com (OpenSSL Error([('SSL routines', '', 'sslv3 alert certificate unknown')]))
[15:09:11.233][127.0.0.1:54318] client disconnect
[15:09:11.234][127.0.0.1:54318] server disconnect www.google.com:443 (***********:443)
[15:09:11.238][127.0.0.1:54324] client connect
🌐 HTTP: GET http://www.google.com/gen_204
[15:09:11.244][127.0.0.1:54324] server connect www.google.com:80 (***********:80)
   ✅ 204 No Content
127.0.0.1:54324: GET http://www.google.com/gen_204
              << 204 No Content 0b
[15:09:12.096][127.0.0.1:54324] server disconnect www.google.com:80 (***********:80)
[15:09:12.097][127.0.0.1:54324] client disconnect
[15:09:13.150][127.0.0.1:54330] client connect
🌐 HTTP: GET http://connectivitycheck.gstatic.com/generate_204
[15:09:13.159][127.0.0.1:54331] client connect
[15:09:13.160][127.0.0.1:54330] server connect connectivitycheck.gstatic.com:80 (************:80)
[15:09:13.166][127.0.0.1:54331] server connect www.google.com:443 (***********:443)
   ✅ 204 No Content
127.0.0.1:54330: GET http://connectivitycheck.gstatic.com/generate_204
              << 204 No Content 0b
[15:09:14.619][127.0.0.1:54330] server disconnect connectivitycheck.gstatic.com:80 (************:80)
[15:09:14.619][127.0.0.1:54330] client disconnect
[15:09:15.391][127.0.0.1:54331] Client TLS handshake failed. The client does not trust the proxy's certificate for www.google.com (OpenSSL Error([('SSL routines', '', 'sslv3 alert certificate unknown')]))
[15:09:15.393][127.0.0.1:54331] client disconnect
[15:09:15.395][127.0.0.1:54331] server disconnect www.google.com:443 (***********:443)
[15:09:15.402][127.0.0.1:54339] client connect
🌐 HTTP: GET http://www.google.com/gen_204
[15:09:15.412][127.0.0.1:54339] server connect www.google.com:80 (***********:80)
   ✅ 204 No Content
127.0.0.1:54339: GET http://www.google.com/gen_204
              << 204 No Content 0b
[15:09:16.641][127.0.0.1:54339] server disconnect www.google.com:80 (***********:80)
[15:09:16.643][127.0.0.1:54339] client disconnect
[15:09:18.692][127.0.0.1:54345] client connect
🌐 HTTP: GET http://connectivitycheck.gstatic.com/generate_204
[15:09:18.699][127.0.0.1:54346] client connect
[15:09:18.701][127.0.0.1:54345] server connect connectivitycheck.gstatic.com:80 (************:80)
[15:09:18.705][127.0.0.1:54346] server connect www.google.com:443 (***********:443)
   ✅ 204 No Content
127.0.0.1:54345: GET http://connectivitycheck.gstatic.com/generate_204
              << 204 No Content 0b
[15:09:19.808][127.0.0.1:54345] server disconnect connectivitycheck.gstatic.com:80 (************:80)
[15:09:19.810][127.0.0.1:54345] client disconnect
[15:09:20.428][127.0.0.1:54346] Client TLS handshake failed. The client does not trust the proxy's certificate for www.google.com (OpenSSL Error([('SSL routines', '', 'sslv3 alert certificate unknown')]))
[15:09:20.429][127.0.0.1:54346] client disconnect
[15:09:20.430][127.0.0.1:54346] server disconnect www.google.com:443 (***********:443)
[15:09:20.434][127.0.0.1:54352] client connect
🌐 HTTP: GET http://play.googleapis.com/generate_204
[15:09:20.439][127.0.0.1:54352] server connect play.googleapis.com:80 (198.18.0.111:80)
   ✅ 204 No Content
127.0.0.1:54352: GET http://play.googleapis.com/generate_204
              << 204 No Content 0b
[15:09:23.168][127.0.0.1:54352] server disconnect play.googleapis.com:80 (198.18.0.111:80)
[15:09:23.169][127.0.0.1:54352] client disconnect
[15:09:27.210][127.0.0.1:54360] client connect
🌐 HTTP: GET http://connectivitycheck.gstatic.com/generate_204
[15:09:27.217][127.0.0.1:54362] client connect
[15:09:27.218][127.0.0.1:54360] server connect connectivitycheck.gstatic.com:80 (************:80)
[15:09:27.222][127.0.0.1:54362] server connect www.google.com:443 (***********:443)
   ✅ 204 No Content
127.0.0.1:54360: GET http://connectivitycheck.gstatic.com/generate_204
              << 204 No Content 0b
[15:09:27.683][127.0.0.1:54360] server disconnect connectivitycheck.gstatic.com:80 (************:80)
[15:09:27.684][127.0.0.1:54360] client disconnect
[15:09:29.795][127.0.0.1:54362] Client TLS handshake failed. The client does not trust the proxy's certificate for www.google.com (OpenSSL Error([('SSL routines', '', 'sslv3 alert certificate unknown')]))
[15:09:29.796][127.0.0.1:54362] client disconnect
[15:09:29.797][127.0.0.1:54362] server disconnect www.google.com:443 (***********:443)
[15:09:29.801][127.0.0.1:54367] client connect
🌐 HTTP: GET http://www.google.com/gen_204
[15:09:29.806][127.0.0.1:54367] server connect www.google.com:80 (***********:80)
   ✅ 204 No Content
127.0.0.1:54367: GET http://www.google.com/gen_204
              << 204 No Content 0b
[15:09:30.754][127.0.0.1:54367] server disconnect www.google.com:80 (***********:80)
[15:09:30.755][127.0.0.1:54367] client disconnect
[15:09:38.804][127.0.0.1:54384] client connect
[15:09:38.812][127.0.0.1:54384] server connect www.google.com:443 (***********:443)
[15:09:38.814][127.0.0.1:54386] client connect
🌐 HTTP: GET http://connectivitycheck.gstatic.com/generate_204
[15:09:38.821][127.0.0.1:54386] server connect connectivitycheck.gstatic.com:80 (************:80)
   ✅ 204 No Content
127.0.0.1:54386: GET http://connectivitycheck.gstatic.com/generate_204
              << 204 No Content 0b
[15:09:39.264][127.0.0.1:54386] server disconnect connectivitycheck.gstatic.com:80 (************:80)
[15:09:39.265][127.0.0.1:54386] client disconnect
[15:09:41.776][127.0.0.1:54392] client connect
🌐 HTTP: GET http://play.googleapis.com/generate_204
[15:09:41.781][127.0.0.1:54392] server connect play.googleapis.com:80 (198.18.0.111:80)
   ✅ 204 No Content
127.0.0.1:54392: GET http://play.googleapis.com/generate_204
              << 204 No Content 0b
[15:09:43.561][127.0.0.1:54392] server disconnect play.googleapis.com:80 (198.18.0.111:80)
[15:09:43.562][127.0.0.1:54392] client disconnect
[15:09:49.381][127.0.0.1:54384] client disconnect
[15:09:49.382][127.0.0.1:54384] server disconnect www.google.com:443 (***********:443)
[15:10:04.878][127.0.0.1:54432] client connect
[15:10:04.885][127.0.0.1:54432] server connect www.google.com:443 (***********:443)
[15:10:04.892][127.0.0.1:54434] client connect
🌐 HTTP: GET http://connectivitycheck.gstatic.com/generate_204
[15:10:04.898][127.0.0.1:54434] server connect connectivitycheck.gstatic.com:80 (************:80)
   ✅ 204 No Content
127.0.0.1:54434: GET http://connectivitycheck.gstatic.com/generate_204
              << 204 No Content 0b
[15:10:05.360][127.0.0.1:54434] server disconnect connectivitycheck.gstatic.com:80 (************:80)
[15:10:05.361][127.0.0.1:54434] client disconnect
[15:10:07.856][127.0.0.1:54448] client connect
🌐 HTTP: GET http://www.google.com/gen_204
[15:10:07.862][127.0.0.1:54448] server connect www.google.com:80 (***********:80)
[15:10:08.225][127.0.0.1:54432] Client TLS handshake failed. The client does not trust the proxy's certificate for www.google.com (OpenSSL Error([('SSL routines', '', 'sslv3 alert certificate unknown')]))
[15:10:08.227][127.0.0.1:54432] client disconnect
[15:10:08.228][127.0.0.1:54432] server disconnect www.google.com:443 (***********:443)
   ✅ 204 No Content
127.0.0.1:54448: GET http://www.google.com/gen_204
              << 204 No Content 0b
[15:10:08.750][127.0.0.1:54448] server disconnect www.google.com:80 (***********:80)
[15:10:08.751][127.0.0.1:54448] client disconnect
[15:10:40.818][127.0.0.1:54528] client connect
[15:10:40.824][127.0.0.1:54529] client connect
[15:10:40.825][127.0.0.1:54528] server connect www.google.com:443 (***********:443)
🌐 HTTP: GET http://connectivitycheck.gstatic.com/generate_204
[15:10:40.832][127.0.0.1:54529] server connect connectivitycheck.gstatic.com:80 (************:80)
   ✅ 204 No Content
127.0.0.1:54529: GET http://connectivitycheck.gstatic.com/generate_204
              << 204 No Content 0b
[15:10:41.303][127.0.0.1:54529] server disconnect connectivitycheck.gstatic.com:80 (************:80)
[15:10:41.303][127.0.0.1:54529] client disconnect
[15:10:43.796][127.0.0.1:54538] client connect
🌐 HTTP: GET http://www.google.com/gen_204
[15:10:43.801][127.0.0.1:54538] server connect www.google.com:80 (***********:80)
   ✅ 204 No Content
127.0.0.1:54538: GET http://www.google.com/gen_204
              << 204 No Content 0b
[15:10:51.213][127.0.0.1:54538] server disconnect www.google.com:80 (***********:80)
[15:10:51.214][127.0.0.1:54538] client disconnect
[15:10:51.437][127.0.0.1:54528] client disconnect
[15:10:51.438][127.0.0.1:54528] server disconnect www.google.com:443 (***********:443)
[15:11:22.292][127.0.0.1:54600] client connect
[15:11:22.305][127.0.0.1:54600] server connect www.google.com:443 (***********:443)
[15:11:25.664][127.0.0.1:54600] Client TLS handshake failed. The client does not trust the proxy's certificate for www.google.com (OpenSSL Error([('SSL routines', '', 'sslv3 alert certificate unknown')]))
[15:11:25.665][127.0.0.1:54600] client disconnect
[15:11:25.666][127.0.0.1:54600] server disconnect www.google.com:443 (***********:443)
[15:11:28.697][127.0.0.1:53878] client disconnect
[15:11:55.275][127.0.0.1:54627] client connect
[15:11:55.281][127.0.0.1:54627] server connect www.google.com:443 (***********:443)
[15:11:55.283][127.0.0.1:54629] client connect
🌐 HTTP: GET http://connectivitycheck.gstatic.com/generate_204
[15:11:55.293][127.0.0.1:54629] server connect connectivitycheck.gstatic.com:80 (************:80)
   ✅ 204 No Content
127.0.0.1:54629: GET http://connectivitycheck.gstatic.com/generate_204
              << 204 No Content 0b
[15:11:55.872][127.0.0.1:54629] server disconnect connectivitycheck.gstatic.com:80 (************:80)
[15:11:55.872][127.0.0.1:54629] client disconnect
[15:11:58.247][127.0.0.1:54634] client connect
🌐 HTTP: GET http://play.googleapis.com/generate_204
[15:11:58.255][127.0.0.1:54634] server connect play.googleapis.com:80 (198.18.0.111:80)
[15:11:58.584][127.0.0.1:54627] Client TLS handshake failed. The client does not trust the proxy's certificate for www.google.com (OpenSSL Error([('SSL routines', '', 'sslv3 alert certificate unknown')]))
[15:11:58.585][127.0.0.1:54627] client disconnect
[15:11:58.586][127.0.0.1:54627] server disconnect www.google.com:443 (***********:443)
   ✅ 204 No Content
127.0.0.1:54634: GET http://play.googleapis.com/generate_204
              << 204 No Content 0b
[15:11:59.108][127.0.0.1:54634] server disconnect play.googleapis.com:80 (198.18.0.111:80)
[15:11:59.109][127.0.0.1:54634] client disconnect
[15:14:07.246][127.0.0.1:54960] client connect
[15:14:07.266][127.0.0.1:54961] client connect
🌐 HTTP: GET http://connectivitycheck.gstatic.com/generate_204
[15:14:07.304][127.0.0.1:54960] server connect www.google.com:443 (***********:443)
[15:14:07.311][127.0.0.1:54961] server connect connectivitycheck.gstatic.com:80 (************:80)
   ✅ 204 No Content
127.0.0.1:54961: GET http://connectivitycheck.gstatic.com/generate_204
              << 204 No Content 0b
[15:14:07.748][127.0.0.1:54961] server disconnect connectivitycheck.gstatic.com:80 (************:80)
[15:14:07.749][127.0.0.1:54961] client disconnect
[15:14:09.508][127.0.0.1:54960] Client TLS handshake failed. The client does not trust the proxy's certificate for www.google.com (OpenSSL Error([('SSL routines', '', 'sslv3 alert certificate unknown')]))
[15:14:09.510][127.0.0.1:54960] client disconnect
[15:14:09.512][127.0.0.1:54960] server disconnect www.google.com:443 (***********:443)
[15:14:09.517][127.0.0.1:54969] client connect
🌐 HTTP: GET http://www.google.com/gen_204
[15:14:09.528][127.0.0.1:54969] server connect www.google.com:80 (***********:80)
   ✅ 204 No Content
127.0.0.1:54969: GET http://www.google.com/gen_204
              << 204 No Content 0b
[15:14:13.070][127.0.0.1:54969] server disconnect www.google.com:80 (***********:80)
[15:14:13.071][127.0.0.1:54969] client disconnect

📊 捕获完成!
总请求数: 45
HTTPS请求: 0
唯一主机: 4
数据已保存到: captured_https_urls.json
