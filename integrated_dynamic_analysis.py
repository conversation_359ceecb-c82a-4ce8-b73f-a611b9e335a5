#!/usr/bin/env python3
"""
集成化动态分析系统
结合文件组织功能的完整动态分析流程
"""

import asyncio
import os
import time
import logging
import json
from datetime import datetime
from pathlib import Path
import subprocess

# 导入我们之前创建的工具
from capture_result_organizer import CaptureResultOrganizer

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class IntegratedDynamicAnalysis:
    """集成化动态分析系统"""
    
    def __init__(self, package_name: str, apk_path: str = None):
        self.package_name = package_name
        self.apk_path = apk_path
        self.device_id = "emulator-5554"
        self.session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.organizer = CaptureResultOrganizer()
        
        # 获取应用专用文件夹
        self.app_folder = self.organizer.get_app_folder(package_name)
        self.session_folder = self.app_folder / f"session_{self.session_id}"
        
    def run_adb_command(self, command, timeout=30):
        """执行ADB命令"""
        try:
            full_command = ['adb', '-s', self.device_id] + command
            result = subprocess.run(
                full_command, 
                capture_output=True, 
                text=True, 
                timeout=timeout
            )
            return result
        except Exception as e:
            logger.error(f"ADB命令执行异常: {e}")
            return None
    
    async def run_complete_analysis(self):
        """运行完整的动态分析流程"""
        logger.info("="*60)
        logger.info(f"🚀 开始 {self.package_name} 的完整动态分析")
        logger.info(f"📁 会话ID: {self.session_id}")
        logger.info("="*60)
        
        analysis_start_time = time.time()
        
        try:
            # 1. 准备环境
            await self._prepare_environment()
            
            # 2. APK安装（如果提供了APK路径）
            if self.apk_path:
                await self._install_apk()
            
            # 3. 启动网络捕获
            mitm_process = await self._start_network_capture()
            
            # 4. 启动SSL绕过
            frida_process = await self._start_ssl_bypass()
            
            # 5. 执行动态分析
            analysis_result = await self._perform_dynamic_analysis()
            
            # 6. 停止捕获服务
            await self._stop_capture_services(mitm_process, frida_process)
            
            # 7. 整理结果文件
            await self._organize_results()
            
            analysis_duration = time.time() - analysis_start_time
            
            logger.info("="*60)
            logger.info(f"✅ 动态分析完成! 耗时: {analysis_duration:.2f}秒")
            logger.info(f"📂 结果文件夹: {self.session_folder}")
            logger.info("="*60)
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"❌ 动态分析过程异常: {e}")
            return None
    
    async def _prepare_environment(self):
        """准备分析环境"""
        logger.info("🔧 准备分析环境...")
        
        # 检查设备连接
        result = self.run_adb_command(['devices'])
        if not (result and self.device_id in result.stdout):
            raise Exception(f"设备 {self.device_id} 未连接")
        
        # 停止应用
        self.run_adb_command(['shell', 'am', 'force-stop', self.package_name])
        
        # 配置代理
        proxy_port = 8090  # 使用新端口避免冲突
        self.proxy_port = proxy_port
        self.run_adb_command(['shell', 'settings', 'put', 'global', 'http_proxy', f'127.0.0.1:{proxy_port}'])
        
        logger.info("✅ 环境准备完成")
    
    async def _install_apk(self):
        """安装APK"""
        logger.info(f"📱 安装APK: {self.apk_path}")
        
        if not os.path.exists(self.apk_path):
            logger.error(f"APK文件不存在: {self.apk_path}")
            return
        
        result = self.run_adb_command(['install', '-r', self.apk_path], timeout=60)
        if result and result.returncode == 0:
            logger.info("✅ APK安装成功")
        else:
            logger.error(f"❌ APK安装失败: {result.stderr if result else '未知错误'}")
    
    async def _start_network_capture(self):
        """启动网络捕获"""
        logger.info("🌐 启动网络流量捕获...")
        
        # 创建网络日志文件
        network_log_file = self.session_folder / "network_logs" / f"mitm_capture_{self.session_id}.log"
        self.session_folder.mkdir(parents=True, exist_ok=True)
        (self.session_folder / "network_logs").mkdir(exist_ok=True)
        
        try:
            # 启动mitmproxy
            mitm_cmd = [
                'mitmproxy',
                '--listen-host', '0.0.0.0',
                '--listen-port', str(self.proxy_port),
                '--set', f'confdir={self.organizer.base_dir}/mitm-config',
                '--save-stream-file', str(network_log_file)
            ]
            
            mitm_process = subprocess.Popen(
                mitm_cmd,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )
            
            # 等待mitmproxy启动
            await asyncio.sleep(3)
            
            logger.info(f"✅ 网络捕获已启动 (端口: {self.proxy_port})")
            return mitm_process
            
        except Exception as e:
            logger.error(f"❌ 网络捕获启动失败: {e}")
            return None
    
    async def _start_ssl_bypass(self):
        """启动SSL绕过"""
        logger.info("🔒 启动SSL绕过...")
        
        try:
            ssl_script_path = self.organizer.base_dir / "ssl_bypass.js"
            if not ssl_script_path.exists():
                logger.warning("SSL绕过脚本不存在，跳过")
                return None
            
            frida_cmd = [
                'frida', '-U', '-l', str(ssl_script_path), '-f', self.package_name
            ]
            
            frida_process = subprocess.Popen(
                frida_cmd,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )
            
            logger.info("✅ SSL绕过已启动")
            return frida_process
            
        except Exception as e:
            logger.error(f"❌ SSL绕过启动失败: {e}")
            return None
    
    async def _perform_dynamic_analysis(self):
        """执行动态分析"""
        logger.info("🎯 执行动态分析...")
        
        analysis_result = {
            "package_name": self.package_name,
            "session_id": self.session_id,
            "start_time": datetime.now().isoformat(),
            "ui_interactions": 0,
            "screenshots_taken": 0,
            "network_requests": 0,
            "analysis_duration": 0
        }
        
        start_time = time.time()
        
        try:
            # 启动应用
            launch_result = self.run_adb_command([
                'shell', 'am', 'start', '-n', 
                f'{self.package_name}/.activity.LoginActivity'  # 可能需要根据实际情况调整
            ])
            
            if not (launch_result and launch_result.returncode == 0):
                # 尝试通过包名启动
                self.run_adb_command(['shell', 'monkey', '-p', self.package_name, '-c', 'android.intent.category.LAUNCHER', '1'])
            
            # 等待应用启动
            await asyncio.sleep(5)
            
            # 截图
            await self._take_screenshot("app_startup")
            analysis_result["screenshots_taken"] += 1
            
            # 模拟用户交互
            interactions = await self._simulate_user_interactions()
            analysis_result["ui_interactions"] = interactions
            
            # 最终截图
            await self._take_screenshot("final_state")
            analysis_result["screenshots_taken"] += 1
            
            analysis_result["analysis_duration"] = time.time() - start_time
            
            logger.info(f"✅ 动态分析完成 ({interactions} 次交互)")
            
        except Exception as e:
            logger.error(f"❌ 动态分析异常: {e}")
            analysis_result["error"] = str(e)
        
        return analysis_result
    
    async def _take_screenshot(self, name: str):
        """截取屏幕截图"""
        try:
            screenshots_dir = self.session_folder / "screenshots"
            screenshots_dir.mkdir(exist_ok=True)
            
            screenshot_file = screenshots_dir / f"{name}_{self.session_id}.png"
            
            # Android截图
            self.run_adb_command(['shell', 'screencap', '/sdcard/temp_screenshot.png'])
            
            # 拉取截图
            pull_result = subprocess.run([
                'adb', '-s', self.device_id, 'pull', '/sdcard/temp_screenshot.png', str(screenshot_file)
            ], capture_output=True, text=True)
            
            if pull_result.returncode == 0:
                logger.info(f"📸 截图已保存: {screenshot_file.name}")
                return True
            
        except Exception as e:
            logger.error(f"截图失败: {e}")
            return False
    
    async def _simulate_user_interactions(self):
        """模拟用户交互"""
        logger.info("🎯 模拟用户交互...")
        
        interaction_count = 0
        
        # 简单的交互序列
        interactions = [
            {"action": "wait", "duration": 3},
            {"action": "tap", "x": 540, "y": 960, "description": "点击屏幕中心"},
            {"action": "wait", "duration": 2},
            {"action": "tap", "x": 800, "y": 1500, "description": "点击右下角"},
            {"action": "wait", "duration": 2},
            {"action": "swipe", "description": "向上滑动"},
            {"action": "wait", "duration": 3},
            {"action": "back", "description": "返回键"},
            {"action": "wait", "duration": 2}
        ]
        
        for interaction in interactions:
            try:
                if interaction["action"] == "wait":
                    await asyncio.sleep(interaction["duration"])
                elif interaction["action"] == "tap":
                    self.run_adb_command(['shell', 'input', 'tap', str(interaction["x"]), str(interaction["y"])])
                    interaction_count += 1
                elif interaction["action"] == "swipe":
                    self.run_adb_command(['shell', 'input', 'swipe', '500', '1000', '500', '500'])
                    interaction_count += 1
                elif interaction["action"] == "back":
                    self.run_adb_command(['shell', 'input', 'keyevent', 'KEYCODE_BACK'])
                    interaction_count += 1
                    
                logger.info(f"执行交互: {interaction.get('description', interaction['action'])}")
                
            except Exception as e:
                logger.error(f"交互执行失败: {e}")
        
        return interaction_count
    
    async def _stop_capture_services(self, mitm_process, frida_process):
        """停止捕获服务"""
        logger.info("🛑 停止捕获服务...")
        
        if mitm_process:
            mitm_process.terminate()
            try:
                mitm_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                mitm_process.kill()
        
        if frida_process:
            frida_process.terminate()
            try:
                frida_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                frida_process.kill()
        
        logger.info("✅ 捕获服务已停止")
    
    async def _organize_results(self):
        """整理结果文件"""
        logger.info("🗂️  整理分析结果...")
        
        # 使用组织器整理文件
        result = self.organizer.organize_files_for_app(self.package_name, self.session_id)
        
        # 更新索引
        self.organizer.create_app_index()
        
        logger.info("✅ 结果文件整理完成")
        return result

async def main():
    """主函数 - 示例用法"""
    
    # 分析参数
    package_name = "com.iloda.beacon"
    apk_path = "/Users/<USER>/Desktop/project/apk_detect/apk/5577.com.iloda.beacon.apk"
    
    # 创建分析实例
    analyzer = IntegratedDynamicAnalysis(package_name, apk_path)
    
    # 运行分析
    result = await analyzer.run_complete_analysis()
    
    if result:
        logger.info("🎉 分析成功完成!")
        logger.info(f"📊 分析结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
    else:
        logger.error("❌ 分析失败")
    
    return 0

if __name__ == "__main__":
    asyncio.run(main())




