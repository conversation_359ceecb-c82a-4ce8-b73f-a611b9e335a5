#!/usr/bin/env python3
"""
完整的集成测试
包括mitmproxy启动、Frida SSL绕过、网络捕获测试
"""

import subprocess
import sys
import time
import json
import signal
from pathlib import Path
import threading

class CompleteIntegrationTest:
    def __init__(self):
        self.package = "com.yjzx.yjzx2017"
        self.mitmproxy_process = None
        self.frida_process = None
        self.running = False
        
    def log(self, message, level="INFO"):
        """日志输出"""
        timestamp = time.strftime("%H:%M:%S")
        if level == "SUCCESS":
            print(f"🎉 [{timestamp}] {message}")
        elif level == "ERROR":
            print(f"❌ [{timestamp}] {message}")
        elif level == "WARNING":
            print(f"⚠️  [{timestamp}] {message}")
        else:
            print(f"📋 [{timestamp}] {message}")
    
    def run_adb(self, cmd, timeout=30):
        """执行adb命令"""
        full_cmd = f"source android_env.sh && adb {cmd}"
        try:
            result = subprocess.run(full_cmd, shell=True, 
                                  capture_output=True, text=True, 
                                  timeout=timeout, check=True)
            return result.returncode, result.stdout.strip(), result.stderr.strip()
        except subprocess.CalledProcessError as e:
            return e.returncode, "", e.stderr
        except subprocess.TimeoutExpired:
            return -1, "", "Timeout"
    
    def start_mitmproxy(self):
        """启动mitmproxy"""
        self.log("启动mitmproxy网络捕获...")
        
        # 确保日志目录存在
        Path("mitm-logs").mkdir(exist_ok=True)
        
        # 清空捕获文件
        capture_file = Path("mitm-logs/realtime_capture.json")
        with open(capture_file, 'w') as f:
            json.dump([], f)
        
        # 启动mitmproxy
        cmd = "mitmdump -s capture_script.py --listen-port 8080 --set confdir=./mitm-config"
        
        try:
            self.mitmproxy_process = subprocess.Popen(
                cmd, shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.log("mitmproxy进程已启动")
            time.sleep(5)  # 等待mitmproxy启动
            
            if self.mitmproxy_process.poll() is None:
                self.log("mitmproxy运行正常")
                return True
            else:
                stdout, stderr = self.mitmproxy_process.communicate()
                self.log(f"mitmproxy启动失败: {stderr}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"mitmproxy启动异常: {e}", "ERROR")
            return False
    
    def setup_android_proxy(self):
        """设置Android代理"""
        self.log("设置Android网络代理...")
        
        # 设置HTTP代理
        returncode, stdout, stderr = self.run_adb("shell settings put global http_proxy *************:8080")
        if returncode == 0:
            self.log("HTTP代理设置成功")
        else:
            self.log(f"HTTP代理设置失败: {stderr}", "WARNING")
        
        # 验证代理设置
        returncode, stdout, stderr = self.run_adb("shell settings get global http_proxy")
        if "*************:8080" in stdout:
            self.log("代理设置验证成功")
            return True
        else:
            self.log("代理设置验证失败", "WARNING")
            return False
    
    def start_frida_environment(self):
        """启动Frida环境"""
        self.log("启动Frida环境...")
        
        # 启动frida-server
        self.run_adb("shell pkill frida-server")
        time.sleep(2)
        
        subprocess.Popen("source android_env.sh && adb shell '/data/local/tmp/frida-server' 2>/dev/null &", shell=True)
        time.sleep(8)
        
        # 验证frida-server
        returncode, stdout, stderr = self.run_adb("shell ps | grep frida-server")
        if "frida-server" not in stdout:
            self.log("frida-server启动失败", "ERROR")
            return False
        
        self.log("frida-server启动成功")
        
        # 启动应用
        self.run_adb(f"shell am force-stop {self.package}")
        time.sleep(2)
        
        returncode, stdout, stderr = self.run_adb(f"shell am start -n {self.package}/.controller.activity.splash.SplashActivity")
        if returncode == 0:
            self.log("应用启动成功")
            time.sleep(8)
            return True
        else:
            self.log(f"应用启动失败: {stderr}", "ERROR")
            return False
    
    def attempt_frida_ssl_bypass(self):
        """尝试Frida SSL绕过"""
        self.log("尝试Frida SSL绕过...")
        
        # 获取应用进程信息
        returncode, stdout, stderr = self.run_adb(f"shell ps | grep {self.package}")
        
        if self.package not in stdout:
            self.log("应用进程未找到", "ERROR")
            return False
        
        # 提取PID
        lines = stdout.split('\n')
        target_pid = None
        
        for line in lines:
            if self.package in line:
                parts = line.split()
                if len(parts) >= 2:
                    pid = parts[1]
                    if 'pushcore' not in line:
                        target_pid = pid
                        break
                    elif not target_pid:
                        target_pid = pid
        
        if target_pid:
            self.log(f"找到目标进程 PID: {target_pid}")
            
            # 尝试使用PID连接
            cmd = f"export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin && timeout 30 frida -U {target_pid} -l ssl_bypass.js"
            
            try:
                self.frida_process = subprocess.Popen(
                    cmd, shell=True,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                
                self.log("Frida SSL绕过进程已启动")
                time.sleep(15)
                
                if self.frida_process.poll() is None:
                    self.log("Frida SSL绕过可能正在运行")
                    return True
                else:
                    stdout, stderr = self.frida_process.communicate()
                    if 'system_server' in stderr:
                        self.log("遇到system_server问题（可忽略）", "WARNING")
                    else:
                        self.log(f"Frida连接问题: {stderr}", "WARNING")
                    return False
                    
            except Exception as e:
                self.log(f"Frida启动异常: {e}", "ERROR")
                return False
        else:
            self.log("未找到有效的目标PID", "ERROR")
            return False
    
    def test_network_capture(self):
        """测试网络捕获"""
        self.log("测试网络捕获功能...")
        
        # 触发网络请求
        test_urls = [
            "http://httpbin.org/get",  # HTTP请求
            "https://httpbin.org/get", # HTTPS请求
            "https://www.baidu.com"    # 另一个HTTPS请求
        ]
        
        for i, url in enumerate(test_urls, 1):
            self.log(f"测试 {i}/{len(test_urls)}: {url}")
            self.run_adb(f"shell am start -a android.intent.action.VIEW -d {url}")
            time.sleep(10)
        
        # 应用内操作
        self.log("执行应用内操作...")
        self.run_adb("shell input tap 540 960")
        time.sleep(5)
        
        # 等待网络请求处理
        self.log("等待网络请求处理...")
        time.sleep(20)
        
        return self.analyze_capture_results()
    
    def analyze_capture_results(self):
        """分析捕获结果"""
        self.log("分析网络捕获结果...")
        
        capture_file = Path("mitm-logs/realtime_capture.json")
        
        try:
            with open(capture_file, 'r') as f:
                data = json.load(f)
            
            if not isinstance(data, list):
                self.log("捕获文件格式错误", "ERROR")
                return False
            
            total = len(data)
            https_reqs = [req for req in data if req.get('scheme') == 'https']
            http_reqs = [req for req in data if req.get('scheme') == 'http']
            
            self.log(f"捕获统计: 总请求={total}, HTTPS={len(https_reqs)}, HTTP={len(http_reqs)}")
            
            if len(https_reqs) > 0:
                self.log("🎉 SSL绕过成功！捕获到HTTPS请求！", "SUCCESS")
                
                self.log("HTTPS请求详情:")
                for i, req in enumerate(https_reqs[:5], 1):
                    url = req.get('url', 'N/A')
                    method = req.get('method', 'N/A')
                    status = req.get('status_code', 'N/A')
                    host = req.get('host', 'N/A')
                    
                    self.log(f"  {i}. {method} {url}")
                    self.log(f"     主机: {host}, 状态: {status}")
                
                return "FULL_SUCCESS"
            elif len(http_reqs) > 0:
                self.log("捕获到HTTP请求，基础网络监控正常", "SUCCESS")
                
                self.log("HTTP请求详情:")
                for i, req in enumerate(http_reqs[:3], 1):
                    url = req.get('url', 'N/A')
                    method = req.get('method', 'N/A')
                    self.log(f"  {i}. {method} {url}")
                
                return "PARTIAL_SUCCESS"
            elif total > 0:
                self.log("捕获到请求但协议未识别", "WARNING")
                return "BASIC_SUCCESS"
            else:
                self.log("没有捕获到网络请求", "WARNING")
                return "NO_CAPTURE"
                
        except Exception as e:
            self.log(f"分析结果失败: {e}", "ERROR")
            return "ERROR"
    
    def cleanup(self):
        """清理资源"""
        self.log("清理系统资源...")
        self.running = False
        
        # 停止Frida进程
        if self.frida_process and self.frida_process.poll() is None:
            self.log("停止Frida进程...")
            self.frida_process.terminate()
            try:
                self.frida_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.frida_process.kill()
        
        # 停止mitmproxy
        if self.mitmproxy_process and self.mitmproxy_process.poll() is None:
            self.log("停止mitmproxy...")
            self.mitmproxy_process.terminate()
            try:
                self.mitmproxy_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.mitmproxy_process.kill()
        
        # 停止frida-server
        self.run_adb("shell pkill frida-server")
        
        # 清除代理设置
        self.run_adb("shell settings delete global http_proxy")
        
        self.log("清理完成")
    
    def run_complete_integration_test(self):
        """运行完整的集成测试"""
        self.log("🚀 启动完整的APK动态分析集成测试")
        self.log("🔧 包括mitmproxy + Frida SSL绕过 + 网络捕获")
        self.log("=" * 80)
        
        try:
            # 步骤1: 启动mitmproxy
            if not self.start_mitmproxy():
                self.log("mitmproxy启动失败，但继续测试", "WARNING")
            
            # 步骤2: 设置Android代理
            self.setup_android_proxy()
            
            # 步骤3: 启动Frida环境
            if not self.start_frida_environment():
                return False
            
            # 步骤4: 尝试Frida SSL绕过
            frida_success = self.attempt_frida_ssl_bypass()
            if frida_success:
                self.log("Frida SSL绕过可能已启动")
            else:
                self.log("Frida SSL绕过未完全成功，但继续测试", "WARNING")
            
            # 步骤5: 测试网络捕获
            self.log("=" * 70)
            self.log("🔍 测试网络捕获功能")
            self.log("=" * 70)
            
            result = self.test_network_capture()
            
            # 分析最终结果
            if result == "FULL_SUCCESS":
                self.log("🎉 完整集成测试成功！", "SUCCESS")
                self.log("✅ HTTPS流量完全透明捕获！")
                self.log("✅ SSL证书验证已被绕过！")
                self.log("✅ APK动态分析系统完全可用！")
                
                self.log("📋 系统现在具备:")
                self.log("   ✅ 完整的网络流量捕获")
                self.log("   ✅ HTTPS SSL绕过")
                self.log("   ✅ 实时流量分析")
                self.log("   ✅ API端点发现")
                self.log("   ✅ 安全漏洞检测")
                
                return True
            elif result == "PARTIAL_SUCCESS":
                self.log("部分成功：HTTP捕获正常，HTTPS需要进一步调试", "SUCCESS")
                self.log("✅ 基础网络监控系统可用")
                self.log("🔧 SSL绕过需要进一步优化")
                return True
            elif result == "BASIC_SUCCESS":
                self.log("基础成功：网络捕获系统工作", "SUCCESS")
                self.log("🔧 需要进一步调试协议识别")
                return True
            else:
                self.log("集成测试未完全成功", "WARNING")
                self.log("🔧 需要检查mitmproxy和网络配置")
                return False
                
        except KeyboardInterrupt:
            self.log("用户中断测试")
            return False
        except Exception as e:
            self.log(f"集成测试异常: {e}", "ERROR")
            return False
        finally:
            self.cleanup()

def main():
    test_system = CompleteIntegrationTest()
    
    def signal_handler(sig, frame):
        print("\n⚠️  接收到中断信号...")
        test_system.cleanup()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    
    success = test_system.run_complete_integration_test()
    
    if success:
        print("\n🎯 APK动态分析系统集成测试成功！")
        print("💡 系统现在可以用于完整的安全分析！")
    else:
        print("\n🔧 集成测试需要进一步调试")
        print("💡 但基础组件已经建立")

if __name__ == "__main__":
    main()
