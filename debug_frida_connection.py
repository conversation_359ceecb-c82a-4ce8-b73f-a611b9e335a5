#!/usr/bin/env python3
"""
调试Frida连接问题
专门解决Frida无法连接到应用的问题
"""

import subprocess
import sys
import time
import json
from pathlib import Path

def run_adb(cmd, timeout=30):
    """执行adb命令"""
    full_cmd = f"source android_env.sh && adb {cmd}"
    try:
        result = subprocess.run(full_cmd, shell=True, 
                              capture_output=True, text=True, 
                              timeout=timeout, check=True)
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        return f"ERROR: {e.stderr}"
    except subprocess.TimeoutExpired:
        return "ERROR: Timeout"

def run_frida_cmd(cmd, timeout=30):
    """执行Frida命令"""
    full_cmd = f"export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin && {cmd}"
    try:
        result = subprocess.run(full_cmd, shell=True, 
                              capture_output=True, text=True, 
                              timeout=timeout)
        return result.returncode, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return -1, "", "Timeout"

def debug_frida_server():
    """调试frida-server状态"""
    print("🔍 调试frida-server状态")
    print("-" * 40)
    
    # 检查frida-server进程
    result = run_adb("shell ps | grep frida-server")
    print(f"frida-server进程: {result}")
    
    if "frida-server" not in result:
        print("❌ frida-server未运行，重新启动...")
        
        # 杀死可能的残留进程
        run_adb("shell pkill frida-server")
        time.sleep(2)
        
        # 重新启动
        subprocess.Popen("source android_env.sh && adb shell '/data/local/tmp/frida-server &'", shell=True)
        time.sleep(8)
        
        # 再次检查
        result = run_adb("shell ps | grep frida-server")
        print(f"重启后frida-server进程: {result}")
        
        if "frida-server" not in result:
            print("❌ frida-server启动失败")
            return False
    
    print("✅ frida-server正在运行")
    
    # 检查端口
    port_result = run_adb("shell netstat -tlnp | grep 27042")
    print(f"Frida端口状态: {port_result}")
    
    return True

def debug_target_app():
    """调试目标应用状态"""
    print("\n🔍 调试目标应用状态")
    print("-" * 40)
    
    package = "com.yjzx.yjzx2017"
    
    # 检查应用进程
    result = run_adb(f"shell ps | grep {package}")
    print(f"应用进程: {result}")
    
    if package not in result:
        print("⚠️  主进程未找到，尝试启动应用...")
        
        # 启动应用
        launch_result = run_adb(f"shell am start -n {package}/.controller.activity.splash.SplashActivity")
        print(f"启动结果: {launch_result}")
        
        time.sleep(5)
        
        # 再次检查
        result = run_adb(f"shell ps | grep {package}")
        print(f"启动后应用进程: {result}")
    
    # 提取PID
    if package in result:
        lines = result.split('\n')
        for line in lines:
            if package in line and 'pushcore' not in line:  # 排除pushcore进程
                parts = line.split()
                if len(parts) >= 2:
                    pid = parts[1]
                    print(f"✅ 找到主应用进程 PID: {pid}")
                    return pid
    
    print("❌ 未找到主应用进程")
    return None

def test_frida_connection():
    """测试Frida连接"""
    print("\n🔍 测试Frida连接")
    print("-" * 40)
    
    # 测试frida-ps
    print("📋 测试frida-ps...")
    returncode, stdout, stderr = run_frida_cmd("frida-ps -U", 10)
    
    if returncode == 0:
        print("✅ frida-ps工作正常")
        
        # 查找目标应用
        if "易金在线" in stdout:
            print("✅ 在进程列表中找到目标应用")
            
            # 提取应用信息
            lines = stdout.split('\n')
            for line in lines:
                if "易金在线" in line:
                    parts = line.split()
                    if len(parts) >= 2:
                        pid = parts[0]
                        name = ' '.join(parts[1:])
                        print(f"   PID: {pid}, 名称: {name}")
                        return pid
        else:
            print("⚠️  在进程列表中未找到目标应用")
            print("进程列表前10行:")
            lines = stdout.split('\n')
            for line in lines[:10]:
                if line.strip():
                    print(f"   {line}")
    else:
        print(f"❌ frida-ps失败: {stderr}")
    
    return None

def try_different_attach_methods(app_pid):
    """尝试不同的附加方法"""
    print(f"\n🔧 尝试不同的Frida附加方法 (PID: {app_pid})")
    print("-" * 50)
    
    package = "com.yjzx.yjzx2017"
    
    # 方法列表
    methods = [
        ("PID附加", f"frida -U {app_pid} -l ssl_bypass.js"),
        ("包名附加", f"frida -U {package} -l ssl_bypass.js"),
        ("应用名附加", f"frida -U '易金在线' -l ssl_bypass.js"),
        ("Spawn模式", f"frida -U -f {package} -l ssl_bypass.js"),
        ("无脚本附加", f"frida -U {app_pid}"),
    ]
    
    for method_name, cmd in methods:
        print(f"\n🔄 尝试方法: {method_name}")
        print(f"   命令: {cmd}")
        
        # 启动Frida进程
        full_cmd = f"export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin && timeout 15 {cmd}"
        
        try:
            process = subprocess.Popen(
                full_cmd, shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True
            )
            
            # 等待一段时间
            time.sleep(10)
            
            if process.poll() is None:
                print("   ✅ 连接成功！Frida正在运行...")
                
                # 读取输出
                try:
                    output = process.stdout.read(1000)
                    if output:
                        print("   📋 Frida输出:")
                        print("   " + "\n   ".join(output.split('\n')[:10]))
                except:
                    pass
                
                # 终止进程
                process.terminate()
                return True
            else:
                stdout, _ = process.communicate()
                print(f"   ❌ 连接失败")
                if stdout:
                    error_lines = stdout.split('\n')[:5]
                    for line in error_lines:
                        if line.strip():
                            print(f"      {line}")
                
        except Exception as e:
            print(f"   ❌ 异常: {e}")
    
    return False

def try_spawn_mode():
    """尝试spawn模式"""
    print("\n🚀 尝试Spawn模式启动")
    print("-" * 40)
    
    package = "com.yjzx.yjzx2017"
    
    # 先杀死应用
    print("🔄 杀死现有应用进程...")
    run_adb(f"shell am force-stop {package}")
    time.sleep(3)
    
    # 使用spawn模式启动
    cmd = f"export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin && frida -U -f {package} -l ssl_bypass.js --resume"
    print(f"📋 执行命令: {cmd}")
    
    try:
        process = subprocess.Popen(
            cmd, shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True
        )
        
        # 等待启动
        time.sleep(15)
        
        if process.poll() is None:
            print("✅ Spawn模式成功！")
            
            # 读取输出
            try:
                output = process.stdout.read(2000)
                if output:
                    print("📋 Frida输出:")
                    lines = output.split('\n')
                    for line in lines[:15]:
                        if line.strip():
                            print(f"   {line}")
            except:
                pass
            
            return process
        else:
            stdout, _ = process.communicate()
            print("❌ Spawn模式失败")
            if stdout:
                lines = stdout.split('\n')
                for line in lines[:10]:
                    if line.strip():
                        print(f"   {line}")
            
    except Exception as e:
        print(f"❌ Spawn模式异常: {e}")
    
    return None

def main():
    print("🚀 Frida连接问题调试")
    print("🔧 专门解决SSL绕过连接问题")
    print("=" * 60)
    
    try:
        # 步骤1: 调试frida-server
        if not debug_frida_server():
            return False
        
        # 步骤2: 调试目标应用
        app_pid = debug_target_app()
        
        # 步骤3: 测试Frida连接
        frida_pid = test_frida_connection()
        if frida_pid:
            app_pid = frida_pid  # 使用Frida检测到的PID
        
        if not app_pid:
            print("❌ 无法找到目标应用，尝试spawn模式...")
            frida_process = try_spawn_mode()
            
            if frida_process:
                print("\n🎉 Spawn模式成功！SSL绕过脚本正在运行！")
                print("⚠️  按Ctrl+C停止")
                
                try:
                    frida_process.wait()
                except KeyboardInterrupt:
                    print("\n⚠️  停止Frida进程...")
                    frida_process.terminate()
                
                return True
            else:
                print("❌ 所有方法都失败了")
                return False
        
        # 步骤4: 尝试不同的附加方法
        success = try_different_attach_methods(app_pid)
        
        if success:
            print("\n🎉 找到了工作的连接方法！")
            print("💡 现在可以手动运行成功的命令来启动SSL绕过")
        else:
            print("\n❌ 所有附加方法都失败了")
            print("🔧 可能的解决方案:")
            print("   1. 重启模拟器")
            print("   2. 重新安装应用")
            print("   3. 使用不同版本的frida-server")
            print("   4. 检查SELinux设置")
        
        return success
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断")
        return False
    except Exception as e:
        print(f"\n❌ 异常: {e}")
        return False
    finally:
        # 清理
        print("🧹 清理资源...")
        subprocess.run("source android_env.sh && adb shell pkill frida-server", shell=True)

if __name__ == "__main__":
    main()
