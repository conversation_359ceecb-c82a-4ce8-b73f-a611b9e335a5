#!/usr/bin/env python3
"""
本地模拟器动态分析结构测试
验证代码结构和基本功能，不依赖真实的Android SDK
"""
import asyncio
import logging
import sys
from pathlib import Path
import time
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class LocalDynamicStructureTester:
    """本地动态分析结构测试类"""
    
    def __init__(self):
        self.test_results = {}
        self.test_apk = project_root / "apk" / "com.android.vending_289.com.apk"
        
        # 确保测试APK存在
        if not self.test_apk.exists():
            logger.warning(f"Test APK not found: {self.test_apk}")
            # 创建一个虚拟APK文件用于测试
            self.test_apk.parent.mkdir(exist_ok=True)
            self.test_apk.write_bytes(b"dummy apk content")
        
        logger.info(f"Using test APK: {self.test_apk}")
    
    async def run_all_tests(self):
        """运行所有结构测试"""
        logger.info("🚀 Starting Local Dynamic Analysis Structure Tests")
        print("=" * 80)
        
        tests = [
            ("Import Test", self.test_imports),
            ("Class Instantiation Test", self.test_class_instantiation),
            ("API Route Test", self.test_api_routes),
            ("Model Structure Test", self.test_model_structure),
        ]
        
        for test_name, test_func in tests:
            try:
                logger.info(f"Running: {test_name}")
                result = await test_func()
                self.test_results[test_name] = {
                    'status': 'PASS' if result else 'FAIL',
                    'details': result if isinstance(result, dict) else {}
                }
                status = "✅ PASS" if result else "❌ FAIL"
                logger.info(f"{test_name}: {status}")
            except Exception as e:
                logger.error(f"{test_name}: ❌ ERROR - {e}")
                self.test_results[test_name] = {
                    'status': 'ERROR',
                    'error': str(e)
                }
            
            print("-" * 40)
        
        # 生成测试报告
        self.generate_test_report()
    
    async def test_imports(self) -> dict:
        """测试所有模块导入"""
        try:
            import_results = {}
            
            # 测试核心模块导入
            modules_to_test = [
                ('LocalEmulatorManager', 'src.services.local_emulator_manager'),
                ('LocalDynamicAnalyzer', 'src.services.local_dynamic_analyzer'),
                ('LocalUIAutomator', 'src.services.local_ui_automator'),
                ('local_dynamic API', 'src.api.routes.local_dynamic'),
            ]
            
            for module_name, module_path in modules_to_test:
                try:
                    __import__(module_path)
                    import_results[module_name] = 'SUCCESS'
                    logger.info(f"✅ {module_name} imported successfully")
                except Exception as e:
                    import_results[module_name] = f'FAILED: {str(e)}'
                    logger.error(f"❌ {module_name} import failed: {e}")
            
            return import_results
            
        except Exception as e:
            logger.error(f"Import test failed: {e}")
            return False
    
    async def test_class_instantiation(self) -> dict:
        """测试类实例化"""
        try:
            instantiation_results = {}
            
            # 测试UI自动化器（不需要Android SDK）
            try:
                from src.services.local_ui_automator import LocalUIAutomator
                ui_automator = LocalUIAutomator()
                instantiation_results['LocalUIAutomator'] = 'SUCCESS'
                logger.info("✅ LocalUIAutomator instantiated successfully")
            except Exception as e:
                instantiation_results['LocalUIAutomator'] = f'FAILED: {str(e)}'
                logger.error(f"❌ LocalUIAutomator instantiation failed: {e}")
            
            # 测试模拟器管理器（预期会失败，但不应该崩溃）
            try:
                from src.services.local_emulator_manager import LocalEmulatorManager
                # 这里预期会失败，因为没有Android SDK
                emulator_manager = LocalEmulatorManager()
                instantiation_results['LocalEmulatorManager'] = 'UNEXPECTED_SUCCESS'
            except Exception as e:
                instantiation_results['LocalEmulatorManager'] = f'EXPECTED_FAILURE: {str(e)}'
                logger.info(f"✅ LocalEmulatorManager failed as expected: {e}")
            
            # 测试动态分析器（预期会失败）
            try:
                from src.services.local_dynamic_analyzer import LocalDynamicAnalyzer
                analyzer = LocalDynamicAnalyzer()
                instantiation_results['LocalDynamicAnalyzer'] = 'UNEXPECTED_SUCCESS'
            except Exception as e:
                instantiation_results['LocalDynamicAnalyzer'] = f'EXPECTED_FAILURE: {str(e)}'
                logger.info(f"✅ LocalDynamicAnalyzer failed as expected: {e}")
            
            return instantiation_results
            
        except Exception as e:
            logger.error(f"Class instantiation test failed: {e}")
            return False
    
    async def test_api_routes(self) -> dict:
        """测试API路由结构"""
        try:
            route_results = {}
            
            # 测试API路由导入
            try:
                from src.api.routes import local_dynamic
                route_results['local_dynamic_import'] = 'SUCCESS'
                
                # 检查路由器是否存在
                if hasattr(local_dynamic, 'router'):
                    route_results['router_exists'] = 'SUCCESS'
                    
                    # 检查路由数量
                    routes = local_dynamic.router.routes
                    route_results['route_count'] = len(routes)
                    
                    # 列出所有路由
                    route_paths = []
                    for route in routes:
                        if hasattr(route, 'path'):
                            route_paths.append(route.path)
                    
                    route_results['route_paths'] = route_paths
                    logger.info(f"✅ Found {len(route_paths)} API routes")
                    
                else:
                    route_results['router_exists'] = 'FAILED: No router found'
                
            except Exception as e:
                route_results['local_dynamic_import'] = f'FAILED: {str(e)}'
                logger.error(f"❌ API route import failed: {e}")
            
            return route_results
            
        except Exception as e:
            logger.error(f"API route test failed: {e}")
            return False
    
    async def test_model_structure(self) -> dict:
        """测试模型结构"""
        try:
            model_results = {}
            
            # 测试数据类
            try:
                from src.services.local_dynamic_analyzer import RuntimeInfo
                from src.services.local_emulator_manager import EmulatorInstance, EmulatorStatus
                from src.services.local_ui_automator import UIElement, InteractionAction, InteractionType
                
                model_results['RuntimeInfo'] = 'SUCCESS'
                model_results['EmulatorInstance'] = 'SUCCESS'
                model_results['EmulatorStatus'] = 'SUCCESS'
                model_results['UIElement'] = 'SUCCESS'
                model_results['InteractionAction'] = 'SUCCESS'
                model_results['InteractionType'] = 'SUCCESS'
                
                logger.info("✅ All model classes imported successfully")
                
            except Exception as e:
                model_results['model_import'] = f'FAILED: {str(e)}'
                logger.error(f"❌ Model import failed: {e}")
            
            # 测试Pydantic模型
            try:
                from src.models.schemas import DynamicAnalysisResult
                model_results['DynamicAnalysisResult'] = 'SUCCESS'
                logger.info("✅ Pydantic models imported successfully")
                
            except Exception as e:
                model_results['pydantic_models'] = f'FAILED: {str(e)}'
                logger.error(f"❌ Pydantic model import failed: {e}")
            
            return model_results
            
        except Exception as e:
            logger.error(f"Model structure test failed: {e}")
            return False
    
    def generate_test_report(self):
        """生成测试报告"""
        logger.info("📊 Generating Structure Test Report")
        print("=" * 80)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result['status'] == 'PASS')
        failed_tests = sum(1 for result in self.test_results.values() if result['status'] == 'FAIL')
        error_tests = sum(1 for result in self.test_results.values() if result['status'] == 'ERROR')
        
        print(f"📈 Structure Test Summary:")
        print(f"   Total Tests: {total_tests}")
        print(f"   ✅ Passed: {passed_tests}")
        print(f"   ❌ Failed: {failed_tests}")
        print(f"   🚨 Errors: {error_tests}")
        print(f"   📊 Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        print()
        
        print("📋 Detailed Results:")
        for test_name, result in self.test_results.items():
            status_icon = {
                'PASS': '✅',
                'FAIL': '❌',
                'ERROR': '🚨'
            }.get(result['status'], '❓')
            
            print(f"   {status_icon} {test_name}: {result['status']}")
            
            if result['status'] == 'ERROR' and 'error' in result:
                print(f"      Error: {result['error']}")
            elif 'details' in result and result['details']:
                if isinstance(result['details'], dict):
                    for key, value in result['details'].items():
                        if isinstance(value, list):
                            print(f"      {key}: {len(value)} items")
                            for item in value[:3]:  # 只显示前3个
                                print(f"        - {item}")
                            if len(value) > 3:
                                print(f"        ... and {len(value) - 3} more")
                        else:
                            print(f"      {key}: {value}")
        
        print()
        
        # 保存测试报告到文件
        report_file = project_root / "local_dynamic_structure_test_report.json"
        try:
            with open(report_file, 'w') as f:
                json.dump({
                    'timestamp': time.time(),
                    'test_type': 'structure',
                    'summary': {
                        'total': total_tests,
                        'passed': passed_tests,
                        'failed': failed_tests,
                        'errors': error_tests,
                        'success_rate': (passed_tests/total_tests)*100
                    },
                    'results': self.test_results
                }, f, indent=2)
            
            logger.info(f"📄 Structure test report saved to: {report_file}")
        except Exception as e:
            logger.error(f"Failed to save test report: {e}")
        
        # 总结
        if passed_tests == total_tests:
            print("🎉 All structure tests passed! Code structure is solid.")
        elif passed_tests > 0:
            print("⚠️  Some structure tests passed. Core structure is functional.")
        else:
            print("🚨 All structure tests failed. Code structure needs attention.")
        
        print("=" * 80)


async def main():
    """主函数"""
    try:
        tester = LocalDynamicStructureTester()
        await tester.run_all_tests()
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
    except Exception as e:
        logger.error(f"Test execution failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
