<?xml version='1.0' encoding='UTF-8' standalone='yes' ?><hierarchy rotation="0"><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.iloda.beacon" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,2009]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.iloda.beacon" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,2009]"><node index="0" text="" resource-id="android:id/content" class="android.widget.FrameLayout" package="com.iloda.beacon" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,66][1080,2009]"><node index="0" text="" resource-id="" class="android.widget.RelativeLayout" package="com.iloda.beacon" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,66][1080,2009]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.iloda.beacon" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,66][1080,2009]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.iloda.beacon" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,66][1080,209]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.iloda.beacon" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,66][1080,204]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.iloda.beacon" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,66][1080,204]"><node index="0" text="Verify the phone number" resource-id="com.iloda.beacon:id/title" class="android.widget.TextView" package="com.iloda.beacon" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[177,99][902,170]" /></node></node><node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="com.iloda.beacon" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,204][1080,209]" /></node><node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="com.iloda.beacon" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,209][1080,2009]"><node index="0" text="" resource-id="com.iloda.beacon:id/country_code_sel" class="android.widget.LinearLayout" package="com.iloda.beacon" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[41,292][1039,441]"><node index="0" text="Country code" resource-id="" class="android.widget.TextView" package="com.iloda.beacon" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[41,292][454,441]" /><node index="1" text="China" resource-id="com.iloda.beacon:id/country_name" class="android.widget.TextView" package="com.iloda.beacon" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[482,292][951,441]" /><node index="2" text="" resource-id="" class="android.widget.Button" package="com.iloda.beacon" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[965,339][998,394]" /></node><node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="com.iloda.beacon" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[41,524][1039,673]"><node index="0" text="+86" resource-id="com.iloda.beacon:id/country_code" class="android.widget.EditText" package="com.iloda.beacon" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" bounds="[41,524][247,673]" /><node index="1" text="Please input a valid phone number." resource-id="com.iloda.beacon:id/phone_number" class="android.widget.EditText" package="com.iloda.beacon" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="true" scrollable="false" long-clickable="true" password="false" selected="false" bounds="[261,538][970,659]" /></node><node index="2" text="" resource-id="" class="android.widget.LinearLayout" package="com.iloda.beacon" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[41,811][1039,962]"><node index="0" text="Verification code" resource-id="com.iloda.beacon:id/btn_send_auth" class="android.widget.Button" package="com.iloda.beacon" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[41,811][1039,962]" /></node><node index="3" text="" resource-id="" class="android.widget.LinearLayout" package="com.iloda.beacon" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[41,1034][1039,1378]"><node index="0" text="1）Click on the Obtain Verification Code buttone means you already agree on《the Disclaimer, Privacy and Manual Agreement》" resource-id="com.iloda.beacon:id/notes" class="android.widget.TextView" package="com.iloda.beacon" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[41,1034][1039,1149]" /><node index="1" text="2）We use phone number as registered account to make sure Primary Guardians can be reached ASAP at emergency such as when kid is lost. We also show the primary guardians phone numbers to the wechat user who found the lost kid. Your phone number will be kept confidential and will not be disclosed without your permission." resource-id="com.iloda.beacon:id/why_use_phone_answer" class="android.widget.TextView" package="com.iloda.beacon" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[41,1149][1039,1378]" /></node></node></node></node></node></node></node></hierarchy>