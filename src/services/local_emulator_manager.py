"""
本地Android模拟器管理器
负责管理本地Android模拟器实例，包括启动、停止、状态监控等功能
"""
import asyncio
import os
import time
import logging
from pathlib import Path
from typing import Dict, List, Optional
from dataclasses import dataclass
from enum import Enum
import shutil

logger = logging.getLogger(__name__)


class EmulatorStatus(Enum):
    """模拟器状态枚举"""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    ERROR = "error"


@dataclass
class EmulatorInstance:
    """模拟器实例信息"""
    name: str
    avd_name: str
    port: int
    pid: Optional[int] = None
    status: EmulatorStatus = EmulatorStatus.STOPPED
    created_at: Optional[float] = None
    last_heartbeat: Optional[float] = None
    adb_device_id: Optional[str] = None

    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            'name': self.name,
            'avd_name': self.avd_name,
            'port': self.port,
            'pid': self.pid,
            'status': self.status.value,
            'created_at': self.created_at,
            'last_heartbeat': self.last_heartbeat,
            'adb_device_id': self.adb_device_id
        }


class LocalEmulatorManager:
    """本地Android模拟器管理器"""

    def __init__(self,
                 android_home: Optional[str] = None,
                 max_instances: int = 3,
                 base_port: int = 5554):
        """
        初始化模拟器管理器

        Args:
            android_home: Android SDK路径
            max_instances: 最大模拟器实例数
            base_port: 基础端口号
        """
        self.android_home = android_home or self._detect_android_home()
        self.max_instances = max_instances
        self.base_port = base_port

        # 工具路径
        self.emulator_path = self._find_emulator_path()
        self.adb_path = self._find_adb_path()
        self.avdmanager_path = self._find_avdmanager_path()

        # 实例管理
        self.instances: Dict[str, EmulatorInstance] = {}
        self.available_ports = list(range(base_port, base_port + max_instances * 2, 2))

        # 状态监控
        self._monitoring = False
        self._monitor_task: Optional[asyncio.Task] = None

        logger.info(f"LocalEmulatorManager initialized with Android SDK: {self.android_home}")
        logger.info(f"Emulator path: {self.emulator_path}")
        logger.info(f"ADB path: {self.adb_path}")

    def _detect_android_home(self) -> str:
        """自动检测Android SDK路径"""
        # 检查环境变量
        android_home = os.getenv('ANDROID_HOME') or os.getenv('ANDROID_SDK_ROOT')
        if android_home and Path(android_home).exists():
            return android_home

        # 检查常见路径
        common_paths = [
            Path.home() / "Library" / "Android" / "sdk",  # macOS
            Path.home() / "Android" / "Sdk",  # Linux
            Path("C:") / "Users" / os.getenv("USERNAME", "") / "AppData" / "Local" / "Android" / "Sdk",  # Windows
            Path("/opt/android-sdk"),  # Linux系统安装
        ]

        for path in common_paths:
            if path.exists() and (path / "emulator").exists():
                return str(path)

        # 如果都找不到，使用项目本地路径
        project_root = Path(__file__).parent.parent.parent
        local_sdk = project_root / "android-sdk"
        if local_sdk.exists():
            return str(local_sdk)

        raise RuntimeError("Android SDK not found. Please set ANDROID_HOME environment variable.")

    def _find_emulator_path(self) -> str:
        """查找emulator工具路径"""
        emulator_paths = [
            Path(self.android_home) / "emulator" / "emulator",
            Path(self.android_home) / "tools" / "emulator",
            shutil.which("emulator")
        ]

        for path in emulator_paths:
            if path and Path(path).exists():
                return str(path)

        raise RuntimeError("Android emulator not found")

    def _find_adb_path(self) -> str:
        """查找adb工具路径"""
        # 优先使用项目本地的adb
        project_root = Path(__file__).parent.parent.parent
        local_adb = project_root / "platform-tools" / "adb"
        if local_adb.exists():
            return str(local_adb)

        adb_paths = [
            Path(self.android_home) / "platform-tools" / "adb",
            shutil.which("adb")
        ]

        for path in adb_paths:
            if path and Path(path).exists():
                return str(path)

        raise RuntimeError("ADB not found")

    def _find_avdmanager_path(self) -> str:
        """查找avdmanager工具路径"""
        avdmanager_paths = [
            Path(self.android_home) / "cmdline-tools" / "latest" / "bin" / "avdmanager",
            Path(self.android_home) / "tools" / "bin" / "avdmanager",
            shutil.which("avdmanager")
        ]

        for path in avdmanager_paths:
            if path and Path(path).exists():
                return str(path)

        logger.warning("avdmanager not found, AVD creation will not be available")
        return ""

    async def start_monitoring(self):
        """启动模拟器状态监控"""
        if self._monitoring:
            return

        self._monitoring = True
        self._monitor_task = asyncio.create_task(self._monitor_instances())
        logger.info("Emulator monitoring started")

    async def stop_monitoring(self):
        """停止模拟器状态监控"""
        self._monitoring = False
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass
        logger.info("Emulator monitoring stopped")

    async def _monitor_instances(self):
        """监控模拟器实例状态"""
        while self._monitoring:
            try:
                await self._update_instance_status()
                await asyncio.sleep(10)  # 每10秒检查一次
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error monitoring instances: {e}")
                await asyncio.sleep(5)

    async def _update_instance_status(self):
        """更新所有实例状态"""
        for instance_name, instance in list(self.instances.items()):
            try:
                # 检查进程是否还在运行
                if instance.pid and not self._is_process_running(instance.pid):
                    logger.warning(f"Emulator process {instance.pid} is no longer running")
                    instance.status = EmulatorStatus.STOPPED
                    instance.pid = None
                    instance.adb_device_id = None
                    continue

                # 检查ADB连接状态
                if instance.status == EmulatorStatus.RUNNING:
                    device_id = f"emulator-{instance.port}"
                    if await self._is_device_online(device_id):
                        instance.last_heartbeat = time.time()
                        instance.adb_device_id = device_id
                    else:
                        logger.warning(f"Device {device_id} is not online")
                        if instance.status == EmulatorStatus.RUNNING:
                            instance.status = EmulatorStatus.ERROR

            except Exception as e:
                logger.error(f"Error updating status for {instance_name}: {e}")

    def _is_process_running(self, pid: int) -> bool:
        """检查进程是否在运行"""
        try:
            import psutil
            return psutil.pid_exists(pid)
        except ImportError:
            # 如果没有psutil，使用系统命令
            try:
                os.kill(pid, 0)
                return True
            except OSError:
                return False

    async def _is_device_online(self, device_id: str) -> bool:
        """检查ADB设备是否在线"""
        try:
            process = await asyncio.create_subprocess_exec(
                self.adb_path, 'devices',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, _ = await process.communicate()

            if process.returncode == 0:
                output = stdout.decode()
                return f"{device_id}\tdevice" in output

            return False
        except Exception as e:
            logger.error(f"Error checking device status: {e}")
            return False

    async def list_available_avds(self) -> List[str]:
        """列出可用的AVD"""
        if not self.avdmanager_path:
            logger.warning("avdmanager not available, cannot list AVDs")
            return []

        try:
            process = await asyncio.create_subprocess_exec(
                self.avdmanager_path, 'list', 'avd',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()

            if process.returncode != 0:
                logger.error(f"Failed to list AVDs: {stderr.decode()}")
                return []

            # 解析AVD列表
            avds = []
            output = stdout.decode()
            for line in output.split('\n'):
                if line.strip().startswith('Name:'):
                    avd_name = line.split(':', 1)[1].strip()
                    avds.append(avd_name)

            return avds

        except Exception as e:
            logger.error(f"Error listing AVDs: {e}")
            return []

    async def create_default_avd(self, avd_name: str = "test_avd") -> bool:
        """创建默认的AVD"""
        if not self.avdmanager_path:
            logger.error("avdmanager not available, cannot create AVD")
            return False

        try:
            # 检查AVD是否已存在
            existing_avds = await self.list_available_avds()
            if avd_name in existing_avds:
                logger.info(f"AVD {avd_name} already exists")
                return True

            # 创建AVD (使用Android 30, Google APIs)
            system_image = "system-images;android-30;google_apis;x86_64"

            logger.info(f"Creating AVD {avd_name} with system image {system_image}")

            process = await asyncio.create_subprocess_exec(
                self.avdmanager_path, 'create', 'avd',
                '-n', avd_name,
                '-k', system_image,
                '--force',
                stdin=asyncio.subprocess.PIPE,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            # 发送"no"来跳过自定义硬件配置
            _, stderr = await process.communicate(input=b'no\n')

            if process.returncode == 0:
                logger.info(f"Successfully created AVD {avd_name}")
                return True
            else:
                logger.error(f"Failed to create AVD: {stderr.decode()}")
                return False

        except Exception as e:
            logger.error(f"Error creating AVD: {e}")
            return False

    async def start_emulator(self,
                           avd_name: str,
                           instance_name: Optional[str] = None,
                           timeout: int = 120) -> Optional[EmulatorInstance]:
        """
        启动模拟器实例

        Args:
            avd_name: AVD名称
            instance_name: 实例名称（可选）
            timeout: 启动超时时间（秒）

        Returns:
            EmulatorInstance: 模拟器实例，失败返回None
        """
        if len(self.instances) >= self.max_instances:
            logger.error(f"Maximum instances ({self.max_instances}) reached")
            return None

        if not self.available_ports:
            logger.error("No available ports for new emulator instance")
            return None

        # 生成实例名称
        if not instance_name:
            instance_name = f"emulator_{avd_name}_{int(time.time())}"

        # 检查实例是否已存在
        if instance_name in self.instances:
            logger.error(f"Instance {instance_name} already exists")
            return None

        # 分配端口
        port = self.available_ports.pop(0)

        try:
            logger.info(f"Starting emulator {instance_name} with AVD {avd_name} on port {port}")

            # 创建实例对象
            instance = EmulatorInstance(
                name=instance_name,
                avd_name=avd_name,
                port=port,
                status=EmulatorStatus.STARTING,
                created_at=time.time()
            )

            self.instances[instance_name] = instance

            # 启动模拟器进程
            success = await self._start_emulator_process(instance, timeout)

            if success:
                instance.status = EmulatorStatus.RUNNING
                logger.info(f"Emulator {instance_name} started successfully")
                return instance
            else:
                # 启动失败，清理资源
                await self._cleanup_failed_instance(instance_name)
                return None

        except Exception as e:
            logger.error(f"Error starting emulator {instance_name}: {e}")
            await self._cleanup_failed_instance(instance_name)
            return None

    async def _start_emulator_process(self, instance: EmulatorInstance, timeout: int) -> bool:
        """启动模拟器进程"""
        try:
            # 构建启动命令
            cmd = [
                self.emulator_path,
                '-avd', instance.avd_name,
                '-port', str(instance.port),
                '-no-window',  # 无窗口模式
                '-no-audio',   # 禁用音频
                '-no-boot-anim',  # 禁用启动动画
                '-gpu', 'off',    # 禁用GPU加速（兼容性更好）
                '-memory', '2048',  # 设置内存
                '-partition-size', '2048',  # 设置分区大小
                '-no-snapshot-save',  # 不保存快照
                '-no-snapshot-load',  # 不加载快照
                '-wipe-data'  # 清除数据（确保干净环境）
            ]

            logger.debug(f"Emulator command: {' '.join(cmd)}")

            # 启动进程
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                preexec_fn=os.setsid if hasattr(os, 'setsid') else None
            )

            instance.pid = process.pid
            logger.info(f"Emulator process started with PID {process.pid}")

            # 等待模拟器启动完成
            return await self._wait_for_emulator_ready(instance, timeout)

        except Exception as e:
            logger.error(f"Failed to start emulator process: {e}")
            return False

    async def _wait_for_emulator_ready(self, instance: EmulatorInstance, timeout: int) -> bool:
        """等待模拟器启动完成"""
        device_id = f"emulator-{instance.port}"
        start_time = time.time()

        logger.info(f"Waiting for emulator {device_id} to be ready (timeout: {timeout}s)")

        while time.time() - start_time < timeout:
            try:
                # 检查ADB设备是否可见
                if await self._is_device_online(device_id):
                    # 等待系统完全启动
                    if await self._is_system_ready(device_id):
                        instance.adb_device_id = device_id
                        instance.last_heartbeat = time.time()
                        logger.info(f"Emulator {device_id} is ready")
                        return True

                await asyncio.sleep(5)  # 每5秒检查一次

            except Exception as e:
                logger.debug(f"Error checking emulator readiness: {e}")
                await asyncio.sleep(2)

        logger.error(f"Emulator {device_id} failed to start within {timeout} seconds")
        return False

    async def _is_system_ready(self, device_id: str) -> bool:
        """检查Android系统是否完全启动"""
        try:
            # 检查系统服务是否就绪
            process = await asyncio.create_subprocess_exec(
                self.adb_path, '-s', device_id, 'shell',
                'getprop', 'sys.boot_completed',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, _ = await process.communicate()

            if process.returncode == 0:
                boot_completed = stdout.decode().strip()
                if boot_completed == '1':
                    # 再检查包管理器是否就绪
                    process2 = await asyncio.create_subprocess_exec(
                        self.adb_path, '-s', device_id, 'shell',
                        'pm', 'list', 'packages', '-f',
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.PIPE
                    )
                    _, _ = await process2.communicate()
                    return process2.returncode == 0

            return False

        except Exception as e:
            logger.debug(f"Error checking system readiness: {e}")
            return False

    async def stop_emulator(self, instance_name: str, force: bool = False) -> bool:
        """
        停止模拟器实例

        Args:
            instance_name: 实例名称
            force: 是否强制停止

        Returns:
            bool: 是否成功停止
        """
        if instance_name not in self.instances:
            logger.warning(f"Instance {instance_name} not found")
            return False

        instance = self.instances[instance_name]

        try:
            logger.info(f"Stopping emulator {instance_name}")
            instance.status = EmulatorStatus.STOPPING

            # 尝试优雅关闭
            if not force and instance.adb_device_id:
                success = await self._graceful_shutdown(instance)
                if success:
                    await self._cleanup_instance(instance_name)
                    return True

            # 强制关闭
            if instance.pid:
                success = await self._force_shutdown(instance)
                if success:
                    await self._cleanup_instance(instance_name)
                    return True

            # 如果都失败了，至少清理实例记录
            await self._cleanup_instance(instance_name)
            return False

        except Exception as e:
            logger.error(f"Error stopping emulator {instance_name}: {e}")
            await self._cleanup_instance(instance_name)
            return False

    async def _graceful_shutdown(self, instance: EmulatorInstance) -> bool:
        """优雅关闭模拟器"""
        try:
            if not instance.adb_device_id:
                return False

            logger.debug(f"Attempting graceful shutdown of {instance.adb_device_id}")

            # 发送关机命令
            process = await asyncio.create_subprocess_exec(
                self.adb_path, '-s', instance.adb_device_id, 'emu', 'kill',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            await process.communicate()

            # 等待进程结束
            if instance.pid:
                for _ in range(30):  # 等待最多30秒
                    if not self._is_process_running(instance.pid):
                        logger.info(f"Emulator {instance.name} shut down gracefully")
                        return True
                    await asyncio.sleep(1)

            return False

        except Exception as e:
            logger.debug(f"Graceful shutdown failed: {e}")
            return False

    async def _force_shutdown(self, instance: EmulatorInstance) -> bool:
        """强制关闭模拟器"""
        try:
            if not instance.pid:
                return False

            logger.debug(f"Force killing emulator process {instance.pid}")

            try:
                import psutil
                process = psutil.Process(instance.pid)
                process.terminate()

                # 等待进程结束
                try:
                    process.wait(timeout=10)
                    logger.info(f"Emulator process {instance.pid} terminated")
                    return True
                except psutil.TimeoutExpired:
                    # 如果10秒后还没结束，强制杀死
                    process.kill()
                    process.wait(timeout=5)
                    logger.info(f"Emulator process {instance.pid} killed")
                    return True

            except ImportError:
                # 如果没有psutil，使用系统命令
                try:
                    os.kill(instance.pid, 15)  # SIGTERM
                    await asyncio.sleep(5)

                    if self._is_process_running(instance.pid):
                        os.kill(instance.pid, 9)  # SIGKILL
                        await asyncio.sleep(2)

                    return not self._is_process_running(instance.pid)

                except OSError as e:
                    if e.errno == 3:  # No such process
                        return True
                    raise

        except Exception as e:
            logger.error(f"Force shutdown failed: {e}")
            return False

    async def _cleanup_instance(self, instance_name: str):
        """清理实例资源"""
        if instance_name in self.instances:
            instance = self.instances[instance_name]

            # 回收端口
            if instance.port in range(self.base_port, self.base_port + self.max_instances * 2):
                if instance.port not in self.available_ports:
                    self.available_ports.append(instance.port)
                    self.available_ports.sort()

            # 删除实例记录
            del self.instances[instance_name]
            logger.debug(f"Cleaned up instance {instance_name}")

    async def _cleanup_failed_instance(self, instance_name: str):
        """清理启动失败的实例"""
        if instance_name in self.instances:
            instance = self.instances[instance_name]

            # 如果有进程ID，尝试杀死进程
            if instance.pid:
                try:
                    await self._force_shutdown(instance)
                except Exception as e:
                    logger.debug(f"Error killing failed instance process: {e}")

            # 清理资源
            await self._cleanup_instance(instance_name)

    async def stop_all_emulators(self, force: bool = False) -> Dict[str, bool]:
        """
        停止所有模拟器实例

        Args:
            force: 是否强制停止

        Returns:
            Dict[str, bool]: 每个实例的停止结果
        """
        results = {}

        # 获取所有实例名称的副本，避免在迭代时修改字典
        instance_names = list(self.instances.keys())

        for instance_name in instance_names:
            try:
                result = await self.stop_emulator(instance_name, force)
                results[instance_name] = result
            except Exception as e:
                logger.error(f"Error stopping {instance_name}: {e}")
                results[instance_name] = False

        return results

    def get_instance(self, instance_name: str) -> Optional[EmulatorInstance]:
        """获取模拟器实例"""
        return self.instances.get(instance_name)

    def get_all_instances(self) -> Dict[str, EmulatorInstance]:
        """获取所有模拟器实例"""
        return self.instances.copy()

    def get_running_instances(self) -> Dict[str, EmulatorInstance]:
        """获取正在运行的模拟器实例"""
        return {
            name: instance for name, instance in self.instances.items()
            if instance.status == EmulatorStatus.RUNNING
        }

    def get_available_instance(self) -> Optional[EmulatorInstance]:
        """获取一个可用的模拟器实例"""
        running_instances = self.get_running_instances()
        if running_instances:
            # 返回最近有心跳的实例
            return max(
                running_instances.values(),
                key=lambda x: x.last_heartbeat or 0
            )
        return None

    async def get_or_create_instance(self,
                                   avd_name: str = "test_avd",
                                   timeout: int = 120) -> Optional[EmulatorInstance]:
        """
        获取或创建模拟器实例

        Args:
            avd_name: AVD名称
            timeout: 启动超时时间

        Returns:
            EmulatorInstance: 可用的模拟器实例
        """
        # 首先尝试获取现有的运行实例
        available_instance = self.get_available_instance()
        if available_instance:
            logger.info(f"Using existing instance: {available_instance.name}")
            return available_instance

        # 如果没有可用实例，创建新的
        logger.info(f"No available instances, creating new one with AVD: {avd_name}")

        # 确保AVD存在
        available_avds = await self.list_available_avds()
        if avd_name not in available_avds:
            logger.info(f"AVD {avd_name} not found, creating it...")
            if not await self.create_default_avd(avd_name):
                logger.error(f"Failed to create AVD {avd_name}")
                return None

        # 启动新实例
        return await self.start_emulator(avd_name, timeout=timeout)

    def get_status_summary(self) -> dict:
        """获取状态摘要"""
        status_counts = {}
        for instance in self.instances.values():
            status = instance.status.value
            status_counts[status] = status_counts.get(status, 0) + 1

        return {
            'total_instances': len(self.instances),
            'max_instances': self.max_instances,
            'available_ports': len(self.available_ports),
            'status_counts': status_counts,
            'android_home': self.android_home,
            'emulator_path': self.emulator_path,
            'adb_path': self.adb_path
        }

    async def cleanup_all(self):
        """清理所有资源"""
        logger.info("Cleaning up all emulator resources...")

        # 停止监控
        await self.stop_monitoring()

        # 停止所有模拟器
        if self.instances:
            results = await self.stop_all_emulators(force=True)
            failed_stops = [name for name, success in results.items() if not success]
            if failed_stops:
                logger.warning(f"Failed to stop instances: {failed_stops}")

        # 清理实例记录
        self.instances.clear()
        self.available_ports = list(range(self.base_port, self.base_port + self.max_instances * 2, 2))

        logger.info("Cleanup completed")

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start_monitoring()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.cleanup_all()
        return False  # 不抑制异常


# 全局实例（单例模式）
_emulator_manager: Optional[LocalEmulatorManager] = None


def get_emulator_manager(**kwargs) -> LocalEmulatorManager:
    """获取全局模拟器管理器实例"""
    global _emulator_manager
    if _emulator_manager is None:
        _emulator_manager = LocalEmulatorManager(**kwargs)
    return _emulator_manager


async def cleanup_global_manager():
    """清理全局管理器"""
    global _emulator_manager
    if _emulator_manager:
        await _emulator_manager.cleanup_all()
        _emulator_manager = None