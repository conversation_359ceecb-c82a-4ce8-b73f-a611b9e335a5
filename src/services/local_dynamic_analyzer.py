"""
本地动态分析器
使用本地Android模拟器进行APK动态分析
"""
import asyncio
import os
import time
import logging
from pathlib import Path
from typing import List, Optional, Dict, Any
import json

from .local_emulator_manager import LocalEmulatorManager, EmulatorInstance, get_emulator_manager
from .local_ui_automator import LocalUIAutomator
from .static_analyzer import AnalysisTools
from .frida_ssl_bypasser import FridaSSLBypasser, FridaBypassResult
from src.models.schemas import DynamicAnalysisResult
from .dynamic_analyzer import NetworkRequest  # 使用现有的NetworkRequest定义
from dataclasses import dataclass
import subprocess
import xml.etree.ElementTree as ET
import re
from typing import Tuple


@dataclass
class RuntimeInfo:
    """运行时信息"""
    device_model: str
    android_version: str
    api_level: int
    architecture: str
    memory_info: Dict[str, Any]
    cpu_info: Dict[str, Any]


class SimpleUIElement:
    """简化的UI元素"""
    def __init__(self, text: str, content_desc: str, resource_id: str, 
                 bounds: Tuple[int, int, int, int], clickable: bool, enabled: bool,
                 class_name: str = ""):
        self.text = text
        self.content_desc = content_desc
        self.resource_id = resource_id
        self.bounds = bounds  # (left, top, right, bottom)
        self.clickable = clickable
        self.enabled = enabled
        self.class_name = class_name
    
    @property
    def center_x(self) -> int:
        return (self.bounds[0] + self.bounds[2]) // 2
    
    @property
    def center_y(self) -> int:
        return (self.bounds[1] + self.bounds[3]) // 2
    
    @property
    def width(self) -> int:
        return self.bounds[2] - self.bounds[0]
    
    @property
    def height(self) -> int:
        return self.bounds[3] - self.bounds[1]


logger = logging.getLogger(__name__)


@dataclass
class AnalysisTools:
    """本地动态分析工具配置"""
    adb_path: str = "/Users/<USER>/Desktop/project/apk_detect/android-sdk/platform-tools/adb"
    aapt_path: str = "/Users/<USER>/Desktop/project/apk_detect/android-sdk/build-tools/30.0.3/aapt"
    emulator_path: str = "/Users/<USER>/Desktop/project/apk_detect/android-sdk/emulator/emulator"
    
    # 为了向后兼容，添加adb属性
    @property
    def adb(self) -> str:
        return self.adb_path


class LocalDynamicAnalyzer:
    """本地动态分析器"""
    
    def __init__(self,
                 emulator_manager: Optional[LocalEmulatorManager] = None,
                 tools: Optional[AnalysisTools] = None,
                 enable_multiple_activities: bool = False,
                 enable_frida_ssl_bypass: bool = False):
        """
        初始化本地动态分析器

        Args:
            emulator_manager: 模拟器管理器实例
            tools: 分析工具配置
            enable_multiple_activities: 是否启用多activities测试
            enable_frida_ssl_bypass: 是否启用Frida SSL绕过
        """
        self.emulator_manager = emulator_manager or get_emulator_manager()
        self.tools = tools or AnalysisTools()
        self.ui_automator = LocalUIAutomator(tools)
        self.enable_multiple_activities = enable_multiple_activities
        self.enable_frida_ssl_bypass = enable_frida_ssl_bypass
        
        # Frida SSL绕过器
        self.frida_bypasser = None
        if self.enable_frida_ssl_bypass:
            self.frida_bypasser = FridaSSLBypasser()
        
        # 分析配置
        self.default_timeout = 300
        self.interaction_timeout = 180
        self.network_capture_timeout = 60

        # 确保mitm日志目录存在
        self.mitm_logs_dir = Path("mitm-logs")
        self.mitm_logs_dir.mkdir(exist_ok=True)

        logger.info(f"LocalDynamicAnalyzer initialized (Frida SSL Bypass: {enable_frida_ssl_bypass})")
    
    async def analyze(self, apk_path: str, timeout: int = None) -> DynamicAnalysisResult:
        """
        执行完整的本地动态分析
        
        Args:
            apk_path: APK文件路径
            timeout: 分析超时时间（秒）
            
        Returns:
            DynamicAnalysisResult: 分析结果
        """
        timeout = timeout or self.default_timeout
        logger.info(f"Starting local dynamic analysis of {apk_path}")
        start_time = time.time()
        
        emulator_instance = None
        
        try:
            print("🚀 开始APK动态分析...")
            print("=" * 60)

            # 1. 获取或创建模拟器实例
            print("📱 步骤1: 获取模拟器实例...")
            emulator_instance = await self.emulator_manager.get_or_create_instance()
            if not emulator_instance:
                raise Exception("Failed to get emulator instance")

            print(f"✅ 模拟器实例获取成功: {emulator_instance.name}")
            print(f"   设备ID: {emulator_instance.adb_device_id}")
            logger.info(f"Using emulator instance: {emulator_instance.name}")

            # 2. 提取APK包名
            print("\n📦 步骤2: 提取APK包名...")
            package_name = await self._extract_package_name(apk_path)
            if not package_name:
                raise Exception("Failed to extract package name from APK")

            print(f"✅ 包名提取成功: {package_name}")

            # 3. 安装APK（全自动化预授权）
            print(f"\n📥 步骤3: 安装APK并预授权...")
            print(f"   APK路径: {apk_path}")
            if not await self._install_apk(apk_path, emulator_instance, package_name):
                raise Exception("Failed to install APK")

            print("✅ APK安装成功，权限已预授权")
            
            try:
                # 4. 启动网络流量捕获
                network_requests = []
                capture_task = None
                
                try:
                    # 4. 启动网络流量捕获
                    print(f"\n🌐 步骤4: 启动网络流量捕获...")
                    print(f"   捕获时长: {self.network_capture_timeout}秒")
                    capture_task = asyncio.create_task(
                        self._capture_network_traffic(emulator_instance, self.network_capture_timeout, package_name)
                    )
                    print("✅ 网络捕获已启动（后台运行）")

                    # 5. 启动应用
                    print(f"\n🚀 步骤5: 启动应用...")
                    print(f"   应用包名: {package_name}")
                    app_launched = await self._launch_app(package_name, emulator_instance)
                    print(f"{'✅' if app_launched else '⚠️'} 应用启动结果: {'成功' if app_launched else '失败'}")
                    logger.info(f"App launch result: {app_launched}")
                    
                    # 5.5. Frida SSL绕过（如果启用）
                    frida_bypass_result = None
                    if self.enable_frida_ssl_bypass and self.frida_bypasser and app_launched:
                        print(f"\n🔓 步骤5.5: 启动Frida SSL深度绕过...")
                        try:
                            frida_bypass_result = await self.frida_bypasser.start_ssl_bypass(package_name)
                            if frida_bypass_result.success:
                                print("✅ Frida SSL绕过成功")
                                logger.info(f"Frida SSL bypass successful: {frida_bypass_result.bypass_details}")
                            else:
                                print(f"⚠️ Frida SSL绕过失败: {frida_bypass_result.error_message}")
                                logger.warning(f"Frida SSL bypass failed: {frida_bypass_result.error_message}")
                        except Exception as e:
                            print(f"❌ Frida SSL绕过异常: {e}")
                            logger.error(f"Frida SSL bypass exception: {e}")
                        
                        # 等待SSL绕过生效
                        await asyncio.sleep(3)

                    # 6. 监控网络流量（确认是否有网络活动）
                    print(f"\n📊 步骤6: 监控应用网络活动...")
                    network_monitor_task = asyncio.create_task(
                        self._monitor_network_activity(emulator_instance, package_name, 10)
                    )

                    # 7. 多activities测试（如果启用）
                    multiple_activities_result = None
                    if hasattr(self, 'enable_multiple_activities') and self.enable_multiple_activities:
                        print(f"\n🎯 步骤7: 开始多activities测试...")
                        multiple_activities_result = await self.test_multiple_activities(
                            emulator_instance, package_name, apk_path, min(timeout, 120)
                        )
                    else:
                        # 8. 自动化UI交互（传统方式）
                        print(f"\n🎮 步骤8: 开始自动化UI交互...")
                        print(f"   交互时长: {min(timeout, 30)}秒")
                        interaction_task = asyncio.create_task(
                            self._handle_app_interaction(emulator_instance, package_name, min(timeout, 30))
                        )

                    # 9. 等待所有任务完成
                    print(f"\n⏳ 步骤9: 等待分析完成...")
                    
                    if multiple_activities_result:
                        # 如果使用了多activities测试，只等待网络任务
                        print("   正在等待网络捕获和监控任务完成...")
                        network_requests, network_activity = await asyncio.gather(
                            capture_task,
                            network_monitor_task
                        )
                        interaction_result = f"multiple_activities_tested:{multiple_activities_result['successful_activities']}"
                    else:
                        # 传统模式：等待所有任务
                        print("   正在并行执行网络捕获、流量监控和UI交互...")
                        network_requests, network_activity, interaction_result = await asyncio.gather(
                            capture_task,
                            network_monitor_task,
                            interaction_task,
                            return_exceptions=True
                        )

                    if isinstance(network_requests, Exception):
                        print(f"❌ 网络捕获失败: {network_requests}")
                        logger.error(f"Network capture failed: {network_requests}")
                        network_requests = []
                    else:
                        print(f"✅ 网络捕获完成: 捕获到 {len(network_requests)} 个请求")

                    if isinstance(network_activity, Exception):
                        print(f"⚠️ 网络监控失败: {network_activity}")
                        logger.warning(f"Network monitoring failed: {network_activity}")
                        network_activity = {}
                    else:
                        print(f"📊 网络活动监控: {network_activity}")

                    if isinstance(interaction_result, Exception):
                        print(f"⚠️ UI交互失败: {interaction_result}")
                        logger.warning(f"App interaction failed: {interaction_result}")
                    else:
                        print(f"✅ UI交互完成: {interaction_result}")
                        logger.info(f"App interaction completed: {interaction_result}")
                    
                except Exception as e:
                    logger.error(f"Error during analysis: {e}")
                    if capture_task and not capture_task.done():
                        capture_task.cancel()
                        try:
                            await capture_task
                        except asyncio.CancelledError:
                            pass
                
                # 7. 处理网络请求
                filtered_requests = self._filter_requests(network_requests)
                dynamic_urls = self._convert_to_url_schemas(filtered_requests)
                
                # 8. 获取运行时信息
                runtime_info_obj = await self._get_runtime_info(emulator_instance)
                runtime_info = None
                if runtime_info_obj:
                    # 将RuntimeInfo对象转换为字典
                    runtime_info = {
                        'device_model': runtime_info_obj.device_model,
                        'android_version': runtime_info_obj.android_version,
                        'api_level': runtime_info_obj.api_level,
                        'architecture': runtime_info_obj.architecture,
                        'memory_info': runtime_info_obj.memory_info,
                        'cpu_info': runtime_info_obj.cpu_info
                    }

                duration = time.time() - start_time

                result = DynamicAnalysisResult(
                    dynamic_urls=dynamic_urls,
                    duration=duration,
                    android_version=await self._get_android_version(emulator_instance),
                    network_requests=filtered_requests,
                    runtime_info=runtime_info
                )
                
                # 添加多activities测试结果
                if multiple_activities_result:
                    result.multiple_activities_result = multiple_activities_result
                
                # 添加Frida SSL绕过结果
                if frida_bypass_result:
                    result.frida_ssl_bypass_result = {
                        "success": frida_bypass_result.success,
                        "frida_connected": frida_bypass_result.frida_connected,
                        "script_injected": frida_bypass_result.script_injected,
                        "ssl_bypassed": frida_bypass_result.ssl_bypassed,
                        "network_hooked": frida_bypass_result.network_hooked,
                        "error_message": frida_bypass_result.error_message,
                        "bypass_details": frida_bypass_result.bypass_details
                    }

                # 详细的结果展示
                print(f"\n📊 步骤8: 分析结果汇总")
                print("=" * 60)
                print(f"✅ 动态分析完成！总用时: {duration:.2f}秒")
                print(f"📱 Android版本: {result.android_version}")
                print(f"🔗 动态URLs: {len(dynamic_urls)} 个")
                print(f"🌐 网络请求: {len(filtered_requests)} 个")
                print(f"💾 运行时信息: {'已获取' if runtime_info else '未获取'}")
                
                # 显示多activities测试结果
                if multiple_activities_result:
                    print(f"🎯 多Activities测试: {multiple_activities_result['successful_activities']}/{multiple_activities_result['total_activities']} 成功")
                    print(f"   测试的Activities:")
                    for i, activity in enumerate(multiple_activities_result['activities_tested'][:3]):
                        status = "✅" if activity['launch_success'] else "❌"
                        consent = "🤖" if activity.get('consent_handled', False) else ""
                        print(f"   {i+1}. {status}{consent} {activity['activity_name']}")

                if dynamic_urls:
                    print(f"\n🔗 发现的动态URLs:")
                    for i, url in enumerate(dynamic_urls[:5]):
                        print(f"   {i+1}. {url.url}")
                    if len(dynamic_urls) > 5:
                        print(f"   ... 还有 {len(dynamic_urls) - 5} 个URL")
                else:
                    print(f"\n⚠️  未发现动态URLs - 可能原因:")
                    print(f"   - 应用无网络活动")
                    print(f"   - 网络代理配置问题")
                    print(f"   - 需要更多用户交互")

                print("=" * 60)

                logger.info(f"Local dynamic analysis completed in {duration:.2f}s")
                logger.info(f"Found {len(dynamic_urls)} dynamic URLs")

                return result
                
            finally:
                # 清理：Frida资源
                if self.frida_bypasser:
                    try:
                        self.frida_bypasser.cleanup()
                    except Exception as e:
                        logger.warning(f"Failed to cleanup Frida resources: {e}")
                
                # 清理：卸载APK
                try:
                    await self._uninstall_apk(package_name, emulator_instance)
                except Exception as e:
                    logger.warning(f"Failed to uninstall APK: {e}")
        
        except Exception as e:
            logger.error(f"Local dynamic analysis failed: {e}")
            duration = time.time() - start_time
            
            # 返回失败结果
            return DynamicAnalysisResult(
                dynamic_urls=[],
                duration=duration,
                android_version="unknown",
                network_requests=[],
                runtime_info=None,
                error=str(e)
            )
    
    async def _extract_package_name(self, apk_path: str) -> Optional[str]:
        """从APK提取包名"""
        try:
            process = await asyncio.create_subprocess_exec(
                self.tools.aapt, 'dump', 'badging', apk_path,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                logger.error(f"Failed to extract package name: {stderr.decode()}")
                return None
            
            # 解析输出获取包名
            output = stdout.decode()
            for line in output.split('\n'):
                if line.startswith('package:'):
                    # 提取 name='...' 部分
                    import re
                    match = re.search(r"name='([^']+)'", line)
                    if match:
                        package_name = match.group(1)
                        logger.info(f"Extracted package name: {package_name}")
                        return package_name
            
            return None
            
        except Exception as e:
            logger.error(f"Error extracting package name: {e}")
            return None
    
    async def _install_apk(self, apk_path: str, emulator: EmulatorInstance, package_name: str = None) -> bool:
        """在模拟器上安装APK"""
        try:
            if not emulator.adb_device_id:
                logger.error("Emulator device ID not available")
                return False
            
            logger.info(f"Installing APK on {emulator.adb_device_id}")
            
            # 安装APK并预授权所有权限（全自动化）
            logger.info(f"Installing APK with pre-granted permissions: {apk_path}")
            process = await asyncio.create_subprocess_exec(
                self.tools.adb, '-s', emulator.adb_device_id, 'install', '-r', '-g', apk_path,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                logger.error(f"APK installation failed: {stderr.decode()}")
                return False
            
            output = stdout.decode()
            success = "Success" in output or "INSTALL_SUCCEEDED" in output
            
            if success:
                logger.info("APK installed successfully with pre-granted permissions")
                # 额外确保所有权限都已授予
                if package_name:
                    await self._ensure_all_permissions_granted(package_name, emulator)
            else:
                logger.error(f"APK installation failed: {output}")

            return success
            
        except Exception as e:
            logger.error(f"Error installing APK: {e}")
            return False

    async def _ensure_all_permissions_granted(self, package_name: str, emulator: EmulatorInstance) -> None:
        """确保所有权限都已授予（全自动化）"""
        try:
            if not emulator.adb_device_id or not package_name:
                return

            logger.info(f"Ensuring all permissions are granted for {package_name}")

            # 获取应用请求的所有权限
            process = await asyncio.create_subprocess_exec(
                self.tools.adb, '-s', emulator.adb_device_id, 'shell',
                'pm', 'dump', package_name,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, _ = await process.communicate()

            if process.returncode == 0:
                output = stdout.decode()
                # 提取需要运行时权限的权限列表
                dangerous_permissions = [
                    'android.permission.READ_EXTERNAL_STORAGE',
                    'android.permission.WRITE_EXTERNAL_STORAGE',
                    'android.permission.CAMERA',
                    'android.permission.RECORD_AUDIO',
                    'android.permission.ACCESS_FINE_LOCATION',
                    'android.permission.ACCESS_COARSE_LOCATION',
                    'android.permission.READ_CONTACTS',
                    'android.permission.WRITE_CONTACTS',
                    'android.permission.READ_PHONE_STATE',
                    'android.permission.CALL_PHONE',
                    'android.permission.READ_SMS',
                    'android.permission.SEND_SMS',
                    'android.permission.ACCESS_MEDIA_LOCATION'
                ]

                # 批量授予危险权限
                for permission in dangerous_permissions:
                    if permission in output:
                        try:
                            await asyncio.create_subprocess_exec(
                                self.tools.adb, '-s', emulator.adb_device_id, 'shell',
                                'pm', 'grant', package_name, permission,
                                stdout=asyncio.subprocess.PIPE,
                                stderr=asyncio.subprocess.PIPE
                            )
                            logger.debug(f"Granted permission: {permission}")
                        except Exception as e:
                            logger.debug(f"Failed to grant {permission}: {e}")

                logger.info("All permissions ensured")

        except Exception as e:
            logger.debug(f"Error ensuring permissions: {e}")

    async def _uninstall_apk(self, package_name: str, emulator: EmulatorInstance) -> bool:
        """从模拟器卸载APK"""
        try:
            if not emulator.adb_device_id:
                return False
            
            logger.info(f"Uninstalling {package_name} from {emulator.adb_device_id}")
            
            process = await asyncio.create_subprocess_exec(
                self.tools.adb, '-s', emulator.adb_device_id, 'uninstall', package_name,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                logger.warning(f"Failed to uninstall APK: {stderr.decode()}")
                return False
            
            output = stdout.decode()
            success = "Success" in output or "UNINSTALL_SUCCEEDED" in output
            
            if success:
                logger.info(f"Successfully uninstalled {package_name}")
            
            return success
            
        except Exception as e:
            logger.warning(f"Error uninstalling APK: {e}")
            return False
    
    async def _launch_app(self, package_name: str, emulator: EmulatorInstance) -> bool:
        """启动应用"""
        try:
            if not emulator.adb_device_id:
                return False
            
            logger.info(f"Launching app {package_name}")
            
            # 全自动化应用启动（多种方式确保成功）
            logger.info(f"Launching app {package_name} with automated dialog handling")
            launch_success = False

            # 方法1: 使用am start启动主Activity
            try:
                process = await asyncio.create_subprocess_exec(
                    self.tools.adb, '-s', emulator.adb_device_id, 'shell',
                    'am', 'start', '-W', '-n', f'{package_name}/.SplashActivity',
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, stderr = await process.communicate()
                output = stdout.decode()
                if process.returncode == 0 and "Error" not in output:
                    launch_success = True
                    logger.info("App launched via direct activity start")
                else:
                    logger.debug(f"Direct activity start output: {output}")
            except Exception as e:
                logger.debug(f"Direct activity start failed: {e}")

            # 方法2: 如果直接启动失败，使用monkey
            if not launch_success:
                try:
                    process = await asyncio.create_subprocess_exec(
                        self.tools.adb, '-s', emulator.adb_device_id, 'shell',
                        'monkey', '-p', package_name, '1',
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.PIPE
                    )
                    stdout, stderr = await process.communicate()
                    if process.returncode == 0:
                        launch_success = True
                        logger.info("App launched via monkey")
                except Exception as e:
                    logger.debug(f"Monkey launch failed: {e}")

            # 方法3: 使用am start启动主launcher activity
            if not launch_success:
                try:
                    process = await asyncio.create_subprocess_exec(
                        self.tools.adb, '-s', emulator.adb_device_id, 'shell',
                        'am', 'start', '-W', '-a', 'android.intent.action.MAIN',
                        '-c', 'android.intent.category.LAUNCHER', package_name,
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.PIPE
                    )
                    stdout, stderr = await process.communicate()
                    if process.returncode == 0:
                        launch_success = True
                        logger.info("App launched via launcher intent")
                except Exception as e:
                    logger.debug(f"Launcher intent failed: {e}")
            
            # 等待应用启动
            await asyncio.sleep(2)

            # 全自动化处理所有可能的对话框
            await self._handle_all_dialogs(emulator)

            # 验证应用是否在运行
            is_running = await self._is_app_running(package_name, emulator)

            if launch_success or is_running:
                logger.info(f"App {package_name} launched successfully")
                return True
            else:
                logger.warning(f"App {package_name} launch verification failed")
                return False
            
        except Exception as e:
            logger.error(f"Error launching app: {e}")
            return False

    async def _handle_all_dialogs(self, emulator: EmulatorInstance) -> None:
        """全自动化处理所有可能的对话框"""
        try:
            if not emulator.adb_device_id:
                return

            logger.info("Auto-handling all dialogs (permissions, system warnings, etc.)")

            # 处理多种类型的对话框，最多尝试10次
            for attempt in range(10):
                process = await asyncio.create_subprocess_exec(
                    self.tools.adb, '-s', emulator.adb_device_id, 'shell',
                    'dumpsys', 'activity', 'activities',
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )

                stdout, _ = await process.communicate()

                if process.returncode == 0:
                    output = stdout.decode()
                    dialog_handled = False

                    # 检查各种对话框类型
                    dialog_patterns = [
                        'ReviewPermissionsActivity',  # 权限审查
                        'PermissionController',       # 权限控制器
                        'DeprecatedTargetSdkVersionDialog',  # SDK版本警告
                        'AlertDialog',                # 通用警告对话框
                        'ConfirmDialog',             # 确认对话框
                        'WarningDialog'              # 警告对话框
                    ]

                    for pattern in dialog_patterns:
                        if pattern in output:
                            logger.info(f"Detected dialog: {pattern}, auto-handling...")

                            # 尝试多种自动处理方式
                            await self._auto_handle_dialog(emulator)
                            dialog_handled = True
                            break

                    if not dialog_handled:
                        logger.info("No dialogs detected, app should be ready")
                        break

                    await asyncio.sleep(1)  # 等待对话框消失
                else:
                    break

        except Exception as e:
            logger.debug(f"Error handling dialogs: {e}")

    async def _auto_handle_dialog(self, emulator: EmulatorInstance) -> None:
        """自动处理对话框的多种方式"""
        try:
            # 方法1: 尝试点击常见的确认按钮位置
            confirm_positions = [
                (800, 1400),   # 右下角 - 允许/确定
                (540, 1400),   # 中下 - 确定
                (800, 1300),   # 稍微上一点
                (600, 1350),   # 中间偏右
                (270, 1400),   # 左下角 - 取消/拒绝（避免点击）
            ]

            for x, y in confirm_positions[:4]:  # 只点击确认类按钮
                await asyncio.create_subprocess_exec(
                    self.tools.adb, '-s', emulator.adb_device_id, 'shell',
                    'input', 'tap', str(x), str(y),
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                await asyncio.sleep(0.3)

            # 方法2: 尝试按键事件
            key_events = ['KEYCODE_ENTER', 'KEYCODE_DPAD_CENTER', 'KEYCODE_SPACE']
            for key in key_events:
                await asyncio.create_subprocess_exec(
                    self.tools.adb, '-s', emulator.adb_device_id, 'shell',
                    'input', 'keyevent', key,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                await asyncio.sleep(0.2)

        except Exception as e:
            logger.debug(f"Error in auto dialog handling: {e}")

    async def _monitor_network_activity(self, emulator: EmulatorInstance, package_name: str, duration: int) -> dict:
        """监控应用的网络活动（使用Android系统工具）"""
        try:
            if not emulator.adb_device_id:
                return {"error": "No device ID"}

            print(f"   开始监控网络活动，时长: {duration}秒")

            # 获取应用的UID
            process = await asyncio.create_subprocess_exec(
                self.tools.adb, '-s', emulator.adb_device_id, 'shell',
                'pm', 'list', 'packages', '-U', package_name,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, _ = await process.communicate()

            app_uid = None
            if process.returncode == 0:
                output = stdout.decode()
                # 解析输出获取UID，格式如: package:com.example.app uid:10123
                for line in output.strip().split('\n'):
                    if 'uid:' in line:
                        app_uid = line.split('uid:')[1].strip()
                        break

            if not app_uid:
                print(f"   ⚠️ 无法获取应用UID")
                return {"error": "Cannot get app UID"}

            print(f"   📱 应用UID: {app_uid}")

            # 监控网络统计
            network_stats = {}
            start_time = time.time()

            # 获取初始网络统计
            initial_stats = await self._get_network_stats(emulator, app_uid)
            print(f"   📊 初始网络统计: {initial_stats}")

            # 等待指定时间
            await asyncio.sleep(duration)

            # 获取结束时的网络统计
            final_stats = await self._get_network_stats(emulator, app_uid)
            print(f"   📊 最终网络统计: {final_stats}")

            # 计算网络活动
            if initial_stats and final_stats:
                rx_bytes_diff = final_stats.get('rx_bytes', 0) - initial_stats.get('rx_bytes', 0)
                tx_bytes_diff = final_stats.get('tx_bytes', 0) - initial_stats.get('tx_bytes', 0)
                rx_packets_diff = final_stats.get('rx_packets', 0) - initial_stats.get('rx_packets', 0)
                tx_packets_diff = final_stats.get('tx_packets', 0) - initial_stats.get('tx_packets', 0)

                network_stats = {
                    'app_uid': app_uid,
                    'duration': duration,
                    'rx_bytes': rx_bytes_diff,
                    'tx_bytes': tx_bytes_diff,
                    'rx_packets': rx_packets_diff,
                    'tx_packets': tx_packets_diff,
                    'total_bytes': rx_bytes_diff + tx_bytes_diff,
                    'total_packets': rx_packets_diff + tx_packets_diff,
                    'has_network_activity': (rx_bytes_diff + tx_bytes_diff) > 0
                }

                print(f"   📈 网络活动统计:")
                print(f"      接收: {rx_bytes_diff} 字节, {rx_packets_diff} 包")
                print(f"      发送: {tx_bytes_diff} 字节, {tx_packets_diff} 包")
                print(f"      总计: {rx_bytes_diff + tx_bytes_diff} 字节, {rx_packets_diff + tx_packets_diff} 包")
                print(f"      有网络活动: {'是' if network_stats['has_network_activity'] else '否'}")
            else:
                network_stats = {"error": "Cannot get network stats"}
                print(f"   ❌ 无法获取网络统计")

            return network_stats

        except Exception as e:
            logger.error(f"Error monitoring network activity: {e}")
            return {"error": str(e)}

    async def _get_network_stats(self, emulator: EmulatorInstance, app_uid: str) -> dict:
        """获取应用的网络统计信息"""
        try:
            # 使用 /proc/net/xt_qtaguid/stats 获取网络统计
            process = await asyncio.create_subprocess_exec(
                self.tools.adb, '-s', emulator.adb_device_id, 'shell',
                'cat', '/proc/net/xt_qtaguid/stats',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, _ = await process.communicate()

            if process.returncode != 0:
                # 回退到 /proc/uid_stat
                process = await asyncio.create_subprocess_exec(
                    self.tools.adb, '-s', emulator.adb_device_id, 'shell',
                    'cat', f'/proc/uid_stat/{app_uid}/tcp_rcv',
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout_rcv, _ = await process.communicate()

                process = await asyncio.create_subprocess_exec(
                    self.tools.adb, '-s', emulator.adb_device_id, 'shell',
                    'cat', f'/proc/uid_stat/{app_uid}/tcp_snd',
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout_snd, _ = await process.communicate()

                if process.returncode == 0:
                    rx_bytes = int(stdout_rcv.decode().strip()) if stdout_rcv.decode().strip().isdigit() else 0
                    tx_bytes = int(stdout_snd.decode().strip()) if stdout_snd.decode().strip().isdigit() else 0
                    return {
                        'rx_bytes': rx_bytes,
                        'tx_bytes': tx_bytes,
                        'rx_packets': 0,  # uid_stat doesn't provide packet counts
                        'tx_packets': 0
                    }
                return {}

            # 解析 xt_qtaguid 输出
            output = stdout.decode()
            rx_bytes = tx_bytes = rx_packets = tx_packets = 0

            for line in output.strip().split('\n'):
                if line.startswith('idx') or not line.strip():
                    continue

                parts = line.split()
                if len(parts) >= 8 and parts[3] == app_uid:
                    rx_bytes += int(parts[5])
                    tx_bytes += int(parts[7])
                    rx_packets += int(parts[4])
                    tx_packets += int(parts[6])

            return {
                'rx_bytes': rx_bytes,
                'tx_bytes': tx_bytes,
                'rx_packets': rx_packets,
                'tx_packets': tx_packets
            }

        except Exception as e:
            logger.debug(f"Error getting network stats: {e}")
            return {}

    async def _handle_app_interaction(self, emulator: EmulatorInstance, package_name: str, timeout: int) -> str:
        """全自动化应用交互（专注于触发网络请求）"""
        try:
            print(f"   开始自动化交互，时长: {timeout}秒")
            logger.info(f"Starting automated app interaction for {timeout}s")

            # 简化的自动化交互策略
            interactions_performed = 0
            start_time = time.time()

            while (time.time() - start_time < timeout and interactions_performed < 10):
                elapsed = int(time.time() - start_time)
                print(f"   🎯 交互轮次 {interactions_performed + 1}/10 (已用时: {elapsed}s)")

                # 1. 基础点击交互
                print(f"      执行基础点击操作...")
                await self._perform_basic_clicks(emulator)
                interactions_performed += 1

                # 2. 滑动操作
                if interactions_performed % 3 == 0:
                    print(f"      执行滑动操作...")
                    await self._perform_basic_swipes(emulator)

                # 3. 按键操作
                if interactions_performed % 5 == 0:
                    print(f"      执行按键操作...")
                    await self._perform_basic_keys(emulator)

                print(f"      等待应用响应...")
                await asyncio.sleep(2)  # 等待响应

            print(f"   ✅ 完成 {interactions_performed} 轮交互")
            logger.info(f"Completed {interactions_performed} automated interactions")
            return f"interactions_completed:{interactions_performed}"

        except Exception as e:
            logger.error(f"Error in app interaction: {e}")
            return f"interaction_error:{str(e)}"

    async def _perform_basic_clicks(self, emulator: EmulatorInstance) -> None:
        """执行基础点击操作"""
        try:
            # 点击屏幕中心和常见UI位置
            click_positions = [
                (540, 1000, "屏幕中心"),
                (540, 800, "中上区域"),
                (540, 1200, "中下区域"),
                (200, 1000, "左中区域"),
                (880, 1000, "右中区域"),
            ]

            for x, y, desc in click_positions:
                print(f"         点击 {desc} ({x}, {y})")
                await asyncio.create_subprocess_exec(
                    self.tools.adb, '-s', emulator.adb_device_id, 'shell',
                    'input', 'tap', str(x), str(y),
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                await asyncio.sleep(0.5)

        except Exception as e:
            logger.debug(f"Error in basic clicks: {e}")

    async def _perform_basic_swipes(self, emulator: EmulatorInstance) -> None:
        """执行基础滑动操作"""
        try:
            # 上下左右滑动
            swipe_actions = [
                (540, 1200, 540, 800, "向上滑动"),
                (540, 800, 540, 1200, "向下滑动"),
                (800, 1000, 300, 1000, "向左滑动"),
                (300, 1000, 800, 1000, "向右滑动"),
            ]

            for x1, y1, x2, y2, desc in swipe_actions:
                print(f"         {desc} ({x1},{y1}) -> ({x2},{y2})")
                await asyncio.create_subprocess_exec(
                    self.tools.adb, '-s', emulator.adb_device_id, 'shell',
                    'input', 'swipe', str(x1), str(y1), str(x2), str(y2), '300',
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                await asyncio.sleep(0.5)

        except Exception as e:
            logger.debug(f"Error in basic swipes: {e}")

    async def _perform_basic_keys(self, emulator: EmulatorInstance) -> None:
        """执行基础按键操作"""
        try:
            # 常用按键
            key_events = [
                ('KEYCODE_BACK', '返回键'),
                ('KEYCODE_MENU', '菜单键'),
                ('KEYCODE_HOME', '主页键'),
                ('KEYCODE_ENTER', '确认键')
            ]

            for key, desc in key_events:
                print(f"         按键 {desc} ({key})")
                await asyncio.create_subprocess_exec(
                    self.tools.adb, '-s', emulator.adb_device_id, 'shell',
                    'input', 'keyevent', key,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                await asyncio.sleep(0.3)

        except Exception as e:
            logger.debug(f"Error in basic keys: {e}")

    async def _handle_permission_dialogs(self, emulator: EmulatorInstance) -> None:
        """处理权限请求对话框"""
        try:
            if not emulator.adb_device_id:
                return

            logger.info("Checking for permission dialogs...")

            # 检查是否有权限对话框，最多尝试5次
            for attempt in range(5):
                process = await asyncio.create_subprocess_exec(
                    self.tools.adb, '-s', emulator.adb_device_id, 'shell',
                    'dumpsys', 'activity', 'activities',
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )

                stdout, _ = await process.communicate()

                if process.returncode == 0:
                    output = stdout.decode()

                    # 检查是否有权限对话框
                    if "ReviewPermissionsActivity" in output or "permissioncontroller" in output.lower():
                        logger.info(f"Permission dialog detected (attempt {attempt + 1}), trying to handle it")

                        # 尝试多种方法点击允许按钮
                        click_attempts = [
                            # 常见的允许按钮位置
                            ('800', '1400'),  # 右下角
                            ('540', '1400'),  # 中下
                            ('800', '1300'),  # 稍微上一点
                            ('600', '1350'),  # 中间偏右
                        ]

                        for x, y in click_attempts:
                            await asyncio.create_subprocess_exec(
                                self.tools.adb, '-s', emulator.adb_device_id, 'shell',
                                'input', 'tap', x, y,
                                stdout=asyncio.subprocess.PIPE,
                                stderr=asyncio.subprocess.PIPE
                            )
                            await asyncio.sleep(0.5)

                        # 也尝试按键事件
                        await asyncio.create_subprocess_exec(
                            self.tools.adb, '-s', emulator.adb_device_id, 'shell',
                            'input', 'keyevent', 'KEYCODE_ENTER',
                            stdout=asyncio.subprocess.PIPE,
                            stderr=asyncio.subprocess.PIPE
                        )

                        await asyncio.sleep(2)  # 等待对话框消失
                    else:
                        logger.info("No permission dialog found")
                        break

        except Exception as e:
            logger.debug(f"Error handling permission dialogs: {e}")

    async def _is_app_running(self, package_name: str, emulator: EmulatorInstance) -> bool:
        """检查应用是否在运行"""
        try:
            if not emulator.adb_device_id:
                return False

            process = await asyncio.create_subprocess_exec(
                self.tools.adb, '-s', emulator.adb_device_id, 'shell',
                'dumpsys', 'activity', 'activities',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, _ = await process.communicate()

            if process.returncode == 0:
                output = stdout.decode()
                return package_name in output and "mCurrentFocus" in output

            return False

        except Exception as e:
            logger.debug(f"Error checking app status: {e}")
            return False

    # UI交互现在由LocalUIAutomator处理，移除了旧的交互方法

    async def _capture_network_traffic(self, emulator: EmulatorInstance, timeout: int, package_name: str) -> List[NetworkRequest]:
        """捕获网络流量"""
        logger.info(f"Starting network traffic capture for {timeout}s")
        
        captured_requests = []
        mitm_process = None
        session_id = f"local_session_{int(time.time())}"
        
        try:
            # 1. 启动mitmproxy进程
            mitm_process = await self._start_mitmproxy_process(session_id)
            
            if mitm_process:
                logger.info("mitmproxy started successfully")
                
                # 2. 等待mitmproxy启动
                await asyncio.sleep(3)
                
                # 3. 安装mitmproxy证书
                cert_installed = await self._install_mitm_certificate(emulator)
                if cert_installed:
                    logger.info("mitmproxy certificate installed successfully")
                else:
                    logger.warning("Failed to install mitmproxy certificate")
                
                # 4. 配置设备代理
                proxy_configured = await self._configure_device_proxy(emulator)
                if proxy_configured:
                    logger.info("Device proxy configured")
                else:
                    logger.warning("Failed to configure device proxy")
                
                # 5. 启用Frida SSL绕过
                ssl_bypass_enabled = await self._enable_frida_ssl_bypass(emulator, package_name)
                if ssl_bypass_enabled:
                    logger.info("Frida SSL bypass enabled")
                else:
                    logger.warning("Failed to enable Frida SSL bypass")
                
                # 6. 等待网络流量捕获（减少等待时间避免卡住）
                actual_timeout = min(timeout, 30)  # 最多等待30秒
                logger.info(f"Capturing network traffic for {actual_timeout} seconds...")
                await asyncio.sleep(actual_timeout)
                
                # 7. 获取捕获的请求
                captured_requests = await self._get_captured_requests_from_session(session_id)
                logger.info(f"Captured {len(captured_requests)} network requests")
            
            else:
                logger.warning("Failed to start mitmproxy, using fallback capture")
                # 使用备用方法捕获网络流量
                captured_requests = await self._fallback_network_capture(emulator, timeout)

        except Exception as e:
            logger.error(f"Network capture error: {e}")

        finally:
            # 清理mitmproxy进程
            if mitm_process:
                try:
                    mitm_process.terminate()
                    await asyncio.wait_for(mitm_process.wait(), timeout=10)
                except asyncio.TimeoutError:
                    mitm_process.kill()
                    await mitm_process.wait()
                except Exception as e:
                    logger.error(f"Error stopping mitmproxy: {e}")

            # 清理设备代理配置
            try:
                await self._clear_device_proxy(emulator)
            except Exception as e:
                logger.debug(f"Error clearing device proxy: {e}")

        return captured_requests

    async def _start_mitmproxy_process(self, session_id: str) -> Optional[asyncio.subprocess.Process]:
        """启动mitmproxy进程"""
        try:
            # 检查mitmproxy是否可用
            which_process = await asyncio.create_subprocess_exec(
                'which', 'mitmdump',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            await which_process.communicate()

            if which_process.returncode != 0:
                logger.warning("mitmdump not found, network capture will be limited")
                return None

            # 使用本地优化的捕获脚本
            mitm_script = Path(__file__).parent.parent.parent / "mitm-scripts" / "local_capture.py"

            if not mitm_script.exists():
                logger.warning(f"Local MITM script not found: {mitm_script}")
                # 回退到原始脚本
                mitm_script = Path(__file__).parent.parent.parent / "mitm-scripts" / "capture.py"
                if not mitm_script.exists():
                    logger.error("No MITM script found")
                    return None

            # 设置环境变量
            env = os.environ.copy()
            env['MITM_SESSION_ID'] = session_id
            env['MITM_OUTPUT_DIR'] = str(self.mitm_logs_dir)

            # 使用透明代理模式以获得更好的兼容性
            cmd = [
                'mitmdump',
                '-s', str(mitm_script),
                '--mode', 'transparent',
                '--listen-port', '8080',
                '--set', 'confdir=~/.mitmproxy',
                '--set', 'stream_large_bodies=1',  # 流式处理大文件
                '--set', 'ssl_insecure=true',      # 忽略SSL证书错误
                '--quiet'  # 减少日志输出
            ]

            process = await asyncio.create_subprocess_exec(
                *cmd,
                env=env,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            logger.info(f"Started mitmproxy with PID {process.pid}")
            return process

        except Exception as e:
            logger.error(f"Failed to start mitmproxy: {e}")
            return None

    async def _configure_device_proxy(self, emulator: EmulatorInstance) -> bool:
        """配置设备代理"""
        try:
            if not emulator.adb_device_id:
                return False

            # 获取电脑的实际IP地址（基于Burp Suite文档的方法）
            import subprocess
            try:
                result = subprocess.run(['ifconfig'], capture_output=True, text=True)
                lines = result.stdout.split('\n')
                host_ip = None
                for line in lines:
                    if 'inet ' in line and '127.0.0.1' not in line and 'inet 169.254' not in line:
                        host_ip = line.split()[1]
                        break

                if not host_ip:
                    # 尝试使用特殊的Android模拟器主机IP
                    host_ip = '********'  # Android模拟器访问主机的特殊IP

            except Exception as e:
                host_ip = '********'  # 回退到Android模拟器主机IP
                logger.debug(f"Failed to get host IP: {e}")

            proxy_host = f'{host_ip}:8080'

            # 使用全局代理设置（设置HTTP和HTTPS代理）
            commands = [
                ('shell', 'settings', 'put', 'global', 'http_proxy', proxy_host),
                ('shell', 'settings', 'put', 'global', 'https_proxy', proxy_host),
                ('shell', 'settings', 'put', 'global', 'global_http_proxy_host', host_ip),
                ('shell', 'settings', 'put', 'global', 'global_http_proxy_port', '8080'),
            ]

            logger.info(f"Configuring device proxy to {proxy_host} (host IP: {host_ip})")

            for cmd in commands:
                process = await asyncio.create_subprocess_exec(
                    self.tools.adb, '-s', emulator.adb_device_id, *cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                await process.communicate()

            # 验证代理设置
            verify_cmd = [
                self.tools.adb, '-s', emulator.adb_device_id, 'shell',
                'settings', 'get', 'global', 'http_proxy'
            ]
            process = await asyncio.create_subprocess_exec(
                *verify_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, _ = await process.communicate()
            
            if proxy_host in stdout.decode():
                logger.info("Device proxy configured successfully")
                return True
            else:
                logger.warning("Device proxy configuration verification failed")
                return False

        except Exception as e:
            logger.error(f"Failed to configure device proxy: {e}")
            return False

    async def _clear_device_proxy(self, emulator: EmulatorInstance):
        """清除设备代理配置"""
        try:
            if not emulator.adb_device_id:
                return

            commands = [
                ('shell', 'settings', 'delete', 'global', 'http_proxy'),
                ('shell', 'settings', 'delete', 'global', 'https_proxy'),
                ('shell', 'settings', 'delete', 'global', 'global_http_proxy_host'),
                ('shell', 'settings', 'delete', 'global', 'global_http_proxy_port'),
            ]

            logger.info("Clearing device proxy settings")
            
            for cmd in commands:
                process = await asyncio.create_subprocess_exec(
                    self.tools.adb, '-s', emulator.adb_device_id, *cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                await process.communicate()

            # 停止frida进程
            try:
                process = await asyncio.create_subprocess_exec(
                    self.tools.adb, '-s', emulator.adb_device_id, 'shell', 'pkill', 'frida',
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                await process.communicate()
                logger.info("Frida processes stopped")
            except Exception as e:
                logger.debug(f"Error stopping Frida processes: {e}")

            logger.info("Device proxy settings cleared")

        except Exception as e:
            logger.debug(f"Error clearing device proxy: {e}")

    async def _get_captured_requests_from_session(self, session_id: str) -> List[NetworkRequest]:
        """从会话获取捕获的请求"""
        try:
            # 从文件读取捕获的请求
            session_file = self.mitm_logs_dir / f"session_{session_id}.json"

            if not session_file.exists():
                logger.warning(f"Session file not found: {session_file}")
                return []

            with open(session_file, 'r') as f:
                requests_data = json.load(f)

            # 转换为NetworkRequest对象
            network_requests = []
            for req_data in requests_data:
                try:
                    network_request = NetworkRequest(
                        url=req_data.get('url', ''),
                        method=req_data.get('method', 'GET'),
                        headers=req_data.get('headers', {}),
                        timestamp=req_data.get('timestamp', 0),
                        response_code=req_data.get('status_code', 0),
                        content_type=req_data.get('content_type', ''),
                        size=req_data.get('content_length', 0)
                    )
                    network_requests.append(network_request)
                except Exception as e:
                    logger.debug(f"Error parsing request data: {e}")

            return network_requests

        except Exception as e:
            logger.error(f"Error reading captured requests: {e}")
            return []

    async def _fallback_network_capture(self, emulator: EmulatorInstance, timeout: int) -> List[NetworkRequest]:
        """备用网络捕获方法"""
        try:
            if not emulator.adb_device_id:
                return []

            logger.info("Using fallback network capture method")

            # 使用tcpdump或其他方法捕获网络流量
            # 这里简化实现，实际可以使用更复杂的方法

            # 启动网络监控
            process = await asyncio.create_subprocess_exec(
                self.tools.adb, '-s', emulator.adb_device_id, 'shell',
                'netstat', '-tuln',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, _ = await process.communicate()

            # 简单解析网络连接（这里只是示例）
            network_requests = []
            if process.returncode == 0:
                output = stdout.decode()
                # 这里可以添加更复杂的网络流量解析逻辑
                lines = output.split('\n')
                logger.debug(f"Network connections: {len(lines)} lines")

            return network_requests

        except Exception as e:
            logger.error(f"Fallback network capture failed: {e}")
            return []

    def _filter_requests(self, requests: List[NetworkRequest]) -> List[NetworkRequest]:
        """过滤网络请求"""
        if not requests:
            return []

        filtered = []
        system_domains = {
            'google.com', 'googleapis.com', 'gstatic.com', 'googleusercontent.com',
            'doubleclick.net', 'googlesyndication.com', 'googleadservices.com',
            'android.com', 'mozilla.org', 'firefox.com', 'gvt1.com',
            'crashlytics.com', 'fabric.io', 'firebase.com'
        }

        for request in requests:
            try:
                # 过滤系统域名
                url_lower = request.url.lower()
                is_system = any(domain in url_lower for domain in system_domains)

                if not is_system:
                    # 过滤广告和分析域名
                    ad_patterns = ['ads', 'analytics', 'tracking', 'telemetry', 'crash', 'metrics']
                    is_ad = any(pattern in url_lower for pattern in ad_patterns)

                    if not is_ad:
                        filtered.append(request)

            except Exception as e:
                logger.debug(f"Error filtering request: {e}")

        logger.info(f"Filtered {len(requests)} -> {len(filtered)} requests")
        return filtered

    def _convert_to_url_schemas(self, requests: List[NetworkRequest]) -> List[Dict[str, Any]]:
        """转换网络请求为URL模式"""
        url_schemas = []

        for request in requests:
            try:
                from urllib.parse import urlparse
                parsed = urlparse(request.url)

                schema = {
                    'url': request.url,
                    'domain': parsed.netloc,
                    'path': parsed.path,
                    'method': request.method,
                    'scheme': parsed.scheme,
                    'query_params': parsed.query,
                    'timestamp': request.timestamp,
                    'response_code': request.response_code,
                    'content_type': request.content_type,
                    'size': request.size
                }

                url_schemas.append(schema)

            except Exception as e:
                logger.debug(f"Error converting request to schema: {e}")

        return url_schemas

    async def _install_mitm_certificate(self, emulator: EmulatorInstance) -> bool:
        """安装mitmproxy证书到Android系统"""
        try:
            if not emulator.adb_device_id:
                return False
            
            logger.info("Installing mitmproxy certificate")
            
            # 1. 检查证书文件
            cert_path = Path.home() / ".mitmproxy" / "mitmproxy-ca-cert.pem"
            if not cert_path.exists():
                logger.warning(f"Certificate file not found: {cert_path}")
                return False
            
            # 2. 推送证书到设备
            push_cmd = [
                self.tools.adb, '-s', emulator.adb_device_id, 'push', 
                str(cert_path), '/sdcard/mitmproxy-ca-cert.pem'
            ]
            process = await asyncio.create_subprocess_exec(
                *push_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            await process.communicate()
            
            # 3. 获取证书哈希值
            hash_cmd = f"openssl x509 -inform PEM -subject_hash_old -in {cert_path} -noout"
            process = await asyncio.create_subprocess_shell(
                hash_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, _ = await process.communicate()
            cert_hash = stdout.decode().strip()
            
            if not cert_hash:
                logger.warning("Failed to get certificate hash")
                return False
            
            # 4. 重新挂载系统分区为可写
            await asyncio.create_subprocess_exec(
                self.tools.adb, '-s', emulator.adb_device_id, 'root',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            await asyncio.sleep(2)
            
            await asyncio.create_subprocess_exec(
                self.tools.adb, '-s', emulator.adb_device_id, 'remount',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            # 5. 安装证书到系统证书目录
            system_cert_name = f"{cert_hash}.0"
            copy_cmd = [
                self.tools.adb, '-s', emulator.adb_device_id, 'shell',
                'cp', '/sdcard/mitmproxy-ca-cert.pem', f'/system/etc/security/cacerts/{system_cert_name}'
            ]
            process = await asyncio.create_subprocess_exec(
                *copy_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            await process.communicate()
            
            # 6. 设置正确的权限
            chmod_cmd = [
                self.tools.adb, '-s', emulator.adb_device_id, 'shell',
                'chmod', '644', f'/system/etc/security/cacerts/{system_cert_name}'
            ]
            process = await asyncio.create_subprocess_exec(
                *chmod_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            await process.communicate()
            
            # 7. 验证证书安装
            verify_cmd = [
                self.tools.adb, '-s', emulator.adb_device_id, 'shell',
                'ls', '-la', f'/system/etc/security/cacerts/{system_cert_name}'
            ]
            process = await asyncio.create_subprocess_exec(
                *verify_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            
            if system_cert_name in stdout.decode():
                logger.info(f"Certificate installed successfully: {system_cert_name}")
                return True
            else:
                logger.warning(f"Certificate installation verification failed: {stderr.decode()}")
                return False
            
        except Exception as e:
            logger.error(f"Error installing certificate: {e}")
            return False
            
    async def _enable_frida_ssl_bypass(self, emulator: EmulatorInstance, package_name: str = None) -> bool:
        """启用Frida SSL绕过
        
        Args:
            emulator: 模拟器实例
            package_name: 目标应用包名，如果为None则尝试自动检测
        
        Returns:
            bool: 是否成功启用Frida SSL绕过
        """
        try:
            if not emulator.adb_device_id:
                logger.error("No ADB device ID available")
                return False
                
            logger.info("Enabling Frida SSL bypass")
            
            # 1. 检查frida-server是否运行
            process = await asyncio.create_subprocess_exec(
                self.tools.adb, '-s', emulator.adb_device_id, 'shell', 'ps | grep frida-server',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, _ = await process.communicate()
            
            if 'frida-server' not in stdout.decode():
                logger.info("Frida server not running, starting it...")
                
                # 推送frida-server到设备
                frida_server_path = Path("frida-server")
                if not frida_server_path.exists():
                    # 尝试使用不同架构的frida-server
                    frida_server_path = Path("frida-server-x86_64")
                    if not frida_server_path.exists():
                        logger.warning("Frida server binary not found")
                        return False
                    
                await asyncio.create_subprocess_exec(
                    self.tools.adb, '-s', emulator.adb_device_id, 'push', 
                    str(frida_server_path), '/data/local/tmp/',
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                # 设置执行权限
                await asyncio.create_subprocess_exec(
                    self.tools.adb, '-s', emulator.adb_device_id, 'shell',
                    'chmod', '755', '/data/local/tmp/frida-server',
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                # 启动frida-server
                process = await asyncio.create_subprocess_exec(
                    self.tools.adb, '-s', emulator.adb_device_id, 'shell',
                    '/data/local/tmp/frida-server &',
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                await asyncio.sleep(3)  # 增加等待时间确保frida-server完全启动
                logger.info("Frida server started successfully")
            
            # 2. 推送SSL绕过脚本
            ssl_bypass_script = Path("ssl_bypass.js")
            if not ssl_bypass_script.exists():
                logger.warning("SSL bypass script not found")
                return False
                
            await asyncio.create_subprocess_exec(
                self.tools.adb, '-s', emulator.adb_device_id, 'push', 
                str(ssl_bypass_script), '/data/local/tmp/',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            logger.info("SSL bypass script pushed to device")
            
            # 3. 获取目标应用包名和进程ID
            target_package = package_name
            if not target_package:
                # 尝试自动检测运行的应用
                logger.info("No package name provided, trying to detect running app...")
                
                # 获取前台应用包名
                cmd = [
                    self.tools.adb, '-s', emulator.adb_device_id, 'shell',
                    'dumpsys', 'window', 'windows', '|', 'grep', '-E', 'mCurrentFocus|mFocusedApp'
                ]
                process = await asyncio.create_subprocess_exec(
                    *cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, _ = await process.communicate()
                
                # 解析包名
                output = stdout.decode()
                import re
                match = re.search(r'([a-zA-Z][a-zA-Z0-9._]*)/[a-zA-Z0-9._]+', output)
                if match:
                    target_package = match.group(1)
                    logger.info(f"Detected foreground app: {target_package}")
                else:
                    # 回退到浏览器
                    target_package = "com.android.browser"
                    logger.warning(f"Could not detect app, falling back to: {target_package}")
            
            # 4. 获取目标应用进程ID
            app_info_cmd = [
                self.tools.adb, '-s', emulator.adb_device_id, 'shell',
                f'ps | grep {target_package}'
            ]
            process = await asyncio.create_subprocess_exec(
                *app_info_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            
            app_processes = stdout.decode().strip()
            if not app_processes:
                logger.warning(f"No running processes found for package: {target_package}")
                return False
            
            # 5. 启动非阻塞的Frida进程
            # 注意：这里使用subprocess.Popen而不是asyncio.create_subprocess_exec
            # 因为我们需要在后台运行Frida而不阻塞分析流程
            import subprocess
            frida_cmd = f"frida -U {target_package} -l /data/local/tmp/ssl_bypass.js --no-pause"
            
            try:
                # 使用nohup确保进程在后台运行
                subprocess.Popen(
                    f"nohup {frida_cmd} > /tmp/frida_{target_package}.log 2>&1 &",
                    shell=True,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE
                )
                logger.info(f"Frida SSL bypass started in background for package: {target_package}")
                
                # 6. 验证Frida注入是否成功
                await asyncio.sleep(2)  # 等待Frida注入
                
                # 检查Frida日志文件
                check_log_cmd = [
                    self.tools.adb, '-s', emulator.adb_device_id, 'shell',
                    'cat', '/tmp/frida_{target_package}.log', '|', 'head', '-5'
                ]
                process = await asyncio.create_subprocess_exec(
                    *check_log_cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, _ = await process.communicate()
                
                log_output = stdout.decode()
                if "SSL Kill Switch started" in log_output or "Script loaded successfully" in log_output:
                    logger.info("Frida SSL bypass injection verified successfully")
                    return True
                else:
                    logger.warning(f"Frida injection verification inconclusive. Log: {log_output}")
                    return True  # 仍然返回True，因为可能只是日志检查有问题
                    
            except Exception as e:
                logger.error(f"Failed to start Frida SSL bypass: {e}")
                return False
            
        except Exception as e:
            logger.error(f"Error setting up Frida SSL bypass: {e}")
            return False
    
    async def _get_runtime_info(self, emulator: EmulatorInstance) -> Optional[RuntimeInfo]:
        """获取运行时信息"""
        try:
            if not emulator.adb_device_id:
                return None

            # 获取设备信息
            device_info = await self._get_device_info(emulator)

            return RuntimeInfo(
                device_model=device_info.get('model', 'Unknown'),
                android_version=device_info.get('version', 'Unknown'),
                api_level=device_info.get('api_level', 0),
                architecture=device_info.get('architecture', 'Unknown'),
                memory_info=device_info.get('memory', {}),
                cpu_info=device_info.get('cpu', {})
            )

        except Exception as e:
            logger.error(f"Error getting runtime info: {e}")
            return None

    async def _get_device_info(self, emulator: EmulatorInstance) -> Dict[str, Any]:
        """获取设备信息"""
        try:
            if not emulator.adb_device_id:
                return {}

            info = {}

            # 获取各种设备属性
            properties = {
                'model': 'ro.product.model',
                'version': 'ro.build.version.release',
                'api_level': 'ro.build.version.sdk',
                'architecture': 'ro.product.cpu.abi'
            }

            for key, prop in properties.items():
                try:
                    process = await asyncio.create_subprocess_exec(
                        self.tools.adb, '-s', emulator.adb_device_id, 'shell',
                        'getprop', prop,
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.PIPE
                    )
                    stdout, _ = await process.communicate()

                    if process.returncode == 0:
                        value = stdout.decode().strip()
                        if key == 'api_level':
                            try:
                                info[key] = int(value)
                            except ValueError:
                                info[key] = 0
                        else:
                            info[key] = value

                except Exception as e:
                    logger.debug(f"Error getting {key}: {e}")

            return info

        except Exception as e:
            logger.error(f"Error getting device info: {e}")
            return {}

    async def _get_android_version(self, emulator: EmulatorInstance) -> str:
        """获取Android版本"""
        try:
            device_info = await self._get_device_info(emulator)
            return device_info.get('version', 'Unknown')
        except Exception as e:
            logger.error(f"Error getting Android version: {e}")
            return "Unknown"

    def get_ui_dump(self, device_id: str = "emulator-5554") -> Optional[List[SimpleUIElement]]:
        """获取当前UI元素"""
        try:
            # 获取UI dump
            subprocess.run([self.tools.adb_path, '-s', device_id, 'shell', 
                           'uiautomator', 'dump', '/sdcard/ui.xml'], 
                         capture_output=True, timeout=10)
            
            # 读取UI XML
            result = subprocess.run([self.tools.adb_path, '-s', device_id, 'shell', 
                                   'cat', '/sdcard/ui.xml'],
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0 and result.stdout:
                return self._parse_ui_xml(result.stdout)
                
        except Exception as e:
            logger.error(f"获取UI dump失败: {e}")
        
        return None
    
    def _parse_ui_xml(self, xml_content: str) -> List[SimpleUIElement]:
        """解析UI XML"""
        elements = []
        
        try:
            root = ET.fromstring(xml_content)
            
            for node in root.iter():
                if node.tag == 'node':
                    try:
                        # 解析bounds属性
                        bounds_str = node.get('bounds', '[0,0][0,0]')
                        bounds_match = re.findall(r'\[(\d+),(\d+)\]', bounds_str)
                        if len(bounds_match) >= 2:
                            left, top = int(bounds_match[0][0]), int(bounds_match[0][1])
                            right, bottom = int(bounds_match[1][0]), int(bounds_match[1][1])
                            bounds = (left, top, right, bottom)
                        else:
                            continue
                        
                        # 创建UI元素
                        element = SimpleUIElement(
                            text=node.get('text', ''),
                            content_desc=node.get('content-desc', ''),
                            resource_id=node.get('resource-id', ''),
                            bounds=bounds,
                            clickable=node.get('clickable', 'false').lower() == 'true',
                            enabled=node.get('enabled', 'true').lower() == 'true',
                            class_name=node.get('class', '')
                        )
                        
                        # 过滤无效元素
                        if (element.width > 0 and element.height > 0 and
                            element.bounds[0] >= 0 and element.bounds[1] >= 0):
                            elements.append(element)
                    
                    except Exception as e:
                        logger.debug(f"解析UI元素出错: {e}")
                        continue
        
        except Exception as e:
            logger.error(f"解析UI XML出错: {e}")
        
        logger.debug(f"解析到 {len(elements)} 个UI元素")
        return elements
    
    def find_consent_buttons(self, elements: List[SimpleUIElement]) -> List[SimpleUIElement]:
        """查找同意/确定/允许类型的按钮"""
        consent_buttons = []
        
        # 定义同意相关的关键词（更全面的列表）
        consent_keywords = [
            '同意', '确定', '允许', '接受', '继续', '进入', '开始',
            'ok', 'agree', 'accept', 'allow', 'permit', 'continue', 
            'yes', '是', '好', '确认', 'confirm', '立即体验', '马上体验',
            '我知道了', '我同意', '好的', 'got it', 'i agree'
        ]
        
        for element in elements:
            if not element.clickable or not element.enabled:
                continue
                
            # 检查文本内容（转为小写进行比较）
            text_content = (element.text + " " + element.content_desc + " " + 
                          element.resource_id + " " + element.class_name).lower()
            
            # 检查是否包含同意相关关键词
            for keyword in consent_keywords:
                if keyword.lower() in text_content:
                    consent_buttons.append(element)
                    logger.info(f"发现同意按钮: '{element.text or element.content_desc}' at {element.bounds}")
                    break
        
        # 按优先级排序：文本更明确的按钮优先
        consent_buttons.sort(key=lambda x: self._get_button_priority(x), reverse=True)
        
        return consent_buttons
    
    def _get_button_priority(self, element: SimpleUIElement) -> int:
        """获取按钮优先级分数"""
        text_content = (element.text + " " + element.content_desc).lower()
        
        # 高优先级关键词
        high_priority = ['同意', '确定', 'ok', 'agree', 'accept']
        medium_priority = ['允许', '接受', '继续', 'allow', 'continue', '我同意']
        low_priority = ['是', '好', 'yes', '我知道了']
        
        score = 0
        if any(keyword.lower() in text_content for keyword in high_priority):
            score += 10
        if any(keyword.lower() in text_content for keyword in medium_priority):
            score += 5
        if any(keyword.lower() in text_content for keyword in low_priority):
            score += 2
            
        # 大小加成：较大的按钮更可能是主要按钮
        if element.width * element.height > 50000:  # 大按钮
            score += 3
            
        # 位置加成：在屏幕下半部分的按钮更可能是确认按钮
        if element.center_y > 1000:  # 下半部分
            score += 2
        
        return score
    
    def click_button(self, element: SimpleUIElement, device_id: str = "emulator-5554") -> bool:
        """点击指定的按钮"""
        try:
            x, y = element.center_x, element.center_y
            logger.info(f"点击按钮: ({x}, {y}) - '{element.text or element.content_desc}'")
            
            result = subprocess.run([self.tools.adb_path, '-s', device_id, 'shell', 
                                   'input', 'tap', str(x), str(y)], 
                                  capture_output=True, timeout=5)
            
            if result.returncode == 0:
                logger.info("✅ 按钮点击成功")
                return True
            else:
                logger.error(f"❌ 按钮点击失败: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 点击按钮时出错: {e}")
            return False
    
    def auto_click_consent_buttons(self, device_id: str = "emulator-5554") -> bool:
        """自动检测并点击同意按钮（仅点击一次）"""
        logger.info("🔍 开始自动检测并点击同意按钮...")
        
        # 获取当前UI元素
        elements = self.get_ui_dump(device_id)
        if not elements:
            logger.warning("无法获取UI元素")
            return False
        
        # 查找同意按钮
        consent_buttons = self.find_consent_buttons(elements)
        
        if not consent_buttons:
            logger.info("未找到同意按钮，检查是否有对话框...")
            
            # 检查是否还有对话框
            dialog_elements = []
            for e in elements:
                if (('dialog' in e.class_name.lower() or 
                     'alert' in e.class_name.lower() or
                     'popup' in e.class_name.lower()) and
                    e.width > 200 and e.height > 200):  # 有实际尺寸的对话框
                    dialog_elements.append(e)
            
            if not dialog_elements:
                logger.info("✅ 没有发现对话框，可能已经完成同意流程")
                return True
            
            logger.info(f"发现 {len(dialog_elements)} 个对话框但没有明确的同意按钮")
            return False
                    
        else:
            # 点击找到的第一个同意按钮（只点击一次）
            if self.click_button(consent_buttons[0], device_id):
                logger.info("⏰ 等待5秒让页面响应...")
                time.sleep(5)
                
                # 检查页面是否已经跳转（通过检查UI是否变化）
                new_elements = self.get_ui_dump(device_id)
                if new_elements:
                    # 简单检查：如果新页面的元素数量明显不同，说明页面跳转了
                    old_clickable_count = len([e for e in elements if e.clickable])
                    new_clickable_count = len([e for e in new_elements if e.clickable])
                    
                    if abs(old_clickable_count - new_clickable_count) > 2:
                        logger.info("✅ 页面已跳转，同意流程完成")
                        return True
                    
                    # 检查是否还有新的同意按钮
                    new_consent_buttons = self.find_consent_buttons(new_elements)
                    if not new_consent_buttons:
                        logger.info("✅ 成功点击同意按钮，没有发现更多同意按钮")
                        return True
                    else:
                        logger.info("⚠️ 还有同意按钮，但只点击一次，让后续流程处理")
                        return True  # 返回成功，让后续流程继续
                else:
                    logger.info("✅ 成功点击同意按钮")
                    return True
            else:
                logger.warning("点击同意按钮失败")
                return False

    async def test_multiple_activities(self, emulator: EmulatorInstance, package_name: str, 
                                     apk_path: Optional[str], timeout: int = 180) -> Dict[str, Any]:
        """测试应用的多个activities并捕获网络流量"""
        logger.info(f"🎯 开始测试多个activities: {package_name}")
        
        result = {
            "package_name": package_name,
            "activities_tested": [],
            "total_activities": 0,
            "successful_activities": 0,
            "network_requests": [],
            "captured_urls": []
        }
        
        # 启动网络流量捕获
        logger.info("🌐 启动网络流量捕获...")
        capture_task = asyncio.create_task(
            self._capture_network_traffic(emulator, timeout, package_name)
        )
        
        try:
            # 1. 从APK中获取exported activities
            exported_activities = self._get_exported_activities(apk_path)
            if not exported_activities:
                logger.warning("未找到exported activities，使用默认启动")
                exported_activities = [None]  # None表示使用默认启动方式
            
            result["total_activities"] = len(exported_activities)
            logger.info(f"发现 {len(exported_activities)} 个可测试的activities")
            
            # 2. 逐个测试activities
            for i, activity in enumerate(exported_activities):
                if result["successful_activities"] >= 3:  # 最多测试3个
                    break
                
                logger.info(f"🎯 测试Activity {i+1}: {activity or 'default'}")
                
                activity_result = {
                    "activity_name": activity or "default_launcher",
                    "launch_success": False,
                    "consent_handled": False,
                    "interactions": [],
                    "duration": 0
                }
                
                start_time = time.time()
                
                try:
                    # 停止应用
                    await self._stop_app(emulator, package_name)
                    await asyncio.sleep(2)
                    
                    # 启动指定activity或默认启动
                    if activity:
                        launch_success = await self._launch_specific_activity(emulator, package_name, activity)
                    else:
                        launch_success = await self._launch_app(package_name, emulator)
                    
                    if launch_success:
                        activity_result["launch_success"] = True
                        result["successful_activities"] += 1
                        
                        # 等待应用启动
                        await asyncio.sleep(3)
                        
                        # 自动处理同意按钮
                        logger.info("🤖 自动检测并处理同意按钮...")
                        consent_handled = self.auto_click_consent_buttons(emulator.device_id)
                        activity_result["consent_handled"] = consent_handled
                        
                        if consent_handled:
                            logger.info("✅ 同意按钮处理完成")
                        
                        # 等待UI稳定
                        await asyncio.sleep(2)
                        
                        # 执行基本交互
                        interactions = await self._perform_activity_interactions(emulator)
                        activity_result["interactions"] = interactions
                        
                        activity_result["duration"] = time.time() - start_time
                        logger.info(f"✅ Activity {activity or 'default'} 测试完成")
                        
                    else:
                        logger.warning(f"❌ Activity {activity or 'default'} 启动失败")
                        
                except Exception as e:
                    logger.error(f"❌ 测试Activity {activity or 'default'} 时出现异常: {e}")
                
                result["activities_tested"].append(activity_result)
                
                # 短暂休息
                await asyncio.sleep(1)
            
            logger.info(f"🏁 多activities测试完成，成功测试了 {result['successful_activities']} 个activities")
            
            # 等待网络捕获完成
            try:
                logger.info("⏳ 等待网络流量捕获完成...")
                network_requests = await capture_task
                result["network_requests"] = network_requests
                
                # 提取URL列表
                captured_urls = []
                for req in network_requests:
                    if hasattr(req, 'url') and req.url:
                        captured_urls.append(req.url)
                result["captured_urls"] = captured_urls
                
                logger.info(f"📊 网络捕获完成: {len(network_requests)} 个请求, {len(captured_urls)} 个URL")
                
            except Exception as e:
                logger.error(f"网络捕获异常: {e}")
                result["network_error"] = str(e)
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 多activities测试出现异常: {e}")
            result["error"] = str(e)
            
            # 即使出错也要尝试获取网络数据
            try:
                network_requests = await capture_task
                result["network_requests"] = network_requests
            except:
                pass
                
            return result

    def _get_exported_activities(self, apk_path: Optional[str]) -> List[str]:
        """从APK中获取exported activities"""
        try:
            # 如果没有APK路径，返回空列表，使用默认启动
            if not apk_path:
                logger.info("无APK路径，跳过Activity解析")
                return []
                
            # 使用aapt获取manifest信息
            result = subprocess.run([
                self.tools.aapt_path, 'dump', 'xmltree', apk_path, 'AndroidManifest.xml'
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode != 0:
                logger.error(f"aapt执行失败: {result.stderr}")
                return []
            
            # 解析exported activities
            exported_activities = []
            lines = result.stdout.split('\n')
            
            current_activity = None
            is_exported = False
            
            for line in lines:
                # 查找activity定义
                if 'android:name' in line and 'activity' in line:
                    # 提取activity名称
                    match = re.search(r'android:name.*?"([^"]+)"', line)
                    if match:
                        current_activity = match.group(1)
                        is_exported = False
                
                # 查找exported属性
                if current_activity and 'android:exported' in line:
                    if '0xffffffff' in line or 'true' in line.lower():
                        is_exported = True
                
                # 如果找到intent-filter，通常意味着activity是exported的
                if current_activity and 'intent-filter' in line:
                    is_exported = True
                
                # 当遇到新的activity或文件结束时，保存当前activity
                if current_activity and (line.strip().startswith('E: activity') or 
                                       (is_exported and ('E:' in line and 'activity' not in line))):
                    if is_exported:
                        # 确保activity名称是完整的
                        if current_activity.startswith('.'):
                            # 如果以.开头，需要从APK中获取包名
                            package_name = self._get_package_name_from_apk(apk_path)
                            if package_name:
                                current_activity = package_name + current_activity
                        
                        if current_activity not in exported_activities:
                            exported_activities.append(current_activity)
                            logger.info(f"✅ 添加可测试Activity: {current_activity}")
                    
                    current_activity = None
                    is_exported = False
            
            logger.info(f"发现 {len(exported_activities)} 个exported activities")
            return exported_activities[:5]  # 限制最多5个
            
        except Exception as e:
            logger.error(f"解析APK activities失败: {e}")
            return []

    def _get_package_name_from_apk(self, apk_path: str) -> Optional[str]:
        """从APK获取包名"""
        try:
            result = subprocess.run([
                self.tools.aapt_path, 'dump', 'badging', apk_path
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                match = re.search(r"package: name='([^']+)'", result.stdout)
                if match:
                    return match.group(1)
        except Exception as e:
            logger.debug(f"获取包名失败: {e}")
        return None

    async def _launch_specific_activity(self, emulator: EmulatorInstance, 
                                       package_name: str, activity: str) -> bool:
        """启动指定的activity，带有智能参数处理"""
        try:
            # 基础启动命令
            cmd = [
                self.tools.adb_path, '-s', emulator.device_id, 'shell', 
                'am', 'start', '-n', f'{package_name}/{activity}'
            ]
            
            # 为特定Activity添加必要的参数
            if 'OrderEntrustActivity' in activity:
                # OrderEntrustActivity需要复杂的序列化对象，直接启动容易崩溃
                # 改为启动主Activity然后导航到此Activity
                logger.warning(f"⚠️ {activity} 需要复杂参数，改为通过主Activity导航")
                return await self._navigate_to_activity_via_main(emulator, package_name, activity)
                
            elif 'AccountWithdrawActivity' in activity:
                # 为提现Activity提供基础参数
                cmd.extend([
                    '--es', 'accountType', 'normal',
                    '--ed', 'balance', '0.0'  # 余额参数
                ])
                logger.info(f"🔧 为AccountWithdrawActivity添加必要参数")
                
            elif 'RedPacketMainActivity' in activity:
                # 为红包Activity提供参数
                cmd.extend([
                    '--es', 'source', 'test',
                    '--ez', 'hasRedPacket', 'false'
                ])
                logger.info(f"🔧 为RedPacketMainActivity添加必要参数")
            
            # 通用参数，帮助Activity更稳定启动
            cmd.extend([
                '--activity-clear-top',  # 清除栈顶
                '--activity-single-top'  # 单例模式
            ])
            
            logger.info(f"🚀 启动Activity: {activity}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
            
            if result.returncode == 0:
                # 等待Activity启动
                await asyncio.sleep(2)
                
                # 检查是否有崩溃
                crash_check = subprocess.run([
                    self.tools.adb_path, '-s', emulator.device_id, 'shell',
                    'logcat', '-d', '-t', '10'
                ], capture_output=True, text=True, timeout=5)
                
                if 'FATAL EXCEPTION' in crash_check.stdout or 'AndroidRuntime' in crash_check.stdout:
                    logger.warning(f"⚠️ Activity {activity} 启动后发生崩溃")
                    # 尝试恢复应用
                    await self._recover_from_crash(emulator, package_name)
                    return False
                else:
                    logger.info(f"✅ Activity {activity} 启动成功")
                    return True
            else:
                logger.warning(f"❌ Activity {activity} 启动失败: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"启动activity时出错: {e}")
            return False

    async def _recover_from_crash(self, emulator: EmulatorInstance, package_name: str):
        """从应用崩溃中恢复"""
        try:
            logger.info("🔄 尝试从崩溃中恢复...")
            
            # 1. 强制停止应用
            await self._stop_app(emulator, package_name)
            await asyncio.sleep(1)
            
            # 2. 清理崩溃对话框
            subprocess.run([
                self.tools.adb_path, '-s', emulator.device_id, 'shell',
                'input', 'keyevent', 'KEYCODE_BACK'
            ], capture_output=True, timeout=5)
            
            await asyncio.sleep(1)
            
            # 3. 点击"Open app again"按钮（如果存在）
            subprocess.run([
                self.tools.adb_path, '-s', emulator.device_id, 'shell',
                'input', 'tap', '318', '804'  # 根据截图的按钮位置
            ], capture_output=True, timeout=5)
            
            await asyncio.sleep(2)
            logger.info("✅ 崩溃恢复完成")
            
        except Exception as e:
            logger.error(f"崩溃恢复失败: {e}")

    async def _stop_app(self, emulator: EmulatorInstance, package_name: str) -> bool:
        """停止应用"""
        try:
            cmd = [
                self.tools.adb_path, '-s', emulator.device_id, 'shell',
                'am', 'force-stop', package_name
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            return result.returncode == 0
        except Exception as e:
            logger.error(f"停止应用失败: {e}")
            return False

    async def _launch_app(self, package_name: str, emulator: EmulatorInstance) -> bool:
        """启动应用的主Activity"""
        try:
            cmd = [
                self.tools.adb_path, '-s', emulator.device_id, 'shell',
                'monkey', '-p', package_name, '-c', 'android.intent.category.LAUNCHER', '1'
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
            return result.returncode == 0
        except Exception as e:
            logger.error(f"启动应用失败: {e}")
            return False

    async def _navigate_to_activity_via_main(self, emulator: EmulatorInstance, 
                                           package_name: str, target_activity: str) -> bool:
        """通过主Activity导航到目标Activity"""
        try:
            logger.info(f"🔄 通过主Activity导航到 {target_activity}")
            
            # 1. 启动主Activity
            main_launch_success = await self._launch_app(package_name, emulator)
            if not main_launch_success:
                logger.warning("❌ 主Activity启动失败")
                return False
            
            await asyncio.sleep(3)
            
            # 2. 尝试通过UI导航到目标Activity
            # 这里可以添加更复杂的导航逻辑，比如点击特定按钮
            logger.info("🤖 尝试通过UI导航...")
            
            # 简单的UI交互，尝试触发目标Activity
            for i in range(3):
                # 点击不同位置，可能触发导航
                positions = [(400, 800), (600, 1200), (300, 600)]
                x, y = positions[i]
                
                subprocess.run([
                    self.tools.adb_path, '-s', emulator.device_id, 'shell',
                    'input', 'tap', str(x), str(y)
                ], capture_output=True, timeout=5)
                
                await asyncio.sleep(2)
                
                # 检查是否成功导航到目标Activity
                current_activity = await self._get_current_activity(emulator)
                if target_activity.split('.')[-1] in current_activity:
                    logger.info(f"✅ 成功导航到 {target_activity}")
                    return True
            
            logger.warning(f"⚠️ 无法通过UI导航到 {target_activity}，但主Activity已启动")
            return True  # 至少主Activity启动了，也算部分成功
            
        except Exception as e:
            logger.error(f"导航到Activity失败: {e}")
            return False

    async def _get_current_activity(self, emulator: EmulatorInstance) -> str:
        """获取当前Activity"""
        try:
            result = subprocess.run([
                self.tools.adb_path, '-s', emulator.device_id, 'shell',
                'dumpsys', 'activity', 'activities'
            ], capture_output=True, text=True, timeout=10)
            
            # 解析当前Activity
            lines = result.stdout.split('\n')
            for line in lines:
                if 'mResumedActivity' in line or 'mFocusedActivity' in line:
                    return line.strip()
            
            return "unknown"
        except Exception as e:
            logger.error(f"获取当前Activity失败: {e}")
            return "unknown"

    async def _perform_activity_interactions(self, emulator: EmulatorInstance) -> List[Dict[str, Any]]:
        """为单个activity执行交互操作"""
        interactions = []
        
        # 定义一系列基本交互
        actions = [
            ("点击屏幕中央", lambda: subprocess.run([
                self.tools.adb_path, '-s', emulator.device_id, 'shell', 
                'input', 'tap', '500', '1000'
            ], capture_output=True, timeout=5)),
            
            ("向下滑动", lambda: subprocess.run([
                self.tools.adb_path, '-s', emulator.device_id, 'shell', 
                'input', 'swipe', '500', '800', '500', '1200'
            ], capture_output=True, timeout=5)),
            
            ("点击右上角", lambda: subprocess.run([
                self.tools.adb_path, '-s', emulator.device_id, 'shell', 
                'input', 'tap', '800', '300'
            ], capture_output=True, timeout=5)),
            
            ("向左滑动", lambda: subprocess.run([
                self.tools.adb_path, '-s', emulator.device_id, 'shell', 
                'input', 'swipe', '800', '1000', '200', '1000'
            ], capture_output=True, timeout=5))
        ]
        
        for action_name, action_func in actions:
            try:
                action_func()
                interactions.append({
                    "action": action_name,
                    "success": True,
                    "timestamp": time.time()
                })
                logger.info(f"  ✅ {action_name} 执行成功")
                await asyncio.sleep(2)  # 等待响应
                
            except Exception as e:
                interactions.append({
                    "action": action_name,
                    "success": False,
                    "error": str(e)
                })
                logger.warning(f"  ⚠️ {action_name} 执行失败: {e}")
        
        return interactions
