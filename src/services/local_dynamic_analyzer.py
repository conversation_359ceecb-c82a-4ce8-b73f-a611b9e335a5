"""
本地动态分析器
使用本地Android模拟器进行APK动态分析
"""
import asyncio
import os
import time
import logging
from pathlib import Path
from typing import List, Optional, Dict, Any
import json

from .local_emulator_manager import LocalEmulatorManager, EmulatorInstance, get_emulator_manager
from .local_ui_automator import LocalUIAutomator
from .static_analyzer import AnalysisTools
from src.models.schemas import DynamicAnalysisResult
from .dynamic_analyzer import NetworkRequest  # 使用现有的NetworkRequest定义
from dataclasses import dataclass


@dataclass
class RuntimeInfo:
    """运行时信息"""
    device_model: str
    android_version: str
    api_level: int
    architecture: str
    memory_info: Dict[str, Any]
    cpu_info: Dict[str, Any]

logger = logging.getLogger(__name__)


class LocalDynamicAnalyzer:
    """本地动态分析器"""
    
    def __init__(self,
                 emulator_manager: Optional[LocalEmulatorManager] = None,
                 tools: Optional[AnalysisTools] = None):
        """
        初始化本地动态分析器

        Args:
            emulator_manager: 模拟器管理器实例
            tools: 分析工具配置
        """
        self.emulator_manager = emulator_manager or get_emulator_manager()
        self.tools = tools or AnalysisTools()
        self.ui_automator = LocalUIAutomator(tools)

        # 分析配置
        self.default_timeout = 300
        self.interaction_timeout = 180
        self.network_capture_timeout = 60

        # 确保mitm日志目录存在
        self.mitm_logs_dir = Path("mitm-logs")
        self.mitm_logs_dir.mkdir(exist_ok=True)

        logger.info("LocalDynamicAnalyzer initialized")
    
    async def analyze(self, apk_path: str, timeout: int = None) -> DynamicAnalysisResult:
        """
        执行完整的本地动态分析
        
        Args:
            apk_path: APK文件路径
            timeout: 分析超时时间（秒）
            
        Returns:
            DynamicAnalysisResult: 分析结果
        """
        timeout = timeout or self.default_timeout
        logger.info(f"Starting local dynamic analysis of {apk_path}")
        start_time = time.time()
        
        emulator_instance = None
        
        try:
            # 1. 获取或创建模拟器实例
            emulator_instance = await self.emulator_manager.get_or_create_instance()
            if not emulator_instance:
                raise Exception("Failed to get emulator instance")
            
            logger.info(f"Using emulator instance: {emulator_instance.name}")
            
            # 2. 提取APK包名
            package_name = await self._extract_package_name(apk_path)
            if not package_name:
                raise Exception("Failed to extract package name from APK")
            
            # 3. 安装APK
            if not await self._install_apk(apk_path, emulator_instance):
                raise Exception("Failed to install APK")
            
            try:
                # 4. 启动网络流量捕获
                network_requests = []
                capture_task = None
                
                try:
                    capture_task = asyncio.create_task(
                        self._capture_network_traffic(emulator_instance, self.network_capture_timeout)
                    )
                    
                    # 5. 启动应用并进行UI交互
                    if await self._launch_app(package_name, emulator_instance):
                        # 使用智能UI自动化器进行交互
                        ui_actions = await self.ui_automator.explore_ui(
                            emulator_instance,
                            package_name,
                            min(timeout, self.interaction_timeout)
                        )
                        logger.info(f"Executed {len(ui_actions)} UI interactions")
                    
                    # 6. 等待网络捕获完成
                    if capture_task:
                        network_requests = await capture_task
                    
                except Exception as e:
                    logger.error(f"Error during analysis: {e}")
                    if capture_task and not capture_task.done():
                        capture_task.cancel()
                        try:
                            await capture_task
                        except asyncio.CancelledError:
                            pass
                
                # 7. 处理网络请求
                filtered_requests = self._filter_requests(network_requests)
                dynamic_urls = self._convert_to_url_schemas(filtered_requests)
                
                # 8. 获取运行时信息
                runtime_info = await self._get_runtime_info(emulator_instance)
                
                duration = time.time() - start_time
                
                result = DynamicAnalysisResult(
                    dynamic_urls=dynamic_urls,
                    duration=duration,
                    android_version=await self._get_android_version(emulator_instance),
                    network_requests=filtered_requests,
                    runtime_info=runtime_info
                )
                
                logger.info(f"Local dynamic analysis completed in {duration:.2f}s")
                logger.info(f"Found {len(dynamic_urls)} dynamic URLs")
                
                return result
                
            finally:
                # 清理：卸载APK
                try:
                    await self._uninstall_apk(package_name, emulator_instance)
                except Exception as e:
                    logger.warning(f"Failed to uninstall APK: {e}")
        
        except Exception as e:
            logger.error(f"Local dynamic analysis failed: {e}")
            duration = time.time() - start_time
            
            # 返回失败结果
            return DynamicAnalysisResult(
                dynamic_urls=[],
                duration=duration,
                android_version="unknown",
                network_requests=[],
                runtime_info=None,
                error=str(e)
            )
    
    async def _extract_package_name(self, apk_path: str) -> Optional[str]:
        """从APK提取包名"""
        try:
            process = await asyncio.create_subprocess_exec(
                self.tools.aapt, 'dump', 'badging', apk_path,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                logger.error(f"Failed to extract package name: {stderr.decode()}")
                return None
            
            # 解析输出获取包名
            output = stdout.decode()
            for line in output.split('\n'):
                if line.startswith('package:'):
                    # 提取 name='...' 部分
                    import re
                    match = re.search(r"name='([^']+)'", line)
                    if match:
                        package_name = match.group(1)
                        logger.info(f"Extracted package name: {package_name}")
                        return package_name
            
            return None
            
        except Exception as e:
            logger.error(f"Error extracting package name: {e}")
            return None
    
    async def _install_apk(self, apk_path: str, emulator: EmulatorInstance) -> bool:
        """在模拟器上安装APK"""
        try:
            if not emulator.adb_device_id:
                logger.error("Emulator device ID not available")
                return False
            
            logger.info(f"Installing APK on {emulator.adb_device_id}")
            
            process = await asyncio.create_subprocess_exec(
                self.tools.adb, '-s', emulator.adb_device_id, 'install', '-r', apk_path,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                logger.error(f"APK installation failed: {stderr.decode()}")
                return False
            
            output = stdout.decode()
            success = "Success" in output or "INSTALL_SUCCEEDED" in output
            
            if success:
                logger.info("APK installed successfully")
            else:
                logger.error(f"APK installation failed: {output}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error installing APK: {e}")
            return False
    
    async def _uninstall_apk(self, package_name: str, emulator: EmulatorInstance) -> bool:
        """从模拟器卸载APK"""
        try:
            if not emulator.adb_device_id:
                return False
            
            logger.info(f"Uninstalling {package_name} from {emulator.adb_device_id}")
            
            process = await asyncio.create_subprocess_exec(
                self.tools.adb, '-s', emulator.adb_device_id, 'uninstall', package_name,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                logger.warning(f"Failed to uninstall APK: {stderr.decode()}")
                return False
            
            output = stdout.decode()
            success = "Success" in output or "UNINSTALL_SUCCEEDED" in output
            
            if success:
                logger.info(f"Successfully uninstalled {package_name}")
            
            return success
            
        except Exception as e:
            logger.warning(f"Error uninstalling APK: {e}")
            return False
    
    async def _launch_app(self, package_name: str, emulator: EmulatorInstance) -> bool:
        """启动应用"""
        try:
            if not emulator.adb_device_id:
                return False
            
            logger.info(f"Launching app {package_name}")
            
            # 使用monkey启动应用
            process = await asyncio.create_subprocess_exec(
                self.tools.adb, '-s', emulator.adb_device_id, 'shell', 
                'monkey', '-p', package_name, '1',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                logger.error(f"Failed to launch app: {stderr.decode()}")
                return False
            
            # 等待应用启动
            await asyncio.sleep(3)
            
            # 验证应用是否在运行
            return await self._is_app_running(package_name, emulator)
            
        except Exception as e:
            logger.error(f"Error launching app: {e}")
            return False
    
    async def _is_app_running(self, package_name: str, emulator: EmulatorInstance) -> bool:
        """检查应用是否在运行"""
        try:
            if not emulator.adb_device_id:
                return False
            
            process = await asyncio.create_subprocess_exec(
                self.tools.adb, '-s', emulator.adb_device_id, 'shell',
                'dumpsys', 'activity', 'activities',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, _ = await process.communicate()
            
            if process.returncode == 0:
                output = stdout.decode()
                return package_name in output and "mCurrentFocus" in output
            
            return False
            
        except Exception as e:
            logger.debug(f"Error checking app status: {e}")
            return False

    # UI交互现在由LocalUIAutomator处理，移除了旧的交互方法

    async def _capture_network_traffic(self, emulator: EmulatorInstance, timeout: int) -> List[NetworkRequest]:
        """捕获网络流量"""
        logger.info(f"Starting network traffic capture for {timeout}s")

        captured_requests = []
        mitm_process = None
        session_id = f"local_session_{int(time.time())}"

        try:
            # 1. 启动mitmproxy进程
            mitm_process = await self._start_mitmproxy_process(session_id)

            if mitm_process:
                logger.info("mitmproxy started successfully")

                # 2. 等待mitmproxy启动
                await asyncio.sleep(3)

                # 3. 配置设备代理
                proxy_configured = await self._configure_device_proxy(emulator)
                if proxy_configured:
                    logger.info("Device proxy configured")
                else:
                    logger.warning("Failed to configure device proxy")

                # 4. 等待网络流量捕获
                logger.info(f"Capturing network traffic for {timeout} seconds...")
                await asyncio.sleep(timeout)

                # 5. 获取捕获的请求
                captured_requests = await self._get_captured_requests_from_session(session_id)
                logger.info(f"Captured {len(captured_requests)} network requests")

            else:
                logger.warning("Failed to start mitmproxy, using fallback capture")
                # 使用备用方法捕获网络流量
                captured_requests = await self._fallback_network_capture(emulator, timeout)

        except Exception as e:
            logger.error(f"Network capture error: {e}")

        finally:
            # 清理mitmproxy进程
            if mitm_process:
                try:
                    mitm_process.terminate()
                    await asyncio.wait_for(mitm_process.wait(), timeout=10)
                except asyncio.TimeoutError:
                    mitm_process.kill()
                    await mitm_process.wait()
                except Exception as e:
                    logger.error(f"Error stopping mitmproxy: {e}")

            # 清理设备代理配置
            try:
                await self._clear_device_proxy(emulator)
            except Exception as e:
                logger.debug(f"Error clearing device proxy: {e}")

        return captured_requests

    async def _start_mitmproxy_process(self, session_id: str) -> Optional[asyncio.subprocess.Process]:
        """启动mitmproxy进程"""
        try:
            # 检查mitmproxy是否可用
            which_process = await asyncio.create_subprocess_exec(
                'which', 'mitmdump',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            await which_process.communicate()

            if which_process.returncode != 0:
                logger.warning("mitmdump not found, network capture will be limited")
                return None

            # 使用本地优化的捕获脚本
            mitm_script = Path(__file__).parent.parent.parent / "mitm-scripts" / "local_capture.py"

            if not mitm_script.exists():
                logger.warning(f"Local MITM script not found: {mitm_script}")
                # 回退到原始脚本
                mitm_script = Path(__file__).parent.parent.parent / "mitm-scripts" / "capture.py"
                if not mitm_script.exists():
                    logger.error("No MITM script found")
                    return None

            # 设置环境变量
            env = os.environ.copy()
            env['MITM_SESSION_ID'] = session_id
            env['MITM_OUTPUT_DIR'] = str(self.mitm_logs_dir)

            cmd = [
                'mitmdump',
                '-s', str(mitm_script),
                '--listen-port', '8080',
                '--set', 'confdir=~/.mitmproxy',
                '--set', 'stream_large_bodies=1',  # 流式处理大文件
                '--quiet'  # 减少日志输出
            ]

            process = await asyncio.create_subprocess_exec(
                *cmd,
                env=env,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            logger.info(f"Started mitmproxy with PID {process.pid}")
            return process

        except Exception as e:
            logger.error(f"Failed to start mitmproxy: {e}")
            return None

    async def _configure_device_proxy(self, emulator: EmulatorInstance) -> bool:
        """配置设备代理"""
        try:
            if not emulator.adb_device_id:
                return False

            # 设置HTTP代理
            commands = [
                ('shell', 'settings', 'put', 'global', 'http_proxy', 'localhost:8080'),
                ('shell', 'settings', 'put', 'global', 'https_proxy', 'localhost:8080'),
            ]

            for cmd in commands:
                process = await asyncio.create_subprocess_exec(
                    self.tools.adb, '-s', emulator.adb_device_id, *cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                await process.communicate()

            logger.debug("Device proxy configured")
            return True

        except Exception as e:
            logger.error(f"Failed to configure device proxy: {e}")
            return False

    async def _clear_device_proxy(self, emulator: EmulatorInstance):
        """清除设备代理配置"""
        try:
            if not emulator.adb_device_id:
                return

            commands = [
                ('shell', 'settings', 'delete', 'global', 'http_proxy'),
                ('shell', 'settings', 'delete', 'global', 'https_proxy'),
            ]

            for cmd in commands:
                process = await asyncio.create_subprocess_exec(
                    self.tools.adb, '-s', emulator.adb_device_id, *cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                await process.communicate()

        except Exception as e:
            logger.debug(f"Error clearing device proxy: {e}")

    async def _get_captured_requests_from_session(self, session_id: str) -> List[NetworkRequest]:
        """从会话获取捕获的请求"""
        try:
            # 从文件读取捕获的请求
            session_file = self.mitm_logs_dir / f"session_{session_id}.json"

            if not session_file.exists():
                logger.warning(f"Session file not found: {session_file}")
                return []

            with open(session_file, 'r') as f:
                requests_data = json.load(f)

            # 转换为NetworkRequest对象
            network_requests = []
            for req_data in requests_data:
                try:
                    network_request = NetworkRequest(
                        url=req_data.get('url', ''),
                        method=req_data.get('method', 'GET'),
                        headers=req_data.get('headers', {}),
                        timestamp=req_data.get('timestamp', 0),
                        response_code=req_data.get('status_code', 0),
                        content_type=req_data.get('content_type', ''),
                        size=req_data.get('content_length', 0)
                    )
                    network_requests.append(network_request)
                except Exception as e:
                    logger.debug(f"Error parsing request data: {e}")

            return network_requests

        except Exception as e:
            logger.error(f"Error reading captured requests: {e}")
            return []

    async def _fallback_network_capture(self, emulator: EmulatorInstance, timeout: int) -> List[NetworkRequest]:
        """备用网络捕获方法"""
        try:
            if not emulator.adb_device_id:
                return []

            logger.info("Using fallback network capture method")

            # 使用tcpdump或其他方法捕获网络流量
            # 这里简化实现，实际可以使用更复杂的方法

            # 启动网络监控
            process = await asyncio.create_subprocess_exec(
                self.tools.adb, '-s', emulator.adb_device_id, 'shell',
                'netstat', '-tuln',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, _ = await process.communicate()

            # 简单解析网络连接（这里只是示例）
            network_requests = []
            if process.returncode == 0:
                output = stdout.decode()
                # 这里可以添加更复杂的网络流量解析逻辑
                lines = output.split('\n')
                logger.debug(f"Network connections: {len(lines)} lines")

            return network_requests

        except Exception as e:
            logger.error(f"Fallback network capture failed: {e}")
            return []

    def _filter_requests(self, requests: List[NetworkRequest]) -> List[NetworkRequest]:
        """过滤网络请求"""
        if not requests:
            return []

        filtered = []
        system_domains = {
            'google.com', 'googleapis.com', 'gstatic.com', 'googleusercontent.com',
            'doubleclick.net', 'googlesyndication.com', 'googleadservices.com',
            'android.com', 'mozilla.org', 'firefox.com', 'gvt1.com',
            'crashlytics.com', 'fabric.io', 'firebase.com'
        }

        for request in requests:
            try:
                # 过滤系统域名
                url_lower = request.url.lower()
                is_system = any(domain in url_lower for domain in system_domains)

                if not is_system:
                    # 过滤广告和分析域名
                    ad_patterns = ['ads', 'analytics', 'tracking', 'telemetry', 'crash', 'metrics']
                    is_ad = any(pattern in url_lower for pattern in ad_patterns)

                    if not is_ad:
                        filtered.append(request)

            except Exception as e:
                logger.debug(f"Error filtering request: {e}")

        logger.info(f"Filtered {len(requests)} -> {len(filtered)} requests")
        return filtered

    def _convert_to_url_schemas(self, requests: List[NetworkRequest]) -> List[Dict[str, Any]]:
        """转换网络请求为URL模式"""
        url_schemas = []

        for request in requests:
            try:
                from urllib.parse import urlparse
                parsed = urlparse(request.url)

                schema = {
                    'url': request.url,
                    'domain': parsed.netloc,
                    'path': parsed.path,
                    'method': request.method,
                    'scheme': parsed.scheme,
                    'query_params': parsed.query,
                    'timestamp': request.timestamp,
                    'response_code': request.response_code,
                    'content_type': request.content_type,
                    'size': request.size
                }

                url_schemas.append(schema)

            except Exception as e:
                logger.debug(f"Error converting request to schema: {e}")

        return url_schemas

    async def _get_runtime_info(self, emulator: EmulatorInstance) -> Optional[RuntimeInfo]:
        """获取运行时信息"""
        try:
            if not emulator.adb_device_id:
                return None

            # 获取设备信息
            device_info = await self._get_device_info(emulator)

            return RuntimeInfo(
                device_model=device_info.get('model', 'Unknown'),
                android_version=device_info.get('version', 'Unknown'),
                api_level=device_info.get('api_level', 0),
                architecture=device_info.get('architecture', 'Unknown'),
                memory_info=device_info.get('memory', {}),
                cpu_info=device_info.get('cpu', {})
            )

        except Exception as e:
            logger.error(f"Error getting runtime info: {e}")
            return None

    async def _get_device_info(self, emulator: EmulatorInstance) -> Dict[str, Any]:
        """获取设备信息"""
        try:
            if not emulator.adb_device_id:
                return {}

            info = {}

            # 获取各种设备属性
            properties = {
                'model': 'ro.product.model',
                'version': 'ro.build.version.release',
                'api_level': 'ro.build.version.sdk',
                'architecture': 'ro.product.cpu.abi'
            }

            for key, prop in properties.items():
                try:
                    process = await asyncio.create_subprocess_exec(
                        self.tools.adb, '-s', emulator.adb_device_id, 'shell',
                        'getprop', prop,
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.PIPE
                    )
                    stdout, _ = await process.communicate()

                    if process.returncode == 0:
                        value = stdout.decode().strip()
                        if key == 'api_level':
                            try:
                                info[key] = int(value)
                            except ValueError:
                                info[key] = 0
                        else:
                            info[key] = value

                except Exception as e:
                    logger.debug(f"Error getting {key}: {e}")

            return info

        except Exception as e:
            logger.error(f"Error getting device info: {e}")
            return {}

    async def _get_android_version(self, emulator: EmulatorInstance) -> str:
        """获取Android版本"""
        try:
            device_info = await self._get_device_info(emulator)
            return device_info.get('version', 'Unknown')
        except Exception as e:
            logger.error(f"Error getting Android version: {e}")
            return "Unknown"
