"""
Enhanced Dynamic APK Analysis Module
支持多种网络捕获方式和智能UI遍历
"""
import asyncio
import json
import os
import re
import subprocess
import tempfile
import time
from pathlib import Path
from dataclasses import dataclass
from datetime import datetime
from typing import List, Optional, Dict, Any
import logging

from src.models.schemas import DynamicAnalysisResult, URLInfoSchema

logger = logging.getLogger(__name__)


@dataclass
class AnalysisSession:
    """动态分析会话"""
    session_id: str
    package_name: str
    start_time: datetime
    emulator_device: Optional[str] = None
    proxy_enabled: bool = False
    ui_exploration_enabled: bool = True
    network_monitoring_enabled: bool = True


@dataclass
class NetworkRequest:
    """Network request captured during dynamic analysis"""
    url: str
    method: str
    timestamp: str
    host: Optional[str] = None
    response_code: Optional[int] = None
    request_headers: Optional[Dict[str, Any]] = None
    response_headers: Optional[Dict[str, Any]] = None
    content_type: Optional[str] = None
    content_length: Optional[int] = None
    response_time_ms: Optional[int] = None


@dataclass
class AnalysisTools:
    """Dynamic analysis tools configuration"""
    adb: str = "adb"
    aapt: str = "/Users/<USER>/Desktop/project/apk_detect/android-sdk/build-tools/30.0.3/aapt"


class DynamicAnalyzer:
    """Dynamic APK analyzer using Android emulator and network proxy"""
    
    def __init__(self, tools: Optional[AnalysisTools] = None):
        self.tools = tools or AnalysisTools()
        self.subprocess_pipe = asyncio.subprocess.PIPE
        self.proxy_port = 8080
        self.captured_requests: List[NetworkRequest] = []
        self.current_session: Optional[AnalysisSession] = None
        
        # Enhanced system domains to filter out
        self.system_domains = {
            'google.com', 'googleapis.com', 'gstatic.com', 'googleusercontent.com',
            'doubleclick.net', 'googlesyndication.com', 'googleadservices.com',
            'android.com', 'mozilla.org', 'firefox.com', 'gvt1.com',
            'crashlytics.com', 'fabric.io', 'firebase.com', 'firebaseapp.com'
        }
        
        # UI exploration patterns (prioritized by likelihood of triggering network requests)
        self.ui_exploration_patterns = [
            # High priority - likely to trigger network requests
            {'text_patterns': ['login', '登录', 'sign in', 'log in'], 'priority': 10},
            {'text_patterns': ['submit', '提交', 'send', '发送'], 'priority': 9},
            {'text_patterns': ['refresh', '刷新', 'reload', '重新加载'], 'priority': 8},
            {'text_patterns': ['search', '搜索', 'find', '查找'], 'priority': 7},
            {'text_patterns': ['load more', '加载更多', 'more', '更多'], 'priority': 6},
            
            # Medium priority - may trigger requests
            {'text_patterns': ['menu', '菜单', 'settings', '设置'], 'priority': 5},
            {'text_patterns': ['profile', '个人资料', 'account', '账户'], 'priority': 4},
            {'text_patterns': ['news', '新闻', 'feed', '动态'], 'priority': 3},
            
            # Low priority - less likely but still useful
            {'text_patterns': ['button', 'btn', 'tab', '选项'], 'priority': 2},
            {'class_patterns': ['Button', 'ImageButton', 'TextView'], 'priority': 1},
        ]
    
    async def create_analysis_session(self, package_name: str) -> AnalysisSession:
        """Create a new analysis session"""
        session_id = f"session_{int(time.time())}_{package_name}"
        session = AnalysisSession(
            session_id=session_id,
            package_name=package_name,
            start_time=datetime.now(),
            proxy_enabled=True,
            ui_exploration_enabled=True,
            network_monitoring_enabled=True
        )
        self.current_session = session
        logger.info(f"Created analysis session: {session_id}")
        return session
    
    async def analyze(self, apk_path: str, timeout: int = 300) -> DynamicAnalysisResult:
        """
        Perform complete dynamic analysis of APK
        
        Args:
            apk_path: Path to the APK file
            timeout: Analysis timeout in seconds
            
        Returns:
            DynamicAnalysisResult: Complete analysis result
        """
        logger.info(f"Starting dynamic analysis of {apk_path}")
        start_time = time.time()
        
        try:
            # Extract package name from APK
            package_name = await self._extract_package_name(apk_path)
            if not package_name:
                raise Exception("Failed to extract package name from APK")
            
            # Install APK on emulator
            if not await self._install_apk(apk_path):
                raise Exception("Failed to install APK on emulator")
            
            try:
                # Start network traffic capture
                network_requests = await self._capture_network_traffic(timeout)
                
                # Launch app and interact with UI
                if await self._launch_app(package_name):
                    await self._interact_with_ui(package_name, timeout=min(timeout, 180))
                
                # Wait for network requests to complete
                await asyncio.sleep(5)
                
                # Get final captured requests
                final_requests = await self._get_captured_requests()
                network_requests.extend(final_requests)
                
                # Filter and process requests
                filtered_requests = self._filter_requests(network_requests)
                dynamic_urls = self._convert_to_url_schemas(filtered_requests)
                
                duration = time.time() - start_time
                
                result = DynamicAnalysisResult(
                    dynamic_urls=dynamic_urls,
                    duration=duration,
                    android_version=await self._get_android_version()
                )
                
                logger.info(f"Dynamic analysis completed for {apk_path}")
                return result
                
            finally:
                # Clean up: uninstall APK
                await self._uninstall_apk(package_name)
                
        except Exception as e:
            logger.error(f"Failed to perform dynamic analysis for {apk_path}: {e}")
            raise Exception(f"Failed to perform dynamic analysis: {str(e)}")
    
    async def _extract_package_name(self, apk_path: str) -> Optional[str]:
        """Extract package name from APK using aapt"""
        try:
            process = await asyncio.create_subprocess_exec(
                self.tools.aapt, 'dump', 'badging', apk_path,
                stdout=self.subprocess_pipe,
                stderr=self.subprocess_pipe
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                logger.error(f"aapt failed: {stderr.decode()}")
                return None
            
            output = stdout.decode()
            match = re.search(r"package: name='([^']+)'", output)
            return match.group(1) if match else None
            
        except Exception as e:
            logger.error(f"Failed to extract package name: {e}")
            return None
    
    async def _install_apk(self, apk_path: str) -> bool:
        """Install APK on Android emulator"""
        try:
            logger.debug(f"Installing APK: {apk_path}")
            
            process = await asyncio.create_subprocess_exec(
                self.tools.adb, 'install', '-r', apk_path,
                stdout=self.subprocess_pipe,
                stderr=self.subprocess_pipe
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                logger.error(f"APK installation failed: {stderr.decode()}")
                return False
            
            output = stdout.decode()
            return "Success" in output
            
        except Exception as e:
            logger.error(f"Failed to install APK: {e}")
            return False
    
    async def _launch_app(self, package_name: str) -> bool:
        """Launch app on Android emulator"""
        try:
            logger.debug(f"Launching app: {package_name}")
            
            # Use monkey to launch the app
            process = await asyncio.create_subprocess_exec(
                self.tools.adb, 'shell', 'monkey', '-p', package_name, '1',
                stdout=self.subprocess_pipe,
                stderr=self.subprocess_pipe
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                logger.error(f"Failed to launch app: {stderr.decode()}")
                return False
            
            # Wait for app to start
            await asyncio.sleep(3)
            return True
            
        except Exception as e:
            logger.error(f"Failed to launch app: {e}")
            return False
    
    async def _interact_with_ui(self, package_name: str, timeout: int = 180):
        """Enhanced UI interaction to trigger network requests"""
        logger.debug(f"Starting enhanced UI interaction for {package_name}")
        
        start_time = time.time()
        interaction_count = 0
        max_interactions = 30
        visited_activities = set()
        
        try:
            while (time.time() - start_time) < timeout and interaction_count < max_interactions:
                # Get current activity
                current_activity = await self._get_current_activity()
                
                # Find prioritized clickable elements
                elements = await self._find_prioritized_elements()
                
                if not elements:
                    logger.debug("No clickable elements found, trying navigation")
                    await self._try_navigation_actions(package_name)
                    await asyncio.sleep(3)
                    continue
                
                # Interact with high-priority elements first
                for element in elements[:3]:  # Focus on top 3 elements
                    if (time.time() - start_time) >= timeout:
                        break
                    
                    success = await self._smart_interact_element(element)
                    if success:
                        interaction_count += 1
                        await asyncio.sleep(3)  # Wait for network requests
                        
                        # Handle potential dialogs
                        await self._handle_dialogs()
                
                # Try some exploration if we haven't seen this activity
                if current_activity not in visited_activities:
                    visited_activities.add(current_activity)
                    await self._explore_activity(package_name)
                
                # Periodic monkey actions for randomness
                if interaction_count % 5 == 0:
                    await self._perform_monkey_actions(package_name, 5)
                
                await asyncio.sleep(2)
                
        except Exception as e:
            logger.warning(f"Enhanced UI interaction error: {e}")
    
    async def _find_clickable_elements(self) -> List[Dict[str, Any]]:
        """Find clickable UI elements using uiautomator"""
        try:
            process = await asyncio.create_subprocess_exec(
                self.tools.adb, 'shell', 'uiautomator', 'dump', '/sdcard/ui.xml',
                stdout=self.subprocess_pipe,
                stderr=self.subprocess_pipe
            )
            
            await process.communicate()
            
            if process.returncode != 0:
                return []
            
            # Get the UI dump
            process = await asyncio.create_subprocess_exec(
                self.tools.adb, 'shell', 'cat', '/sdcard/ui.xml',
                stdout=self.subprocess_pipe,
                stderr=self.subprocess_pipe
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                return []
            
            # Parse clickable elements (simplified)
            xml_content = stdout.decode()
            elements = []
            
            # Find clickable elements using regex (simplified parsing)
            clickable_pattern = r'clickable="true"[^>]*bounds="\[(\d+),(\d+)\]\[(\d+),(\d+)\]"'
            matches = re.finditer(clickable_pattern, xml_content)
            
            for match in matches:
                x1, y1, x2, y2 = match.groups()
                center_x = (int(x1) + int(x2)) // 2
                center_y = (int(y1) + int(y2)) // 2
                
                elements.append({
                    'x': center_x,
                    'y': center_y,
                    'bounds': f"[{x1},{y1}][{x2},{y2}]"
                })
            
            return elements[:10]  # Limit to 10 elements
            
        except Exception as e:
            logger.debug(f"Failed to find clickable elements: {e}")
            return []
    
    async def _click_element(self, element: Dict[str, Any]) -> bool:
        """Click on a UI element"""
        try:
            x, y = element['x'], element['y']
            
            process = await asyncio.create_subprocess_exec(
                self.tools.adb, 'shell', 'input', 'tap', str(x), str(y),
                stdout=self.subprocess_pipe,
                stderr=self.subprocess_pipe
            )
            
            await process.communicate()
            return process.returncode == 0
            
        except Exception as e:
            logger.debug(f"Failed to click element: {e}")
            return False
    
    async def _perform_monkey_actions(self, package_name: str, count: int):
        """Perform random actions using monkey tool"""
        try:
            process = await asyncio.create_subprocess_exec(
                self.tools.adb, 'shell', 'monkey', '-p', package_name,
                '--throttle', '1000', str(count),
                stdout=self.subprocess_pipe,
                stderr=self.subprocess_pipe
            )
            
            await process.communicate()
            
        except Exception as e:
            logger.debug(f"Monkey actions failed: {e}")
    
    async def _capture_network_traffic(self, timeout: int) -> List[NetworkRequest]:
        """Capture network traffic using real mitmproxy integration"""
        logger.info(f"Starting real network traffic capture for {timeout}s")
        
        captured_requests = []
        mitm_process = None
        session_id = f"session_{int(time.time())}"
        
        try:
            # 1. 确保mitm日志目录存在
            mitm_log_dir = Path("mitm-logs")
            mitm_log_dir.mkdir(exist_ok=True)
            
            # 2. 启动mitmproxy进程
            mitm_process = await self._start_mitmproxy_process(session_id)
            
            if mitm_process:
                logger.info("mitmproxy started successfully")
                
                # 3. 等待mitmproxy启动
                await asyncio.sleep(3)
                
                # 4. 配置Android设备使用代理
                proxy_configured = await self._configure_device_proxy()
                if proxy_configured:
                    logger.info("Device proxy configured")
                else:
                    logger.warning("Failed to configure device proxy")
                
                # 5. 等待网络流量捕获
                logger.info(f"Capturing network traffic for {timeout} seconds...")
                await asyncio.sleep(timeout)
                
                # 6. 获取捕获的请求
                captured_requests = await self._get_captured_requests_from_session(session_id)
                logger.info(f"Captured {len(captured_requests)} network requests from mitmproxy")
            
            # 7. 备用方法：使用系统网络监控
            if not captured_requests:
                logger.info("Using fallback network monitoring")
                captured_requests = await self._monitor_network_connections(timeout)
            
            return captured_requests
            
        except Exception as e:
            logger.error(f"Network traffic capture error: {e}")
            # 使用备用网络监控方法
            return await self._monitor_network_connections(timeout)
            
        finally:
            # 8. 清理：停止mitmproxy和恢复网络设置
            if mitm_process:
                await self._stop_mitmproxy_process(mitm_process)
            await self._restore_network_settings()
    
    async def _get_mitm_captured_requests(self, session_id: str) -> List[NetworkRequest]:
        """Get captured network requests from mitmproxy"""
        try:
            captured_requests = []
            mitm_log_paths = [
                "/app/mitm-logs/captured_requests.json",
                "mitm-logs/captured_requests.json",
                f"mitm-logs/session_{session_id}.json"
            ]
            
            # Try different log paths
            for log_path in mitm_log_paths:
                if os.path.exists(log_path):
                    try:
                        with open(log_path, 'r') as f:
                            data = json.load(f)
                            for item in data:
                                captured_requests.append(NetworkRequest(
                                    url=item.get('url', ''),
                                    method=item.get('method', 'GET'),
                                    timestamp=item.get('timestamp', ''),
                                    host=item.get('host', ''),
                                    response_code=item.get('response_code', 0),
                                    request_headers=item.get('request_headers'),
                                    response_headers=item.get('response_headers'),
                                    content_type=item.get('content_type', ''),
                                    response_time_ms=item.get('response_time_ms', 0)
                                ))
                        break
                    except (json.JSONDecodeError, KeyError) as e:
                        logger.warning(f"Failed to parse mitm log {log_path}: {e}")
            
            logger.debug(f"Retrieved {len(captured_requests)} requests from mitmproxy logs")
            return captured_requests
            
        except Exception as e:
            logger.error(f"Failed to get mitm captured requests: {e}")
            return []

    async def _capture_with_tcpdump(self, timeout: int) -> List[NetworkRequest]:
        """Capture network traffic using tcpdump"""
        try:
            logger.debug("Starting tcpdump network capture")
            
            # Check if tcpdump is available
            tcpdump_check = await asyncio.create_subprocess_exec(
                "which", "tcpdump",
                stdout=self.subprocess_pipe,
                stderr=self.subprocess_pipe
            )
            await tcpdump_check.communicate()
            
            if tcpdump_check.returncode != 0:
                logger.debug("tcpdump not available, skipping")
                return []
            
            # Create temporary file for tcpdump output
            with tempfile.NamedTemporaryFile(mode='w+', suffix='.pcap', delete=False) as temp_file:
                pcap_file = temp_file.name
            
            try:
                # Start tcpdump process
                tcpdump_cmd = [
                    "tcpdump", "-i", "any", "-w", pcap_file,
                    "-G", str(min(timeout, 60)),  # Rotate every minute or timeout
                    "tcp", "port", "80", "or", "port", "443", "or", "port", "8080"
                ]
                
                process = await asyncio.create_subprocess_exec(
                    *tcpdump_cmd,
                    stdout=self.subprocess_pipe,
                    stderr=self.subprocess_pipe
                )
                
                # Let it run for a bit
                await asyncio.sleep(min(timeout, 30))
                
                # Stop tcpdump
                process.terminate()
                await process.wait()
                
                # Parse pcap file (simplified - would need proper pcap parsing library)
                requests = await self._parse_pcap_file(pcap_file)
                
                return requests
                
            finally:
                # Clean up pcap file
                if os.path.exists(pcap_file):
                    os.unlink(pcap_file)
            
        except Exception as e:
            logger.debug(f"tcpdump capture failed: {e}")
            return []

    async def _parse_pcap_file(self, pcap_file: str) -> List[NetworkRequest]:
        """Parse pcap file to extract HTTP requests (simplified)"""
        try:
            # This is a simplified implementation
            # In a real scenario, you'd use libraries like scapy or pyshark
            
            # For now, return empty list as pcap parsing requires additional libraries
            logger.debug("Pcap parsing not implemented (requires scapy/pyshark)")
            return []
            
        except Exception as e:
            logger.debug(f"Pcap parsing failed: {e}")
            return []

    async def _monitor_network_connections(self, timeout: Optional[int] = None) -> List[NetworkRequest]:
        """Monitor network connections using netstat. Timeout is unused but accepted for API compatibility."""
        try:
            logger.debug("Monitoring network connections")
            
            # Get current network connections
            process = await asyncio.create_subprocess_exec(
                "netstat", "-tn",
                stdout=self.subprocess_pipe,
                stderr=self.subprocess_pipe
            )
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                logger.debug("netstat failed, skipping connection monitoring")
                return []
            
            connections = []
            lines = stdout.decode().split('\n')
            
            for line in lines[2:]:  # Skip header lines
                if 'ESTABLISHED' in line:
                    parts = line.split()
                    if len(parts) >= 4:
                        local_addr = parts[3]
                        remote_addr = parts[4]
                        
                        # Extract remote host and port
                        if ':' in remote_addr:
                            host, port = remote_addr.rsplit(':', 1)
                            
                            # Create a network request entry
                            if port in ['80', '443', '8080', '8443']:
                                protocol = 'https' if port in ['443', '8443'] else 'http'
                                url = f"{protocol}://{host}:{port}/"
                                
                                connections.append(NetworkRequest(
                                    url=url,
                                    method='GET',
                                    timestamp=datetime.now().isoformat(),
                                    host=host,
                                    response_code=None
                                ))
            
            logger.debug(f"Found {len(connections)} active connections")
            return connections
            
        except Exception as e:
            logger.debug(f"Network connection monitoring failed: {e}")
            return []

    async def _get_captured_requests(self) -> List[NetworkRequest]:
        """Legacy method - redirects to enhanced capture"""
        return await self._get_mitm_captured_requests("legacy")
    
    def _filter_requests(self, requests: List[NetworkRequest]) -> List[NetworkRequest]:
        """Filter out system and irrelevant network requests"""
        filtered = []
        
        for request in requests:
            try:
                # Parse domain from URL
                if '://' in request.url:
                    domain_part = request.url.split('://', 1)[1]
                    domain = domain_part.split('/')[0].split(':')[0]
                    
                    # Skip system domains
                    if any(sys_domain in domain for sys_domain in self.system_domains):
                        continue
                    
                    # Skip common ad and analytics domains
                    if any(pattern in domain.lower() for pattern in ['ads', 'analytics', 'tracking']):
                        continue
                    
                    filtered.append(request)
                    
            except Exception as e:
                logger.debug(f"Failed to filter request {request.url}: {e}")
                continue
        
        return filtered
    
    def _convert_to_url_schemas(self, requests: List[NetworkRequest]) -> List[URLInfoSchema]:
        """Convert NetworkRequest objects to URLInfoSchema objects"""
        url_schemas = []
        
        for request in requests:
            url_schema = URLInfoSchema(
                url=request.url,
                type=self._classify_dynamic_url(request.url),
                file_location="dynamic_analysis",
                context=f"{request.method} {request.response_code or 'N/A'}"
            )
            url_schemas.append(url_schema)
        
        return url_schemas
    
    def _classify_dynamic_url(self, url: str) -> str:
        """Classify URL type for dynamic analysis"""
        url_lower = url.lower()
        
        if '/api/' in url_lower:
            return 'api_endpoint'
        elif any(pattern in url_lower for pattern in ['cdn', 'assets', 'static']):
            return 'cdn'
        elif any(pattern in url_lower for pattern in ['analytics', 'track', 'metric']):
            return 'analytics'
        elif url_lower.startswith(('ws://', 'wss://')):
            return 'websocket'
        else:
            return 'general'
    
    async def _uninstall_apk(self, package_name: str) -> bool:
        """Uninstall APK from emulator"""
        try:
            logger.debug(f"Uninstalling app: {package_name}")
            
            process = await asyncio.create_subprocess_exec(
                self.tools.adb, 'uninstall', package_name,
                stdout=self.subprocess_pipe,
                stderr=self.subprocess_pipe
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                logger.warning(f"Failed to uninstall app: {stderr.decode()}")
                return False
            
            return "Success" in stdout.decode()
            
        except Exception as e:
            logger.warning(f"Failed to uninstall app: {e}")
            return False
    
    async def _get_android_version(self) -> str:
        """Get Android version from emulator"""
        try:
            process = await asyncio.create_subprocess_exec(
                self.tools.adb, 'shell', 'getprop', 'ro.build.version.release',
                stdout=self.subprocess_pipe,
                stderr=self.subprocess_pipe
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                return stdout.decode().strip()
            
        except Exception as e:
            logger.debug(f"Failed to get Android version: {e}")
        
        return "unknown"

    async def _get_current_activity(self) -> str:
        """Get current activity name"""
        try:
            process = await asyncio.create_subprocess_exec(
                self.tools.adb, "shell", "dumpsys", "activity", "activities",
                stdout=self.subprocess_pipe,
                stderr=self.subprocess_pipe
            )
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                output = stdout.decode()
                # Look for current focus
                for line in output.split('\n'):
                    if 'mCurrentFocus' in line or 'mFocusedActivity' in line:
                        # Extract activity name
                        import re
                        match = re.search(r'([a-zA-Z0-9_.]+)/([a-zA-Z0-9_.]+)', line)
                        if match:
                            return match.group(2)
            
            return "unknown"
            
        except Exception as e:
            logger.debug(f"Failed to get current activity: {e}")
            return "unknown"

    async def _find_prioritized_elements(self) -> List[Dict[str, Any]]:
        """Find UI elements prioritized by likelihood of triggering network requests"""
        try:
            # Get UI dump
            process = await asyncio.create_subprocess_exec(
                self.tools.adb, 'shell', 'uiautomator', 'dump', '/sdcard/ui.xml',
                stdout=self.subprocess_pipe,
                stderr=self.subprocess_pipe
            )
            await process.communicate()
            
            if process.returncode != 0:
                return []
            
            # Get the UI dump content
            process = await asyncio.create_subprocess_exec(
                self.tools.adb, 'shell', 'cat', '/sdcard/ui.xml',
                stdout=self.subprocess_pipe,
                stderr=self.subprocess_pipe
            )
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                return []
            
            xml_content = stdout.decode()
            elements = []
            
            # Parse elements and assign priorities
            import xml.etree.ElementTree as ET
            try:
                root = ET.fromstring(xml_content)
                for elem in root.iter():
                    if elem.get('clickable') == 'true':
                        bounds = elem.get('bounds', '')
                        text = elem.get('text', '') or elem.get('content-desc', '')
                        class_name = elem.get('class', '')
                        
                        if bounds and self._parse_bounds(bounds):
                            priority = self._calculate_element_priority(text, class_name)
                            if priority > 0:
                                x1, y1, x2, y2 = self._parse_bounds(bounds)
                                elements.append({
                                    'x': (x1 + x2) // 2,
                                    'y': (y1 + y2) // 2,
                                    'text': text,
                                    'class': class_name,
                                    'bounds': bounds,
                                    'priority': priority
                                })
            except ET.ParseError:
                logger.debug("Failed to parse UI XML")
            
            # Sort by priority (highest first)
            elements.sort(key=lambda x: x['priority'], reverse=True)
            return elements[:10]  # Return top 10 elements
            
        except Exception as e:
            logger.debug(f"Failed to find prioritized elements: {e}")
            return []

    def _parse_bounds(self, bounds_str: str) -> Optional[tuple]:
        """Parse bounds string like '[x1,y1][x2,y2]' to (x1, y1, x2, y2)"""
        try:
            import re
            match = re.match(r'\[(\d+),(\d+)\]\[(\d+),(\d+)\]', bounds_str)
            if match:
                return tuple(map(int, match.groups()))
        except Exception:
            pass
        return None

    def _calculate_element_priority(self, text: str, class_name: str) -> int:
        """Calculate priority score for UI element"""
        priority = 0
        text_lower = text.lower() if text else ""
        class_lower = class_name.lower() if class_name else ""
        
        # Check against our priority patterns
        for pattern_group in self.ui_exploration_patterns:
            if 'text_patterns' in pattern_group:
                for pattern in pattern_group['text_patterns']:
                    if pattern.lower() in text_lower:
                        priority = max(priority, pattern_group['priority'])
            
            if 'class_patterns' in pattern_group:
                for pattern in pattern_group['class_patterns']:
                    if pattern.lower() in class_lower:
                        priority = max(priority, pattern_group['priority'])
        
        # Bonus for buttons and interactive elements
        if 'button' in class_lower:
            priority += 2
        if 'edit' in class_lower or 'input' in class_lower:
            priority += 1
        
        return priority

    async def _smart_interact_element(self, element: Dict[str, Any]) -> bool:
        """Smart interaction with UI element based on its type"""
        try:
            x, y = element['x'], element['y']
            text = element.get('text', '')
            class_name = element.get('class', '')
            
            logger.debug(f"Smart interacting with element: {text or class_name} at ({x}, {y})")
            
            # Different interaction strategies based on element type
            if 'edit' in class_name.lower() or 'input' in class_name.lower():
                # For input fields, try entering some test data
                await self._tap_and_input(x, y, "test123")
            elif any(keyword in text.lower() for keyword in ['login', '登录', 'sign']):
                # For login elements, just tap and wait longer
                await self._tap_element(x, y)
                await asyncio.sleep(5)  # Wait longer for login processes
            else:
                # Regular tap for other elements
                await self._tap_element(x, y)
            
            return True
            
        except Exception as e:
            logger.debug(f"Smart interaction failed: {e}")
            return False

    async def _tap_element(self, x: int, y: int) -> bool:
        """Tap on screen coordinates"""
        try:
            process = await asyncio.create_subprocess_exec(
                self.tools.adb, 'shell', 'input', 'tap', str(x), str(y),
                stdout=self.subprocess_pipe,
                stderr=self.subprocess_pipe
            )
            await process.communicate()
            return process.returncode == 0
        except Exception as e:
            logger.debug(f"Tap failed: {e}")
            return False

    async def _tap_and_input(self, x: int, y: int, text: str) -> bool:
        """Tap element and input text"""
        try:
            # First tap to focus
            await self._tap_element(x, y)
            await asyncio.sleep(1)
            
            # Then input text
            process = await asyncio.create_subprocess_exec(
                self.tools.adb, 'shell', 'input', 'text', text,
                stdout=self.subprocess_pipe,
                stderr=self.subprocess_pipe
            )
            await process.communicate()
            return process.returncode == 0
        except Exception as e:
            logger.debug(f"Tap and input failed: {e}")
            return False

    async def _try_navigation_actions(self, package_name: str):
        """Try various navigation actions when no clickable elements found"""
        try:
            actions = [
                # Swipe down (refresh gesture)
                ['shell', 'input', 'swipe', '500', '300', '500', '800'],
                # Swipe up (scroll up)
                ['shell', 'input', 'swipe', '500', '800', '500', '300'],
                # Swipe left (navigate)
                ['shell', 'input', 'swipe', '800', '500', '200', '500'],
                # Menu key
                ['shell', 'input', 'keyevent', '82'],
            ]
            
            for action in actions:
                process = await asyncio.create_subprocess_exec(
                    self.tools.adb, *action,
                    stdout=self.subprocess_pipe,
                    stderr=self.subprocess_pipe
                )
                await process.communicate()
                await asyncio.sleep(2)
                
        except Exception as e:
            logger.debug(f"Navigation actions failed: {e}")

    async def _explore_activity(self, package_name: str):
        """Explore current activity more thoroughly"""
        try:
            logger.debug(f"Exploring activity for {package_name}")
            
            # Try opening menu
            await asyncio.create_subprocess_exec(
                self.tools.adb, 'shell', 'input', 'keyevent', '82',  # MENU key
                stdout=self.subprocess_pipe,
                stderr=self.subprocess_pipe
            )
            await asyncio.sleep(2)
            
            # Try scrolling to reveal more content
            for _ in range(3):
                await asyncio.create_subprocess_exec(
                    self.tools.adb, 'shell', 'input', 'swipe', '500', '800', '500', '400',
                    stdout=self.subprocess_pipe,
                    stderr=self.subprocess_pipe
                )
                await asyncio.sleep(1)
                
        except Exception as e:
            logger.debug(f"Activity exploration failed: {e}")

    async def _handle_dialogs(self):
        """Handle system dialogs and permission requests"""
        try:
            # Common dialog responses
            dialog_responses = [
                # Allow permissions
                ['shell', 'input', 'keyevent', '22', '&&', 'input', 'keyevent', '66'],  # Right + Enter
                # OK button (approximate position)
                ['shell', 'input', 'tap', '600', '1000'],
                # Allow button (approximate position)
                ['shell', 'input', 'tap', '700', '1000'],
            ]
            
            for response in dialog_responses:
                try:
                    process = await asyncio.create_subprocess_exec(
                        self.tools.adb, *response,
                        stdout=self.subprocess_pipe,
                        stderr=self.subprocess_pipe
                    )
                    await process.communicate()
                    await asyncio.sleep(1)
                except Exception:
                    continue
                    
        except Exception as e:
            logger.debug(f"Dialog handling failed: {e}")