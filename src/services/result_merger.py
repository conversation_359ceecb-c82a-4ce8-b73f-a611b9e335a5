"""
Result Merger Module - Combines static and dynamic analysis results
"""
import time
from datetime import datetime
from typing import Optional, List
import logging

from src.models.schemas import (
    StaticAnalysisResult,
    DynamicAnalysisResult,
    AnalysisResult,
    BasicInfo,
    CertificateInfo,
    URLInfo,
    AnalysisMetadata,
    BasicInfoSchema,
    CertificateSchema,
    URLInfoSchema
)

logger = logging.getLogger(__name__)


class ResultMerger:
    """Merges static and dynamic analysis results into final format"""
    
    def __init__(self):
        pass
    
    def merge(
        self, 
        static_result: Optional[StaticAnalysisResult], 
        dynamic_result: Optional[DynamicAnalysisResult],
        static_duration: Optional[float] = None
    ) -> AnalysisResult:
        """
        Merge static and dynamic analysis results
        
        Args:
            static_result: Static analysis result
            dynamic_result: Dynamic analysis result
            static_duration: Duration of static analysis in seconds
            
        Returns:
            AnalysisResult: Merged analysis result
        """
        logger.info("Merging analysis results")
        
        if not static_result:
            raise ValueError("Static analysis result is required")
        
        try:
            # Convert basic info
            basic_info = self._convert_basic_info(static_result.basic_info)
            
            # Convert certificate info
            certificate = self._convert_certificate_info(static_result.certificate)
            
            # Convert URLs
            static_urls = self._convert_urls(static_result.static_urls)
            dynamic_urls = self._convert_urls(dynamic_result.dynamic_urls) if dynamic_result else []
            
            # Create analysis metadata
            metadata = self._create_analysis_metadata(static_duration, dynamic_result)
            
            # Create merged result
            merged_result = AnalysisResult(
                basic_info=basic_info,
                certificate=certificate,
                static_urls=static_urls,
                dynamic_urls=dynamic_urls,
                analysis_metadata=metadata
            )
            
            logger.info(f"Successfully merged results: {len(static_urls)} static URLs, {len(dynamic_urls)} dynamic URLs")
            return merged_result
            
        except Exception as e:
            logger.error(f"Failed to merge analysis results: {e}")
            raise Exception(f"Failed to merge analysis results: {str(e)}")
    
    def _convert_basic_info(self, basic_info: BasicInfoSchema) -> BasicInfo:
        """Convert BasicInfoSchema to BasicInfo"""
        return BasicInfo(
            package_name=basic_info.package_name,
            app_name=basic_info.app_name,
            version_name=basic_info.version_name,
            version_code=basic_info.version_code,
            md5=basic_info.md5,
            sha256=basic_info.sha256,
            file_size=basic_info.file_size
        )
    
    def _convert_certificate_info(self, certificate: Optional[CertificateSchema]) -> CertificateInfo:
        """Convert CertificateSchema to CertificateInfo"""
        if not certificate:
            certificate = self._create_default_certificate()
        
        return CertificateInfo(
            public_key_sha256=certificate.public_key_sha256,
            subject=certificate.subject,
            valid_from=datetime.now(),  # Default to current time
            valid_to=datetime(2099, 12, 31),  # Default far future
            signature_algorithm=certificate.signature_algorithm
        )
    
    def _create_default_certificate(self) -> CertificateSchema:
        """Create default certificate when none is available"""
        return CertificateSchema(
            public_key_sha256="unknown",
            subject="unknown",
            issuer="unknown",
            signature_algorithm="unknown"
        )
    
    def _convert_urls(self, url_schemas: List[URLInfoSchema]) -> List[URLInfo]:
        """Convert URLInfoSchema list to URLInfo list"""
        converted_urls = []
        
        for url_schema in url_schemas:
            url_info = URLInfo(
                url=url_schema.url,
                type=url_schema.type,
                context=url_schema.context,
                file_location=url_schema.file_location,
                method=self._extract_method_from_context(url_schema.context),
                timestamp=datetime.now() if 'dynamic' in url_schema.file_location else None
            )
            converted_urls.append(url_info)
        
        return converted_urls
    
    def _extract_method_from_context(self, context: str) -> Optional[str]:
        """Extract HTTP method from context string"""
        if not context:
            return None
        
        # Look for common HTTP methods in context
        methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS']
        context_upper = context.upper()
        
        for method in methods:
            if method in context_upper:
                return method
        
        return None
    
    def _create_analysis_metadata(
        self, 
        static_duration: Optional[float], 
        dynamic_result: Optional[DynamicAnalysisResult]
    ) -> AnalysisMetadata:
        """Create analysis metadata"""
        dynamic_duration = dynamic_result.duration if dynamic_result else None
        total_duration = None
        
        if static_duration is not None and dynamic_duration is not None:
            total_duration = static_duration + dynamic_duration
        elif static_duration is not None:
            total_duration = static_duration
        elif dynamic_duration is not None:
            total_duration = dynamic_duration
        
        return AnalysisMetadata(
            static_duration=static_duration,
            dynamic_duration=dynamic_duration,
            total_duration=total_duration,
            android_version=dynamic_result.android_version if dynamic_result else None,
            analysis_timestamp=datetime.now()
        )
    
    def create_error_result(self, error_message: str, basic_info: Optional[BasicInfoSchema] = None) -> AnalysisResult:
        """Create an error result when analysis fails"""
        try:
            # Use provided basic info or create minimal default
            if basic_info:
                final_basic_info = self._convert_basic_info(basic_info)
            else:
                final_basic_info = BasicInfo(
                    package_name="unknown",
                    app_name="unknown",
                    version_name="unknown",
                    version_code=0,
                    md5="unknown",
                    sha256="unknown",
                    file_size=0
                )
            
            # Create default certificate
            certificate = self._convert_certificate_info(None)
            
            # Create error metadata
            metadata = AnalysisMetadata(
                static_duration=None,
                dynamic_duration=None,
                total_duration=None,
                android_version=None,
                analysis_timestamp=datetime.now()
            )
            
            return AnalysisResult(
                basic_info=final_basic_info,
                certificate=certificate,
                static_urls=[],
                dynamic_urls=[],
                analysis_metadata=metadata
            )
            
        except Exception as e:
            logger.error(f"Failed to create error result: {e}")
            raise
    
    def deduplicate_urls(self, static_urls: List[URLInfo], dynamic_urls: List[URLInfo]) -> tuple[List[URLInfo], List[URLInfo]]:
        """
        Remove duplicate URLs between static and dynamic results
        Keep both versions but mark them appropriately
        """
        # For now, keep all URLs as they provide different context
        # Static URLs show where they were found in code
        # Dynamic URLs show actual runtime behavior
        
        # Could implement more sophisticated deduplication logic here
        # For example, merge URLs that are identical but have different contexts
        
        return static_urls, dynamic_urls
    
    def calculate_analysis_summary(self, result: AnalysisResult) -> dict:
        """Calculate summary statistics for the analysis"""
        all_urls = result.static_urls + result.dynamic_urls
        
        # Count unique domains
        domains = set()
        for url in all_urls:
            try:
                if '://' in url.url:
                    domain = url.url.split('://')[1].split('/')[0]
                    domains.add(domain)
            except Exception:
                continue
        
        # Count by URL type
        url_types = {}
        for url in all_urls:
            url_types[url.type] = url_types.get(url.type, 0) + 1
        
        return {
            'total_urls': len(all_urls),
            'static_urls': len(result.static_urls),
            'dynamic_urls': len(result.dynamic_urls),
            'unique_domains': len(domains),
            'url_types': url_types,
            'analysis_duration': result.analysis_metadata.total_duration,
            'android_version': result.analysis_metadata.android_version
        }
    
    async def merge_results(
        self,
        task_id: str,
        static_result: Optional[dict],
        dynamic_result: Optional[dict]
    ) -> dict:
        """
        Merge static and dynamic analysis results into API response format

        Args:
            task_id: Task identifier
            static_result: Static analysis result (dict format from task queue)
            dynamic_result: Dynamic analysis result (dict format from task queue)

        Returns:
            dict: Merged result in API format
        """
        try:
            logger.info("Merging analysis results")

            # Extract actual results from task queue response format
            static_data = None
            dynamic_data = None

            if static_result and static_result.get('status') == 'completed':
                # Static analysis result is directly in the response, not nested under 'result'
                static_data = static_result

            if dynamic_result and dynamic_result.get('status') == 'completed':
                # Dynamic analysis result is also directly in the response
                dynamic_data = dynamic_result

            # Handle case where static analysis is required
            if not static_data:
                raise ValueError("Static analysis result is required")

            # Combine static and dynamic URLs
            static_urls = static_data.get('static_urls', [])
            dynamic_urls = dynamic_data.get('network_requests', []) if dynamic_data else []
            all_urls = static_urls + dynamic_urls

            # Build response directly from the data
            response = {
                'task_id': task_id,
                'status': 'completed',
                'basic_info': static_data.get('basic_info', {}),
                'certificate': static_data.get('certificate', {}),
                'urls': all_urls,
                'analysis_summary': self._generate_summary_from_data(static_data, dynamic_data)
            }

            logger.info(f"Successfully merged results: {len(static_urls)} static URLs, {len(dynamic_urls)} dynamic URLs")
            return response

        except Exception as e:
            logger.error(f"Failed to merge analysis results: {e}")
            raise

    def _generate_summary_from_data(self, static_data: dict, dynamic_data: Optional[dict]) -> dict:
        """Generate summary from raw data"""
        try:
            static_urls = static_data.get('static_urls', [])
            dynamic_urls = dynamic_data.get('network_requests', []) if dynamic_data else []

            # Count URL types
            url_types = {}
            for url in static_urls:
                url_type = url.get('type', 'unknown')
                url_types[url_type] = url_types.get(url_type, 0) + 1

            # Count HTTP methods for dynamic URLs
            http_methods = {}
            for url in dynamic_urls:
                method = url.get('method', 'GET')
                http_methods[method] = http_methods.get(method, 0) + 1

            return {
                'total_static_urls': len(static_urls),
                'total_dynamic_urls': len(dynamic_urls),
                'url_types': url_types,
                'http_methods': http_methods,
                'has_dynamic_analysis': dynamic_data is not None and dynamic_data.get('status') == 'completed'
            }
        except Exception as e:
            logger.error(f"Failed to generate summary: {e}")
            return {
                'total_static_urls': 0,
                'total_dynamic_urls': 0,
                'url_types': {},
                'http_methods': {},
                'has_dynamic_analysis': False
            }