"""
本地模拟器UI自动化交互模块
提供智能的UI探索和交互功能
"""
import asyncio
import logging
import xml.etree.ElementTree as ET
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import re
import time

from .local_emulator_manager import EmulatorInstance
from .static_analyzer import AnalysisTools

logger = logging.getLogger(__name__)


class InteractionType(Enum):
    """交互类型枚举"""
    CLICK = "click"
    LONG_CLICK = "long_click"
    SWIPE = "swipe"
    INPUT_TEXT = "input_text"
    KEY_EVENT = "key_event"
    SCROLL = "scroll"


@dataclass
class UIElement:
    """UI元素信息"""
    class_name: str
    text: str
    content_desc: str
    resource_id: str
    bounds: Tuple[int, int, int, int]  # (left, top, right, bottom)
    clickable: bool
    scrollable: bool
    focusable: bool
    enabled: bool
    
    @property
    def center_x(self) -> int:
        """元素中心X坐标"""
        return (self.bounds[0] + self.bounds[2]) // 2
    
    @property
    def center_y(self) -> int:
        """元素中心Y坐标"""
        return (self.bounds[1] + self.bounds[3]) // 2
    
    @property
    def width(self) -> int:
        """元素宽度"""
        return self.bounds[2] - self.bounds[0]
    
    @property
    def height(self) -> int:
        """元素高度"""
        return self.bounds[3] - self.bounds[1]


@dataclass
class InteractionAction:
    """交互动作"""
    action_type: InteractionType
    element: Optional[UIElement] = None
    coordinates: Optional[Tuple[int, int]] = None
    text_input: Optional[str] = None
    key_code: Optional[str] = None
    swipe_direction: Optional[str] = None
    description: str = ""


class LocalUIAutomator:
    """本地UI自动化器"""
    
    def __init__(self, tools: Optional[AnalysisTools] = None):
        """
        初始化UI自动化器
        
        Args:
            tools: 分析工具配置
        """
        self.tools = tools or AnalysisTools()
        
        # 交互配置
        self.interaction_delay = 2  # 交互间隔（秒）
        self.max_interactions = 50  # 最大交互次数
        self.screen_size = (1080, 1920)  # 默认屏幕尺寸
        
        # 智能交互策略
        self.visited_elements = set()
        self.interaction_history = []
        
        logger.info("LocalUIAutomator initialized")

    async def _handle_permission_dialogs(self, emulator: EmulatorInstance) -> bool:
        """处理权限请求对话框"""
        try:
            logger.info("Checking for permission dialogs...")

            # 最多尝试5次处理权限对话框
            for attempt in range(5):
                ui_elements = await self._get_ui_elements(emulator)
                if not ui_elements:
                    break

                # 查找权限相关的UI元素
                permission_found = False
                for element in ui_elements:
                    # 检查是否是权限对话框
                    text_lower = element.text.lower()
                    desc_lower = element.content_desc.lower()

                    if any(keyword in text_lower or keyword in desc_lower
                           for keyword in ['allow', '允许', 'permit', 'grant', 'ok', '确定']):
                        if element.clickable:
                            logger.info(f"Found permission button: {element.text}")
                            # 点击允许按钮
                            await self._click_element(emulator, element)
                            await asyncio.sleep(1)
                            permission_found = True
                            break

                if not permission_found:
                    # 检查是否还有权限对话框
                    has_permission_dialog = any(
                        'permission' in element.text.lower() or
                        'permission' in element.content_desc.lower() or
                        'permissioncontroller' in element.class_name.lower()
                        for element in ui_elements
                    )

                    if not has_permission_dialog:
                        logger.info("No more permission dialogs found")
                        break
                    else:
                        # 尝试通用的点击位置
                        logger.info("Trying generic permission dialog handling")
                        await self._click_coordinates(emulator, 800, 1400)
                        await asyncio.sleep(1)

                await asyncio.sleep(1)

            return True

        except Exception as e:
            logger.error(f"Error handling permission dialogs: {e}")
            return False

    async def explore_ui(self,
                        emulator: EmulatorInstance, 
                        package_name: str,
                        timeout: int = 180) -> List[InteractionAction]:
        """
        智能UI探索
        
        Args:
            emulator: 模拟器实例
            package_name: 应用包名
            timeout: 探索超时时间
            
        Returns:
            List[InteractionAction]: 执行的交互动作列表
        """
        if not emulator.adb_device_id:
            logger.error("Emulator device ID not available")
            return []
        
        logger.info(f"Starting UI exploration for {package_name}")
        start_time = time.time()
        executed_actions = []
        
        try:
            # 获取屏幕尺寸
            await self._update_screen_size(emulator)

            # 首先处理可能的权限对话框
            await self._handle_permission_dialogs(emulator)

            interaction_count = 0
            while (time.time() - start_time < timeout and
                   interaction_count < self.max_interactions):
                
                # 获取当前UI状态
                ui_elements = await self._get_ui_elements(emulator)
                if not ui_elements:
                    logger.warning("No UI elements found, trying basic interactions")
                    await self._perform_basic_interactions(emulator)
                    interaction_count += 1
                    continue
                
                # 选择下一个交互动作
                action = self._select_next_action(ui_elements, package_name)
                if not action:
                    logger.info("No more meaningful actions available")
                    break
                
                # 执行交互动作
                success = await self._execute_action(emulator, action)
                if success:
                    executed_actions.append(action)
                    self.interaction_history.append(action)
                    logger.debug(f"Executed action: {action.description}")
                
                # 等待UI响应
                await asyncio.sleep(self.interaction_delay)
                interaction_count += 1
            
            logger.info(f"UI exploration completed: {len(executed_actions)} actions executed")
            return executed_actions
            
        except Exception as e:
            logger.error(f"Error during UI exploration: {e}")
            return executed_actions
    
    async def _update_screen_size(self, emulator: EmulatorInstance):
        """更新屏幕尺寸"""
        try:
            process = await asyncio.create_subprocess_exec(
                self.tools.adb, '-s', emulator.adb_device_id, 'shell',
                'wm', 'size',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, _ = await process.communicate()
            
            if process.returncode == 0:
                output = stdout.decode().strip()
                # 解析输出: "Physical size: 1080x1920"
                match = re.search(r'(\d+)x(\d+)', output)
                if match:
                    width, height = int(match.group(1)), int(match.group(2))
                    self.screen_size = (width, height)
                    logger.debug(f"Screen size: {self.screen_size}")
        
        except Exception as e:
            logger.debug(f"Failed to get screen size: {e}")
    
    async def _get_ui_elements(self, emulator: EmulatorInstance) -> List[UIElement]:
        """获取当前UI元素"""
        try:
            # 获取UI dump
            process = await asyncio.create_subprocess_exec(
                self.tools.adb, '-s', emulator.adb_device_id, 'shell',
                'uiautomator', 'dump', '/sdcard/ui.xml',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            await process.communicate()
            
            if process.returncode != 0:
                return []
            
            # 读取UI XML
            process = await asyncio.create_subprocess_exec(
                self.tools.adb, '-s', emulator.adb_device_id, 'shell',
                'cat', '/sdcard/ui.xml',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, _ = await process.communicate()
            
            if process.returncode != 0:
                return []
            
            # 解析XML
            xml_content = stdout.decode()
            return self._parse_ui_xml(xml_content)
            
        except Exception as e:
            logger.error(f"Failed to get UI elements: {e}")
            return []
    
    def _parse_ui_xml(self, xml_content: str) -> List[UIElement]:
        """解析UI XML"""
        elements = []
        
        try:
            root = ET.fromstring(xml_content)
            
            for node in root.iter():
                if node.tag == 'node':
                    try:
                        # 解析bounds属性
                        bounds_str = node.get('bounds', '[0,0][0,0]')
                        bounds_match = re.findall(r'\[(\d+),(\d+)\]', bounds_str)
                        if len(bounds_match) >= 2:
                            left, top = int(bounds_match[0][0]), int(bounds_match[0][1])
                            right, bottom = int(bounds_match[1][0]), int(bounds_match[1][1])
                            bounds = (left, top, right, bottom)
                        else:
                            continue
                        
                        # 创建UI元素
                        element = UIElement(
                            class_name=node.get('class', ''),
                            text=node.get('text', ''),
                            content_desc=node.get('content-desc', ''),
                            resource_id=node.get('resource-id', ''),
                            bounds=bounds,
                            clickable=node.get('clickable', 'false').lower() == 'true',
                            scrollable=node.get('scrollable', 'false').lower() == 'true',
                            focusable=node.get('focusable', 'false').lower() == 'true',
                            enabled=node.get('enabled', 'true').lower() == 'true'
                        )
                        
                        # 过滤无效元素
                        if (element.width > 0 and element.height > 0 and
                            element.bounds[0] >= 0 and element.bounds[1] >= 0):
                            elements.append(element)
                    
                    except Exception as e:
                        logger.debug(f"Error parsing UI element: {e}")
                        continue
        
        except Exception as e:
            logger.error(f"Error parsing UI XML: {e}")
        
        logger.debug(f"Parsed {len(elements)} UI elements")
        return elements
    
    def _select_next_action(self, elements: List[UIElement], package_name: str) -> Optional[InteractionAction]:
        """选择下一个交互动作"""
        # 优先级策略：
        # 1. 可点击的按钮和链接
        # 2. 输入框
        # 3. 可滚动的列表
        # 4. 其他可交互元素
        
        clickable_elements = [e for e in elements if e.clickable and e.enabled]
        input_elements = [e for e in elements if 'EditText' in e.class_name and e.enabled]
        scrollable_elements = [e for e in elements if e.scrollable and e.enabled]
        
        # 优先点击未访问过的可点击元素
        for element in clickable_elements:
            element_id = self._get_element_id(element)
            if element_id not in self.visited_elements:
                self.visited_elements.add(element_id)
                return InteractionAction(
                    action_type=InteractionType.CLICK,
                    element=element,
                    coordinates=(element.center_x, element.center_y),
                    description=f"Click {element.class_name}: {element.text or element.content_desc}"
                )
        
        # 尝试输入文本到输入框
        for element in input_elements:
            element_id = self._get_element_id(element)
            if element_id not in self.visited_elements:
                self.visited_elements.add(element_id)
                # 根据输入框类型选择合适的测试文本
                test_text = self._get_test_input(element)
                return InteractionAction(
                    action_type=InteractionType.INPUT_TEXT,
                    element=element,
                    coordinates=(element.center_x, element.center_y),
                    text_input=test_text,
                    description=f"Input text to {element.class_name}: {test_text}"
                )
        
        # 尝试滚动可滚动元素
        for element in scrollable_elements:
            return InteractionAction(
                action_type=InteractionType.SCROLL,
                element=element,
                coordinates=(element.center_x, element.center_y),
                swipe_direction="down",
                description=f"Scroll {element.class_name}"
            )
        
        # 如果没有明显的交互元素，尝试一些通用操作
        return self._get_fallback_action()
    
    def _get_element_id(self, element: UIElement) -> str:
        """获取元素唯一标识"""
        return f"{element.class_name}:{element.resource_id}:{element.bounds}"
    
    def _get_test_input(self, element: UIElement) -> str:
        """根据输入框类型获取测试输入"""
        text_lower = element.text.lower()
        desc_lower = element.content_desc.lower()
        resource_lower = element.resource_id.lower()
        
        # 邮箱输入框
        if any(keyword in text_lower + desc_lower + resource_lower 
               for keyword in ['email', 'mail', '@']):
            return "<EMAIL>"
        
        # 密码输入框
        if any(keyword in text_lower + desc_lower + resource_lower 
               for keyword in ['password', 'pwd', 'pass']):
            return "password123"
        
        # 用户名输入框
        if any(keyword in text_lower + desc_lower + resource_lower 
               for keyword in ['username', 'user', 'name']):
            return "testuser"
        
        # 电话号码输入框
        if any(keyword in text_lower + desc_lower + resource_lower 
               for keyword in ['phone', 'tel', 'mobile']):
            return "1234567890"
        
        # 搜索框
        if any(keyword in text_lower + desc_lower + resource_lower 
               for keyword in ['search', 'query']):
            return "test search"
        
        # 默认文本
        return "Hello World"
    
    def _get_fallback_action(self) -> Optional[InteractionAction]:
        """获取备用交互动作"""
        # 返回一些通用的交互动作
        fallback_actions = [
            InteractionAction(
                action_type=InteractionType.KEY_EVENT,
                key_code="KEYCODE_BACK",
                description="Press back key"
            ),
            InteractionAction(
                action_type=InteractionType.SWIPE,
                coordinates=(self.screen_size[0] // 2, self.screen_size[1] // 2),
                swipe_direction="up",
                description="Swipe up"
            ),
            InteractionAction(
                action_type=InteractionType.CLICK,
                coordinates=(self.screen_size[0] // 2, self.screen_size[1] // 2),
                description="Click center"
            )
        ]
        
        # 返回还没执行过的动作
        for action in fallback_actions:
            if not any(h.description == action.description for h in self.interaction_history[-5:]):
                return action
        
        return None

    async def _execute_action(self, emulator: EmulatorInstance, action: InteractionAction) -> bool:
        """执行交互动作"""
        try:
            if action.action_type == InteractionType.CLICK:
                return await self._perform_click(emulator, action)

            elif action.action_type == InteractionType.LONG_CLICK:
                return await self._perform_long_click(emulator, action)

            elif action.action_type == InteractionType.INPUT_TEXT:
                return await self._perform_input_text(emulator, action)

            elif action.action_type == InteractionType.KEY_EVENT:
                return await self._perform_key_event(emulator, action)

            elif action.action_type == InteractionType.SWIPE:
                return await self._perform_swipe(emulator, action)

            elif action.action_type == InteractionType.SCROLL:
                return await self._perform_scroll(emulator, action)

            else:
                logger.warning(f"Unknown action type: {action.action_type}")
                return False

        except Exception as e:
            logger.error(f"Error executing action {action.description}: {e}")
            return False

    async def _perform_click(self, emulator: EmulatorInstance, action: InteractionAction) -> bool:
        """执行点击操作"""
        if not action.coordinates:
            return False

        x, y = action.coordinates
        process = await asyncio.create_subprocess_exec(
            self.tools.adb, '-s', emulator.adb_device_id, 'shell',
            'input', 'tap', str(x), str(y),
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )

        await process.communicate()
        return process.returncode == 0

    async def _perform_long_click(self, emulator: EmulatorInstance, action: InteractionAction) -> bool:
        """执行长按操作"""
        if not action.coordinates:
            return False

        x, y = action.coordinates
        # 使用swipe模拟长按（同一位置滑动1000ms）
        process = await asyncio.create_subprocess_exec(
            self.tools.adb, '-s', emulator.adb_device_id, 'shell',
            'input', 'swipe', str(x), str(y), str(x), str(y), '1000',
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )

        await process.communicate()
        return process.returncode == 0

    async def _perform_input_text(self, emulator: EmulatorInstance, action: InteractionAction) -> bool:
        """执行文本输入操作"""
        if not action.coordinates or not action.text_input:
            return False

        # 先点击输入框
        await self._perform_click(emulator, action)
        await asyncio.sleep(0.5)

        # 清除现有文本
        process = await asyncio.create_subprocess_exec(
            self.tools.adb, '-s', emulator.adb_device_id, 'shell',
            'input', 'keyevent', 'KEYCODE_CTRL_A',
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        await process.communicate()

        # 输入新文本
        process = await asyncio.create_subprocess_exec(
            self.tools.adb, '-s', emulator.adb_device_id, 'shell',
            'input', 'text', action.text_input,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )

        await process.communicate()
        return process.returncode == 0

    async def _perform_key_event(self, emulator: EmulatorInstance, action: InteractionAction) -> bool:
        """执行按键事件"""
        if not action.key_code:
            return False

        process = await asyncio.create_subprocess_exec(
            self.tools.adb, '-s', emulator.adb_device_id, 'shell',
            'input', 'keyevent', action.key_code,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )

        await process.communicate()
        return process.returncode == 0

    async def _perform_swipe(self, emulator: EmulatorInstance, action: InteractionAction) -> bool:
        """执行滑动操作"""
        if not action.coordinates or not action.swipe_direction:
            return False

        x, y = action.coordinates

        # 根据方向计算终点坐标
        if action.swipe_direction == "up":
            end_x, end_y = x, y - 300
        elif action.swipe_direction == "down":
            end_x, end_y = x, y + 300
        elif action.swipe_direction == "left":
            end_x, end_y = x - 300, y
        elif action.swipe_direction == "right":
            end_x, end_y = x + 300, y
        else:
            return False

        process = await asyncio.create_subprocess_exec(
            self.tools.adb, '-s', emulator.adb_device_id, 'shell',
            'input', 'swipe', str(x), str(y), str(end_x), str(end_y), '500',
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )

        await process.communicate()
        return process.returncode == 0

    async def _perform_scroll(self, emulator: EmulatorInstance, action: InteractionAction) -> bool:
        """执行滚动操作"""
        if not action.element:
            return False

        # 在元素区域内滚动
        element = action.element
        start_x = element.center_x
        start_y = element.bounds[1] + element.height * 3 // 4  # 从下方开始
        end_x = element.center_x
        end_y = element.bounds[1] + element.height // 4  # 到上方结束

        process = await asyncio.create_subprocess_exec(
            self.tools.adb, '-s', emulator.adb_device_id, 'shell',
            'input', 'swipe', str(start_x), str(start_y), str(end_x), str(end_y), '500',
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )

        await process.communicate()
        return process.returncode == 0

    async def _perform_basic_interactions(self, emulator: EmulatorInstance):
        """执行基本交互操作"""
        basic_actions = [
            # 点击屏幕中心
            (self.screen_size[0] // 2, self.screen_size[1] // 2),
            # 点击屏幕上方
            (self.screen_size[0] // 2, self.screen_size[1] // 4),
            # 点击屏幕下方
            (self.screen_size[0] // 2, self.screen_size[1] * 3 // 4),
        ]

        for x, y in basic_actions:
            try:
                process = await asyncio.create_subprocess_exec(
                    self.tools.adb, '-s', emulator.adb_device_id, 'shell',
                    'input', 'tap', str(x), str(y),
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                await process.communicate()
                await asyncio.sleep(1)
            except Exception as e:
                logger.debug(f"Basic interaction failed: {e}")

    def get_interaction_summary(self) -> Dict[str, Any]:
        """获取交互摘要"""
        action_counts = {}
        for action in self.interaction_history:
            action_type = action.action_type.value
            action_counts[action_type] = action_counts.get(action_type, 0) + 1

        return {
            'total_interactions': len(self.interaction_history),
            'unique_elements_visited': len(self.visited_elements),
            'action_counts': action_counts,
            'last_actions': [action.description for action in self.interaction_history[-5:]]
        }

    def reset_state(self):
        """重置交互状态"""
        self.visited_elements.clear()
        self.interaction_history.clear()
        logger.debug("UI automator state reset")
