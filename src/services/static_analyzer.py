"""
Static APK Analysis Module
"""
import asyncio
import os
import re
import tempfile
import subprocess
import hashlib
from pathlib import Path
from typing import List, Optional
import logging
from dataclasses import dataclass

from src.models.schemas import (
    StaticAnalysisResult,
    BasicInfoSchema, 
    CertificateSchema,
    URLInfoSchema
)

logger = logging.getLogger(__name__)


@dataclass
class AnalysisTools:
    """APK analysis tools configuration"""
    aapt: str = "./android-14/aapt"
    keytool: str = os.getenv("KEYTOOL_PATH", "/Users/<USER>/Desktop/project/apk_detect/jdk-11.0.1.jdk/Contents/Home/bin/keytool")
    unzip: str = "unzip"
    adb: str = "./platform-tools/adb"  # 添加ADB路径


class StaticAnalyzer:
    """Static APK analyzer"""
    
    def __init__(self, tools: Optional[AnalysisTools] = None):
        self.tools = tools or AnalysisTools()
        self.url_patterns = [
            # HTTP/HTTPS URLs
            re.compile(r'https?://[^\s\'"<>]+'),
            # API endpoints
            re.compile(r'["\']/?api/[^"\']*["\']'),
            # Domain patterns
            re.compile(r'[a-zA-Z0-9-]+\.[a-zA-Z]{2,}(?:\.[a-zA-Z]{2,})?'),
            # WebSocket URLs
            re.compile(r'wss?://[^\s\'"<>]+'),
        ]
    
    async def analyze(self, apk_path: str) -> StaticAnalysisResult:
        """
        Perform complete static analysis of APK
        
        Args:
            apk_path: Path to the APK file
            
        Returns:
            StaticAnalysisResult: Complete analysis result
        """
        logger.info(f"Starting static analysis of {apk_path}")
        
        try:
            # Create temporary working directory
            with tempfile.TemporaryDirectory() as temp_dir:
                # Extract basic APK information
                basic_info = await self._extract_basic_info(apk_path)
                
                # Analyze certificate
                certificate = await self._analyze_certificate(apk_path) 
                
                # Extract static URLs
                static_urls = await self._extract_static_urls(apk_path, temp_dir)
                
                result = StaticAnalysisResult(
                    basic_info=basic_info,
                    certificate=certificate,
                    static_urls=static_urls
                )
                
                logger.info(f"Static analysis completed for {apk_path}")
                return result
                
        except Exception as e:
            logger.error(f"Failed to analyze APK {apk_path}: {e}")
            raise Exception(f"Failed to analyze APK: {str(e)}")
    
    async def _extract_basic_info(self, apk_path: str) -> BasicInfoSchema:
        """Extract basic APK information using aapt"""
        logger.debug(f"Extracting basic info from {apk_path}")
        
        try:
            # Run aapt dump badging command
            process = await asyncio.create_subprocess_exec(
                self.tools.aapt, 'dump', 'badging', apk_path,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                raise Exception(f"aapt failed: {stderr.decode()}")
            
            output = stdout.decode()
            
            # Parse aapt output
            package_name = self._extract_regex(output, r"package: name='([^']+)'")
            version_code = self._extract_regex(output, r"versionCode='([^']+)'")
            version_name = self._extract_regex(output, r"versionName='([^']+)'")
            app_name = self._extract_regex(output, r"application-label:'([^']+)'")
            
            # Get file size
            file_size = os.path.getsize(apk_path)
            
            # Calculate MD5 hash
            md5_hash = await self._calculate_file_hash(apk_path, 'md5')
            sha256_hash = await self._calculate_file_hash(apk_path, 'sha256')
            
            return BasicInfoSchema(
                package_name=package_name or "unknown",
                app_name=app_name or "unknown",
                version_name=version_name or "unknown", 
                version_code=int(version_code) if version_code else 0,
                file_size=file_size,
                md5_hash=md5_hash,
                sha256_hash=sha256_hash
            )
            
        except Exception as e:
            logger.error(f"Failed to extract basic info: {e}")
            raise
    
    async def _analyze_certificate(self, apk_path: str) -> CertificateSchema:
        """Analyze APK certificate information"""
        logger.debug(f"Analyzing certificate for {apk_path}")
        
        try:
            # Use keytool to analyze certificate
            process = await asyncio.create_subprocess_exec(
                self.tools.keytool, '-printcert', '-jarfile', apk_path,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                raise Exception(f"keytool failed: {stderr.decode()}")
            
            output = stdout.decode()
            
            # Parse certificate information (support both English and Chinese output)
            subject = (self._extract_regex(output, r"Owner: (.+)") or 
                      self._extract_regex(output, r"所有者: (.+)"))
            issuer = (self._extract_regex(output, r"Issuer: (.+)") or 
                     self._extract_regex(output, r"发布者: (.+)"))
            sha256_fingerprint = (self._extract_regex(output, r"SHA256: ([A-F0-9:]+)") or 
                                self._extract_regex(output, r"SHA256: ([A-F0-9:]+)"))
            signature_algorithm = (self._extract_regex(output, r"Signature algorithm name: (.+)") or 
                                 self._extract_regex(output, r"签名算法名称: (.+)"))
            
            # Clean up SHA256 fingerprint (remove colons)
            if sha256_fingerprint:
                sha256_fingerprint = sha256_fingerprint.replace(":", "")
            
            return CertificateSchema(
                public_key_sha256=sha256_fingerprint or "unknown",
                subject=subject or "unknown",
                issuer=issuer or "unknown", 
                signature_algorithm=signature_algorithm or "unknown"
            )
            
        except FileNotFoundError:
            # keytool not available, return placeholder data
            logger.warning("keytool not available, returning placeholder certificate data")
            return CertificateSchema(
                public_key_sha256="keytool_not_available",
                subject="keytool_not_available",
                issuer="keytool_not_available", 
                signature_algorithm="keytool_not_available"
            )
        except Exception as e:
            logger.error(f"Failed to analyze certificate: {e}")
            # Return placeholder data instead of raising
            return CertificateSchema(
                public_key_sha256="analysis_failed",
                subject="analysis_failed",
                issuer="analysis_failed", 
                signature_algorithm="analysis_failed"
            )
    
    async def _extract_static_urls(self, apk_path: str, temp_dir: str) -> List[URLInfoSchema]:
        """Extract URLs from APK by decompiling and analyzing code"""
        logger.debug(f"Extracting static URLs from {apk_path}")
        
        urls = []
        
        try:
            # Extract APK contents
            extract_dir = os.path.join(temp_dir, "extracted")
            os.makedirs(extract_dir, exist_ok=True)
            
            # Use unzip to extract APK with automatic overwrite
            process = await asyncio.create_subprocess_exec(
                self.tools.unzip, '-q', '-o', apk_path, '-d', extract_dir,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                logger.warning(f"Failed to extract APK: {stderr.decode()}")
                return urls
            
            # Search for URLs in various file types
            for root, dirs, files in os.walk(extract_dir):
                for file in files:
                    if file.endswith(('.xml', '.txt', '.json', '.properties')):
                        file_path = os.path.join(root, file)
                        try:
                            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                                content = f.read()
                                file_urls = await self._extract_urls_from_content(content, file_path)
                                urls.extend(file_urls)
                        except Exception as e:
                            logger.debug(f"Failed to read {file_path}: {e}")
                            continue
            
            # Deduplicate URLs
            unique_urls = self._deduplicate_urls(urls)
            
            logger.debug(f"Extracted {len(unique_urls)} unique URLs")
            return unique_urls
            
        except Exception as e:
            logger.error(f"Failed to extract static URLs: {e}")
            return []
    
    async def _extract_urls_from_content(self, content: str, file_path: str) -> List[URLInfoSchema]:
        """Extract URLs from file content using regex patterns"""
        urls = []
        
        for line_num, line in enumerate(content.split('\n'), 1):
            for pattern in self.url_patterns:
                matches = pattern.finditer(line)
                for match in matches:
                    url = self._clean_url(match.group())
                    if self._is_valid_url(url):
                        urls.append(URLInfoSchema(
                            url=url,
                            type=self._classify_url(url),
                            file_location=f"{file_path}:{line_num}",
                            context=line.strip()[:100]  # Limit context length
                        ))
        
        return urls
    
    def _clean_url(self, url: str) -> str:
        """Clean and normalize URL"""
        # Remove quotes and extra characters
        url = url.strip('"\'<>')
        
        # Remove trailing slashes and parameters for consistency
        if url.endswith('/'):
            url = url.rstrip('/')
            
        return url
    
    def _is_valid_url(self, url: str) -> bool:
        """Check if URL is valid and not a common false positive"""
        if len(url) < 8:  # Minimum valid URL length
            return False
            
        # Skip common false positives
        false_positives = [
            'android.', 'java.', 'com.google.android',
            'xmlns:', 'http://www.w3.org',
            'http://schemas.android.com'
        ]
        
        for fp in false_positives:
            if url.startswith(fp):
                return False
                
        return True
    
    def _classify_url(self, url: str) -> str:
        """Classify URL type based on content"""
        if '/api/' in url.lower():
            return 'api_endpoint'
        elif 'cdn' in url.lower() or '/assets/' in url.lower():
            return 'cdn'
        elif 'analytics' in url.lower() or 'track' in url.lower():
            return 'analytics'
        elif 'ws://' in url.lower() or 'wss://' in url.lower():
            return 'websocket'
        else:
            return 'general'
    
    def _deduplicate_urls(self, urls: List[URLInfoSchema]) -> List[URLInfoSchema]:
        """Remove duplicate URLs while preserving context"""
        seen_urls = {}
        unique_urls = []
        
        for url_info in urls:
            if url_info.url not in seen_urls:
                seen_urls[url_info.url] = True
                unique_urls.append(url_info)
        
        return unique_urls
    
    def _extract_regex(self, text: str, pattern: str) -> Optional[str]:
        """Extract first match from regex pattern"""
        match = re.search(pattern, text)
        return match.group(1) if match else None
    
    async def _calculate_file_hash(self, file_path: str, algorithm: str = 'md5') -> str:
        """Calculate file hash"""
        hash_obj = hashlib.new(algorithm)
        
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_obj.update(chunk)
        
        return hash_obj.hexdigest()