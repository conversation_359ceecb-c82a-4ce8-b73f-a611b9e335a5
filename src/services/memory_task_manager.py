"""
任务管理服务 - 内存队列版本
"""
import asyncio
from typing import Optional, Dict, Any
from datetime import datetime
import uuid
import logging

from src.workers.memory_queue import submit_task, is_task_ready, get_task_result
from src.models.schemas import AnalysisRequest, AnalysisResponse, TaskStatusResponse, ProgressInfo

logger = logging.getLogger(__name__)


class MemoryTaskManager:
    """内存任务管理器"""
    
    def __init__(self):
        self.active_tasks: Dict[str, Dict[str, Any]] = {}
        
    async def submit_analysis_task(self, request: AnalysisRequest) -> str:
        """
        提交分析任务
        
        Args:
            request: 分析请求
            
        Returns:
            str: 任务ID
        """
        task_id = str(uuid.uuid4())
        
        # 记录任务信息
        self.active_tasks[task_id] = {
            'task_id': task_id,
            'status': 'submitted',
            'created_at': datetime.now(),
            'apk_path': request.file_path,
            'options': request.options,
            'static_task_id': None,
            'dynamic_task_id': None,
            'merge_task_id': None,
            'static_result': None,
            'dynamic_result': None,
            'final_result': None,
            'error': None
        }
        
        logger.info(f"Analysis task submitted: {task_id}")
        
        # 启动分析流程
        asyncio.create_task(self._run_analysis_pipeline(task_id, request))
        
        return task_id
        
    async def _run_analysis_pipeline(self, task_id: str, request: AnalysisRequest):
        """运行分析流水线"""
        task_info = self.active_tasks[task_id]
        
        try:
            task_info['status'] = 'processing'
            
            # 第1步：提交静态分析任务
            logger.info(f"Submitting static analysis for task: {task_id}")
            static_task_id = await submit_task("analyze_apk_static", task_id, request.file_path)
            task_info['static_task_id'] = static_task_id
            
            # 第2步：提交动态分析任务（如果启用）
            dynamic_task_id = None
            if request.options.get("enable_dynamic", True):
                logger.info(f"Submitting dynamic analysis for task: {task_id}")
                dynamic_task_id = await submit_task("analyze_apk_dynamic", task_id, request.file_path)
                task_info['dynamic_task_id'] = dynamic_task_id
            
            # 第3步：等待任务完成
            await self._wait_for_analysis_completion(task_id)
            
        except Exception as e:
            logger.error(f"Analysis pipeline failed for task {task_id}: {e}")
            task_info['status'] = 'failed'
            task_info['error'] = str(e)
            
    async def _wait_for_analysis_completion(self, task_id: str):
        """等待分析任务完成"""
        task_info = self.active_tasks[task_id]
        
        # 等待静态分析完成
        while not is_task_ready(task_info['static_task_id']):
            await asyncio.sleep(1)
            
        static_result = get_task_result(task_info['static_task_id'])
        task_info['static_result'] = static_result
        
        # 等待动态分析完成（如果有）
        dynamic_result = {'task_id': task_id, 'status': 'skipped', 'network_requests': []}
        if task_info['dynamic_task_id']:
            while not is_task_ready(task_info['dynamic_task_id']):
                await asyncio.sleep(1)

            dynamic_result = get_task_result(task_info['dynamic_task_id'])
            task_info['dynamic_result'] = dynamic_result
        else:
            # 如果没有动态分析任务，设置默认结果
            task_info['dynamic_result'] = dynamic_result
            
        # 第4步：合并结果
        logger.info(f"Merging results for task: {task_id}")
        merge_task_id = await submit_task("merge_analysis_results", task_id, static_result, dynamic_result)
        task_info['merge_task_id'] = merge_task_id
        
        # 等待合并完成
        while not is_task_ready(merge_task_id):
            await asyncio.sleep(1)
            
        final_result = get_task_result(merge_task_id)
        task_info['final_result'] = final_result
        task_info['status'] = 'completed'
        
        logger.info(f"Analysis pipeline completed for task: {task_id}")
        
    async def get_task_status(self, task_id: str) -> Optional[TaskStatusResponse]:
        """获取任务状态"""
        if task_id not in self.active_tasks:
            return None

        task_info = self.active_tasks[task_id]

        return TaskStatusResponse(
            task_id=task_id,
            status=task_info['status'],
            progress=self._calculate_progress_info(task_info),
            estimated_remaining=self._estimate_remaining_time(task_info)
        )

    def _calculate_progress_info(self, task_info: Dict[str, Any]) -> ProgressInfo:
        """计算进度信息"""

        static_status = "completed" if task_info['static_result'] else (
            "processing" if task_info['status'] == 'processing' else "pending"
        )

        dynamic_status = "completed" if task_info['dynamic_result'] else (
            "processing" if task_info['static_result'] and task_info['status'] == 'processing' else "pending"
        )

        return ProgressInfo(
            static_analysis=static_status,
            dynamic_analysis=dynamic_status
        )

    def _estimate_remaining_time(self, task_info: Dict[str, Any]) -> Optional[int]:
        """估算剩余时间"""
        if task_info['status'] in ['completed', 'failed']:
            return 0
        elif task_info['static_result'] and task_info['dynamic_result']:
            return 10  # 合并结果大约需要10秒
        elif task_info['static_result']:
            return 30  # 动态分析大约需要30秒
        else:
            return 60  # 静态分析大约需要60秒
            
    def _get_status_message(self, task_info: Dict[str, Any]) -> str:
        """获取状态消息"""
        if task_info['status'] == 'completed':
            return "Analysis completed successfully"
        elif task_info['status'] == 'failed':
            return f"Analysis failed: {task_info['error']}"
        elif task_info['final_result']:
            return "Merging results..."
        elif task_info['static_result'] and task_info['dynamic_result']:
            return "Both analyses completed, merging results..."
        elif task_info['static_result']:
            return "Static analysis completed, running dynamic analysis..."
        elif task_info['status'] == 'running':
            return "Running static and dynamic analysis..."
        else:
            return "Task submitted, waiting to start..."
            
    async def get_task_result(self, task_id: str) -> Optional[AnalysisResponse]:
        """获取任务结果"""
        if task_id not in self.active_tasks:
            return None

        task_info = self.active_tasks[task_id]

        if task_info['status'] != 'completed' or not task_info['final_result']:
            return None

        final_result = task_info['final_result']
        if not final_result or final_result.get('status') != 'completed':
            return None

        merged_data = final_result.get('merged_result')
        if not merged_data:
            return None

        return AnalysisResponse(**merged_data)
        
    def cleanup_old_tasks(self, max_age_hours: int = 24):
        """清理旧任务"""
        cutoff_time = datetime.now().timestamp() - (max_age_hours * 3600)
        
        to_remove = []
        for task_id, task_info in self.active_tasks.items():
            if task_info['created_at'].timestamp() < cutoff_time:
                to_remove.append(task_id)
                
        for task_id in to_remove:
            del self.active_tasks[task_id]
            
        if to_remove:
            logger.info(f"Cleaned up {len(to_remove)} old tasks")


# 全局任务管理器实例
memory_task_manager = MemoryTaskManager()