"""
文件处理工具
"""
from fastapi import UploadFile, HTTPException
import hashlib
import os
from pathlib import Path
import aiofiles
import uuid
from src.config import settings
import logging

logger = logging.getLogger(__name__)


class FileHandler:
    """文件处理类"""
    
    def __init__(self):
        self.storage_path = Path(settings.storage_path)
        self.apk_storage = self.storage_path / "apks"
        self.temp_storage = self.storage_path / "temp"
        
        # 确保目录存在
        self.apk_storage.mkdir(parents=True, exist_ok=True)
        self.temp_storage.mkdir(parents=True, exist_ok=True)
    
    async def validate_file(self, file: UploadFile) -> None:
        """
        验证上传的文件
        
        Args:
            file: 上传的文件
            
        Raises:
            HTTPException: 文件验证失败
        """
        # 检查文件名
        if not file.filename:
            raise HTTPException(status_code=400, detail="No filename provided")
        
        # 检查文件扩展名
        file_extension = Path(file.filename).suffix.lower()
        if file_extension not in settings.allowed_extensions:
            raise HTTPException(
                status_code=400, 
                detail=f"File type not allowed. Allowed types: {', '.join(settings.allowed_extensions)}"
            )
        
        # 检查文件大小
        if file.size and file.size > settings.max_file_size:
            raise HTTPException(
                status_code=400, 
                detail=f"File too large. Maximum size: {settings.max_file_size} bytes"
            )
        
        # 重置文件指针
        await file.seek(0)
        
        # 读取文件头检查是否为APK文件
        header = await file.read(4)
        await file.seek(0)
        
        # APK文件应该以ZIP文件头开始（PK\x03\x04）
        if header != b'PK\x03\x04':
            raise HTTPException(
                status_code=400, 
                detail="Invalid APK file format"
            )
    
    async def save_file(self, file: UploadFile, task_id: str) -> tuple[str, str, str]:
        """
        保存上传的文件
        
        Args:
            file: 上传的文件
            task_id: 任务ID
            
        Returns:
            tuple: (文件路径, MD5, SHA256)
        """
        # 生成文件名
        file_extension = Path(file.filename).suffix
        filename = f"{task_id}{file_extension}"
        file_path = self.apk_storage / filename
        
        # 计算哈希值
        md5_hash = hashlib.md5()
        sha256_hash = hashlib.sha256()
        
        # 保存文件并计算哈希
        async with aiofiles.open(file_path, 'wb') as f:
            while chunk := await file.read(8192):
                await f.write(chunk)
                md5_hash.update(chunk)
                sha256_hash.update(chunk)
        
        md5_value = md5_hash.hexdigest()
        sha256_value = sha256_hash.hexdigest()
        
        logger.info(f"File saved: {file_path} (MD5: {md5_value}, SHA256: {sha256_value})")
        
        return str(file_path), md5_value, sha256_value
    
    async def delete_file(self, file_path: str) -> None:
        """
        删除文件
        
        Args:
            file_path: 文件路径
        """
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"File deleted: {file_path}")
        except Exception as e:
            logger.error(f"Failed to delete file {file_path}: {e}")
    
    def get_file_info(self, file_path: str) -> dict:
        """
        获取文件信息

        Args:
            file_path: 文件路径

        Returns:
            dict: 文件信息
        """
        if not os.path.exists(file_path):
            return {}

        stat = os.stat(file_path)
        return {
            "size": stat.st_size,
            "created_time": stat.st_ctime,
            "modified_time": stat.st_mtime,
        }

    async def cleanup_file(self, file_path: str) -> None:
        """
        清理文件（异步版本的delete_file）

        Args:
            file_path: 文件路径
        """
        await self.delete_file(file_path)