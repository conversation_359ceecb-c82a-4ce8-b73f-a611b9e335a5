"""
动态分析任务 - 内存队列版本
"""
import asyncio
from src.workers.memory_queue import task
from src.services.dynamic_analyzer import DynamicAnalyzer, AnalysisTools
import logging

logger = logging.getLogger(__name__)


@task("analyze_apk_dynamic")
async def analyze_apk_dynamic(task_id: str, apk_path: str):
    """
    动态分析任务

    Args:
        task_id: 任务ID
        apk_path: APK文件路径

    Returns:
        dict: 分析结果
    """
    logger.info(f"Starting dynamic analysis for task: {task_id}")

    try:
        # 配置正确的工具路径
        tools = AnalysisTools(
            adb="/Users/<USER>/Desktop/project/apk_detect/platform-tools/adb",
            aapt="/Users/<USER>/Desktop/project/apk_detect/android-14/aapt"
        )

        # 创建动态分析器
        analyzer = DynamicAnalyzer(tools=tools)
        
        # 执行分析
        analysis_result = await analyzer.analyze(apk_path, timeout=60)
        
        # 转换结果为字典格式
        result = {
            'task_id': task_id,
            'status': 'completed',
            'analysis_type': 'dynamic',
            'network_requests': [req.dict() for req in analysis_result.network_requests],
            'ui_interactions': analysis_result.ui_interactions,
            'runtime_info': analysis_result.runtime_info.dict() if analysis_result.runtime_info else None,
            'error': None
        }
        
        logger.info(f"Dynamic analysis completed for task: {task_id}")
        return result
        
    except Exception as e:
        logger.error(f"Dynamic analysis failed for task {task_id}: {e}")
        return {
            'task_id': task_id,
            'status': 'failed',
            'analysis_type': 'dynamic',
            'error': str(e),
            'network_requests': [],
            'ui_interactions': [],
            'runtime_info': None
        }