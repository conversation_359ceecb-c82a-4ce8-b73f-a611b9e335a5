"""
结果合并任务 - 内存队列版本
"""
import asyncio
from src.workers.memory_queue import task
from src.services.result_merger import ResultMerger
import logging

logger = logging.getLogger(__name__)


@task("merge_analysis_results")
async def merge_analysis_results(task_id: str, static_result: dict, dynamic_result: dict):
    """
    合并分析结果任务
    
    Args:
        task_id: 任务ID
        static_result: 静态分析结果
        dynamic_result: 动态分析结果
        
    Returns:
        dict: 合并后的结果
    """
    logger.info(f"Starting result merging for task: {task_id}")
    
    try:
        # 创建结果合并器
        merger = ResultMerger()
        
        # 执行合并
        merged_result = await merger.merge_results(task_id, static_result, dynamic_result)
        
        result = {
            'task_id': task_id,
            'status': 'completed',
            'merged_result': merged_result,
            'error': None
        }
        
        logger.info(f"Result merging completed for task: {task_id}")
        return result
        
    except Exception as e:
        logger.error(f"Result merging failed for task {task_id}: {e}")
        return {
            'task_id': task_id,
            'status': 'failed',
            'error': str(e),
            'merged_result': None
        }