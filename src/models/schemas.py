"""
Pydantic数据模型和API schemas
"""
from pydantic import BaseModel, Field, ConfigDict
from typing import Optional, Dict, List, Any, Union
from datetime import datetime
from enum import Enum
import uuid


class TaskStatus(str, Enum):
    """任务状态枚举"""
    SUBMITTED = "submitted"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class AnalysisType(str, Enum):
    """分析类型枚举"""
    STATIC = "static"
    DYNAMIC = "dynamic"


class Priority(str, Enum):
    """任务优先级枚举"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"


class AnalysisOptions(BaseModel):
    """分析选项"""
    enable_static: bool = Field(default=True, description="是否启用静态分析")
    enable_dynamic: bool = Field(default=True, description="是否启用动态分析")
    timeout: int = Field(default=300, ge=60, le=1800, description="分析超时时间(秒)")
    priority: Priority = Field(default=Priority.NORMAL, description="任务优先级")
    
    model_config = ConfigDict(use_enum_values=True)


class AnalysisRequest(BaseModel):
    """分析请求"""
    file_path: str = Field(..., description="APK文件路径")
    options: Dict[str, Any] = Field(default_factory=dict, description="分析选项")


class AnalysisResponse(BaseModel):
    """分析响应"""
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="分析状态")
    basic_info: Dict[str, Any] = Field(..., description="基础信息")
    certificate: Dict[str, Any] = Field(..., description="证书信息")
    urls: List[Dict[str, Any]] = Field(..., description="提取的URL")
    analysis_summary: Dict[str, Any] = Field(..., description="分析摘要")


class TaskSubmitRequest(BaseModel):
    """任务提交请求"""
    options: Optional[AnalysisOptions] = Field(default_factory=AnalysisOptions)


class TaskSubmitResponse(BaseModel):
    """任务提交响应"""
    task_id: str = Field(..., description="任务ID")
    status: TaskStatus = Field(..., description="任务状态")
    estimated_time: int = Field(..., description="预估完成时间(秒)")
    created_at: datetime = Field(..., description="创建时间")
    
    model_config = ConfigDict(use_enum_values=True)


class ProgressInfo(BaseModel):
    """进度信息"""
    static_analysis: str = Field(..., description="静态分析状态")
    dynamic_analysis: str = Field(..., description="动态分析状态")


class TaskStatusResponse(BaseModel):
    """任务状态响应"""
    task_id: str = Field(..., description="任务ID")
    status: TaskStatus = Field(..., description="任务状态")
    progress: ProgressInfo = Field(..., description="进度信息")
    estimated_remaining: Optional[int] = Field(None, description="剩余时间(秒)")
    
    model_config = ConfigDict(use_enum_values=True)


class BasicInfo(BaseModel):
    """APK基础信息"""
    package_name: str = Field(..., description="包名")
    app_name: str = Field(..., description="应用名")
    version_name: str = Field(..., description="版本名")
    version_code: int = Field(..., description="版本号")
    md5: str = Field(..., description="文件MD5")
    sha256: str = Field(..., description="文件SHA256")
    file_size: int = Field(..., description="文件大小")


class CertificateInfo(BaseModel):
    """证书信息"""
    public_key_sha256: str = Field(..., description="公钥SHA256")
    subject: str = Field(..., description="证书主题")
    valid_from: Optional[datetime] = Field(None, description="有效期开始")
    valid_to: Optional[datetime] = Field(None, description="有效期结束")
    signature_algorithm: str = Field(..., description="签名算法")


class URLInfo(BaseModel):
    """URL信息"""
    url: str = Field(..., description="URL地址")
    type: str = Field(..., description="URL类型")
    context: Optional[str] = Field(None, description="上下文信息")
    file_location: Optional[str] = Field(None, description="文件位置")
    method: Optional[str] = Field(None, description="HTTP方法")
    timestamp: Optional[datetime] = Field(None, description="捕获时间")
    request_headers: Optional[Dict[str, Any]] = Field(None, description="请求头")
    response_code: Optional[int] = Field(None, description="响应状态码")


class AnalysisMetadata(BaseModel):
    """分析元数据"""
    static_duration: Optional[float] = Field(None, description="静态分析耗时")
    dynamic_duration: Optional[float] = Field(None, description="动态分析耗时")
    total_duration: Optional[float] = Field(None, description="总耗时")
    android_version: Optional[str] = Field(None, description="Android版本")
    analysis_timestamp: datetime = Field(..., description="分析时间")


class AnalysisResult(BaseModel):
    """分析结果"""
    basic_info: BasicInfo = Field(..., description="基础信息")
    certificate: CertificateInfo = Field(..., description="证书信息")
    static_urls: List[URLInfo] = Field(default_factory=list, description="静态URL列表")
    dynamic_urls: List[URLInfo] = Field(default_factory=list, description="动态URL列表")
    analysis_metadata: AnalysisMetadata = Field(..., description="分析元数据")


class TaskResultResponse(BaseModel):
    """任务结果响应"""
    task_id: str = Field(..., description="任务ID")
    status: TaskStatus = Field(..., description="任务状态")
    result: Optional[Union[AnalysisResult, Dict[str, Any]]] = Field(None, description="分析结果")
    error_message: Optional[str] = Field(None, description="错误信息")
    
    model_config = ConfigDict(use_enum_values=True)


class BatchSubmitResponse(BaseModel):
    """批量提交响应"""
    batch_id: str = Field(..., description="批次ID")
    task_ids: List[str] = Field(..., description="任务ID列表")
    total_count: int = Field(..., description="总任务数")
    estimated_time: int = Field(..., description="预估完成时间")


class HealthStatus(BaseModel):
    """健康检查状态"""
    status: str = Field(..., description="整体状态")
    services: Dict[str, str] = Field(..., description="服务状态")
    queue_size: int = Field(..., description="队列长度")
    active_tasks: int = Field(..., description="活跃任务数")


class ErrorResponse(BaseModel):
    """错误响应"""
    error: str = Field(..., description="错误类型")
    message: str = Field(..., description="错误消息")
    details: Optional[Dict[str, Any]] = Field(None, description="详细信息")


# Additional schemas for StaticAnalyzer
class BasicInfoSchema(BaseModel):
    """APK基础信息Schema"""
    package_name: str
    app_name: str
    version_name: str
    version_code: int
    min_sdk_version: Optional[int] = None
    target_sdk_version: Optional[int] = None
    file_size: int
    md5_hash: str
    sha256_hash: str
    permissions: Optional[List[str]] = None


class CertificateSchema(BaseModel):
    """证书信息Schema"""
    public_key_sha256: str
    subject: str
    issuer: str
    signature_algorithm: str
    valid_from: Optional[datetime] = None
    valid_to: Optional[datetime] = None


class URLInfoSchema(BaseModel):
    """URL信息Schema"""
    url: str
    type: str
    file_location: str
    context: str


class StaticAnalysisResult(BaseModel):
    """静态分析结果"""
    basic_info: BasicInfoSchema
    certificate: CertificateSchema
    static_urls: List[URLInfoSchema]


class DynamicAnalysisResult(BaseModel):
    """动态分析结果"""
    dynamic_urls: List[URLInfoSchema]
    duration: float
    android_version: str
    network_requests: Optional[List[dict]] = None
    runtime_info: Optional[dict] = None
    error: Optional[str] = None