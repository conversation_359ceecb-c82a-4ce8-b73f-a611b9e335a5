"""
本地动态分析API路由
提供本地模拟器动态分析功能的API端点
"""
from fastapi import APIRouter, File, UploadFile, Depends, HTTPException, Form, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, Dict, Any
import uuid
import json
import logging
import asyncio
from pathlib import Path

from src.models import get_db_session, TaskStatus
from src.services.local_dynamic_analyzer import LocalDynamicAnalyzer
from src.services.local_emulator_manager import get_emulator_manager, cleanup_global_manager
from src.utils.file_handler import FileHandler

router = APIRouter()
logger = logging.getLogger(__name__)

# 全局分析器实例
_local_analyzer: Optional[LocalDynamicAnalyzer] = None


def get_local_analyzer() -> LocalDynamicAnalyzer:
    """获取本地动态分析器实例"""
    global _local_analyzer
    if _local_analyzer is None:
        _local_analyzer = LocalDynamicAnalyzer()
    return _local_analyzer


@router.post("/local-dynamic/analyze")
async def analyze_apk_local(
    file: UploadFile = File(..., description="APK文件"),
    timeout: Optional[int] = Form(default=300, description="分析超时时间（秒）"),
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    """
    使用本地模拟器进行APK动态分析
    
    Args:
        file: APK文件
        timeout: 分析超时时间
        
    Returns:
        分析结果
    """
    task_id = str(uuid.uuid4())
    
    try:
        # 验证文件
        file_handler = FileHandler()
        await file_handler.validate_file(file)
        
        # 保存文件
        file_path = await file_handler.save_file(file, task_id)
        
        logger.info(f"Starting local dynamic analysis for task {task_id}")
        
        # 获取分析器
        analyzer = get_local_analyzer()
        
        # 执行分析
        result = await analyzer.analyze(file_path, timeout)
        
        # 添加清理任务
        background_tasks.add_task(file_handler.cleanup_file, file_path)
        
        # 构建响应
        response = {
            "task_id": task_id,
            "status": "completed" if not result.error else "failed",
            "analysis_type": "local_dynamic",
            "result": {
                "dynamic_urls": result.dynamic_urls,
                "network_requests": [req.dict() for req in result.network_requests] if result.network_requests else [],
                "runtime_info": result.runtime_info.dict() if result.runtime_info else None,
                "android_version": result.android_version,
                "duration": result.duration,
                "error": result.error
            }
        }
        
        logger.info(f"Local dynamic analysis completed for task {task_id}")
        return response
        
    except Exception as e:
        logger.error(f"Local dynamic analysis failed for task {task_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Analysis failed: {str(e)}"
        )


@router.get("/local-dynamic/emulator/status")
async def get_emulator_status():
    """获取本地模拟器状态"""
    try:
        emulator_manager = get_emulator_manager()
        status = emulator_manager.get_status_summary()
        
        # 添加实例详情
        instances = {}
        for name, instance in emulator_manager.get_all_instances().items():
            instances[name] = instance.to_dict()
        
        status["instances"] = instances
        return status
        
    except Exception as e:
        logger.error(f"Failed to get emulator status: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get status: {str(e)}"
        )


@router.post("/local-dynamic/emulator/start")
async def start_emulator(
    avd_name: Optional[str] = Form(default="test_avd", description="AVD名称"),
    timeout: Optional[int] = Form(default=120, description="启动超时时间")
):
    """启动本地模拟器实例"""
    try:
        emulator_manager = get_emulator_manager()
        
        # 启动监控（如果还没启动）
        await emulator_manager.start_monitoring()
        
        # 启动模拟器
        instance = await emulator_manager.start_emulator(avd_name, timeout=timeout)
        
        if instance:
            return {
                "success": True,
                "message": f"Emulator started successfully",
                "instance": instance.to_dict()
            }
        else:
            raise HTTPException(
                status_code=500,
                detail="Failed to start emulator"
            )
            
    except Exception as e:
        logger.error(f"Failed to start emulator: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to start emulator: {str(e)}"
        )


@router.post("/local-dynamic/emulator/stop/{instance_name}")
async def stop_emulator(
    instance_name: str,
    force: Optional[bool] = Form(default=False, description="是否强制停止")
):
    """停止指定的模拟器实例"""
    try:
        emulator_manager = get_emulator_manager()
        
        success = await emulator_manager.stop_emulator(instance_name, force)
        
        if success:
            return {
                "success": True,
                "message": f"Emulator {instance_name} stopped successfully"
            }
        else:
            raise HTTPException(
                status_code=500,
                detail=f"Failed to stop emulator {instance_name}"
            )
            
    except Exception as e:
        logger.error(f"Failed to stop emulator {instance_name}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to stop emulator: {str(e)}"
        )


@router.post("/local-dynamic/emulator/stop-all")
async def stop_all_emulators(
    force: Optional[bool] = Form(default=False, description="是否强制停止")
):
    """停止所有模拟器实例"""
    try:
        emulator_manager = get_emulator_manager()
        
        results = await emulator_manager.stop_all_emulators(force)
        
        success_count = sum(1 for success in results.values() if success)
        total_count = len(results)
        
        return {
            "success": success_count == total_count,
            "message": f"Stopped {success_count}/{total_count} emulators",
            "results": results
        }
        
    except Exception as e:
        logger.error(f"Failed to stop all emulators: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to stop emulators: {str(e)}"
        )


@router.get("/local-dynamic/emulator/avds")
async def list_available_avds():
    """列出可用的AVD"""
    try:
        emulator_manager = get_emulator_manager()
        avds = await emulator_manager.list_available_avds()
        
        return {
            "avds": avds,
            "count": len(avds)
        }
        
    except Exception as e:
        logger.error(f"Failed to list AVDs: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list AVDs: {str(e)}"
        )


@router.post("/local-dynamic/emulator/create-avd")
async def create_avd(
    avd_name: str = Form(..., description="AVD名称")
):
    """创建新的AVD"""
    try:
        emulator_manager = get_emulator_manager()
        
        success = await emulator_manager.create_default_avd(avd_name)
        
        if success:
            return {
                "success": True,
                "message": f"AVD {avd_name} created successfully"
            }
        else:
            raise HTTPException(
                status_code=500,
                detail=f"Failed to create AVD {avd_name}"
            )
            
    except Exception as e:
        logger.error(f"Failed to create AVD {avd_name}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create AVD: {str(e)}"
        )


@router.post("/local-dynamic/cleanup")
async def cleanup_resources():
    """清理所有本地动态分析资源"""
    try:
        # 清理全局模拟器管理器
        await cleanup_global_manager()
        
        # 重置全局分析器
        global _local_analyzer
        _local_analyzer = None
        
        return {
            "success": True,
            "message": "Resources cleaned up successfully"
        }
        
    except Exception as e:
        logger.error(f"Failed to cleanup resources: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to cleanup: {str(e)}"
        )


@router.get("/local-dynamic/health")
async def health_check():
    """本地动态分析健康检查"""
    try:
        emulator_manager = get_emulator_manager()
        status = emulator_manager.get_status_summary()
        
        # 检查关键组件
        health_status = {
            "status": "healthy",
            "emulator_manager": "available",
            "android_tools": {
                "adb": "available" if Path(emulator_manager.adb_path).exists() else "missing",
                "emulator": "available" if Path(emulator_manager.emulator_path).exists() else "missing"
            },
            "running_instances": len(emulator_manager.get_running_instances()),
            "total_instances": len(emulator_manager.get_all_instances())
        }
        
        # 检查是否有关键问题
        if not Path(emulator_manager.adb_path).exists():
            health_status["status"] = "unhealthy"
            health_status["issues"] = health_status.get("issues", []) + ["ADB not found"]
        
        if not Path(emulator_manager.emulator_path).exists():
            health_status["status"] = "unhealthy"
            health_status["issues"] = health_status.get("issues", []) + ["Emulator not found"]
        
        return health_status
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e)
        }
