"""
简化版APK分析API - 使用内存任务队列
"""
from fastapi import APIRouter, File, UploadFile, HTTPException, Form
from typing import Optional
import json
import uuid
import logging

from src.models.schemas import AnalysisRequest, AnalysisResponse, TaskStatusResponse
from src.services.memory_task_manager import memory_task_manager
from src.utils.file_handler import FileHandler

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/simple/analyze")
async def submit_simple_analysis(
    file: UploadFile = File(..., description="APK文件"),
    options: Optional[str] = Form(default=None, description="分析选项JSON")
):
    """
    提交APK分析任务 - 简化版
    """
    # 验证文件
    file_handler = FileHandler()
    
    try:
        await file_handler.validate_file(file)
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
    
    # 生成任务ID
    task_id = str(uuid.uuid4())

    # 保存文件
    try:
        saved_path, md5_hash, sha256_hash = await file_handler.save_file(file, task_id)
        logger.info(f"File saved to: {saved_path}")
    except Exception as e:
        logger.error(f"Failed to save file: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to save file: {str(e)}")
    
    # 解析选项
    analysis_options = {}
    if options:
        try:
            analysis_options = json.loads(options)
        except json.JSONDecodeError as e:
            raise HTTPException(status_code=400, detail=f"Invalid options JSON: {str(e)}")
    
    # 创建分析请求
    request = AnalysisRequest(
        file_path=saved_path,
        options=analysis_options
    )
    
    try:
        # 提交任务
        task_id = await memory_task_manager.submit_analysis_task(request)
        
        return {
            "task_id": task_id,
            "status": "submitted",
            "message": "Analysis task submitted successfully"
        }
        
    except Exception as e:
        logger.error(f"Failed to submit task: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to submit task: {str(e)}")


@router.get("/simple/analyze/{task_id}/status")
async def get_simple_task_status(task_id: str):
    """
    获取任务状态 - 简化版
    """
    try:
        uuid.UUID(task_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid task ID format")
    
    status = await memory_task_manager.get_task_status(task_id)
    
    if not status:
        raise HTTPException(status_code=404, detail="Task not found")
    
    return status


@router.get("/simple/analyze/{task_id}/result")
async def get_simple_task_result(task_id: str):
    """
    获取分析结果 - 简化版
    """
    try:
        uuid.UUID(task_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid task ID format")
    
    result = await memory_task_manager.get_task_result(task_id)
    
    if not result:
        # 检查任务是否存在但未完成
        status = await memory_task_manager.get_task_status(task_id)
        if status:
            if status.status == 'failed':
                raise HTTPException(status_code=500, detail="Analysis failed")
            else:
                raise HTTPException(status_code=202, detail="Analysis still in progress")
        else:
            raise HTTPException(status_code=404, detail="Task not found")
    
    return result


@router.delete("/simple/analyze/{task_id}")
async def cancel_simple_task(task_id: str):
    """
    取消任务 - 简化版
    """
    try:
        uuid.UUID(task_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid task ID format")
    
    # 对于内存队列，我们只是将任务标记为取消
    status = await memory_task_manager.get_task_status(task_id)
    
    if not status:
        raise HTTPException(status_code=404, detail="Task not found")
    
    if status.status in ['completed', 'failed']:
        raise HTTPException(status_code=400, detail="Cannot cancel completed or failed task")
    
    # 简单地从活动任务中删除
    if task_id in memory_task_manager.active_tasks:
        memory_task_manager.active_tasks[task_id]['status'] = 'cancelled'
    
    return {"message": "Task cancelled successfully"}