{"test_time": "2025-09-09 15:50:24", "package": "com.yjzx.yjzx2017", "android_version": "7.0 (<PERSON> 24)", "total_activities": 340, "tested_activities": 20, "activities": [{"index": 1, "name": "CameraNewActivity", "full_path": "com.yjzx.yjzx2017.controller.camera.CameraNewActivity", "launch_success": true, "strategies_tried": ["FLAG_NEW_TASK"], "successful_strategy": "FLAG_NEW_TASK", "output_snippet": "Starting: Intent { flg=0x10000000 cmp=com.yjzx.yjzx2017/.controller.camera.CameraNewActivity }"}, {"index": 2, "name": "CameraTakeShowActivity", "full_path": "com.yjzx.yjzx2017.controller.camera.CameraTakeShowActivity", "launch_success": true, "strategies_tried": ["FLAG_NEW_TASK"], "successful_strategy": "FLAG_NEW_TASK", "output_snippet": "Starting: Intent { flg=0x10000000 cmp=com.yjzx.yjzx2017/.controller.camera.CameraTakeShowActivity }"}, {"index": 3, "name": "CameraXTestActivity", "full_path": "com.yjzx.yjzx2017.controller.selfAuction.CameraXTestActivity", "launch_success": true, "strategies_tried": ["FLAG_NEW_TASK"], "successful_strategy": "FLAG_NEW_TASK", "output_snippet": "Starting: Intent { flg=0x10000000 cmp=com.yjzx.yjzx2017/.controller.selfAuction.CameraXTestActivity }"}, {"index": 4, "name": "AuctionSelfBuyActivity", "full_path": "com.yjzx.yjzx2017.controller.activity.auction.AuctionSelfBuyActivity", "launch_success": true, "strategies_tried": ["FLAG_NEW_TASK"], "successful_strategy": "FLAG_NEW_TASK", "output_snippet": "Starting: Intent { flg=0x10000000 cmp=com.yjzx.yjzx2017/.controller.activity.auction.AuctionSelfBuyActivity }"}, {"index": 5, "name": "SelfAuctionAddActivity", "full_path": "com.yjzx.yjzx2017.controller.selfAuction.SelfAuctionAddActivity", "launch_success": true, "strategies_tried": ["FLAG_NEW_TASK"], "successful_strategy": "FLAG_NEW_TASK", "output_snippet": "Starting: Intent { flg=0x10000000 cmp=com.yjzx.yjzx2017/.controller.selfAuction.SelfAuctionAddActivity }"}, {"index": 6, "name": "SelfAuctionConfirmActivity", "full_path": "com.yjzx.yjzx2017.controller.selfAuction.SelfAuctionConfirmActivity", "launch_success": true, "strategies_tried": ["FLAG_NEW_TASK"], "successful_strategy": "FLAG_NEW_TASK", "output_snippet": "Starting: Intent { flg=0x10000000 cmp=com.yjzx.yjzx2017/.controller.selfAuction.SelfAuctionConfirmActivity }"}, {"index": 7, "name": "SelfAuctionScanCodeActivity", "full_path": "com.yjzx.yjzx2017.controller.selfAuction.SelfAuctionScanCodeActivity", "launch_success": true, "strategies_tried": ["FLAG_NEW_TASK"], "successful_strategy": "FLAG_NEW_TASK", "output_snippet": "Starting: Intent { flg=0x10000000 cmp=com.yjzx.yjzx2017/.controller.selfAuction.SelfAuctionScanCodeActivity }"}, {"index": 8, "name": "SelfAuctionManageActivity", "full_path": "com.yjzx.yjzx2017.controller.selfAuction.SelfAuctionManageActivity", "launch_success": true, "strategies_tried": ["FLAG_NEW_TASK"], "successful_strategy": "FLAG_NEW_TASK", "output_snippet": "Starting: Intent { flg=0x10000000 cmp=com.yjzx.yjzx2017/.controller.selfAuction.SelfAuctionManageActivity }"}, {"index": 9, "name": "AddressCompanyActivity", "full_path": "com.yjzx.yjzx2017.controller.activity.setting.AddressCompanyActivity", "launch_success": true, "strategies_tried": ["FLAG_NEW_TASK"], "successful_strategy": "FLAG_NEW_TASK", "output_snippet": "Starting: Intent { flg=0x10000000 cmp=com.yjzx.yjzx2017/.controller.activity.setting.AddressCompanyActivity }"}, {"index": 10, "name": "SplashActivity", "full_path": "com.yjzx.yjzx2017.controller.activity.splash.SplashActivity", "launch_success": true, "strategies_tried": ["FLAG_NEW_TASK"], "successful_strategy": "FLAG_NEW_TASK", "output_snippet": "Starting: Intent { flg=0x10000000 cmp=com.yjzx.yjzx2017/.controller.activity.splash.SplashActivity }"}, {"index": 11, "name": "MainActivity", "full_path": "com.yjzx.yjzx2017.controller.activity.MainActivity", "launch_success": true, "strategies_tried": ["FLAG_NEW_TASK"], "successful_strategy": "FLAG_NEW_TASK", "output_snippet": "Starting: Intent { flg=0x10000000 cmp=com.yjzx.yjzx2017/.controller.activity.MainActivity }"}, {"index": 12, "name": "MiddleActivity", "full_path": "com.yjzx.yjzx2017.common.MiddleActivity", "launch_success": true, "strategies_tried": ["FLAG_NEW_TASK"], "successful_strategy": "FLAG_NEW_TASK", "output_snippet": "Starting: Intent { flg=0x10000000 cmp=com.yjzx.yjzx2017/.common.MiddleActivity }"}, {"index": 13, "name": "UriSchemeProcessActivity", "full_path": "com.yjzx.yjzx2017.common.UriSchemeProcessActivity", "launch_success": true, "strategies_tried": ["FLAG_NEW_TASK"], "successful_strategy": "FLAG_NEW_TASK", "output_snippet": "Starting: Intent { flg=0x10000000 cmp=com.yjzx.yjzx2017/.common.UriSchemeProcessActivity }"}, {"index": 14, "name": "LoginActivity", "full_path": "com.yjzx.yjzx2017.controller.login.activity.LoginActivity", "launch_success": true, "strategies_tried": ["FLAG_NEW_TASK"], "successful_strategy": "FLAG_NEW_TASK", "output_snippet": "Starting: Intent { flg=0x10000000 cmp=com.yjzx.yjzx2017/.controller.login.activity.LoginActivity }"}, {"index": 15, "name": "LoginSmsCodeActivity", "full_path": "com.yjzx.yjzx2017.controller.login.activity.LoginSmsCodeActivity", "launch_success": true, "strategies_tried": ["FLAG_NEW_TASK"], "successful_strategy": "FLAG_NEW_TASK", "output_snippet": "Starting: Intent { flg=0x10000000 cmp=com.yjzx.yjzx2017/.controller.login.activity.LoginSmsCodeActivity }"}, {"index": 16, "name": "RegistActivity", "full_path": "com.yjzx.yjzx2017.controller.login.activity.RegistActivity", "launch_success": true, "strategies_tried": ["FLAG_NEW_TASK"], "successful_strategy": "FLAG_NEW_TASK", "output_snippet": "Starting: Intent { flg=0x10000000 cmp=com.yjzx.yjzx2017/.controller.login.activity.RegistActivity }"}, {"index": 17, "name": "ForgotPasswordActivity", "full_path": "com.yjzx.yjzx2017.controller.login.activity.ForgotPasswordActivity", "launch_success": true, "strategies_tried": ["FLAG_NEW_TASK"], "successful_strategy": "FLAG_NEW_TASK", "output_snippet": "Starting: Intent { flg=0x10000000 cmp=com.yjzx.yjzx2017/.controller.login.activity.ForgotPasswordActivity }"}, {"index": 18, "name": "SettingActivity", "full_path": "com.yjzx.yjzx2017.controller.activity.setting.SettingActivity", "launch_success": true, "strategies_tried": ["FLAG_NEW_TASK"], "successful_strategy": "FLAG_NEW_TASK", "output_snippet": "Starting: Intent { flg=0x10000000 cmp=com.yjzx.yjzx2017/.controller.activity.setting.SettingActivity }"}, {"index": 19, "name": "OpenFingerActivity", "full_path": "com.yjzx.yjzx2017.controller.activity.setting.OpenFingerActivity", "launch_success": true, "strategies_tried": ["FLAG_NEW_TASK"], "successful_strategy": "FLAG_NEW_TASK", "output_snippet": "Starting: Intent { flg=0x10000000 cmp=com.yjzx.yjzx2017/.controller.activity.setting.OpenFingerActivity }"}, {"index": 20, "name": "OpenLoginFingerActivity", "full_path": "com.yjzx.yjzx2017.controller.activity.setting.OpenLoginFingerActivity", "launch_success": true, "strategies_tried": ["FLAG_NEW_TASK"], "successful_strategy": "FLAG_NEW_TASK", "output_snippet": "Starting: Intent { flg=0x10000000 cmp=com.yjzx.yjzx2017/.controller.activity.setting.OpenLoginFingerActivity }"}], "summary": {"success": 20, "failed": 0, "success_rate": 100.0}, "strategy_stats": {"FLAG_NEW_TASK": 20}}