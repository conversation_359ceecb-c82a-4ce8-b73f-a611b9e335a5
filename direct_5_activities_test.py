#!/usr/bin/env python3
"""
直接测试金融APP的5个activity
简化版本，专注于SSL绕过和网络捕获
"""

import subprocess
import sys
import time
import json
from pathlib import Path
import signal

def log(message, level="INFO"):
    """日志输出"""
    timestamp = time.strftime("%H:%M:%S")
    if level == "SUCCESS":
        print(f"🎉 [{timestamp}] {message}")
    elif level == "ERROR":
        print(f"❌ [{timestamp}] {message}")
    elif level == "WARNING":
        print(f"⚠️  [{timestamp}] {message}")
    else:
        print(f"📋 [{timestamp}] {message}")

def run_adb(cmd, timeout=30):
    """执行adb命令"""
    full_cmd = f"source android_env.sh && adb {cmd}"
    try:
        result = subprocess.run(full_cmd, shell=True, 
                              capture_output=True, text=True, 
                              timeout=timeout, check=True)
        return result.returncode, result.stdout.strip(), result.stderr.strip()
    except subprocess.CalledProcessError as e:
        return e.returncode, "", e.stderr
    except subprocess.TimeoutExpired:
        return -1, "", "Timeout"

def cleanup_all_processes():
    """清理所有相关进程"""
    log("清理所有相关进程...")
    
    # 清理mitmproxy
    subprocess.run("pkill -f mitmdump", shell=True, capture_output=True)
    
    # 清理frida-server
    run_adb("shell pkill frida-server")
    
    # 清理代理设置
    run_adb("shell settings delete global http_proxy")
    
    time.sleep(3)
    log("✅ 进程清理完成")

def start_mitmproxy_clean():
    """干净地启动mitmproxy"""
    log("启动mitmproxy...")
    
    # 确保目录存在
    Path("mitm-logs").mkdir(exist_ok=True)
    
    # 清空捕获文件
    capture_file = Path("mitm-logs/realtime_capture.json")
    with open(capture_file, 'w') as f:
        json.dump([], f)
    
    # 启动mitmproxy
    cmd = "/Users/<USER>/Library/Python/3.9/bin/mitmdump -s mitm-scripts/capture.py --listen-port 8080"
    
    try:
        process = subprocess.Popen(
            cmd, shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        log("mitmproxy进程已启动，等待初始化...")
        time.sleep(15)  # 给足够时间初始化
        
        if process.poll() is None:
            log("✅ mitmproxy运行正常", "SUCCESS")
            return process
        else:
            stdout, stderr = process.communicate()
            log(f"mitmproxy启动失败: {stderr}", "ERROR")
            return None
            
    except Exception as e:
        log(f"mitmproxy启动异常: {e}", "ERROR")
        return None

def setup_proxy_and_ssl():
    """设置代理和SSL绕过"""
    log("设置代理和SSL绕过...")
    
    # 获取本机IP
    try:
        result = subprocess.run("ifconfig en0 | grep 'inet ' | awk '{print $2}'", 
                              shell=True, capture_output=True, text=True)
        host_ip = result.stdout.strip()
        if not host_ip:
            host_ip = "*************"
    except:
        host_ip = "*************"
    
    log(f"使用主机IP: {host_ip}")
    
    # 设置代理
    proxy_setting = f"{host_ip}:8080"
    returncode, stdout, stderr = run_adb(f"shell settings put global http_proxy {proxy_setting}")
    
    if returncode == 0:
        log(f"✅ 代理设置成功: {proxy_setting}", "SUCCESS")
    else:
        log(f"代理设置失败: {stderr}", "ERROR")
        return False
    
    # 启动Frida SSL绕过
    log("启动Frida SSL绕过...")
    
    # 确保root权限
    subprocess.run("adb root", shell=True, capture_output=True)
    time.sleep(3)
    
    # 推送并启动frida-server
    run_adb("push frida-server /data/local/tmp/frida-server")
    run_adb("shell chmod 755 /data/local/tmp/frida-server")
    
    subprocess.Popen("source android_env.sh && adb shell '/data/local/tmp/frida-server' &", shell=True)
    time.sleep(10)
    
    # 验证frida-server
    returncode, stdout, stderr = run_adb("shell ps | grep frida-server")
    if "frida-server" not in stdout:
        log("frida-server启动失败", "ERROR")
        return False
    
    log("✅ frida-server启动成功", "SUCCESS")
    
    # 准备应用
    package = "com.yjzx.yjzx2017"
    run_adb(f"shell am force-stop {package}")
    time.sleep(2)
    
    returncode, stdout, stderr = run_adb(f"shell am start -n {package}/.controller.activity.splash.SplashActivity")
    if returncode != 0:
        log(f"应用启动失败: {stderr}", "ERROR")
        return False
    
    log("✅ 应用启动成功", "SUCCESS")
    time.sleep(10)
    
    # 获取PID并启动SSL绕过
    returncode, stdout, stderr = run_adb(f"shell ps | grep {package}")
    if package not in stdout:
        log("未找到应用进程", "ERROR")
        return False
    
    # 提取PID
    lines = stdout.split('\n')
    target_pid = None
    for line in lines:
        if package in line:
            parts = line.split()
            if len(parts) >= 2:
                target_pid = parts[1]
                break
    
    if not target_pid:
        log("无法获取PID", "ERROR")
        return False
    
    log(f"目标PID: {target_pid}")
    
    # 启动SSL绕过（后台）
    ssl_cmd = f"export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin && frida -U {target_pid} -l interactive_ssl_bypass.js"
    
    ssl_process = subprocess.Popen(
        ssl_cmd, shell=True,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    
    log("SSL绕过进程已启动，等待加载...")
    time.sleep(20)
    
    if ssl_process.poll() is None:
        log("✅ SSL绕过正在运行", "SUCCESS")
        return ssl_process
    else:
        log("SSL绕过启动可能有问题", "WARNING")
        return ssl_process  # 仍然返回

def test_5_activities_direct():
    """直接测试5个activity"""
    log("🏦 直接测试金融APP的5个activity")
    log("=" * 60)
    
    package = "com.yjzx.yjzx2017"
    
    # 5个测试场景
    activities = [
        {
            "name": "启动页面测试",
            "actions": [
                ("重启应用", f"shell am force-stop {package} && sleep 2 && shell am start -n {package}/.controller.activity.splash.SplashActivity"),
                ("等待启动", "sleep 8"),
                ("点击继续", "shell input tap 540 960"),
                ("等待", "sleep 5")
            ]
        },
        {
            "name": "主界面测试",
            "actions": [
                ("点击中心", "shell input tap 540 960"),
                ("等待", "sleep 3"),
                ("下拉刷新", "shell input swipe 540 400 540 800"),
                ("等待", "sleep 8"),
                ("点击菜单", "shell input tap 100 100"),
                ("等待", "sleep 3")
            ]
        },
        {
            "name": "导航测试",
            "actions": [
                ("点击底部导航1", "shell input tap 200 1500"),
                ("等待", "sleep 5"),
                ("点击底部导航2", "shell input tap 400 1500"),
                ("等待", "sleep 5"),
                ("点击底部导航3", "shell input tap 600 1500"),
                ("等待", "sleep 5")
            ]
        },
        {
            "name": "数据刷新测试",
            "actions": [
                ("刷新1", "shell input swipe 540 400 540 800"),
                ("等待", "sleep 8"),
                ("刷新2", "shell input swipe 540 400 540 800"),
                ("等待", "sleep 8"),
                ("点击刷新按钮", "shell input tap 540 200"),
                ("等待", "sleep 5")
            ]
        },
        {
            "name": "深度交互测试",
            "actions": [
                ("长按操作", "shell input swipe 540 960 540 960 1000"),
                ("等待", "sleep 3"),
                ("多点触控", "shell input tap 300 600 && shell input tap 700 600"),
                ("等待", "sleep 3"),
                ("滑动操作", "shell input swipe 200 800 800 800"),
                ("等待", "sleep 5")
            ]
        }
    ]
    
    results = []
    
    for i, activity in enumerate(activities, 1):
        log(f"📱 测试 {i}/5: {activity['name']}")
        
        before_count = get_request_count()
        
        for action_name, action_cmd in activity['actions']:
            log(f"   🔄 {action_name}")
            
            if action_cmd.startswith("sleep"):
                time.sleep(int(action_cmd.split()[1]))
            elif "&&" in action_cmd:
                # 复合命令
                parts = action_cmd.split(" && ")
                for part in parts:
                    if part.startswith("sleep"):
                        time.sleep(int(part.split()[1]))
                    else:
                        run_adb(part)
                        time.sleep(1)
            else:
                run_adb(action_cmd)
                time.sleep(1)
        
        # 等待网络请求
        time.sleep(10)
        
        after_count = get_request_count()
        new_requests = after_count - before_count
        
        results.append({
            "activity": activity['name'],
            "new_requests": new_requests
        })
        
        log(f"   📊 {activity['name']} 完成，新增请求: {new_requests}")
        log("")
    
    return results

def get_request_count():
    """获取请求数量"""
    try:
        capture_file = Path("mitm-logs/realtime_capture.json")
        if capture_file.exists():
            with open(capture_file, 'r') as f:
                data = json.load(f)
            return len(data) if isinstance(data, list) else 0
        return 0
    except:
        return 0

def analyze_results():
    """分析结果"""
    log("📊 分析测试结果...")
    
    capture_file = Path("mitm-logs/realtime_capture.json")
    
    try:
        if not capture_file.exists():
            log("捕获文件不存在", "ERROR")
            return False
        
        with open(capture_file, 'r') as f:
            data = json.load(f)
        
        total = len(data) if isinstance(data, list) else 0
        https_reqs = [req for req in data if req.get('url', '').startswith('https://')]
        
        log("=" * 60)
        log("🎉 金融APP 5个Activity测试结果", "SUCCESS")
        log("=" * 60)
        
        log(f"📊 网络捕获统计:")
        log(f"   总请求数: {total}")
        log(f"   HTTPS请求: {len(https_reqs)}")
        
        if len(https_reqs) > 0:
            log("🔒 HTTPS请求分析:", "SUCCESS")
            
            domains = {}
            for req in https_reqs:
                host = req.get('host', '')
                if host:
                    domains[host] = domains.get(host, 0) + 1
            
            log("🌐 访问的域名:")
            for domain, count in sorted(domains.items(), key=lambda x: x[1], reverse=True):
                log(f"   {domain}: {count} 请求")
            
            log("📋 HTTPS请求示例:")
            for i, req in enumerate(https_reqs[:8], 1):
                url = req.get('url', 'N/A')
                method = req.get('method', 'N/A')
                log(f"   {i}. {method} {url}")
            
            log("🎉 SSL绕过完全成功！", "SUCCESS")
            log("✅ 金融APP的HTTPS流量已被成功捕获！")
            return True
        else:
            log("❌ 没有捕获到HTTPS请求", "ERROR")
            return False
            
    except Exception as e:
        log(f"分析失败: {e}", "ERROR")
        return False

def main():
    """主函数"""
    log("🚀 金融APP 5个Activity直接测试")
    log("🔧 简化版本，专注于SSL绕过和网络捕获")
    log("=" * 70)
    
    mitmproxy_process = None
    ssl_process = None
    
    try:
        # 步骤1: 清理环境
        cleanup_all_processes()
        
        # 步骤2: 启动mitmproxy
        mitmproxy_process = start_mitmproxy_clean()
        if not mitmproxy_process:
            log("mitmproxy启动失败", "ERROR")
            return False
        
        # 步骤3: 设置代理和SSL绕过
        ssl_process = setup_proxy_and_ssl()
        if not ssl_process:
            log("SSL绕过设置失败", "ERROR")
            return False
        
        # 步骤4: 等待系统稳定
        log("⏳ 等待系统稳定...")
        time.sleep(20)
        
        # 步骤5: 测试5个activity
        log("=" * 60)
        log("🔍 开始测试5个Activity")
        log("=" * 60)
        
        results = test_5_activities_direct()
        
        # 步骤6: 等待最后处理
        log("⏳ 等待最后的网络请求处理...")
        time.sleep(20)
        
        # 步骤7: 分析结果
        success = analyze_results()
        
        # 显示统计
        log("📊 各Activity统计:")
        for result in results:
            log(f"   {result['activity']}: {result['new_requests']} 新请求")
        
        if success:
            log("🎉 5个Activity测试完全成功！", "SUCCESS")
            return True
        else:
            log("⚠️  测试需要进一步优化", "WARNING")
            return False
            
    except KeyboardInterrupt:
        log("用户中断测试")
        return False
    except Exception as e:
        log(f"测试异常: {e}", "ERROR")
        return False
    finally:
        # 清理
        log("🧹 清理资源...")
        if mitmproxy_process and mitmproxy_process.poll() is None:
            mitmproxy_process.terminate()
        if ssl_process and ssl_process.poll() is None:
            ssl_process.terminate()
        cleanup_all_processes()

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎯 金融APP 5个Activity测试成功！")
            print("💡 SSL绕过和网络捕获功能正常！")
        else:
            print("\n🔧 测试需要进一步调试")
    except KeyboardInterrupt:
        print("\n⚠️  用户中断")
    except Exception as e:
        print(f"\n❌ 异常: {e}")
