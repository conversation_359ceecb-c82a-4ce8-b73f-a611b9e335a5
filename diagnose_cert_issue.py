#!/usr/bin/env python3
"""
诊断mitmproxy证书安装问题
分析根本原因并提供解决方案
"""

import subprocess
import sys
import time
from pathlib import Path
import hashlib
import base64

class CertificateDiagnostic:
    def __init__(self):
        self.cert_file = "mitmproxy-ca-cert.pem"
        
    def run_adb(self, cmd, timeout=30):
        """执行adb命令"""
        full_cmd = f"source android_env.sh && adb {cmd}"
        try:
            result = subprocess.run(full_cmd, shell=True, 
                                  capture_output=True, text=True, 
                                  timeout=timeout, check=True)
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            return f"ERROR: {e.stderr}"
        except subprocess.TimeoutExpired:
            return "ERROR: Timeout"
    
    def check_device_connection(self):
        """检查设备连接"""
        print("🔍 检查设备连接...")
        result = self.run_adb("devices")
        
        if "device" in result and "ERROR" not in result:
            print("✅ Android设备已连接")
            return True
        else:
            print("❌ Android设备未连接")
            print("🔧 请重新启动Android模拟器")
            return False
    
    def analyze_certificate_file(self):
        """分析证书文件"""
        print("🔍 分析证书文件...")
        
        if not Path(self.cert_file).exists():
            print("❌ 证书文件不存在")
            return False
        
        # 读取证书内容
        with open(self.cert_file, 'r') as f:
            content = f.read()
        
        print(f"📋 证书文件大小: {len(content)} 字符")
        
        # 检查证书格式
        if "-----BEGIN CERTIFICATE-----" not in content:
            print("❌ 证书格式错误：缺少BEGIN标记")
            return False
        
        if "-----END CERTIFICATE-----" not in content:
            print("❌ 证书格式错误：缺少END标记")
            return False
        
        # 提取证书数据
        cert_lines = content.split('\n')
        cert_data_lines = []
        in_cert = False
        
        for line in cert_lines:
            if "-----BEGIN CERTIFICATE-----" in line:
                in_cert = True
                continue
            elif "-----END CERTIFICATE-----" in line:
                break
            elif in_cert:
                cert_data_lines.append(line.strip())
        
        cert_data = ''.join(cert_data_lines)
        
        try:
            # 验证Base64编码
            decoded = base64.b64decode(cert_data)
            print(f"✅ 证书Base64解码成功，长度: {len(decoded)} 字节")
        except Exception as e:
            print(f"❌ 证书Base64解码失败: {e}")
            return False
        
        # 使用openssl验证证书
        print("🔍 使用OpenSSL验证证书...")
        cmd = f"openssl x509 -in {self.cert_file} -text -noout"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ OpenSSL验证证书成功")
            
            # 提取关键信息
            output = result.stdout
            if "Subject:" in output:
                subject_line = [line for line in output.split('\n') if 'Subject:' in line][0]
                print(f"📋 证书主题: {subject_line.strip()}")
            
            if "Issuer:" in output:
                issuer_line = [line for line in output.split('\n') if 'Issuer:' in line][0]
                print(f"📋 证书颁发者: {issuer_line.strip()}")
            
            return True
        else:
            print(f"❌ OpenSSL验证失败: {result.stderr}")
            return False
    
    def get_android_version_info(self):
        """获取Android版本信息"""
        print("🔍 获取Android版本信息...")
        
        if not self.check_device_connection():
            return None
        
        # 获取Android版本
        version = self.run_adb("shell getprop ro.build.version.release")
        sdk_version = self.run_adb("shell getprop ro.build.version.sdk")
        
        print(f"📋 Android版本: {version}")
        print(f"📋 SDK版本: {sdk_version}")
        
        # 检查是否是Android 7.0+（API 24+）
        try:
            sdk_int = int(sdk_version) if sdk_version.isdigit() else 0
            if sdk_int >= 24:
                print("⚠️  Android 7.0+ 默认不信任用户安装的CA证书")
                return {"version": version, "sdk": sdk_int, "user_cert_issue": True}
            else:
                print("✅ Android版本支持用户CA证书")
                return {"version": version, "sdk": sdk_int, "user_cert_issue": False}
        except:
            return {"version": version, "sdk": 0, "user_cert_issue": True}
    
    def check_certificate_installation_requirements(self):
        """检查证书安装要求"""
        print("🔍 检查证书安装要求...")
        
        if not self.check_device_connection():
            return False
        
        # 检查屏幕锁定
        lock_disabled = self.run_adb("shell settings get secure lockscreen.disabled")
        lock_type = self.run_adb("shell settings get secure lockscreen.password_type")
        
        print(f"📋 屏幕锁定禁用: {lock_disabled}")
        print(f"📋 锁定类型: {lock_type}")
        
        if "1" in lock_disabled:
            print("❌ 屏幕锁定已禁用 - 这是证书安装失败的主要原因！")
            return False
        elif lock_type == "0" or "null" in lock_type:
            print("❌ 未设置屏幕锁定 - Android要求设置PIN/密码/图案才能安装CA证书")
            return False
        else:
            print("✅ 屏幕锁定已正确设置")
            return True
    
    def analyze_certificate_error_message(self):
        """分析证书错误信息"""
        print("🔍 分析证书安装错误...")
        
        print("📋 '无法安装 CA 证书' 错误的常见原因:")
        print("   1. 屏幕锁定未设置（最常见）")
        print("   2. 证书文件格式问题")
        print("   3. Android版本限制（7.0+）")
        print("   4. 证书文件损坏")
        print("   5. 存储权限问题")
        print("   6. 证书已存在")
        print()
        
        print("📋 错误信息 'This certificate from null must be installed in Settings':")
        print("   - 'from null' 表示证书的颁发者信息读取失败")
        print("   - 可能是文件路径或权限问题")
        print("   - 也可能是Android系统的解析问题")
    
    def provide_solutions(self):
        """提供解决方案"""
        print("🔧 证书安装问题解决方案:")
        print("=" * 50)
        
        print("方案1: 确保屏幕锁定设置")
        print("   - 设置 -> 安全 -> 屏幕锁定")
        print("   - 选择PIN、密码或图案")
        print("   - 这是最重要的前提条件")
        print()
        
        print("方案2: 使用正确的安装路径")
        print("   - 将证书放在 /sdcard/Download/")
        print("   - 使用设置 -> 安全 -> 加密与凭据 -> 从存储设备安装")
        print("   - 不要通过文件管理器直接点击")
        print()
        
        print("方案3: 转换证书格式")
        print("   - 将.pem文件重命名为.crt")
        print("   - 或转换为DER格式")
        print("   - 某些Android版本对格式敏感")
        print()
        
        print("方案4: 使用root权限安装系统证书")
        print("   - 需要root设备")
        print("   - 安装到 /system/etc/security/cacerts/")
        print("   - 应用会自动信任系统证书")
        print()
        
        print("方案5: 修改应用网络安全配置")
        print("   - 反编译APK")
        print("   - 修改network_security_config.xml")
        print("   - 允许用户证书")
        print()
        
        print("方案6: 使用Frida绕过证书验证")
        print("   - 安装Frida")
        print("   - 使用SSL Kill Switch脚本")
        print("   - 运行时禁用证书验证")
    
    def run_comprehensive_diagnosis(self):
        """运行全面诊断"""
        print("🚀 mitmproxy证书安装问题诊断")
        print("🔧 分析根本原因并提供解决方案")
        print("=" * 60)
        
        # 1. 检查证书文件
        print("\n1. 证书文件分析")
        print("-" * 30)
        cert_ok = self.analyze_certificate_file()
        
        # 2. 检查设备连接
        print("\n2. 设备连接检查")
        print("-" * 30)
        device_ok = self.check_device_connection()
        
        if device_ok:
            # 3. 获取Android版本信息
            print("\n3. Android版本信息")
            print("-" * 30)
            version_info = self.get_android_version_info()
            
            # 4. 检查安装要求
            print("\n4. 安装要求检查")
            print("-" * 30)
            requirements_ok = self.check_certificate_installation_requirements()
        else:
            version_info = None
            requirements_ok = False
        
        # 5. 分析错误信息
        print("\n5. 错误信息分析")
        print("-" * 30)
        self.analyze_certificate_error_message()
        
        # 6. 提供解决方案
        print("\n6. 解决方案")
        print("-" * 30)
        self.provide_solutions()
        
        # 7. 总结
        print("\n" + "=" * 60)
        print("📊 诊断总结")
        print("=" * 60)
        
        issues = []
        if not cert_ok:
            issues.append("证书文件问题")
        if not device_ok:
            issues.append("设备连接问题")
        if not requirements_ok:
            issues.append("安装要求不满足")
        if version_info and version_info.get("user_cert_issue"):
            issues.append("Android版本限制")
        
        if issues:
            print("❌ 发现的问题:")
            for issue in issues:
                print(f"   - {issue}")
            
            print("\n🎯 建议的解决顺序:")
            if not device_ok:
                print("   1. 重新启动Android模拟器")
            if not requirements_ok:
                print("   2. 设置屏幕锁定（PIN/密码/图案）")
            if not cert_ok:
                print("   3. 重新下载证书文件")
            if version_info and version_info.get("user_cert_issue"):
                print("   4. 考虑使用root权限或其他绕过方法")
        else:
            print("✅ 未发现明显问题")
            print("🔧 建议尝试通过设置手动安装证书")
        
        return len(issues) == 0

def main():
    diagnostic = CertificateDiagnostic()
    
    try:
        success = diagnostic.run_comprehensive_diagnosis()
        
        if success:
            print("\n🎯 诊断完成，未发现明显问题")
        else:
            print("\n🔧 请按照建议解决发现的问题")
            
    except KeyboardInterrupt:
        print("\n⚠️  诊断被用户中断")
    except Exception as e:
        print(f"\n❌ 诊断异常: {e}")

if __name__ == "__main__":
    main()
