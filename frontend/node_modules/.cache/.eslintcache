[{"/Users/<USER>/Desktop/project/apk_detect/frontend/src/index.tsx": "1", "/Users/<USER>/Desktop/project/apk_detect/frontend/src/App.tsx": "2", "/Users/<USER>/Desktop/project/apk_detect/frontend/src/services/api.ts": "3", "/Users/<USER>/Desktop/project/apk_detect/frontend/src/components/FileUpload.tsx": "4", "/Users/<USER>/Desktop/project/apk_detect/frontend/src/components/AnalysisControls.tsx": "5", "/Users/<USER>/Desktop/project/apk_detect/frontend/src/components/ProgressDisplay.tsx": "6", "/Users/<USER>/Desktop/project/apk_detect/frontend/src/components/ResultsDisplay.tsx": "7", "/Users/<USER>/Desktop/project/apk_detect/frontend/src/hooks/useLocalStorage.ts": "8", "/Users/<USER>/Desktop/project/apk_detect/frontend/src/hooks/useDebounce.ts": "9", "/Users/<USER>/Desktop/project/apk_detect/frontend/src/components/LoadingSpinner.tsx": "10", "/Users/<USER>/Desktop/project/apk_detect/frontend/src/components/ErrorBoundary.tsx": "11"}, {"size": 272, "mtime": 1756791396701, "results": "12", "hashOfConfig": "13"}, {"size": 14374, "mtime": 1756978122806, "results": "14", "hashOfConfig": "13"}, {"size": 7443, "mtime": 1756978107583, "results": "15", "hashOfConfig": "13"}, {"size": 6535, "mtime": 1756791238790, "results": "16", "hashOfConfig": "13"}, {"size": 12096, "mtime": 1756791291685, "results": "17", "hashOfConfig": "13"}, {"size": 13285, "mtime": 1756791513263, "results": "18", "hashOfConfig": "13"}, {"size": 19122, "mtime": 1756791781000, "results": "19", "hashOfConfig": "13"}, {"size": 1098, "mtime": 1756792151341, "results": "20", "hashOfConfig": "13"}, {"size": 538, "mtime": 1756792112728, "results": "21", "hashOfConfig": "13"}, {"size": 2930, "mtime": 1756792222973, "results": "22", "hashOfConfig": "13"}, {"size": 3239, "mtime": 1756792200186, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1qaww0i", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/project/apk_detect/frontend/src/index.tsx", [], [], "/Users/<USER>/Desktop/project/apk_detect/frontend/src/App.tsx", [], [], "/Users/<USER>/Desktop/project/apk_detect/frontend/src/services/api.ts", [], [], "/Users/<USER>/Desktop/project/apk_detect/frontend/src/components/FileUpload.tsx", [], [], "/Users/<USER>/Desktop/project/apk_detect/frontend/src/components/AnalysisControls.tsx", [], [], "/Users/<USER>/Desktop/project/apk_detect/frontend/src/components/ProgressDisplay.tsx", [], [], "/Users/<USER>/Desktop/project/apk_detect/frontend/src/components/ResultsDisplay.tsx", [], [], "/Users/<USER>/Desktop/project/apk_detect/frontend/src/hooks/useLocalStorage.ts", [], [], "/Users/<USER>/Desktop/project/apk_detect/frontend/src/hooks/useDebounce.ts", [], [], "/Users/<USER>/Desktop/project/apk_detect/frontend/src/components/LoadingSpinner.tsx", [], [], "/Users/<USER>/Desktop/project/apk_detect/frontend/src/components/ErrorBoundary.tsx", [], []]