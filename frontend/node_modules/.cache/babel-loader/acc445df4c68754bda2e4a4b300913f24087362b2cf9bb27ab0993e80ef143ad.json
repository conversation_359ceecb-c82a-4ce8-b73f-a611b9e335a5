{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/project/apk_detect/frontend/src/App.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Layout, Typography, message, Space, Button, Alert } from 'antd';\nimport { ReloadOutlined, ApiOutlined } from '@ant-design/icons';\nimport styled from 'styled-components';\nimport FileUpload from './components/FileUpload';\nimport AnalysisControls from './components/AnalysisControls';\nimport ProgressDisplay from './components/ProgressDisplay';\nimport ResultsDisplay from './components/ResultsDisplay';\nimport ErrorBoundary from './components/ErrorBoundary';\nimport LoadingSpinner from './components/LoadingSpinner';\nimport { apkAnalysisAPI, WebSocketManager } from './services/api';\nimport { useLocalStorage } from './hooks/useLocalStorage';\nimport { useDebounce } from './hooks/useDebounce';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Header,\n  Content,\n  Footer\n} = Layout;\nconst {\n  Title\n} = Typography;\nconst AppContainer = styled(Layout)`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  \n  .app-header {\n    background: rgba(255, 255, 255, 0.95);\n    backdrop-filter: blur(10px);\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n    padding: 0 24px;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n  }\n  \n  .app-content {\n    padding: 24px;\n    max-width: 1200px;\n    margin: 0 auto;\n    width: 100%;\n  }\n  \n  .app-footer {\n    background: rgba(255, 255, 255, 0.9);\n    text-align: center;\n    color: #666;\n  }\n  \n  .main-title {\n    margin: 0 !important;\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    -webkit-background-clip: text;\n    -webkit-text-fill-color: transparent;\n    background-clip: text;\n  }\n  \n  .status-indicator {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n  }\n  \n  .connected { color: #52c41a; }\n  .disconnected { color: #ff4d4f; }\n`;\n_c = AppContainer;\nconst App = () => {\n  _s();\n  // 应用状态\n  const [uploadedFile, setUploadedFile] = useState(null);\n  const [currentTaskId, setCurrentTaskId] = useState(null);\n  const [analysisStatus, setAnalysisStatus] = useState({});\n  const [progressData, setProgressData] = useState({});\n  const [analysisResults, setAnalysisResults] = useState(null);\n  const [isAnalysisActive, setIsAnalysisActive] = useState(false);\n  const [apiConnected, setApiConnected] = useState(false);\n  const [wsManager] = useState(new WebSocketManager());\n\n  // 持久化用户设置\n  const [userSettings, setUserSettings] = useLocalStorage('apk-analyzer-settings', {\n    defaultTimeout: 300,\n    enableStaticByDefault: true,\n    enableDynamicByDefault: true,\n    theme: 'light'\n  });\n\n  // API连接状态防抖，避免频繁检查\n  const debouncedApiCheck = useDebounce(apiConnected, 1000);\n\n  // 初始化API连接检查\n  useEffect(() => {\n    checkAPIConnection();\n  }, []);\n\n  // WebSocket消息处理\n  useEffect(() => {\n    if (currentTaskId) {\n      wsManager.connect(currentTaskId);\n      wsManager.on('progress_update', handleProgressUpdate);\n      wsManager.on('analysis_complete', handleAnalysisComplete);\n      wsManager.on('analysis_error', handleAnalysisError);\n      return () => {\n        wsManager.off('progress_update', handleProgressUpdate);\n        wsManager.off('analysis_complete', handleAnalysisComplete);\n        wsManager.off('analysis_error', handleAnalysisError);\n        wsManager.disconnect();\n      };\n    }\n    return undefined;\n  }, [currentTaskId]);\n\n  // 检查API连接\n  const checkAPIConnection = useCallback(async () => {\n    try {\n      await apkAnalysisAPI.healthCheck();\n      setApiConnected(true);\n      message.success('API连接正常');\n    } catch (error) {\n      setApiConnected(false);\n      message.error('API连接失败，请检查后端服务');\n      console.error('API connection failed:', error);\n    }\n  }, []);\n\n  // 文件上传处理\n  const handleFileUploaded = useCallback(fileInfo => {\n    setUploadedFile(fileInfo);\n    // 重置之前的结果\n    setAnalysisResults(null);\n    setProgressData({});\n    setAnalysisStatus({});\n    message.success('文件上传成功，可以开始分析了');\n  }, []);\n\n  // 文件移除处理\n  const handleFileRemoved = useCallback(() => {\n    setUploadedFile(null);\n    setAnalysisResults(null);\n    setProgressData({});\n    setAnalysisStatus({});\n    setCurrentTaskId(null);\n    setIsAnalysisActive(false);\n    message.info('文件已移除');\n  }, []);\n\n  // 开始分析\n  const handleStartAnalysis = useCallback(async (type, options) => {\n    if (!uploadedFile) {\n      message.error('请先上传APK文件');\n      return;\n    }\n    if (!apiConnected) {\n      message.error('API连接异常，请检查后端服务');\n      return;\n    }\n    try {\n      setIsAnalysisActive(true);\n\n      // 重置状态\n      setAnalysisResults(null);\n      setProgressData({\n        currentStep: 0,\n        logs: []\n      });\n\n      // 更新分析状态\n      const newStatus = {\n        ...analysisStatus\n      };\n      if (type === 'static' || type === 'both') {\n        newStatus.static = 'running';\n      }\n      if (type === 'dynamic' || type === 'both') {\n        newStatus.dynamic = 'running';\n      }\n      setAnalysisStatus(newStatus);\n\n      // 调用简化版API开始分析\n      const response = await apkAnalysisAPI.simpleAnalyze(uploadedFile.file, options);\n      const taskId = response.data.task_id;\n      setCurrentTaskId(taskId);\n      message.success(`分析任务已启动: ${taskId}`);\n\n      // 开始轮询任务状态\n      pollTaskStatus(taskId);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      setIsAnalysisActive(false);\n      setAnalysisStatus({});\n      message.error(`启动分析失败: ${((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || error.message}`);\n      console.error('Analysis start failed:', error);\n      return;\n    }\n  }, [uploadedFile, apiConnected, analysisStatus]);\n\n  // 停止分析\n  const handleStopAnalysis = useCallback(async type => {\n    if (!currentTaskId) {\n      message.warning('没有正在运行的分析任务');\n      return;\n    }\n    try {\n      await apkAnalysisAPI.stopAnalysis(currentTaskId);\n\n      // 更新状态\n      const newStatus = {\n        ...analysisStatus\n      };\n      if (type === 'static' || type === 'all') {\n        newStatus.static = 'stopped';\n      }\n      if (type === 'dynamic' || type === 'all') {\n        newStatus.dynamic = 'stopped';\n      }\n      setAnalysisStatus(newStatus);\n      if (type === 'all') {\n        setIsAnalysisActive(false);\n        setCurrentTaskId(null);\n      }\n      message.info('分析任务已停止');\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      message.error(`停止分析失败: ${((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || error.message}`);\n      console.error('Stop analysis failed:', error);\n    }\n  }, [currentTaskId, analysisStatus]);\n\n  // 轮询任务状态\n  const pollTaskStatus = useCallback(async taskId => {\n    const pollInterval = setInterval(async () => {\n      try {\n        const statusResponse = await apkAnalysisAPI.getSimpleTaskStatus(taskId);\n        const status = statusResponse.data.status;\n        if (status === 'completed') {\n          clearInterval(pollInterval);\n          // 获取结果\n          const resultResponse = await apkAnalysisAPI.getSimpleAnalysisResult(taskId);\n          console.log('Analysis result received:', resultResponse.data);\n          handleAnalysisComplete(resultResponse.data);\n        } else if (status === 'failed') {\n          clearInterval(pollInterval);\n          handleAnalysisError({\n            error: '分析任务失败'\n          });\n        }\n      } catch (error) {\n        console.error('Poll task status failed:', error);\n        // 如果轮询失败太多次，停止轮询\n      }\n    }, 2000); // 每2秒轮询一次\n\n    // 设置最大轮询时间（10分钟）\n    setTimeout(() => {\n      clearInterval(pollInterval);\n    }, 600000);\n  }, []);\n\n  // 处理进度更新\n  const handleProgressUpdate = useCallback(data => {\n    setProgressData(prevData => ({\n      ...prevData,\n      ...data\n    }));\n\n    // 更新步骤状态\n    if (data.step !== undefined) {\n      setProgressData(prevData => ({\n        ...prevData,\n        currentStep: data.step\n      }));\n    }\n\n    // 添加日志\n    if (data.message) {\n      setProgressData(prevData => ({\n        ...prevData,\n        logs: [...(prevData.logs || []), {\n          id: Date.now().toString(),\n          level: data.level || 'info',\n          message: data.message || '',\n          timestamp: new Date()\n        }]\n      }));\n    }\n  }, []);\n\n  // 处理分析完成\n  const handleAnalysisComplete = useCallback(results => {\n    setIsAnalysisActive(false);\n    setAnalysisResults(results);\n    setAnalysisStatus({\n      static: 'completed',\n      dynamic: 'completed'\n    });\n\n    // 更新进度为100%\n    setProgressData(prevData => ({\n      ...prevData,\n      currentStep: 6,\n      // 假设总共6步\n      logs: [...(prevData.logs || []), {\n        id: Date.now().toString(),\n        level: 'success',\n        message: '分析完成！',\n        timestamp: new Date()\n      }]\n    }));\n    message.success('APK分析完成！');\n  }, []);\n\n  // 处理分析错误\n  const handleAnalysisError = useCallback(error => {\n    setIsAnalysisActive(false);\n    setAnalysisStatus({\n      static: 'error',\n      dynamic: 'error'\n    });\n    setProgressData(prevData => ({\n      ...prevData,\n      error: error.error || '分析过程中发生错误',\n      logs: [...(prevData.logs || []), {\n        id: Date.now().toString(),\n        level: 'error',\n        message: error.error || '分析失败',\n        timestamp: new Date()\n      }]\n    }));\n    message.error(`分析失败: ${error.error || '未知错误'}`);\n  }, []);\n\n  // 处理结果导出\n  const handleExport = useCallback((format, filename) => {\n    message.success(`结果已导出为 ${filename}`);\n  }, []);\n\n  // 处理结果分享\n  const handleShare = useCallback(results => {\n    // 这里可以实现分享功能，比如生成链接等\n    message.info('分享功能开发中...');\n  }, []);\n  return /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n    children: /*#__PURE__*/_jsxDEV(AppContainer, {\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        className: \"app-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(Title, {\n            level: 2,\n            className: \"main-title\",\n            children: \"\\uD83D\\uDCF1 APK \\u5206\\u6790\\u7CFB\\u7EDF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"status-indicator\",\n            children: [/*#__PURE__*/_jsxDEV(ApiOutlined, {\n              className: debouncedApiCheck ? 'connected' : 'disconnected'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: debouncedApiCheck ? 'connected' : 'disconnected',\n              children: [\"API \", debouncedApiCheck ? '已连接' : '未连接']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"text\",\n            icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 21\n            }, this),\n            onClick: checkAPIConnection,\n            loading: !debouncedApiCheck,\n            children: \"\\u91CD\\u65B0\\u8FDE\\u63A5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Content, {\n        className: \"app-content\",\n        children: [!debouncedApiCheck && /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"API\\u8FDE\\u63A5\\u5F02\\u5E38\",\n          description: \"\\u540E\\u7AEF\\u670D\\u52A1\\u8FDE\\u63A5\\u5931\\u8D25\\uFF0C\\u8BF7\\u786E\\u4FDD\\u540E\\u7AEF\\u670D\\u52A1\\u6B63\\u5728\\u8FD0\\u884C\\u5E76\\u68C0\\u67E5\\u7F51\\u7EDC\\u8FDE\\u63A5\",\n          type: \"warning\",\n          showIcon: true,\n          style: {\n            marginBottom: 20\n          },\n          action: /*#__PURE__*/_jsxDEV(Button, {\n            size: \"small\",\n            onClick: checkAPIConnection,\n            children: \"\\u91CD\\u8BD5\\u8FDE\\u63A5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n          fallback: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"\\u6587\\u4EF6\\u4E0A\\u4F20\\u7EC4\\u4EF6\\u51FA\\u73B0\\u9519\\u8BEF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 36\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(FileUpload, {\n            onFileUploaded: handleFileUploaded,\n            onFileRemoved: handleFileRemoved,\n            disabled: isAnalysisActive\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n          fallback: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"\\u5206\\u6790\\u63A7\\u5236\\u7EC4\\u4EF6\\u51FA\\u73B0\\u9519\\u8BEF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 36\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(AnalysisControls, {\n            uploadedFile: uploadedFile,\n            onStartAnalysis: handleStartAnalysis,\n            onStopAnalysis: handleStopAnalysis,\n            analysisStatus: analysisStatus,\n            disabled: !debouncedApiCheck\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 404,\n          columnNumber: 11\n        }, this), (isAnalysisActive || Object.keys(progressData).length > 0) && /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n          fallback: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"\\u8FDB\\u5EA6\\u663E\\u793A\\u7EC4\\u4EF6\\u51FA\\u73B0\\u9519\\u8BEF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 38\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(ProgressDisplay, {\n            analysisType: \"both\",\n            isActive: isAnalysisActive,\n            progressData: progressData\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 13\n        }, this), analysisResults && /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n          fallback: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"\\u7ED3\\u679C\\u663E\\u793A\\u7EC4\\u4EF6\\u51FA\\u73B0\\u9519\\u8BEF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 38\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(ResultsDisplay, {\n            analysisResults: analysisResults,\n            onExport: handleExport,\n            onShare: handleShare\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 13\n        }, this), isAnalysisActive && /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n          loading: isAnalysisActive,\n          tip: \"\\u6B63\\u5728\\u5206\\u6790APK\\u6587\\u4EF6\\uFF0C\\u8BF7\\u8010\\u5FC3\\u7B49\\u5F85...\",\n          overlay: false,\n          showProgress: true,\n          progress: Math.round((progressData.currentStep || 0) / 6 * 100)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 438,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 377,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Footer, {\n        className: \"app-footer\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"APK Analysis System \\xA9 2024 - \\u4E13\\u4E1A\\u7684APK\\u9759\\u6001\\u548C\\u52A8\\u6001\\u5206\\u6790\\u5DE5\\u5177\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 449,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            split: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"|\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 27\n            }, this),\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u652F\\u6301\\u9759\\u6001\\u4EE3\\u7801\\u5206\\u6790\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u52A8\\u6001\\u8FD0\\u884C\\u76D1\\u63A7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u7F51\\u7EDC\\u6D41\\u91CF\\u6355\\u83B7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u667A\\u80FDUI\\u904D\\u5386\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 455,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 450,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 448,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 350,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 349,\n    columnNumber: 5\n  }, this);\n};\n_s(App, \"1paHAdJraa4AooDL/8FMA9UTpPI=\", false, function () {\n  return [useLocalStorage, useDebounce];\n});\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"AppContainer\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Layout", "Typography", "message", "Space", "<PERSON><PERSON>", "<PERSON><PERSON>", "ReloadOutlined", "ApiOutlined", "styled", "FileUpload", "AnalysisControls", "ProgressDisplay", "ResultsDisplay", "Error<PERSON>ou<PERSON><PERSON>", "LoadingSpinner", "apkAnalysisAPI", "WebSocketManager", "useLocalStorage", "useDebounce", "jsxDEV", "_jsxDEV", "Header", "Content", "Footer", "Title", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_c", "App", "_s", "uploadedFile", "setUploadedFile", "currentTaskId", "setCurrentTaskId", "analysisStatus", "setAnalysisStatus", "progressData", "setProgressData", "analysisResults", "setAnalysisResults", "isAnalysisActive", "setIsAnalysisActive", "apiConnected", "setApiConnected", "wsManager", "userSettings", "setUserSettings", "defaultTimeout", "enableStaticByDefault", "enableDynamicByDefault", "theme", "deboun<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "checkAPIConnection", "connect", "on", "handleProgressUpdate", "handleAnalysisComplete", "handleAnalysisError", "off", "disconnect", "undefined", "healthCheck", "success", "error", "console", "handleFileUploaded", "fileInfo", "handleFileRemoved", "info", "handleStartAnalysis", "type", "options", "currentStep", "logs", "newStatus", "static", "dynamic", "response", "simpleAnalyze", "file", "taskId", "data", "task_id", "pollTaskStatus", "_error$response", "_error$response$data", "detail", "handleStopAnalysis", "warning", "stopAnalysis", "_error$response2", "_error$response2$data", "pollInterval", "setInterval", "statusResponse", "getSimpleTaskStatus", "status", "clearInterval", "resultResponse", "getSimpleAnalysisResult", "log", "setTimeout", "prevData", "step", "id", "Date", "now", "toString", "level", "timestamp", "results", "handleExport", "format", "filename", "handleShare", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "icon", "onClick", "loading", "description", "showIcon", "style", "marginBottom", "action", "size", "fallback", "onFileUploaded", "onFileRemoved", "disabled", "onStartAnalysis", "onStopAnalysis", "Object", "keys", "length", "analysisType", "isActive", "onExport", "onShare", "tip", "overlay", "showProgress", "progress", "Math", "round", "split", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/project/apk_detect/frontend/src/App.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { Layout, Typography, message, Space, Button, Alert } from 'antd';\nimport { ReloadOutlined, ApiOutlined } from '@ant-design/icons';\nimport styled from 'styled-components';\n\nimport FileUpload from './components/FileUpload';\nimport AnalysisControls from './components/AnalysisControls';\nimport ProgressDisplay from './components/ProgressDisplay';\nimport ResultsDisplay from './components/ResultsDisplay';\nimport ErrorBoundary from './components/ErrorBoundary';\nimport LoadingSpinner from './components/LoadingSpinner';\nimport { apkAnalysisAPI, WebSocketManager } from './services/api';\nimport { useLocalStorage } from './hooks/useLocalStorage';\nimport { useDebounce } from './hooks/useDebounce';\nimport { \n  UploadedFile, \n  AnalysisStatus, \n  ProgressData, \n  AnalysisResults,\n  AnalysisResultResponse,\n  AnalysisOptions,\n  ProgressUpdateMessage\n} from './types';\n\nconst { Header, Content, Footer } = Layout;\nconst { Title } = Typography;\n\nconst AppContainer = styled(Layout)`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  \n  .app-header {\n    background: rgba(255, 255, 255, 0.95);\n    backdrop-filter: blur(10px);\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n    padding: 0 24px;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n  }\n  \n  .app-content {\n    padding: 24px;\n    max-width: 1200px;\n    margin: 0 auto;\n    width: 100%;\n  }\n  \n  .app-footer {\n    background: rgba(255, 255, 255, 0.9);\n    text-align: center;\n    color: #666;\n  }\n  \n  .main-title {\n    margin: 0 !important;\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    -webkit-background-clip: text;\n    -webkit-text-fill-color: transparent;\n    background-clip: text;\n  }\n  \n  .status-indicator {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n  }\n  \n  .connected { color: #52c41a; }\n  .disconnected { color: #ff4d4f; }\n`;\n\nconst App: React.FC = () => {\n  // 应用状态\n  const [uploadedFile, setUploadedFile] = useState<UploadedFile | null>(null);\n  const [currentTaskId, setCurrentTaskId] = useState<string | null>(null);\n  const [analysisStatus, setAnalysisStatus] = useState<AnalysisStatus>({});\n  const [progressData, setProgressData] = useState<ProgressData>({});\n  const [analysisResults, setAnalysisResults] = useState<AnalysisResults | null>(null);\n  const [isAnalysisActive, setIsAnalysisActive] = useState<boolean>(false);\n  const [apiConnected, setApiConnected] = useState<boolean>(false);\n  const [wsManager] = useState<WebSocketManager>(new WebSocketManager());\n  \n  // 持久化用户设置\n  const [userSettings, setUserSettings] = useLocalStorage('apk-analyzer-settings', {\n    defaultTimeout: 300,\n    enableStaticByDefault: true,\n    enableDynamicByDefault: true,\n    theme: 'light'\n  });\n  \n  // API连接状态防抖，避免频繁检查\n  const debouncedApiCheck = useDebounce(apiConnected, 1000);\n\n  // 初始化API连接检查\n  useEffect(() => {\n    checkAPIConnection();\n  }, []);\n\n  // WebSocket消息处理\n  useEffect(() => {\n    if (currentTaskId) {\n      wsManager.connect(currentTaskId);\n      \n      wsManager.on('progress_update', handleProgressUpdate);\n      wsManager.on('analysis_complete', handleAnalysisComplete);\n      wsManager.on('analysis_error', handleAnalysisError);\n      \n      return () => {\n        wsManager.off('progress_update', handleProgressUpdate);\n        wsManager.off('analysis_complete', handleAnalysisComplete);\n        wsManager.off('analysis_error', handleAnalysisError);\n        wsManager.disconnect();\n      };\n    }\n    return undefined;\n  }, [currentTaskId]);\n\n  // 检查API连接\n  const checkAPIConnection = useCallback(async (): Promise<void> => {\n    try {\n      await apkAnalysisAPI.healthCheck();\n      setApiConnected(true);\n      message.success('API连接正常');\n    } catch (error: any) {\n      setApiConnected(false);\n      message.error('API连接失败，请检查后端服务');\n      console.error('API connection failed:', error);\n    }\n  }, []);\n\n  // 文件上传处理\n  const handleFileUploaded = useCallback((fileInfo: UploadedFile): void => {\n    setUploadedFile(fileInfo);\n    // 重置之前的结果\n    setAnalysisResults(null);\n    setProgressData({});\n    setAnalysisStatus({});\n    message.success('文件上传成功，可以开始分析了');\n  }, []);\n\n  // 文件移除处理\n  const handleFileRemoved = useCallback((): void => {\n    setUploadedFile(null);\n    setAnalysisResults(null);\n    setProgressData({});\n    setAnalysisStatus({});\n    setCurrentTaskId(null);\n    setIsAnalysisActive(false);\n    message.info('文件已移除');\n  }, []);\n\n  // 开始分析\n  const handleStartAnalysis = useCallback(async (type: string, options: AnalysisOptions): Promise<void> => {\n    if (!uploadedFile) {\n      message.error('请先上传APK文件');\n      return;\n    }\n\n    if (!apiConnected) {\n      message.error('API连接异常，请检查后端服务');\n      return;\n    }\n\n    try {\n      setIsAnalysisActive(true);\n      \n      // 重置状态\n      setAnalysisResults(null);\n      setProgressData({ currentStep: 0, logs: [] });\n      \n      // 更新分析状态\n      const newStatus = { ...analysisStatus };\n      if (type === 'static' || type === 'both') {\n        newStatus.static = 'running';\n      }\n      if (type === 'dynamic' || type === 'both') {\n        newStatus.dynamic = 'running';\n      }\n      setAnalysisStatus(newStatus);\n\n      // 调用简化版API开始分析\n      const response = await apkAnalysisAPI.simpleAnalyze(uploadedFile.file, options);\n      const taskId = response.data.task_id;\n      \n      setCurrentTaskId(taskId);\n      message.success(`分析任务已启动: ${taskId}`);\n      \n      // 开始轮询任务状态\n      pollTaskStatus(taskId);\n      \n    } catch (error: any) {\n      setIsAnalysisActive(false);\n      setAnalysisStatus({});\n      message.error(`启动分析失败: ${error.response?.data?.detail || error.message}`);\n      console.error('Analysis start failed:', error);\n      return;\n    }\n  }, [uploadedFile, apiConnected, analysisStatus]);\n\n  // 停止分析\n  const handleStopAnalysis = useCallback(async (type: string): Promise<void> => {\n    if (!currentTaskId) {\n      message.warning('没有正在运行的分析任务');\n      return;\n    }\n\n    try {\n      await apkAnalysisAPI.stopAnalysis(currentTaskId);\n      \n      // 更新状态\n      const newStatus = { ...analysisStatus };\n      if (type === 'static' || type === 'all') {\n        newStatus.static = 'stopped';\n      }\n      if (type === 'dynamic' || type === 'all') {\n        newStatus.dynamic = 'stopped';\n      }\n      setAnalysisStatus(newStatus);\n      \n      if (type === 'all') {\n        setIsAnalysisActive(false);\n        setCurrentTaskId(null);\n      }\n      \n      message.info('分析任务已停止');\n    } catch (error: any) {\n      message.error(`停止分析失败: ${error.response?.data?.detail || error.message}`);\n      console.error('Stop analysis failed:', error);\n    }\n  }, [currentTaskId, analysisStatus]);\n\n  // 轮询任务状态\n  const pollTaskStatus = useCallback(async (taskId: string): Promise<void> => {\n    const pollInterval = setInterval(async () => {\n      try {\n        const statusResponse = await apkAnalysisAPI.getSimpleTaskStatus(taskId);\n        const status = statusResponse.data.status;\n\n        if (status === 'completed') {\n          clearInterval(pollInterval);\n          // 获取结果\n          const resultResponse = await apkAnalysisAPI.getSimpleAnalysisResult(taskId);\n          console.log('Analysis result received:', resultResponse.data);\n          handleAnalysisComplete(resultResponse.data);\n        } else if (status === 'failed') {\n          clearInterval(pollInterval);\n          handleAnalysisError({ error: '分析任务失败' });\n        }\n      } catch (error) {\n        console.error('Poll task status failed:', error);\n        // 如果轮询失败太多次，停止轮询\n      }\n    }, 2000); // 每2秒轮询一次\n\n    // 设置最大轮询时间（10分钟）\n    setTimeout(() => {\n      clearInterval(pollInterval);\n    }, 600000);\n  }, []);\n\n  // 处理进度更新\n  const handleProgressUpdate = useCallback((data: ProgressUpdateMessage): void => {\n    setProgressData(prevData => ({\n      ...prevData,\n      ...data\n    }));\n\n    // 更新步骤状态\n    if (data.step !== undefined) {\n      setProgressData(prevData => ({\n        ...prevData,\n        currentStep: data.step\n      }));\n    }\n\n    // 添加日志\n    if (data.message) {\n      setProgressData(prevData => ({\n        ...prevData,\n        logs: [...(prevData.logs || []), {\n          id: Date.now().toString(),\n          level: data.level || 'info',\n          message: data.message || '',\n          timestamp: new Date()\n        }]\n      }));\n    }\n  }, []);\n\n  // 处理分析完成\n  const handleAnalysisComplete = useCallback((results: AnalysisResults): void => {\n    setIsAnalysisActive(false);\n    setAnalysisResults(results);\n    setAnalysisStatus({\n      static: 'completed',\n      dynamic: 'completed'\n    });\n    \n    // 更新进度为100%\n    setProgressData(prevData => ({\n      ...prevData,\n      currentStep: 6, // 假设总共6步\n      logs: [...(prevData.logs || []), {\n        id: Date.now().toString(),\n        level: 'success',\n        message: '分析完成！',\n        timestamp: new Date()\n      }]\n    }));\n    \n    message.success('APK分析完成！');\n  }, []);\n\n  // 处理分析错误\n  const handleAnalysisError = useCallback((error: { error: string }): void => {\n    setIsAnalysisActive(false);\n    setAnalysisStatus({\n      static: 'error',\n      dynamic: 'error'\n    });\n    \n    setProgressData(prevData => ({\n      ...prevData,\n      error: error.error || '分析过程中发生错误',\n      logs: [...(prevData.logs || []), {\n        id: Date.now().toString(),\n        level: 'error',\n        message: error.error || '分析失败',\n        timestamp: new Date()\n      }]\n    }));\n    \n    message.error(`分析失败: ${error.error || '未知错误'}`);\n  }, []);\n\n  // 处理结果导出\n  const handleExport = useCallback((format: string, filename: string): void => {\n    message.success(`结果已导出为 ${filename}`);\n  }, []);\n\n  // 处理结果分享\n  const handleShare = useCallback((results: AnalysisResults): void => {\n    // 这里可以实现分享功能，比如生成链接等\n    message.info('分享功能开发中...');\n  }, []);\n\n  return (\n    <ErrorBoundary>\n      <AppContainer>\n        <Header className=\"app-header\">\n          <div>\n            <Title level={2} className=\"main-title\">\n              📱 APK 分析系统\n            </Title>\n          </div>\n          \n          <Space>\n            <div className=\"status-indicator\">\n              <ApiOutlined className={debouncedApiCheck ? 'connected' : 'disconnected'} />\n              <span className={debouncedApiCheck ? 'connected' : 'disconnected'}>\n                API {debouncedApiCheck ? '已连接' : '未连接'}\n              </span>\n            </div>\n            \n            <Button \n              type=\"text\" \n              icon={<ReloadOutlined />} \n              onClick={checkAPIConnection}\n              loading={!debouncedApiCheck}\n            >\n              重新连接\n            </Button>\n          </Space>\n        </Header>\n\n        <Content className=\"app-content\">\n          {/* API连接警告 */}\n          {!debouncedApiCheck && (\n            <Alert\n              message=\"API连接异常\"\n              description=\"后端服务连接失败，请确保后端服务正在运行并检查网络连接\"\n              type=\"warning\"\n              showIcon\n              style={{ marginBottom: 20 }}\n              action={\n                <Button size=\"small\" onClick={checkAPIConnection}>\n                  重试连接\n                </Button>\n              }\n            />\n          )}\n\n          {/* 文件上传组件 */}\n          <ErrorBoundary fallback={<div>文件上传组件出现错误</div>}>\n            <FileUpload\n              onFileUploaded={handleFileUploaded}\n              onFileRemoved={handleFileRemoved}\n              disabled={isAnalysisActive}\n            />\n          </ErrorBoundary>\n\n          {/* 分析控制组件 */}\n          <ErrorBoundary fallback={<div>分析控制组件出现错误</div>}>\n            <AnalysisControls\n              uploadedFile={uploadedFile}\n              onStartAnalysis={handleStartAnalysis}\n              onStopAnalysis={handleStopAnalysis}\n              analysisStatus={analysisStatus}\n              disabled={!debouncedApiCheck}\n            />\n          </ErrorBoundary>\n\n          {/* 进度显示组件 */}\n          {(isAnalysisActive || Object.keys(progressData).length > 0) && (\n            <ErrorBoundary fallback={<div>进度显示组件出现错误</div>}>\n              <ProgressDisplay\n                analysisType=\"both\"\n                isActive={isAnalysisActive}\n                progressData={progressData}\n              />\n            </ErrorBoundary>\n          )}\n\n          {/* 结果显示组件 */}\n          {analysisResults && (\n            <ErrorBoundary fallback={<div>结果显示组件出现错误</div>}>\n              <ResultsDisplay\n                analysisResults={analysisResults}\n                onExport={handleExport}\n                onShare={handleShare}\n              />\n            </ErrorBoundary>\n          )}\n\n          {/* 全局加载遮罩 */}\n          {isAnalysisActive && (\n            <LoadingSpinner\n              loading={isAnalysisActive}\n              tip=\"正在分析APK文件，请耐心等待...\"\n              overlay={false}\n              showProgress={true}\n              progress={Math.round((progressData.currentStep || 0) / 6 * 100)}\n            />\n          )}\n        </Content>\n\n        <Footer className=\"app-footer\">\n          <p>APK Analysis System © 2024 - 专业的APK静态和动态分析工具</p>\n          <p>\n            <Space split={<span>|</span>}>\n              <span>支持静态代码分析</span>\n              <span>动态运行监控</span>\n              <span>网络流量捕获</span>\n              <span>智能UI遍历</span>\n            </Space>\n          </p>\n        </Footer>\n      </AppContainer>\n    </ErrorBoundary>\n  );\n};\n\nexport default App;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,MAAM,EAAEC,UAAU,EAAEC,OAAO,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,QAAQ,MAAM;AACxE,SAASC,cAAc,EAAEC,WAAW,QAAQ,mBAAmB;AAC/D,OAAOC,MAAM,MAAM,mBAAmB;AAEtC,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,cAAc,MAAM,6BAA6B;AACxD,SAASC,cAAc,EAAEC,gBAAgB,QAAQ,gBAAgB;AACjE,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,WAAW,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAWlD,MAAM;EAAEC,MAAM;EAAEC,OAAO;EAAEC;AAAO,CAAC,GAAGvB,MAAM;AAC1C,MAAM;EAAEwB;AAAM,CAAC,GAAGvB,UAAU;AAE5B,MAAMwB,YAAY,GAAGjB,MAAM,CAACR,MAAM,CAAC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC0B,EAAA,GA3CID,YAAY;AA6ClB,MAAME,GAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGjC,QAAQ,CAAsB,IAAI,CAAC;EAC3E,MAAM,CAACkC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnC,QAAQ,CAAgB,IAAI,CAAC;EACvE,MAAM,CAACoC,cAAc,EAAEC,iBAAiB,CAAC,GAAGrC,QAAQ,CAAiB,CAAC,CAAC,CAAC;EACxE,MAAM,CAACsC,YAAY,EAAEC,eAAe,CAAC,GAAGvC,QAAQ,CAAe,CAAC,CAAC,CAAC;EAClE,MAAM,CAACwC,eAAe,EAAEC,kBAAkB,CAAC,GAAGzC,QAAQ,CAAyB,IAAI,CAAC;EACpF,MAAM,CAAC0C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3C,QAAQ,CAAU,KAAK,CAAC;EACxE,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAU,KAAK,CAAC;EAChE,MAAM,CAAC8C,SAAS,CAAC,GAAG9C,QAAQ,CAAmB,IAAImB,gBAAgB,CAAC,CAAC,CAAC;;EAEtE;EACA,MAAM,CAAC4B,YAAY,EAAEC,eAAe,CAAC,GAAG5B,eAAe,CAAC,uBAAuB,EAAE;IAC/E6B,cAAc,EAAE,GAAG;IACnBC,qBAAqB,EAAE,IAAI;IAC3BC,sBAAsB,EAAE,IAAI;IAC5BC,KAAK,EAAE;EACT,CAAC,CAAC;;EAEF;EACA,MAAMC,iBAAiB,GAAGhC,WAAW,CAACuB,YAAY,EAAE,IAAI,CAAC;;EAEzD;EACA3C,SAAS,CAAC,MAAM;IACdqD,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACArD,SAAS,CAAC,MAAM;IACd,IAAIiC,aAAa,EAAE;MACjBY,SAAS,CAACS,OAAO,CAACrB,aAAa,CAAC;MAEhCY,SAAS,CAACU,EAAE,CAAC,iBAAiB,EAAEC,oBAAoB,CAAC;MACrDX,SAAS,CAACU,EAAE,CAAC,mBAAmB,EAAEE,sBAAsB,CAAC;MACzDZ,SAAS,CAACU,EAAE,CAAC,gBAAgB,EAAEG,mBAAmB,CAAC;MAEnD,OAAO,MAAM;QACXb,SAAS,CAACc,GAAG,CAAC,iBAAiB,EAAEH,oBAAoB,CAAC;QACtDX,SAAS,CAACc,GAAG,CAAC,mBAAmB,EAAEF,sBAAsB,CAAC;QAC1DZ,SAAS,CAACc,GAAG,CAAC,gBAAgB,EAAED,mBAAmB,CAAC;QACpDb,SAAS,CAACe,UAAU,CAAC,CAAC;MACxB,CAAC;IACH;IACA,OAAOC,SAAS;EAClB,CAAC,EAAE,CAAC5B,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAMoB,kBAAkB,GAAGpD,WAAW,CAAC,YAA2B;IAChE,IAAI;MACF,MAAMgB,cAAc,CAAC6C,WAAW,CAAC,CAAC;MAClClB,eAAe,CAAC,IAAI,CAAC;MACrBxC,OAAO,CAAC2D,OAAO,CAAC,SAAS,CAAC;IAC5B,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBpB,eAAe,CAAC,KAAK,CAAC;MACtBxC,OAAO,CAAC4D,KAAK,CAAC,iBAAiB,CAAC;MAChCC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAME,kBAAkB,GAAGjE,WAAW,CAAEkE,QAAsB,IAAW;IACvEnC,eAAe,CAACmC,QAAQ,CAAC;IACzB;IACA3B,kBAAkB,CAAC,IAAI,CAAC;IACxBF,eAAe,CAAC,CAAC,CAAC,CAAC;IACnBF,iBAAiB,CAAC,CAAC,CAAC,CAAC;IACrBhC,OAAO,CAAC2D,OAAO,CAAC,gBAAgB,CAAC;EACnC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMK,iBAAiB,GAAGnE,WAAW,CAAC,MAAY;IAChD+B,eAAe,CAAC,IAAI,CAAC;IACrBQ,kBAAkB,CAAC,IAAI,CAAC;IACxBF,eAAe,CAAC,CAAC,CAAC,CAAC;IACnBF,iBAAiB,CAAC,CAAC,CAAC,CAAC;IACrBF,gBAAgB,CAAC,IAAI,CAAC;IACtBQ,mBAAmB,CAAC,KAAK,CAAC;IAC1BtC,OAAO,CAACiE,IAAI,CAAC,OAAO,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,mBAAmB,GAAGrE,WAAW,CAAC,OAAOsE,IAAY,EAAEC,OAAwB,KAAoB;IACvG,IAAI,CAACzC,YAAY,EAAE;MACjB3B,OAAO,CAAC4D,KAAK,CAAC,WAAW,CAAC;MAC1B;IACF;IAEA,IAAI,CAACrB,YAAY,EAAE;MACjBvC,OAAO,CAAC4D,KAAK,CAAC,iBAAiB,CAAC;MAChC;IACF;IAEA,IAAI;MACFtB,mBAAmB,CAAC,IAAI,CAAC;;MAEzB;MACAF,kBAAkB,CAAC,IAAI,CAAC;MACxBF,eAAe,CAAC;QAAEmC,WAAW,EAAE,CAAC;QAAEC,IAAI,EAAE;MAAG,CAAC,CAAC;;MAE7C;MACA,MAAMC,SAAS,GAAG;QAAE,GAAGxC;MAAe,CAAC;MACvC,IAAIoC,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,MAAM,EAAE;QACxCI,SAAS,CAACC,MAAM,GAAG,SAAS;MAC9B;MACA,IAAIL,IAAI,KAAK,SAAS,IAAIA,IAAI,KAAK,MAAM,EAAE;QACzCI,SAAS,CAACE,OAAO,GAAG,SAAS;MAC/B;MACAzC,iBAAiB,CAACuC,SAAS,CAAC;;MAE5B;MACA,MAAMG,QAAQ,GAAG,MAAM7D,cAAc,CAAC8D,aAAa,CAAChD,YAAY,CAACiD,IAAI,EAAER,OAAO,CAAC;MAC/E,MAAMS,MAAM,GAAGH,QAAQ,CAACI,IAAI,CAACC,OAAO;MAEpCjD,gBAAgB,CAAC+C,MAAM,CAAC;MACxB7E,OAAO,CAAC2D,OAAO,CAAC,YAAYkB,MAAM,EAAE,CAAC;;MAErC;MACAG,cAAc,CAACH,MAAM,CAAC;IAExB,CAAC,CAAC,OAAOjB,KAAU,EAAE;MAAA,IAAAqB,eAAA,EAAAC,oBAAA;MACnB5C,mBAAmB,CAAC,KAAK,CAAC;MAC1BN,iBAAiB,CAAC,CAAC,CAAC,CAAC;MACrBhC,OAAO,CAAC4D,KAAK,CAAC,WAAW,EAAAqB,eAAA,GAAArB,KAAK,CAACc,QAAQ,cAAAO,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBH,IAAI,cAAAI,oBAAA,uBAApBA,oBAAA,CAAsBC,MAAM,KAAIvB,KAAK,CAAC5D,OAAO,EAAE,CAAC;MACzE6D,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C;IACF;EACF,CAAC,EAAE,CAACjC,YAAY,EAAEY,YAAY,EAAER,cAAc,CAAC,CAAC;;EAEhD;EACA,MAAMqD,kBAAkB,GAAGvF,WAAW,CAAC,MAAOsE,IAAY,IAAoB;IAC5E,IAAI,CAACtC,aAAa,EAAE;MAClB7B,OAAO,CAACqF,OAAO,CAAC,aAAa,CAAC;MAC9B;IACF;IAEA,IAAI;MACF,MAAMxE,cAAc,CAACyE,YAAY,CAACzD,aAAa,CAAC;;MAEhD;MACA,MAAM0C,SAAS,GAAG;QAAE,GAAGxC;MAAe,CAAC;MACvC,IAAIoC,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,KAAK,EAAE;QACvCI,SAAS,CAACC,MAAM,GAAG,SAAS;MAC9B;MACA,IAAIL,IAAI,KAAK,SAAS,IAAIA,IAAI,KAAK,KAAK,EAAE;QACxCI,SAAS,CAACE,OAAO,GAAG,SAAS;MAC/B;MACAzC,iBAAiB,CAACuC,SAAS,CAAC;MAE5B,IAAIJ,IAAI,KAAK,KAAK,EAAE;QAClB7B,mBAAmB,CAAC,KAAK,CAAC;QAC1BR,gBAAgB,CAAC,IAAI,CAAC;MACxB;MAEA9B,OAAO,CAACiE,IAAI,CAAC,SAAS,CAAC;IACzB,CAAC,CAAC,OAAOL,KAAU,EAAE;MAAA,IAAA2B,gBAAA,EAAAC,qBAAA;MACnBxF,OAAO,CAAC4D,KAAK,CAAC,WAAW,EAAA2B,gBAAA,GAAA3B,KAAK,CAACc,QAAQ,cAAAa,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBT,IAAI,cAAAU,qBAAA,uBAApBA,qBAAA,CAAsBL,MAAM,KAAIvB,KAAK,CAAC5D,OAAO,EAAE,CAAC;MACzE6D,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC,EAAE,CAAC/B,aAAa,EAAEE,cAAc,CAAC,CAAC;;EAEnC;EACA,MAAMiD,cAAc,GAAGnF,WAAW,CAAC,MAAOgF,MAAc,IAAoB;IAC1E,MAAMY,YAAY,GAAGC,WAAW,CAAC,YAAY;MAC3C,IAAI;QACF,MAAMC,cAAc,GAAG,MAAM9E,cAAc,CAAC+E,mBAAmB,CAACf,MAAM,CAAC;QACvE,MAAMgB,MAAM,GAAGF,cAAc,CAACb,IAAI,CAACe,MAAM;QAEzC,IAAIA,MAAM,KAAK,WAAW,EAAE;UAC1BC,aAAa,CAACL,YAAY,CAAC;UAC3B;UACA,MAAMM,cAAc,GAAG,MAAMlF,cAAc,CAACmF,uBAAuB,CAACnB,MAAM,CAAC;UAC3EhB,OAAO,CAACoC,GAAG,CAAC,2BAA2B,EAAEF,cAAc,CAACjB,IAAI,CAAC;UAC7DzB,sBAAsB,CAAC0C,cAAc,CAACjB,IAAI,CAAC;QAC7C,CAAC,MAAM,IAAIe,MAAM,KAAK,QAAQ,EAAE;UAC9BC,aAAa,CAACL,YAAY,CAAC;UAC3BnC,mBAAmB,CAAC;YAAEM,KAAK,EAAE;UAAS,CAAC,CAAC;QAC1C;MACF,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD;MACF;IACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEV;IACAsC,UAAU,CAAC,MAAM;MACfJ,aAAa,CAACL,YAAY,CAAC;IAC7B,CAAC,EAAE,MAAM,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMrC,oBAAoB,GAAGvD,WAAW,CAAEiF,IAA2B,IAAW;IAC9E5C,eAAe,CAACiE,QAAQ,KAAK;MAC3B,GAAGA,QAAQ;MACX,GAAGrB;IACL,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIA,IAAI,CAACsB,IAAI,KAAK3C,SAAS,EAAE;MAC3BvB,eAAe,CAACiE,QAAQ,KAAK;QAC3B,GAAGA,QAAQ;QACX9B,WAAW,EAAES,IAAI,CAACsB;MACpB,CAAC,CAAC,CAAC;IACL;;IAEA;IACA,IAAItB,IAAI,CAAC9E,OAAO,EAAE;MAChBkC,eAAe,CAACiE,QAAQ,KAAK;QAC3B,GAAGA,QAAQ;QACX7B,IAAI,EAAE,CAAC,IAAI6B,QAAQ,CAAC7B,IAAI,IAAI,EAAE,CAAC,EAAE;UAC/B+B,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;UACzBC,KAAK,EAAE3B,IAAI,CAAC2B,KAAK,IAAI,MAAM;UAC3BzG,OAAO,EAAE8E,IAAI,CAAC9E,OAAO,IAAI,EAAE;UAC3B0G,SAAS,EAAE,IAAIJ,IAAI,CAAC;QACtB,CAAC;MACH,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMjD,sBAAsB,GAAGxD,WAAW,CAAE8G,OAAwB,IAAW;IAC7ErE,mBAAmB,CAAC,KAAK,CAAC;IAC1BF,kBAAkB,CAACuE,OAAO,CAAC;IAC3B3E,iBAAiB,CAAC;MAChBwC,MAAM,EAAE,WAAW;MACnBC,OAAO,EAAE;IACX,CAAC,CAAC;;IAEF;IACAvC,eAAe,CAACiE,QAAQ,KAAK;MAC3B,GAAGA,QAAQ;MACX9B,WAAW,EAAE,CAAC;MAAE;MAChBC,IAAI,EAAE,CAAC,IAAI6B,QAAQ,CAAC7B,IAAI,IAAI,EAAE,CAAC,EAAE;QAC/B+B,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;QACzBC,KAAK,EAAE,SAAS;QAChBzG,OAAO,EAAE,OAAO;QAChB0G,SAAS,EAAE,IAAIJ,IAAI,CAAC;MACtB,CAAC;IACH,CAAC,CAAC,CAAC;IAEHtG,OAAO,CAAC2D,OAAO,CAAC,UAAU,CAAC;EAC7B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAML,mBAAmB,GAAGzD,WAAW,CAAE+D,KAAwB,IAAW;IAC1EtB,mBAAmB,CAAC,KAAK,CAAC;IAC1BN,iBAAiB,CAAC;MAChBwC,MAAM,EAAE,OAAO;MACfC,OAAO,EAAE;IACX,CAAC,CAAC;IAEFvC,eAAe,CAACiE,QAAQ,KAAK;MAC3B,GAAGA,QAAQ;MACXvC,KAAK,EAAEA,KAAK,CAACA,KAAK,IAAI,WAAW;MACjCU,IAAI,EAAE,CAAC,IAAI6B,QAAQ,CAAC7B,IAAI,IAAI,EAAE,CAAC,EAAE;QAC/B+B,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;QACzBC,KAAK,EAAE,OAAO;QACdzG,OAAO,EAAE4D,KAAK,CAACA,KAAK,IAAI,MAAM;QAC9B8C,SAAS,EAAE,IAAIJ,IAAI,CAAC;MACtB,CAAC;IACH,CAAC,CAAC,CAAC;IAEHtG,OAAO,CAAC4D,KAAK,CAAC,SAASA,KAAK,CAACA,KAAK,IAAI,MAAM,EAAE,CAAC;EACjD,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMgD,YAAY,GAAG/G,WAAW,CAAC,CAACgH,MAAc,EAAEC,QAAgB,KAAW;IAC3E9G,OAAO,CAAC2D,OAAO,CAAC,UAAUmD,QAAQ,EAAE,CAAC;EACvC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,WAAW,GAAGlH,WAAW,CAAE8G,OAAwB,IAAW;IAClE;IACA3G,OAAO,CAACiE,IAAI,CAAC,YAAY,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;EAEN,oBACE/C,OAAA,CAACP,aAAa;IAAAqG,QAAA,eACZ9F,OAAA,CAACK,YAAY;MAAAyF,QAAA,gBACX9F,OAAA,CAACC,MAAM;QAAC8F,SAAS,EAAC,YAAY;QAAAD,QAAA,gBAC5B9F,OAAA;UAAA8F,QAAA,eACE9F,OAAA,CAACI,KAAK;YAACmF,KAAK,EAAE,CAAE;YAACQ,SAAS,EAAC,YAAY;YAAAD,QAAA,EAAC;UAExC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENnG,OAAA,CAACjB,KAAK;UAAA+G,QAAA,gBACJ9F,OAAA;YAAK+F,SAAS,EAAC,kBAAkB;YAAAD,QAAA,gBAC/B9F,OAAA,CAACb,WAAW;cAAC4G,SAAS,EAAEjE,iBAAiB,GAAG,WAAW,GAAG;YAAe;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5EnG,OAAA;cAAM+F,SAAS,EAAEjE,iBAAiB,GAAG,WAAW,GAAG,cAAe;cAAAgE,QAAA,GAAC,MAC7D,EAAChE,iBAAiB,GAAG,KAAK,GAAG,KAAK;YAAA;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAENnG,OAAA,CAAChB,MAAM;YACLiE,IAAI,EAAC,MAAM;YACXmD,IAAI,eAAEpG,OAAA,CAACd,cAAc;cAAA8G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBE,OAAO,EAAEtE,kBAAmB;YAC5BuE,OAAO,EAAE,CAACxE,iBAAkB;YAAAgE,QAAA,EAC7B;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAETnG,OAAA,CAACE,OAAO;QAAC6F,SAAS,EAAC,aAAa;QAAAD,QAAA,GAE7B,CAAChE,iBAAiB,iBACjB9B,OAAA,CAACf,KAAK;UACJH,OAAO,EAAC,6BAAS;UACjByH,WAAW,EAAC,oKAA6B;UACzCtD,IAAI,EAAC,SAAS;UACduD,QAAQ;UACRC,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAE;UAC5BC,MAAM,eACJ3G,OAAA,CAAChB,MAAM;YAAC4H,IAAI,EAAC,OAAO;YAACP,OAAO,EAAEtE,kBAAmB;YAAA+D,QAAA,EAAC;UAElD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QACT;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CACF,eAGDnG,OAAA,CAACP,aAAa;UAACoH,QAAQ,eAAE7G,OAAA;YAAA8F,QAAA,EAAK;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAE;UAAAL,QAAA,eAC7C9F,OAAA,CAACX,UAAU;YACTyH,cAAc,EAAElE,kBAAmB;YACnCmE,aAAa,EAAEjE,iBAAkB;YACjCkE,QAAQ,EAAE7F;UAAiB;YAAA6E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW,CAAC,eAGhBnG,OAAA,CAACP,aAAa;UAACoH,QAAQ,eAAE7G,OAAA;YAAA8F,QAAA,EAAK;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAE;UAAAL,QAAA,eAC7C9F,OAAA,CAACV,gBAAgB;YACfmB,YAAY,EAAEA,YAAa;YAC3BwG,eAAe,EAAEjE,mBAAoB;YACrCkE,cAAc,EAAEhD,kBAAmB;YACnCrD,cAAc,EAAEA,cAAe;YAC/BmG,QAAQ,EAAE,CAAClF;UAAkB;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW,CAAC,EAGf,CAAChF,gBAAgB,IAAIgG,MAAM,CAACC,IAAI,CAACrG,YAAY,CAAC,CAACsG,MAAM,GAAG,CAAC,kBACxDrH,OAAA,CAACP,aAAa;UAACoH,QAAQ,eAAE7G,OAAA;YAAA8F,QAAA,EAAK;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAE;UAAAL,QAAA,eAC7C9F,OAAA,CAACT,eAAe;YACd+H,YAAY,EAAC,MAAM;YACnBC,QAAQ,EAAEpG,gBAAiB;YAC3BJ,YAAY,EAAEA;UAAa;YAAAiF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW,CAChB,EAGAlF,eAAe,iBACdjB,OAAA,CAACP,aAAa;UAACoH,QAAQ,eAAE7G,OAAA;YAAA8F,QAAA,EAAK;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAE;UAAAL,QAAA,eAC7C9F,OAAA,CAACR,cAAc;YACbyB,eAAe,EAAEA,eAAgB;YACjCuG,QAAQ,EAAE9B,YAAa;YACvB+B,OAAO,EAAE5B;UAAY;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW,CAChB,EAGAhF,gBAAgB,iBACfnB,OAAA,CAACN,cAAc;UACb4G,OAAO,EAAEnF,gBAAiB;UAC1BuG,GAAG,EAAC,gFAAoB;UACxBC,OAAO,EAAE,KAAM;UACfC,YAAY,EAAE,IAAK;UACnBC,QAAQ,EAAEC,IAAI,CAACC,KAAK,CAAC,CAAChH,YAAY,CAACoC,WAAW,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG;QAAE;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAEVnG,OAAA,CAACG,MAAM;QAAC4F,SAAS,EAAC,YAAY;QAAAD,QAAA,gBAC5B9F,OAAA;UAAA8F,QAAA,EAAG;QAA4C;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACnDnG,OAAA;UAAA8F,QAAA,eACE9F,OAAA,CAACjB,KAAK;YAACiJ,KAAK,eAAEhI,OAAA;cAAA8F,QAAA,EAAM;YAAC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAE;YAAAL,QAAA,gBAC3B9F,OAAA;cAAA8F,QAAA,EAAM;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrBnG,OAAA;cAAA8F,QAAA,EAAM;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnBnG,OAAA;cAAA8F,QAAA,EAAM;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnBnG,OAAA;cAAA8F,QAAA,EAAM;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEpB,CAAC;AAAC3F,EAAA,CArYID,GAAa;EAAA,QAYuBV,eAAe,EAQ7BC,WAAW;AAAA;AAAAmI,GAAA,GApBjC1H,GAAa;AAuYnB,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAA2H,GAAA;AAAAC,YAAA,CAAA5H,EAAA;AAAA4H,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}