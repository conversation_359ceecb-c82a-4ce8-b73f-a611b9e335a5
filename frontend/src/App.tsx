import React, { useState, useEffect, useCallback } from 'react';
import { Layout, Typography, message, Space, Button, Alert } from 'antd';
import { ReloadOutlined, ApiOutlined } from '@ant-design/icons';
import styled from 'styled-components';

import FileUpload from './components/FileUpload';
import AnalysisControls from './components/AnalysisControls';
import ProgressDisplay from './components/ProgressDisplay';
import ResultsDisplay from './components/ResultsDisplay';
import ErrorBoundary from './components/ErrorBoundary';
import LoadingSpinner from './components/LoadingSpinner';
import { apkAnalysisAPI, WebSocketManager } from './services/api';
import { useLocalStorage } from './hooks/useLocalStorage';
import { useDebounce } from './hooks/useDebounce';
import { 
  UploadedFile, 
  AnalysisStatus, 
  ProgressData, 
  AnalysisResults,
  AnalysisResultResponse,
  AnalysisOptions,
  ProgressUpdateMessage
} from './types';

const { Header, Content, Footer } = Layout;
const { Title } = Typography;

const AppContainer = styled(Layout)`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  
  .app-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 0 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  .app-content {
    padding: 24px;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
  }
  
  .app-footer {
    background: rgba(255, 255, 255, 0.9);
    text-align: center;
    color: #666;
  }
  
  .main-title {
    margin: 0 !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .connected { color: #52c41a; }
  .disconnected { color: #ff4d4f; }
`;

const App: React.FC = () => {
  // 应用状态
  const [uploadedFile, setUploadedFile] = useState<UploadedFile | null>(null);
  const [currentTaskId, setCurrentTaskId] = useState<string | null>(null);
  const [analysisStatus, setAnalysisStatus] = useState<AnalysisStatus>({});
  const [progressData, setProgressData] = useState<ProgressData>({});
  const [analysisResults, setAnalysisResults] = useState<AnalysisResults | null>(null);
  const [isAnalysisActive, setIsAnalysisActive] = useState<boolean>(false);
  const [apiConnected, setApiConnected] = useState<boolean>(false);
  const [wsManager] = useState<WebSocketManager>(new WebSocketManager());
  
  // 持久化用户设置
  const [userSettings, setUserSettings] = useLocalStorage('apk-analyzer-settings', {
    defaultTimeout: 300,
    enableStaticByDefault: true,
    enableDynamicByDefault: true,
    theme: 'light'
  });
  
  // API连接状态防抖，避免频繁检查
  const debouncedApiCheck = useDebounce(apiConnected, 1000);

  // 初始化API连接检查
  useEffect(() => {
    checkAPIConnection();
  }, []);

  // WebSocket消息处理
  useEffect(() => {
    if (currentTaskId) {
      wsManager.connect(currentTaskId);
      
      wsManager.on('progress_update', handleProgressUpdate);
      wsManager.on('analysis_complete', handleAnalysisComplete);
      wsManager.on('analysis_error', handleAnalysisError);
      
      return () => {
        wsManager.off('progress_update', handleProgressUpdate);
        wsManager.off('analysis_complete', handleAnalysisComplete);
        wsManager.off('analysis_error', handleAnalysisError);
        wsManager.disconnect();
      };
    }
    return undefined;
  }, [currentTaskId]);

  // 检查API连接
  const checkAPIConnection = useCallback(async (): Promise<void> => {
    try {
      await apkAnalysisAPI.healthCheck();
      setApiConnected(true);
      message.success('API连接正常');
    } catch (error: any) {
      setApiConnected(false);
      message.error('API连接失败，请检查后端服务');
      console.error('API connection failed:', error);
    }
  }, []);

  // 文件上传处理
  const handleFileUploaded = useCallback((fileInfo: UploadedFile): void => {
    setUploadedFile(fileInfo);
    // 重置之前的结果
    setAnalysisResults(null);
    setProgressData({});
    setAnalysisStatus({});
    message.success('文件上传成功，可以开始分析了');
  }, []);

  // 文件移除处理
  const handleFileRemoved = useCallback((): void => {
    setUploadedFile(null);
    setAnalysisResults(null);
    setProgressData({});
    setAnalysisStatus({});
    setCurrentTaskId(null);
    setIsAnalysisActive(false);
    message.info('文件已移除');
  }, []);

  // 开始分析
  const handleStartAnalysis = useCallback(async (type: string, options: AnalysisOptions): Promise<void> => {
    if (!uploadedFile) {
      message.error('请先上传APK文件');
      return;
    }

    if (!apiConnected) {
      message.error('API连接异常，请检查后端服务');
      return;
    }

    try {
      setIsAnalysisActive(true);
      
      // 重置状态
      setAnalysisResults(null);
      setProgressData({ currentStep: 0, logs: [] });
      
      // 更新分析状态
      const newStatus = { ...analysisStatus };
      if (type === 'static' || type === 'both') {
        newStatus.static = 'running';
      }
      if (type === 'dynamic' || type === 'both') {
        newStatus.dynamic = 'running';
      }
      setAnalysisStatus(newStatus);

      // 调用简化版API开始分析
      const response = await apkAnalysisAPI.simpleAnalyze(uploadedFile.file, options);
      const taskId = response.data.task_id;
      
      setCurrentTaskId(taskId);
      message.success(`分析任务已启动: ${taskId}`);
      
      // 开始轮询任务状态
      pollTaskStatus(taskId);
      
    } catch (error: any) {
      setIsAnalysisActive(false);
      setAnalysisStatus({});
      message.error(`启动分析失败: ${error.response?.data?.detail || error.message}`);
      console.error('Analysis start failed:', error);
      return;
    }
  }, [uploadedFile, apiConnected, analysisStatus]);

  // 停止分析
  const handleStopAnalysis = useCallback(async (type: string): Promise<void> => {
    if (!currentTaskId) {
      message.warning('没有正在运行的分析任务');
      return;
    }

    try {
      await apkAnalysisAPI.stopAnalysis(currentTaskId);
      
      // 更新状态
      const newStatus = { ...analysisStatus };
      if (type === 'static' || type === 'all') {
        newStatus.static = 'stopped';
      }
      if (type === 'dynamic' || type === 'all') {
        newStatus.dynamic = 'stopped';
      }
      setAnalysisStatus(newStatus);
      
      if (type === 'all') {
        setIsAnalysisActive(false);
        setCurrentTaskId(null);
      }
      
      message.info('分析任务已停止');
    } catch (error: any) {
      message.error(`停止分析失败: ${error.response?.data?.detail || error.message}`);
      console.error('Stop analysis failed:', error);
    }
  }, [currentTaskId, analysisStatus]);

  // 轮询任务状态
  const pollTaskStatus = useCallback(async (taskId: string): Promise<void> => {
    const pollInterval = setInterval(async () => {
      try {
        const statusResponse = await apkAnalysisAPI.getSimpleTaskStatus(taskId);
        const status = statusResponse.data.status;

        if (status === 'completed') {
          clearInterval(pollInterval);
          // 获取结果
          const resultResponse = await apkAnalysisAPI.getSimpleAnalysisResult(taskId);
          console.log('Analysis result received:', resultResponse.data);
          handleAnalysisComplete(resultResponse.data);
        } else if (status === 'failed') {
          clearInterval(pollInterval);
          handleAnalysisError({ error: '分析任务失败' });
        }
      } catch (error) {
        console.error('Poll task status failed:', error);
        // 如果轮询失败太多次，停止轮询
      }
    }, 2000); // 每2秒轮询一次

    // 设置最大轮询时间（10分钟）
    setTimeout(() => {
      clearInterval(pollInterval);
    }, 600000);
  }, []);

  // 处理进度更新
  const handleProgressUpdate = useCallback((data: ProgressUpdateMessage): void => {
    setProgressData(prevData => ({
      ...prevData,
      ...data
    }));

    // 更新步骤状态
    if (data.step !== undefined) {
      setProgressData(prevData => ({
        ...prevData,
        currentStep: data.step
      }));
    }

    // 添加日志
    if (data.message) {
      setProgressData(prevData => ({
        ...prevData,
        logs: [...(prevData.logs || []), {
          id: Date.now().toString(),
          level: data.level || 'info',
          message: data.message || '',
          timestamp: new Date()
        }]
      }));
    }
  }, []);

  // 处理分析完成
  const handleAnalysisComplete = useCallback((results: AnalysisResults): void => {
    setIsAnalysisActive(false);
    setAnalysisResults(results);
    setAnalysisStatus({
      static: 'completed',
      dynamic: 'completed'
    });
    
    // 更新进度为100%
    setProgressData(prevData => ({
      ...prevData,
      currentStep: 6, // 假设总共6步
      logs: [...(prevData.logs || []), {
        id: Date.now().toString(),
        level: 'success',
        message: '分析完成！',
        timestamp: new Date()
      }]
    }));
    
    message.success('APK分析完成！');
  }, []);

  // 处理分析错误
  const handleAnalysisError = useCallback((error: { error: string }): void => {
    setIsAnalysisActive(false);
    setAnalysisStatus({
      static: 'error',
      dynamic: 'error'
    });
    
    setProgressData(prevData => ({
      ...prevData,
      error: error.error || '分析过程中发生错误',
      logs: [...(prevData.logs || []), {
        id: Date.now().toString(),
        level: 'error',
        message: error.error || '分析失败',
        timestamp: new Date()
      }]
    }));
    
    message.error(`分析失败: ${error.error || '未知错误'}`);
  }, []);

  // 处理结果导出
  const handleExport = useCallback((format: string, filename: string): void => {
    message.success(`结果已导出为 ${filename}`);
  }, []);

  // 处理结果分享
  const handleShare = useCallback((results: AnalysisResults): void => {
    // 这里可以实现分享功能，比如生成链接等
    message.info('分享功能开发中...');
  }, []);

  return (
    <ErrorBoundary>
      <AppContainer>
        <Header className="app-header">
          <div>
            <Title level={2} className="main-title">
              📱 APK 分析系统
            </Title>
          </div>
          
          <Space>
            <div className="status-indicator">
              <ApiOutlined className={debouncedApiCheck ? 'connected' : 'disconnected'} />
              <span className={debouncedApiCheck ? 'connected' : 'disconnected'}>
                API {debouncedApiCheck ? '已连接' : '未连接'}
              </span>
            </div>
            
            <Button 
              type="text" 
              icon={<ReloadOutlined />} 
              onClick={checkAPIConnection}
              loading={!debouncedApiCheck}
            >
              重新连接
            </Button>
          </Space>
        </Header>

        <Content className="app-content">
          {/* API连接警告 */}
          {!debouncedApiCheck && (
            <Alert
              message="API连接异常"
              description="后端服务连接失败，请确保后端服务正在运行并检查网络连接"
              type="warning"
              showIcon
              style={{ marginBottom: 20 }}
              action={
                <Button size="small" onClick={checkAPIConnection}>
                  重试连接
                </Button>
              }
            />
          )}

          {/* 文件上传组件 */}
          <ErrorBoundary fallback={<div>文件上传组件出现错误</div>}>
            <FileUpload
              onFileUploaded={handleFileUploaded}
              onFileRemoved={handleFileRemoved}
              disabled={isAnalysisActive}
            />
          </ErrorBoundary>

          {/* 分析控制组件 */}
          <ErrorBoundary fallback={<div>分析控制组件出现错误</div>}>
            <AnalysisControls
              uploadedFile={uploadedFile}
              onStartAnalysis={handleStartAnalysis}
              onStopAnalysis={handleStopAnalysis}
              analysisStatus={analysisStatus}
              disabled={!debouncedApiCheck}
            />
          </ErrorBoundary>

          {/* 进度显示组件 */}
          {(isAnalysisActive || Object.keys(progressData).length > 0) && (
            <ErrorBoundary fallback={<div>进度显示组件出现错误</div>}>
              <ProgressDisplay
                analysisType="both"
                isActive={isAnalysisActive}
                progressData={progressData}
              />
            </ErrorBoundary>
          )}

          {/* 结果显示组件 */}
          {analysisResults && (
            <ErrorBoundary fallback={<div>结果显示组件出现错误</div>}>
              <ResultsDisplay
                analysisResults={analysisResults}
                onExport={handleExport}
                onShare={handleShare}
              />
            </ErrorBoundary>
          )}

          {/* 全局加载遮罩 */}
          {isAnalysisActive && (
            <LoadingSpinner
              loading={isAnalysisActive}
              tip="正在分析APK文件，请耐心等待..."
              overlay={false}
              showProgress={true}
              progress={Math.round((progressData.currentStep || 0) / 6 * 100)}
            />
          )}
        </Content>

        <Footer className="app-footer">
          <p>APK Analysis System © 2024 - 专业的APK静态和动态分析工具</p>
          <p>
            <Space split={<span>|</span>}>
              <span>支持静态代码分析</span>
              <span>动态运行监控</span>
              <span>网络流量捕获</span>
              <span>智能UI遍历</span>
            </Space>
          </p>
        </Footer>
      </AppContainer>
    </ErrorBoundary>
  );
};

export default App;