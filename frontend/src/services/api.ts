import axios, { AxiosInstance, AxiosResponse } from 'axios';
import {
  HealthCheckResponse,
  TaskResponse,
  TaskStatusResponse,
  AnalysisResults,
  AnalysisResultResponse,
  AnalysisOptions,
  WebSocketMessage,
  ProgressUpdateMessage,
  ExportFormat
} from '../types';

// API 基础配置
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

// 创建 axios 实例
const api: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    console.log('API Request:', config.method?.toUpperCase(), config.url);
    return config;
  },
  (error) => {
    console.error('Request Error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    console.log('API Response:', response.status, response.config.url);
    return response;
  },
  (error) => {
    console.error('Response Error:', error.response?.status, error.message);
    return Promise.reject(error);
  }
);

// APK 分析 API 服务
export const apkAnalysisAPI = {
  // 健康检查
  healthCheck: (): Promise<AxiosResponse<HealthCheckResponse>> =>
    api.get('/api/v1/health'),

  // 上传APK文件进行分析
  uploadAndAnalyze: (
    file: File, 
    options: AnalysisOptions = {}
  ): Promise<AxiosResponse<TaskResponse>> => {
    const formData = new FormData();
    formData.append('file', file);
    
    // 分析选项
    const analysisOptions = {
      enable_static: options.enableStatic !== false,
      enable_dynamic: options.enableDynamic !== false,
      timeout: options.timeout || 300,
      priority: options.priority || 'normal',
      ...options
    };
    
    formData.append('options', JSON.stringify(analysisOptions));

    return api.post('/api/v1/analyze', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 60000, // 上传可能需要更长时间
    });
  },

  // 简化分析接口
  simpleAnalyze: (file: File, options: AnalysisOptions = {}): Promise<AxiosResponse<AnalysisResults>> => {
    const formData = new FormData();
    formData.append('file', file);

    // 添加分析选项（只进行静态分析）
    const analysisOptions = {
      static_analysis: options.enableStatic !== false,
      dynamic_analysis: options.enableDynamic !== false,
      timeout: options.timeout || 300,
      ...options
    };
    formData.append('options', JSON.stringify(analysisOptions));

    return api.post('/api/v1/simple/analyze', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 120000,
    });
  },

  // 查询任务状态
  getTaskStatus: (taskId: string): Promise<AxiosResponse<TaskStatusResponse>> =>
    api.get(`/api/v1/analyze/${taskId}/status`),

  // 获取分析结果
  getAnalysisResult: (taskId: string): Promise<AxiosResponse<AnalysisResultResponse>> =>
    api.get(`/api/v1/analyze/${taskId}/result`),

  // 简化版API - 查询任务状态
  getSimpleTaskStatus: (taskId: string): Promise<AxiosResponse<any>> =>
    api.get(`/api/v1/simple/analyze/${taskId}/status`),

  // 简化版API - 获取分析结果
  getSimpleAnalysisResult: (taskId: string): Promise<AxiosResponse<any>> =>
    api.get(`/api/v1/simple/analyze/${taskId}/result`),

  // 停止分析任务
  stopAnalysis: (taskId: string): Promise<AxiosResponse<{ status: string }>> =>
    api.post(`/api/v1/analyze/${taskId}/stop`),

  // 获取任务列表
  getTaskList: (params: Record<string, any> = {}): Promise<AxiosResponse<TaskResponse[]>> =>
    api.get('/api/v1/tasks', { params }),

  // 删除任务
  deleteTask: (taskId: string): Promise<AxiosResponse<{ status: string }>> =>
    api.delete(`/api/v1/analyze/${taskId}`),

  // 本地动态分析相关API
  localDynamic: {
    // 健康检查
    healthCheck: (): Promise<AxiosResponse<any>> =>
      api.get('/api/v1/local-dynamic/health'),

    // 模拟器状态
    getEmulatorStatus: (): Promise<AxiosResponse<any>> =>
      api.get('/api/v1/local-dynamic/emulator/status'),

    // 获取AVD列表
    getAVDs: (): Promise<AxiosResponse<any>> =>
      api.get('/api/v1/local-dynamic/emulator/avds'),

    // 本地动态分析
    analyze: (file: File, timeout: number = 300): Promise<AxiosResponse<any>> => {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('timeout', timeout.toString());

      return api.post('/api/v1/local-dynamic/analyze', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        timeout: (timeout + 60) * 1000, // 给额外的缓冲时间
      });
    }
  }
};

// WebSocket 连接管理
export class WebSocketManager {
  private socket: WebSocket | null = null;
  private callbacks = new Map<string, Array<(data: any) => void>>();

  connect(taskId: string): void {
    if (this.socket) {
      this.disconnect();
    }

    const wsUrl = `ws://localhost:8000/ws/analysis/${taskId}`;
    this.socket = new WebSocket(wsUrl);

    this.socket.onopen = () => {
      console.log('WebSocket connected for task:', taskId);
    };

    this.socket.onmessage = (event: MessageEvent) => {
      try {
        const data: WebSocketMessage = JSON.parse(event.data);
        this.handleMessage(data);
      } catch (error) {
        console.error('WebSocket message parse error:', error);
      }
    };

    this.socket.onerror = (error: Event) => {
      console.error('WebSocket error:', error);
    };

    this.socket.onclose = () => {
      console.log('WebSocket connection closed');
      this.socket = null;
    };
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
  }

  private handleMessage(data: WebSocketMessage): void {
    const { type, payload } = data;
    const callbacks = this.callbacks.get(type) || [];
    callbacks.forEach(callback => callback(payload));
  }

  on(eventType: string, callback: (data: any) => void): void {
    if (!this.callbacks.has(eventType)) {
      this.callbacks.set(eventType, []);
    }
    this.callbacks.get(eventType)!.push(callback);
  }

  off(eventType: string, callback: (data: any) => void): void {
    const callbacks = this.callbacks.get(eventType) || [];
    const index = callbacks.indexOf(callback);
    if (index > -1) {
      callbacks.splice(index, 1);
    }
  }
}

// 文件下载工具
export const downloadFile = (url: string, filename: string): void => {
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// 导出分析结果
export const exportAnalysisResult = async (
  taskId: string, 
  format: ExportFormat = 'json'
): Promise<boolean> => {
  try {
    const response = await api.get(`/api/v1/analyze/${taskId}/export`, {
      params: { format },
      responseType: 'blob',
    });

    const blob = new Blob([response.data], {
      type: format === 'json' ? 'application/json' : 'text/plain',
    });

    const url = window.URL.createObjectURL(blob);
    downloadFile(url, `analysis_result_${taskId}.${format}`);
    window.URL.revokeObjectURL(url);

    return true;
  } catch (error) {
    console.error('Export failed:', error);
    throw error;
  }
};

export default api;