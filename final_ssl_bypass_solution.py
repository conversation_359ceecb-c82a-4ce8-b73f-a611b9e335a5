#!/usr/bin/env python3
"""
最终SSL绕过解决方案
提供多种方法和完整的手动指南
"""

import subprocess
import sys
import time
import json
from pathlib import Path

class FinalSSLBypassSolution:
    def __init__(self):
        self.package = "com.yjzx.yjzx2017"
        
    def log(self, message, level="INFO"):
        """日志输出"""
        timestamp = time.strftime("%H:%M:%S")
        if level == "SUCCESS":
            print(f"🎉 [{timestamp}] {message}")
        elif level == "ERROR":
            print(f"❌ [{timestamp}] {message}")
        elif level == "WARNING":
            print(f"⚠️  [{timestamp}] {message}")
        else:
            print(f"📋 [{timestamp}] {message}")
    
    def run_adb(self, cmd, timeout=30):
        """执行adb命令"""
        full_cmd = f"source android_env.sh && adb {cmd}"
        try:
            result = subprocess.run(full_cmd, shell=True, 
                                  capture_output=True, text=True, 
                                  timeout=timeout, check=True)
            return result.returncode, result.stdout.strip(), result.stderr.strip()
        except subprocess.CalledProcessError as e:
            return e.returncode, "", e.stderr
        except subprocess.TimeoutExpired:
            return -1, "", "Timeout"
    
    def check_environment(self):
        """检查环境状态"""
        self.log("检查Frida SSL绕过环境...")
        
        issues = []
        
        # 检查设备连接
        returncode, stdout, stderr = self.run_adb("devices")
        if "device" in stdout:
            self.log("✅ Android设备已连接")
        else:
            issues.append("Android设备未连接")
        
        # 检查frida-server
        if Path("frida-server").exists():
            self.log("✅ frida-server文件存在")
        else:
            issues.append("frida-server文件不存在")
        
        # 检查SSL绕过脚本
        if Path("ssl_bypass.js").exists():
            self.log("✅ SSL绕过脚本存在")
        else:
            issues.append("SSL绕过脚本不存在")
        
        # 检查Frida安装
        try:
            result = subprocess.run("export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin && frida --version", 
                                  shell=True, capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                self.log(f"✅ Frida已安装: {result.stdout.strip()}")
            else:
                issues.append("Frida未正确安装")
        except:
            issues.append("Frida命令无法执行")
        
        # 检查应用
        returncode, stdout, stderr = self.run_adb(f"shell pm list packages | grep {self.package}")
        if self.package in stdout:
            self.log("✅ 目标应用已安装")
        else:
            issues.append("目标应用未安装")
        
        # 检查网络捕获目录
        if Path("mitm-logs").exists():
            self.log("✅ 网络捕获目录存在")
        else:
            issues.append("网络捕获目录不存在")
        
        if issues:
            self.log("发现以下问题:", "WARNING")
            for issue in issues:
                self.log(f"  • {issue}", "WARNING")
            return False
        
        self.log("环境检查通过", "SUCCESS")
        return True
    
    def create_manual_guide(self):
        """创建手动操作指南"""
        self.log("创建完整的手动操作指南...")
        
        guide_content = """
# Frida SSL绕过完整手动指南

## 🎯 目标
实现对易金在线APK的HTTPS流量完全捕获，绕过SSL证书验证。

## 📋 前置条件检查
1. Android模拟器运行正常
2. frida-server文件存在
3. ssl_bypass.js脚本存在
4. Frida工具已安装
5. 目标应用已安装

## 🔧 手动执行步骤

### 步骤1: 启动frida-server
```bash
# 设置环境
source android_env.sh

# 检查设备连接
adb devices

# 推送frida-server（如果需要）
adb push frida-server /data/local/tmp/frida-server
adb shell chmod 755 /data/local/tmp/frida-server

# 启动frida-server（忽略SELinux警告）
adb shell '/data/local/tmp/frida-server &'

# 验证启动
adb shell ps | grep frida-server
```

### 步骤2: 准备目标应用
```bash
# 停止应用
adb shell am force-stop com.yjzx.yjzx2017

# 启动应用
adb shell am start -n com.yjzx.yjzx2017/.controller.activity.splash.SplashActivity

# 检查应用进程
adb shell ps | grep yjzx
```

### 步骤3: 运行Frida SSL绕过
```bash
# 设置PATH
export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin

# 方法1: 直接附加到应用
frida -U com.yjzx.yjzx2017 -l ssl_bypass.js

# 方法2: 使用PID附加（如果知道PID）
frida -U [PID] -l ssl_bypass.js

# 方法3: Spawn模式
frida -U -f com.yjzx.yjzx2017 -l ssl_bypass.js
```

### 步骤4: 测试HTTPS捕获
在另一个终端中执行：
```bash
# 清空捕获文件
echo "[]" > mitm-logs/realtime_capture.json

# 触发HTTPS请求
adb shell am start -a android.intent.action.VIEW -d https://httpbin.org/get
adb shell am start -a android.intent.action.VIEW -d https://www.baidu.com

# 应用内操作
adb shell input tap 540 960

# 等待并检查结果
sleep 20
cat mitm-logs/realtime_capture.json
```

## 🔧 故障排除

### 问题1: "system_server"错误
这是Android模拟器环境的已知问题，可以安全忽略。

### 问题2: "unable to access process"
- 重启frida-server
- 重启应用
- 尝试不同的连接方法

### 问题3: SELinux警告
这些警告不影响功能，可以安全忽略。

### 问题4: 没有捕获到HTTPS请求
- 检查mitmproxy是否运行
- 检查代理设置
- 确认SSL绕过脚本已加载

## 💡 成功指标
1. frida-server进程正在运行
2. Frida成功连接到应用
3. SSL绕过脚本加载成功
4. 在realtime_capture.json中看到HTTPS请求

## 🎉 成功后的能力
- 完整的APK动态分析
- HTTPS流量透明捕获
- SSL证书验证绕过
- 实时网络监控
- API端点发现

## 📞 如果仍有问题
1. 检查所有前置条件
2. 重启模拟器和所有服务
3. 尝试不同的Frida连接方法
4. 查看详细的错误日志
"""
        
        with open("frida_ssl_bypass_manual_guide.md", 'w', encoding='utf-8') as f:
            f.write(guide_content)
        
        self.log("手动指南已保存: frida_ssl_bypass_manual_guide.md", "SUCCESS")
    
    def create_automated_scripts(self):
        """创建自动化脚本"""
        self.log("创建自动化脚本...")
        
        # 创建启动脚本
        start_script = '''#!/bin/bash
# Frida SSL绕过启动脚本

echo "🚀 启动Frida SSL绕过..."

# 设置环境
source android_env.sh
export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin

# 检查设备
echo "📱 检查设备连接..."
adb devices

# 启动frida-server
echo "🔧 启动frida-server..."
adb shell pkill frida-server 2>/dev/null
sleep 2
adb shell '/data/local/tmp/frida-server' 2>/dev/null &
sleep 8

# 检查frida-server
echo "🔍 检查frida-server状态..."
adb shell ps | grep frida-server

# 启动应用
echo "📱 启动应用..."
adb shell am force-stop com.yjzx.yjzx2017
sleep 2
adb shell am start -n com.yjzx.yjzx2017/.controller.activity.splash.SplashActivity
sleep 5

echo "✅ 环境准备完成"
echo "💡 现在可以运行: frida -U com.yjzx.yjzx2017 -l ssl_bypass.js"
'''
        
        with open("start_frida_env.sh", 'w') as f:
            f.write(start_script)
        
        subprocess.run("chmod +x start_frida_env.sh", shell=True)
        
        # 创建测试脚本
        test_script = '''#!/bin/bash
# HTTPS捕获测试脚本

echo "🔍 测试HTTPS捕获..."

# 清空捕获文件
echo "[]" > mitm-logs/realtime_capture.json
echo "🗑️  清空捕获文件"

# 设置环境
source android_env.sh

# 触发HTTPS请求
echo "🌐 触发HTTPS请求..."
adb shell am start -a android.intent.action.VIEW -d https://httpbin.org/get
sleep 8
adb shell am start -a android.intent.action.VIEW -d https://www.baidu.com
sleep 8

# 应用内操作
echo "📱 应用内操作..."
adb shell input tap 540 960
sleep 5

# 等待处理
echo "⏳ 等待网络请求处理..."
sleep 20

# 检查结果
echo "📊 检查捕获结果..."
HTTPS_COUNT=$(python3 -c "
import json
try:
    with open('mitm-logs/realtime_capture.json', 'r') as f:
        data = json.load(f)
        if isinstance(data, list):
            https_count = len([req for req in data if req.get('scheme') == 'https'])
            print(https_count)
        else:
            print(0)
except:
    print(0)
")

TOTAL_COUNT=$(python3 -c "
import json
try:
    with open('mitm-logs/realtime_capture.json', 'r') as f:
        data = json.load(f)
        if isinstance(data, list):
            print(len(data))
        else:
            print(0)
except:
    print(0)
")

echo "📊 捕获统计:"
echo "   总请求数: $TOTAL_COUNT"
echo "   HTTPS请求数: $HTTPS_COUNT"

if [ "$HTTPS_COUNT" -gt 0 ]; then
    echo "🎉 SSL绕过成功！捕获到 $HTTPS_COUNT 个HTTPS请求！"
    echo "✅ 系统现在可以完全分析HTTPS流量！"
else
    echo "⚠️  未捕获到HTTPS请求"
    echo "🔧 请检查Frida SSL绕过是否正常运行"
fi
'''
        
        with open("test_https_capture.sh", 'w') as f:
            f.write(test_script)
        
        subprocess.run("chmod +x test_https_capture.sh", shell=True)
        
        self.log("自动化脚本已创建:", "SUCCESS")
        self.log("  • start_frida_env.sh - 启动环境")
        self.log("  • test_https_capture.sh - 测试捕获")
    
    def provide_complete_solution(self):
        """提供完整解决方案"""
        self.log("🚀 Frida SSL绕过完整解决方案")
        self.log("=" * 70)
        
        # 检查环境
        env_ok = self.check_environment()
        
        # 创建指南和脚本
        self.create_manual_guide()
        self.create_automated_scripts()
        
        self.log("=" * 70)
        self.log("📋 完整解决方案已准备就绪", "SUCCESS")
        self.log("=" * 70)
        
        self.log("📁 已创建的文件:")
        self.log("  • frida_ssl_bypass_manual_guide.md - 详细手动指南")
        self.log("  • start_frida_env.sh - 环境启动脚本")
        self.log("  • test_https_capture.sh - HTTPS捕获测试脚本")
        
        self.log("🔧 推荐执行顺序:")
        self.log("  1. 阅读手动指南: cat frida_ssl_bypass_manual_guide.md")
        self.log("  2. 启动环境: ./start_frida_env.sh")
        self.log("  3. 手动运行Frida: frida -U com.yjzx.yjzx2017 -l ssl_bypass.js")
        self.log("  4. 测试捕获: ./test_https_capture.sh")
        
        if env_ok:
            self.log("✅ 环境检查通过，可以立即开始", "SUCCESS")
        else:
            self.log("⚠️  请先解决环境问题", "WARNING")
        
        self.log("💡 关键提示:")
        self.log("  • SELinux和system_server警告可以安全忽略")
        self.log("  • 如果一种方法失败，尝试其他连接方法")
        self.log("  • 确保mitmproxy在后台运行")
        self.log("  • 耐心等待Frida脚本加载")
        
        self.log("🎯 成功后你将拥有:")
        self.log("  ✅ 完整的APK动态分析平台")
        self.log("  ✅ HTTPS流量完全透明")
        self.log("  ✅ SSL证书验证绕过")
        self.log("  ✅ 实时网络监控")
        self.log("  ✅ API端点发现和分析")
        
        return True

def main():
    solution = FinalSSLBypassSolution()
    
    try:
        success = solution.provide_complete_solution()
        
        if success:
            print("\n🎉 Frida SSL绕过完整解决方案已准备就绪！")
            print("📋 请按照生成的指南和脚本进行操作")
            print("💡 所有必要的工具和文档都已创建")
        else:
            print("\n🔧 解决方案准备过程中遇到问题")
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断")
    except Exception as e:
        print(f"\n❌ 异常: {e}")

if __name__ == "__main__":
    main()
