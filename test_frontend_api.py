#!/usr/bin/env python3
"""
测试前端API调用
"""
import requests
import json
import time

def test_simple_api():
    """测试简化版API"""
    base_url = "http://localhost:8000"
    
    # 1. 健康检查
    print("1. 测试健康检查...")
    try:
        response = requests.get(f"{base_url}/api/v1/health")
        print(f"健康检查状态: {response.status_code}")
        print(f"响应: {response.json()}")
    except Exception as e:
        print(f"健康检查失败: {e}")
    
    # 2. 测试简化分析API
    print("\n2. 测试简化分析API...")
    try:
        # 使用测试APK文件
        apk_path = "apk/test_calculator.apk"
        
        with open(apk_path, 'rb') as f:
            files = {'file': f}
            data = {
                'options': json.dumps({
                    'static_analysis': True,
                    'dynamic_analysis': False,  # 禁用动态分析避免错误
                    'timeout': 60
                })
            }
            
            response = requests.post(
                f"{base_url}/api/v1/simple/analyze",
                files=files,
                data=data
            )
            
            print(f"提交分析状态: {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                print(f"任务ID: {result['task_id']}")
                
                # 3. 轮询任务状态
                print("\n3. 轮询任务状态...")
                task_id = result['task_id']
                
                for i in range(30):  # 最多等待60秒
                    time.sleep(2)
                    status_response = requests.get(
                        f"{base_url}/api/v1/simple/analyze/{task_id}/status"
                    )
                    
                    if status_response.status_code == 200:
                        status_data = status_response.json()
                        print(f"状态检查 {i+1}: {status_data['status']}")
                        
                        if status_data['status'] == 'completed':
                            print("\n4. 获取分析结果...")
                            result_response = requests.get(
                                f"{base_url}/api/v1/simple/analyze/{task_id}/result"
                            )
                            
                            if result_response.status_code == 200:
                                result_data = result_response.json()
                                print("分析结果获取成功!")
                                print(f"基础信息: {result_data.get('basic_info', {}).get('package_name', 'N/A')}")
                                print(f"URL数量: {len(result_data.get('urls', []))}")
                                print(f"证书信息: {'有' if result_data.get('certificate') else '无'}")
                            else:
                                print(f"获取结果失败: {result_response.status_code}")
                                print(result_response.text)
                            break
                        elif status_data['status'] == 'failed':
                            print("分析失败!")
                            break
                    else:
                        print(f"状态检查失败: {status_response.status_code}")
                        break
                else:
                    print("分析超时!")
            else:
                print(f"提交分析失败: {response.text}")
                
    except Exception as e:
        print(f"测试失败: {e}")

if __name__ == "__main__":
    test_simple_api()
