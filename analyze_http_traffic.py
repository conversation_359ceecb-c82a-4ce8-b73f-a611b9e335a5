#!/usr/bin/env python3
"""
专注于HTTP流量分析
绕过HTTPS证书问题，分析已经可以捕获的HTTP通信
"""

import subprocess
import sys
import time
import json
from pathlib import Path
from datetime import datetime
from collections import defaultdict

class HTTPTrafficAnalyzer:
    def __init__(self):
        self.package = "com.yjzx.yjzx2017"
        
    def run_adb(self, cmd, timeout=30):
        """执行adb命令"""
        full_cmd = f"source android_env.sh && adb {cmd}"
        try:
            result = subprocess.run(full_cmd, shell=True, 
                                  capture_output=True, text=True, 
                                  timeout=timeout, check=True)
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            return f"ERROR: {e.stderr}"
        except subprocess.TimeoutExpired:
            return "ERROR: Timeout"
    
    def clear_capture_file(self):
        """清空捕获文件"""
        realtime_file = Path("mitm-logs/realtime_capture.json")
        if realtime_file.exists():
            with open(realtime_file, 'w') as f:
                json.dump([], f)
        print("🗑️  清空了网络捕获文件")
    
    def get_network_requests(self):
        """获取网络请求"""
        try:
            realtime_file = Path("mitm-logs/realtime_capture.json")
            if realtime_file.exists():
                with open(realtime_file, 'r') as f:
                    data = json.load(f)
                    if isinstance(data, list):
                        return data
            return []
        except Exception as e:
            print(f"⚠️  读取网络请求失败: {e}")
            return []
    
    def comprehensive_app_testing(self):
        """全面的应用测试"""
        print("🚀 全面的易金在线应用HTTP流量分析")
        print("🔧 专注于可以捕获的HTTP通信")
        print("=" * 60)
        
        self.clear_capture_file()
        time.sleep(2)
        
        initial_count = len(self.get_network_requests())
        print(f"📡 初始网络请求数: {initial_count}")
        
        # 测试多个Activity和用户场景
        test_scenarios = [
            {
                "name": "应用启动",
                "activities": [
                    f"{self.package}.controller.activity.splash.SplashActivity",
                    f"{self.package}.controller.activity.MainActivity"
                ],
                "interactions": [
                    ("tap", 540, 960),
                    ("wait", 3)
                ]
            },
            {
                "name": "登录流程",
                "activities": [
                    f"{self.package}.controller.login.activity.LoginActivity"
                ],
                "interactions": [
                    ("tap", 400, 600),  # 用户名输入框
                    ("text", "testuser"),
                    ("tap", 400, 700),  # 密码输入框
                    ("text", "testpass"),
                    ("tap", 400, 800),  # 登录按钮
                    ("wait", 5)
                ]
            },
            {
                "name": "设置页面",
                "activities": [
                    f"{self.package}.controller.activity.setting.SettingActivity"
                ],
                "interactions": [
                    ("tap", 540, 400),
                    ("tap", 540, 500),
                    ("wait", 3)
                ]
            },
            {
                "name": "拍卖功能",
                "activities": [
                    f"{self.package}.controller.activity.auction.AuctionSelfBuyActivity",
                    f"{self.package}.controller.selfAuction.SelfAuctionAddActivity"
                ],
                "interactions": [
                    ("tap", 540, 600),
                    ("swipe", 540, 800, 540, 400),
                    ("wait", 3)
                ]
            }
        ]
        
        all_requests = []
        
        for scenario in test_scenarios:
            print(f"\n🎯 测试场景: {scenario['name']}")
            print("-" * 40)
            
            scenario_start_count = len(self.get_network_requests())
            
            # 启动Activities
            for activity in scenario['activities']:
                activity_name = activity.split('.')[-1]
                print(f"   🚀 启动: {activity_name}")
                self.run_adb(f"shell am broadcast -a android.intent.action.MAIN -n {self.package}/{activity}")
                time.sleep(2)
            
            # 执行交互
            for interaction in scenario['interactions']:
                if interaction[0] == "tap":
                    x, y = interaction[1], interaction[2]
                    print(f"   🖱️  点击: ({x}, {y})")
                    self.run_adb(f"shell input tap {x} {y}")
                elif interaction[0] == "text":
                    text = interaction[1]
                    print(f"   ⌨️  输入: {text}")
                    self.run_adb(f"shell input text {text}")
                elif interaction[0] == "swipe":
                    x1, y1, x2, y2 = interaction[1], interaction[2], interaction[3], interaction[4]
                    print(f"   👆 滑动: ({x1},{y1}) -> ({x2},{y2})")
                    self.run_adb(f"shell input swipe {x1} {y1} {x2} {y2}")
                elif interaction[0] == "wait":
                    wait_time = interaction[1]
                    print(f"   ⏳ 等待: {wait_time}秒")
                    time.sleep(wait_time)
            
            # 等待网络请求
            print("   📡 等待网络请求...")
            time.sleep(5)
            
            scenario_end_count = len(self.get_network_requests())
            scenario_requests = scenario_end_count - scenario_start_count
            
            print(f"   📊 场景新增请求: {scenario_requests}")
            
            if scenario_requests > 0:
                current_requests = self.get_network_requests()
                new_requests = current_requests[-scenario_requests:]
                all_requests.extend(new_requests)
                
                # 显示场景中的关键请求
                for req in new_requests[:3]:  # 显示前3个
                    print(f"      • {req.get('method', 'N/A')} {req.get('url', 'N/A')}")
        
        return all_requests
    
    def analyze_traffic_patterns(self, requests):
        """分析流量模式"""
        print("\n📊 HTTP流量模式分析")
        print("=" * 50)
        
        if not requests:
            print("❌ 没有捕获到HTTP请求")
            return
        
        # 按主机分组
        hosts = defaultdict(list)
        methods = defaultdict(int)
        status_codes = defaultdict(int)
        
        for req in requests:
            host = req.get('host', 'unknown')
            method = req.get('method', 'unknown')
            status = req.get('status_code', 'unknown')
            
            hosts[host].append(req)
            methods[method] += 1
            if status != 'unknown':
                status_codes[status] += 1
        
        print(f"📡 总HTTP请求数: {len(requests)}")
        print(f"🌐 涉及主机数: {len(hosts)}")
        
        print("\n🏠 主机访问统计:")
        for host, host_requests in sorted(hosts.items(), key=lambda x: len(x[1]), reverse=True):
            print(f"   {host}: {len(host_requests)} 请求")
            
            # 显示该主机的请求详情
            for req in host_requests[:2]:  # 显示前2个请求
                path = req.get('path', '/')
                method = req.get('method', 'GET')
                print(f"      • {method} {path}")
        
        print("\n📋 HTTP方法统计:")
        for method, count in sorted(methods.items(), key=lambda x: x[1], reverse=True):
            print(f"   {method}: {count}")
        
        if status_codes:
            print("\n📈 状态码统计:")
            for status, count in sorted(status_codes.items(), key=lambda x: x[1], reverse=True):
                print(f"   {status}: {count}")
    
    def identify_api_endpoints(self, requests):
        """识别API端点"""
        print("\n🎯 API端点识别")
        print("=" * 50)
        
        api_patterns = [
            'api', 'service', 'rest', 'json', 'xml', 
            'login', 'auth', 'user', 'data', 'upload'
        ]
        
        api_requests = []
        
        for req in requests:
            url = req.get('url', '').lower()
            path = req.get('path', '').lower()
            
            if any(pattern in url or pattern in path for pattern in api_patterns):
                api_requests.append(req)
        
        if api_requests:
            print(f"🔍 发现 {len(api_requests)} 个可能的API请求:")
            
            for i, req in enumerate(api_requests, 1):
                method = req.get('method', 'N/A')
                url = req.get('url', 'N/A')
                status = req.get('status_code', 'N/A')
                
                print(f"   {i}. {method} {url}")
                print(f"      状态: {status}")
                
                # 显示请求头中的关键信息
                headers = req.get('headers', {})
                if 'content-type' in headers:
                    print(f"      类型: {headers['content-type']}")
                if 'user-agent' in headers:
                    ua = headers['user-agent'][:50] + "..." if len(headers['user-agent']) > 50 else headers['user-agent']
                    print(f"      UA: {ua}")
                print()
        else:
            print("⚠️  未发现明显的API端点")
    
    def generate_traffic_report(self, requests):
        """生成流量报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"http_traffic_analysis_{timestamp}.json"
        
        # 准备报告数据
        report_data = {
            "timestamp": datetime.now().isoformat(),
            "total_requests": len(requests),
            "analysis_summary": {
                "hosts": list(set(req.get('host', 'unknown') for req in requests)),
                "methods": list(set(req.get('method', 'unknown') for req in requests)),
                "status_codes": list(set(str(req.get('status_code', 'unknown')) for req in requests if req.get('status_code')))
            },
            "requests": requests
        }
        
        # 保存报告
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 详细报告已保存: {report_file}")
        return report_file
    
    def run_http_analysis(self):
        """运行HTTP流量分析"""
        print("🚀 易金在线APK - HTTP流量深度分析")
        print("🔧 绕过HTTPS证书问题，专注于HTTP通信分析")
        print("=" * 70)
        
        # 检查设备连接
        result = self.run_adb("devices")
        if "device" not in result or "ERROR" in result:
            print("❌ Android设备未连接，请重新启动模拟器")
            return False
        
        print("✅ Android设备已连接")
        
        # 设置代理
        print("🔧 设置网络代理...")
        self.run_adb("shell settings put global http_proxy 192.168.1.123:8080")
        
        # 执行全面测试
        print("\n开始全面的应用测试...")
        requests = self.comprehensive_app_testing()
        
        if requests:
            # 分析流量模式
            self.analyze_traffic_patterns(requests)
            
            # 识别API端点
            self.identify_api_endpoints(requests)
            
            # 生成报告
            report_file = self.generate_traffic_report(requests)
            
            print("\n🎉 HTTP流量分析完成！")
            print(f"📊 成功分析了 {len(requests)} 个HTTP请求")
            print("🔧 虽然HTTPS证书有问题，但HTTP分析提供了有价值的信息")
            
            return True
        else:
            print("\n❌ 没有捕获到HTTP请求")
            print("🔧 请检查网络代理设置和应用网络活动")
            return False

def main():
    analyzer = HTTPTrafficAnalyzer()
    
    try:
        success = analyzer.run_http_analysis()
        
        if success:
            print("\n🎯 HTTP流量分析成功完成！")
            print("💡 建议：虽然HTTPS证书有问题，但HTTP分析已经提供了应用网络行为的重要信息")
        else:
            print("\n🔧 需要进一步排查网络捕获问题")
            
    except KeyboardInterrupt:
        print("\n⚠️  分析被用户中断")
    except Exception as e:
        print(f"\n❌ 分析异常: {e}")

if __name__ == "__main__":
    main()
