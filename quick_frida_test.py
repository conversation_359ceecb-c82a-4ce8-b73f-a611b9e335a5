#!/usr/bin/env python3
"""
快速Frida测试
解决启动问题的最简化版本
"""

import subprocess
import sys
import time
from pathlib import Path

def log(message, level="INFO"):
    """日志输出"""
    timestamp = time.strftime("%H:%M:%S")
    if level == "SUCCESS":
        print(f"🎉 [{timestamp}] {message}")
    elif level == "ERROR":
        print(f"❌ [{timestamp}] {message}")
    elif level == "WARNING":
        print(f"⚠️  [{timestamp}] {message}")
    else:
        print(f"📋 [{timestamp}] {message}")

def run_adb(cmd, timeout=30):
    """执行adb命令"""
    full_cmd = f"source android_env.sh && adb {cmd}"
    try:
        result = subprocess.run(full_cmd, shell=True, 
                              capture_output=True, text=True, 
                              timeout=timeout, check=True)
        return result.returncode, result.stdout.strip(), result.stderr.strip()
    except subprocess.CalledProcessError as e:
        return e.returncode, "", e.stderr
    except subprocess.TimeoutExpired:
        return -1, "", "Timeout"

def setup_frida_server():
    """设置frida-server"""
    log("设置frida-server...")
    
    # 1. 检查设备
    returncode, stdout, stderr = run_adb("devices")
    if "device" not in stdout:
        log("设备未连接", "ERROR")
        return False
    log("✅ 设备已连接")
    
    # 2. 推送frida-server
    log("推送frida-server...")
    returncode, stdout, stderr = run_adb("push frida-server /data/local/tmp/frida-server")
    if returncode != 0:
        log(f"推送失败: {stderr}", "ERROR")
        return False
    log("✅ frida-server推送成功")
    
    # 3. 设置权限并启动
    run_adb("shell chmod 755 /data/local/tmp/frida-server")
    run_adb("shell pkill frida-server")
    time.sleep(2)
    
    log("启动frida-server...")
    subprocess.Popen("source android_env.sh && adb shell '/data/local/tmp/frida-server' 2>/dev/null &", shell=True)
    time.sleep(8)
    
    # 4. 验证
    returncode, stdout, stderr = run_adb("shell ps | grep frida-server")
    if "frida-server" in stdout:
        log("✅ frida-server启动成功", "SUCCESS")
        return True
    else:
        log("frida-server启动失败", "ERROR")
        return False

def setup_app():
    """设置应用"""
    package = "com.yjzx.yjzx2017"
    log("设置目标应用...")
    
    # 1. 检查应用
    returncode, stdout, stderr = run_adb(f"shell pm list packages | grep {package}")
    if package not in stdout:
        log("应用未安装", "ERROR")
        return False, None
    log("✅ 应用已安装")
    
    # 2. 启动应用
    run_adb(f"shell am force-stop {package}")
    time.sleep(2)
    
    returncode, stdout, stderr = run_adb(f"shell am start -n {package}/.controller.activity.splash.SplashActivity")
    if returncode != 0:
        log(f"应用启动失败: {stderr}", "ERROR")
        return False, None
    
    log("✅ 应用启动成功")
    time.sleep(8)
    
    # 3. 获取PID
    returncode, stdout, stderr = run_adb(f"shell ps | grep {package}")
    if package in stdout:
        lines = stdout.split('\n')
        for line in lines:
            if package in line:
                parts = line.split()
                if len(parts) >= 2:
                    pid = parts[1]
                    log(f"✅ 找到应用进程 PID: {pid}")
                    return True, pid
    
    log("未找到应用进程", "ERROR")
    return False, None

def test_frida_basic_connection(pid):
    """测试基础Frida连接"""
    log(f"测试Frida连接 (PID: {pid})...")
    
    # 创建最简单的测试脚本
    test_script = '''
console.log("Frida connection test successful!");
Java.perform(function() {
    console.log("Java.perform is working!");
});
'''
    
    # 保存测试脚本
    test_file = Path("frida_test.js")
    with open(test_file, 'w') as f:
        f.write(test_script)
    
    # 测试连接
    cmd = f"export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin && timeout 15 frida -U {pid} -l frida_test.js"
    
    try:
        result = subprocess.run(cmd, shell=True, 
                              capture_output=True, text=True, 
                              timeout=20)
        
        if "Frida connection test successful!" in result.stdout:
            log("✅ Frida连接测试成功！", "SUCCESS")
            return True
        else:
            log("Frida连接测试失败", "WARNING")
            if result.stderr:
                error_lines = result.stderr.split('\n')[:3]
                for line in error_lines:
                    if line.strip() and 'system_server' not in line:
                        log(f"   错误: {line.strip()}")
            return False
            
    except Exception as e:
        log(f"Frida连接异常: {e}", "ERROR")
        return False

def run_ssl_bypass_test(pid):
    """运行SSL绕过测试"""
    log(f"运行SSL绕过测试 (PID: {pid})...")
    
    # 使用现有的SSL绕过脚本
    if not Path("ssl_bypass.js").exists():
        log("SSL绕过脚本不存在", "ERROR")
        return False
    
    # 运行SSL绕过
    cmd = f"export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin && frida -U {pid} -l ssl_bypass.js"
    
    try:
        process = subprocess.Popen(
            cmd, shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        log("SSL绕过脚本已启动")
        time.sleep(15)  # 等待脚本加载
        
        if process.poll() is None:
            log("✅ SSL绕过脚本正在运行", "SUCCESS")
            
            # 简单测试
            log("执行简单的HTTPS测试...")
            run_adb("shell am start -a android.intent.action.VIEW -d https://httpbin.org/get")
            time.sleep(5)
            run_adb("shell am start -a android.intent.action.VIEW -d https://www.baidu.com")
            time.sleep(5)
            run_adb("shell input tap 540 960")
            time.sleep(3)
            
            log("🎉 SSL绕过测试完成！", "SUCCESS")
            log("💡 SSL绕过正在运行，按Ctrl+C停止...")
            
            try:
                # 保持运行
                while process.poll() is None:
                    time.sleep(30)
                    log("💡 SSL绕过仍在运行...")
            except KeyboardInterrupt:
                log("⚠️  停止SSL绕过...")
                process.terminate()
            
            return True
        else:
            stdout, stderr = process.communicate()
            log("SSL绕过进程退出", "WARNING")
            
            # 显示有用的输出
            if stdout:
                useful_lines = [line for line in stdout.split('\n')[:10] if line.strip() and 'SSL Kill Switch' in line]
                if useful_lines:
                    log("SSL绕过输出:")
                    for line in useful_lines:
                        log(f"   {line.strip()}")
            
            if stderr and 'system_server' not in stderr:
                error_lines = stderr.split('\n')[:3]
                for line in error_lines:
                    if line.strip():
                        log(f"   错误: {line.strip()}")
            
            return False
            
    except Exception as e:
        log(f"SSL绕过异常: {e}", "ERROR")
        return False

def main():
    """主函数"""
    log("🚀 快速Frida SSL绕过测试")
    log("🔧 解决启动问题的最简化版本")
    log("=" * 60)
    
    try:
        # 步骤1: 设置frida-server
        if not setup_frida_server():
            log("frida-server设置失败", "ERROR")
            return False
        
        # 步骤2: 设置应用
        app_ok, pid = setup_app()
        if not app_ok or not pid:
            log("应用设置失败", "ERROR")
            return False
        
        # 步骤3: 测试基础连接
        if not test_frida_basic_connection(pid):
            log("Frida基础连接失败", "ERROR")
            log("可能的解决方案:")
            log("  1. 重启模拟器")
            log("  2. 重新安装应用")
            log("  3. 检查frida-server版本")
            return False
        
        # 步骤4: 运行SSL绕过
        log("=" * 50)
        log("🔍 运行SSL绕过测试")
        log("=" * 50)
        
        success = run_ssl_bypass_test(pid)
        
        if success:
            log("🎉 快速Frida SSL绕过测试成功！", "SUCCESS")
            log("✅ Frida启动问题已解决")
            log("✅ SSL绕过功能正常工作")
            log("💡 现在可以运行完整的自动化系统")
            return True
        else:
            log("SSL绕过测试失败", "WARNING")
            log("但Frida基础连接已经成功")
            return False
            
    except KeyboardInterrupt:
        log("用户中断测试")
        return False
    except Exception as e:
        log(f"测试异常: {e}", "ERROR")
        return False
    finally:
        # 清理
        log("清理资源...")
        run_adb("shell pkill frida-server")

if __name__ == "__main__":
    try:
        success = main()
        
        if success:
            print("\n🎯 快速Frida测试成功！")
            print("💡 Frida启动问题已解决！")
            print("🔧 现在可以运行完整的SSL绕过系统！")
        else:
            print("\n🔧 Frida启动仍有问题")
            print("💡 请检查错误信息并尝试建议的解决方案")
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断")
    except Exception as e:
        print(f"\n❌ 异常: {e}")
