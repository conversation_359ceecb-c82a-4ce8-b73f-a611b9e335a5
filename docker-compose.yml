version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: apk_analyzer_db
    environment:
      POSTGRES_DB: apk_analyzer
      POSTGRES_USER: apk_user
      POSTGRES_PASSWORD: apk_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U apk_user -d apk_analyzer"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis缓存和消息队列
  redis:
    image: redis:7-alpine
    container_name: apk_analyzer_redis
    command: redis-server --maxmemory 1gb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # API服务
  api:
    build: .
    container_name: apk_analyzer_api
    environment:
      - DATABASE_URL=postgresql+asyncpg://apk_user:apk_password@postgres:5432/apk_analyzer
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
      - DEBUG=true
    volumes:
      - ./storage:/app/storage
      - ./logs:/app/logs
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery静态分析工作器
  static-worker:
    build: .
    container_name: apk_analyzer_static_worker
    command: celery -A src.workers.celery_app worker -Q static_analysis --loglevel=info --concurrency=2
    environment:
      - DATABASE_URL=postgresql+asyncpg://apk_user:apk_password@postgres:5432/apk_analyzer
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    volumes:
      - ./storage:/app/storage
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

  # Celery动态分析工作器
  dynamic-worker:
    build: .
    container_name: apk_analyzer_dynamic_worker
    command: celery -A src.workers.celery_app worker -Q dynamic_analysis --loglevel=info --concurrency=1
    environment:
      - DATABASE_URL=postgresql+asyncpg://apk_user:apk_password@postgres:5432/apk_analyzer
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    volumes:
      - ./storage:/app/storage
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

  # Celery结果处理工作器
  result-worker:
    build: .
    container_name: apk_analyzer_result_worker
    command: celery -A src.workers.celery_app worker -Q result_processing --loglevel=info --concurrency=2
    environment:
      - DATABASE_URL=postgresql+asyncpg://apk_user:apk_password@postgres:5432/apk_analyzer
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    volumes:
      - ./storage:/app/storage
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

  # Celery监控
  flower:
    build: .
    container_name: apk_analyzer_flower
    command: celery -A src.workers.celery_app flower --port=5555
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    ports:
      - "5555:5555"
    depends_on:
      - redis
    restart: unless-stopped

  # Android沙箱模拟器
  android-emulator:
    image: budtmo/docker-android:emulator_11.0
    container_name: apk_analyzer_android
    privileged: true
    environment:
      - EMULATOR_DEVICE=Samsung Galaxy S10
      - WEB_VNC=true
      - APPIUM=true
      - EMULATOR_TIMEOUT=300
      - PROXY_ENABLED=true
      - PROXY_HOST=mitm-proxy
      - PROXY_PORT=8080
    volumes:
      - ./storage/apks:/apks
      - android_data:/android
    ports:
      - "6080:6080"  # VNC Web界面
      - "5554:5554"  # ADB端口（pair 1）
      - "5555:5555"  # ADB端口（pair 2）
      - "4723:4723"  # Appium端口
    networks:
      - sandbox_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "adb", "shell", "echo", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # 网络代理和流量捕获
  mitm-proxy:
    image: mitmproxy/mitmproxy:latest
    container_name: apk_analyzer_mitm
    # 直接启动 mitmweb（脚本中 Redis 为可选）
    command: ["mitmweb", "-s", "/scripts/capture.py", "--listen-port", "8080", "--web-host", "0.0.0.0", "--web-port", "8081"]
    ports:
      - "8080:8080"  # 代理端口
      - "8081:8081"  # Web管理界面
    volumes:
      - ./mitm-scripts:/scripts
      - ./mitm-logs:/logs
      - mitm_data:/home/<USER>/.mitmproxy
    networks:
      - sandbox_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
  redis_data:
  android_data:
  mitm_data:

networks:
  default:
    name: apk_analyzer_network
  sandbox_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16