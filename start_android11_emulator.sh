#!/bin/bash

echo "===== Android 11 模拟器启动指南 ====="
echo ""
echo "方法1: 使用Android Studio (推荐)"
echo "--------------------------------------"
echo "1. 打开Android Studio"
echo "2. 点击 Tools -> AVD Manager"
echo "3. 创建新AVD:"
echo "   - 选择设备: Pixel 4 或其他"
echo "   - 选择系统镜像: Android 11 (API 30)"
echo "   - 重要: 选择 'Google APIs' 而不是 'Google Play'"
echo "   - 完成创建"
echo "4. 启动AVD"
echo ""
echo "方法2: 命令行创建Android 11 AVD"
echo "--------------------------------------"
echo "# 设置SDK路径"
echo "export ANDROID_SDK_ROOT=~/Library/Android/sdk"
echo "export PATH=\$ANDROID_SDK_ROOT/cmdline-tools/latest/bin:\$PATH"
echo "export PATH=\$ANDROID_SDK_ROOT/emulator:\$PATH"
echo "export PATH=\$ANDROID_SDK_ROOT/platform-tools:\$PATH"
echo ""
echo "# 下载Android 11系统镜像"
echo "sdkmanager 'system-images;android-30;google_apis;x86_64'"
echo ""
echo "# 创建AVD"
echo "avdmanager create avd -n Android11_API30 -k 'system-images;android-30;google_apis;x86_64' -d pixel_4"
echo ""
echo "# 启动模拟器（重要：使用-writable-system参数）"
echo "emulator -avd Android11_API30 -writable-system -no-snapshot-load"
echo ""
echo "方法3: 如果已有Android 11 AVD"
echo "--------------------------------------"
echo "# 列出现有AVD"
echo "emulator -list-avds"
echo ""
echo "# 启动现有的Android 11 AVD"
echo "emulator -avd <AVD_NAME> -writable-system -no-snapshot-load"
echo ""
echo "===== 重要提示 ====="
echo "1. 必须使用 -writable-system 参数启动"
echo "2. 必须使用 -no-snapshot-load 避免快照问题"
echo "3. 选择不带Google Play的镜像（Google APIs）"
echo "4. 这样可以获得可写的系统分区"
