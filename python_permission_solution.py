#!/usr/bin/env python3
"""
Python版本的权限问题解决方案
"""

import subprocess
import sys
import time

def run_frida_with_spawn():
    """使用spawn模式运行Frida"""
    print("🚀 使用spawn模式启动Frida SSL绕过...")
    
    # 设置环境
    env = {
        'PATH': '/Users/<USER>/Library/Python/3.9/bin:' + os.environ.get('PATH', ''),
        **os.environ
    }
    
    # 启动应用并注入
    cmd = ['frida', '-U', '-f', 'com.yjzx.yjzx2017', '--no-pause', '-l', 'optimized_ssl_bypass.js']
    
    try:
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            env=env
        )
        
        print("⏳ 等待Frida连接和脚本加载...")
        time.sleep(20)
        
        if process.poll() is None:
            print("✅ Frida spawn模式启动成功！")
            print("💡 SSL绕过脚本正在运行...")
            print("⚠️  按Ctrl+C停止")
            
            try:
                while process.poll() is None:
                    time.sleep(30)
                    print("💡 SSL绕过仍在运行...")
            except KeyboardInterrupt:
                print("\n⚠️  停止SSL绕过...")
                process.terminate()
            
            return True
        else:
            stdout, stderr = process.communicate()
            print("❌ Frida启动失败")
            if stdout:
                print("输出:", stdout[:500])
            if stderr:
                print("错误:", stderr[:500])
            return False
            
    except Exception as e:
        print(f"❌ 异常: {e}")
        return False

if __name__ == "__main__":
    import os
    success = run_frida_with_spawn()
    if success:
        print("🎉 权限问题解决成功！")
    else:
        print("🔧 需要进一步调试")
