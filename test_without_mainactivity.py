import subprocess
import time

def run_cmd(cmd):
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    return result.stdout

print("测试策略：避开会触发MainActivity的Activity")
print("-" * 50)

# 先停止应用
run_cmd("adb shell am force-stop com.yjzx.yjzx2017")
time.sleep(1)

# 直接启动SplashActivity（入口Activity）
print("1. 启动SplashActivity...")
run_cmd("adb shell am start -n com.yjzx.yjzx2017/.controller.activity.splash.SplashActivity")
time.sleep(5)  # 等待Splash完成

# 检查是否有崩溃
crash_check = run_cmd("adb shell dumpsys window windows | grep 'Application Error'")
if "Application Error" in crash_check:
    print("   检测到崩溃弹窗，关闭它...")
    run_cmd("adb shell input keyevent 4")
    time.sleep(1)

# 检查流量
print("\n2. 检查捕获的流量...")
run_cmd("tail -10 android7_new_capture.log | grep '🔒' | grep -v google")

print("\n测试完成！")
