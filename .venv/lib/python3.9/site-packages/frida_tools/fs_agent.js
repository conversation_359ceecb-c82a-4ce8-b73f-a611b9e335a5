📦
4644 /agent.js
1428 /node_modules/@frida/base64-js/index.js
↻ base64-js
25206 /node_modules/@frida/buffer/index.js
↻ buffer
5739 /node_modules/@frida/events/events.js
↻ events
1001 /node_modules/@frida/ieee754/index.js
↻ ieee754
9763 /node_modules/@frida/path/index.js
↻ path
1110 /node_modules/@frida/process/index.js
↻ process
2217 /node_modules/@frida/readable-stream/errors.js
1595 /node_modules/@frida/readable-stream/lib/abort_controller.js
609 /node_modules/@frida/readable-stream/lib/add-abort-signal.js
1736 /node_modules/@frida/readable-stream/lib/buffer_list.js
1771 /node_modules/@frida/readable-stream/lib/compose.js
3161 /node_modules/@frida/readable-stream/lib/destroy.js
4977 /node_modules/@frida/readable-stream/lib/duplex.js
2065 /node_modules/@frida/readable-stream/lib/end-of-stream.js
10300 /node_modules/@frida/readable-stream/lib/event_target.js
1086 /node_modules/@frida/readable-stream/lib/from.js
1189 /node_modules/@frida/readable-stream/lib/legacy.js
95 /node_modules/@frida/readable-stream/lib/once.js
238 /node_modules/@frida/readable-stream/lib/passthrough.js
2667 /node_modules/@frida/readable-stream/lib/pipeline.js
409 /node_modules/@frida/readable-stream/lib/promises.js
13334 /node_modules/@frida/readable-stream/lib/readable.js
426 /node_modules/@frida/readable-stream/lib/state.js
1644 /node_modules/@frida/readable-stream/lib/transform.js
3422 /node_modules/@frida/readable-stream/lib/utils.js
9107 /node_modules/@frida/readable-stream/lib/writable.js
1243 /node_modules/@frida/readable-stream/readable.js
↻ @frida/readable-stream
445 /node_modules/@frida/stream/index.js
↻ stream
3481 /node_modules/@frida/string_decoder/lib/string_decoder.js
↻ string_decoder
3002 /node_modules/@frida/util/support/types.js
8557 /node_modules/@frida/util/util.js
↻ util
14331 /node_modules/frida-fs/dist/index.js
↻ fs
3010 /node_modules/frida-remote-stream/dist/index.js
↻ frida-remote-stream
✄
import{Buffer as e}from"buffer";import r from"frida-remote-stream";import t from"fs";import n from"path";const{S_IFMT:o,S_IFREG:s,S_IFDIR:i,S_IFCHR:l,S_IFBLK:a,S_IFIFO:c,S_IFLNK:u,S_IFSOCK:d}=t.constants,{pointerSize:f}=Process,m=new Map,p=new Map;let h=null,y=null;function S(e,r,n){const{mode:o}=n,s=g(o);let i;if("l"===s){const r=t.readlinkSync(e);let n,o;try{const r=t.statSync(e);o=w(r.mode),n=g(r.mode)}catch(e){n=null}i=[r,null!==n?[n,o]:null]}else i=null;return[r,i,s,w(o),n.nlink,v(n.uid),b(n.gid),n.size,n.mtimeMs]}function g(e){switch(e&o){case s:return"-";case i:return"d";case l:return"c";case a:return"b";case c:return"p";case u:return"l";case d:return"s"}throw new Error(`Invalid mode: 0x${e.toString(16)}`)}function w(e){let r="";for(let t=8;-1!==t;t-=3)r+=0!=(e>>>t&1)?"r":"-",r+=0!=(e>>>t-1&1)?"w":"-",r+=0!=(e>>>t-2&1)?"x":"-";return r}function v(e){let r=m.get(e);if(void 0!==r)return r;if("windows"===Process.platform)r=e.toString();else{let t;null===h&&(h=new SystemFunction(Module.getGlobalExportByName("getpwuid_r"),"int",["uint","pointer","pointer","size_t","pointer"]));let n,o,s=128,i=1024;for(;;){t=Memory.alloc(s+i+f),n=t.add(s),o=n.add(i);const r=h(e,t,n,i,o);if(0===r.value)break;if(34!==r.errno)throw new Error(`Unable to resolve user ID ${e}: ${r.errno}`);i*=2}const l=o.readPointer();r=l.isNull()?e.toString():l.readPointer().readUtf8String()}return m.set(e,r),r}function b(e){let r=p.get(e);if(void 0!==r)return r;if("windows"===Process.platform)r=e.toString();else{let t;null===y&&(y=new SystemFunction(Module.getGlobalExportByName("getgrgid_r"),"int",["uint","pointer","pointer","size_t","pointer"]));let n,o,s=128,i=1024;for(;;){t=Memory.alloc(s+i+f),n=t.add(s),o=n.add(i);const r=y(e,t,n,i,o);if(0===r.value)break;if(34!==r.errno)throw new Error(`Unable to resolve group ID ${e}: ${r.errno}`);i*=2}const l=o.readPointer();r=l.isNull()?e.toString():l.readPointer().readUtf8String()}return p.set(e,r),r}const L=new class{#e=new r;constructor(){recv(this.#r),this.#e.events.on("send",this.#t),this.#e.events.on("stream",this.#n)}ls(e){0===e.length&&(e=["windows"===Process.platform?"C:\\":"/"]);const r={path:"",entries:[],errors:[]},o=[];for(const s of e){let e,i;try{e=t.lstatSync(s)}catch(e){r.errors.push([s,e.message]);continue}if(e.isSymbolicLink()){let r;try{r=t.statSync(s),i=r.isDirectory(),i&&(e=r)}catch(e){i=!1}}else i=e.isDirectory();if(i){let r;try{r=t.readdirSync(s)}catch(e){o.push({path:s,entries:[],errors:[[s,e.message]]});continue}const i=[];for(const o of r){const r=n.join(s,o);try{const n=S(r,o,"."===o?e:t.lstatSync(r));i.push(n)}catch(e){}}o.push({path:s,entries:i,errors:[]})}else r.entries.push(S(s,s,e))}return r.entries.length>0||r.errors.length>0?[r,...o]:o}rm(e,r){const o=[],s=[],i=[],l=r.includes("force");if(r.includes("recursive")){const r=e.slice();for(;;){const e=r.shift();if(void 0===e)break;let o;try{o=t.statSync(e)}catch(r){i.push(e);continue}o.isDirectory()?(r.push(...t.readdirSync(e).filter((e=>"."!==e&&".."!==e)).map((r=>n.join(e,r)))),s.unshift(e)):i.unshift(e)}}else i.push(...e);for(const e of i)try{t.unlinkSync(e)}catch(r){l||a(e,r)}for(const e of s)try{t.rmdirSync(e)}catch(r){a(e,r)}function a(e,r){o.push(`${e}: ${r.message}`)}return o}async pull(e){let r=0;for(const n of e)try{r+=t.statSync(n).size}catch(e){}send({type:"pull:status",total:r});let n=0;for(const r of e){const e=t.createReadStream(r),o=e.pipe(this.#e.open(n.toString())),s=new Promise(((r,t)=>{function n(e){l(),o.end(),t(e)}function s(t){l(),e.destroy(),r(null)}function i(){l(),r(null)}function l(){o.removeListener("finish",i),o.removeListener("error",s),e.removeListener("error",n)}e.addListener("error",n),o.addListener("error",s),o.addListener("finish",i)}));try{await s}catch(e){send({type:"pull:io-error",index:n,error:e.message})}n++}}#r=(r,t)=>{if("stream"===r.type){const n=null!==t?e.from(t):null;this.#e.receive({stanza:r.payload,data:n})}recv(this.#r)};#t=e=>{send({type:"stream",payload:e.stanza},e.data?.buffer)};#n=e=>{const r=parseInt(e.label),o=e.details,s=o.filename,i=o.target;let l=null;try{t.statSync(i).isDirectory()&&(l=n.join(i,s))}catch(e){}null===l&&(l=i);const a=t.createWriteStream(l);function c(e){f(),a.end(),send({type:"push:io-error",index:r,error:e.message})}function u(t){f(),e.destroy(),send({type:"push:io-error",index:r,error:t.message})}function d(){f(),send({type:"push:io-success",index:r})}function f(){a.removeListener("finish",d),a.removeListener("error",u),e.removeListener("error",c)}e.pipe(a),e.addListener("error",c),a.addListener("error",u),a.addListener("finish",d)}};rpc.exports={ls:L.ls.bind(L),rm:L.rm.bind(L),pull:L.pull.bind(L)};
✄
const t=[],o=[],n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";for(let c=0,h=n.length;c<h;++c)t[c]=n[c],o[n.charCodeAt(c)]=c;function r(t){const o=t.length;if(o%4>0)throw new Error("Invalid string. Length must be a multiple of 4");let n=t.indexOf("=");-1===n&&(n=o);return[n,n===o?0:4-n%4]}o["-".charCodeAt(0)]=62,o["_".charCodeAt(0)]=63;export function byteLength(t){const o=r(t),n=o[0],e=o[1];return 3*(n+e)/4-e}export function toByteArray(t){const n=r(t),e=n[0],c=n[1],h=new Uint8Array(function(t,o,n){return 3*(o+n)/4-n}(0,e,c));let s=0;const a=c>0?e-4:e;let f;for(f=0;f<a;f+=4){const n=o[t.charCodeAt(f)]<<18|o[t.charCodeAt(f+1)]<<12|o[t.charCodeAt(f+2)]<<6|o[t.charCodeAt(f+3)];h[s++]=n>>16&255,h[s++]=n>>8&255,h[s++]=255&n}if(2===c){const n=o[t.charCodeAt(f)]<<2|o[t.charCodeAt(f+1)]>>4;h[s++]=255&n}if(1===c){const n=o[t.charCodeAt(f)]<<10|o[t.charCodeAt(f+1)]<<4|o[t.charCodeAt(f+2)]>>2;h[s++]=n>>8&255,h[s++]=255&n}return h}function e(o,n,r){const e=[];for(let h=n;h<r;h+=3){const n=(o[h]<<16&16711680)+(o[h+1]<<8&65280)+(255&o[h+2]);e.push(t[(c=n)>>18&63]+t[c>>12&63]+t[c>>6&63]+t[63&c])}var c;return e.join("")}export function fromByteArray(o){const n=o.length,r=n%3,c=[],h=16383;for(let t=0,s=n-r;t<s;t+=h)c.push(e(o,t,t+h>s?s:t+h));if(1===r){const r=o[n-1];c.push(t[r>>2]+t[r<<4&63]+"==")}else if(2===r){const r=(o[n-2]<<8)+o[n-1];c.push(t[r>>10]+t[r>>4&63]+t[r<<2&63]+"=")}return c.join("")}
✄
/*!
 * The buffer module from node.js, for Frida.
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */
import*as t from"base64-js";import*as e from"ieee754";export const config={INSPECT_MAX_BYTES:50};const r=**********;export{r as kMaxLength};function n(t){if(t>**********)throw new RangeError('The value "'+t+'" is invalid for option "size"');const e=new Uint8Array(t);return Object.setPrototypeOf(e,Buffer.prototype),e}Buffer.TYPED_ARRAY_SUPPORT=!0,Object.defineProperty(Buffer.prototype,"parent",{enumerable:!0,get:function(){if(Buffer.isBuffer(this))return this.buffer}}),Object.defineProperty(Buffer.prototype,"offset",{enumerable:!0,get:function(){if(Buffer.isBuffer(this))return this.byteOffset}});export function Buffer(t,e,r){if("number"==typeof t){if("string"==typeof e)throw new TypeError('The "string" argument must be of type string. Received type number');return o(t)}return f(t,e,r)}function f(t,e,r){if("string"==typeof t)return function(t,e){"string"==typeof e&&""!==e||(e="utf8");if(!Buffer.isEncoding(e))throw new TypeError("Unknown encoding: "+e);const r=0|a(t,e);let f=n(r);const i=f.write(t,e);i!==r&&(f=f.slice(0,i));return f}(t,e);if(ArrayBuffer.isView(t))return function(t){if(t instanceof Uint8Array){const e=new Uint8Array(t);return s(e.buffer,e.byteOffset,e.byteLength)}return u(t)}(t);if(null==t)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t);if(t instanceof ArrayBuffer||t&&t.buffer instanceof ArrayBuffer)return s(t,e,r);if(t instanceof SharedArrayBuffer||t&&t.buffer instanceof SharedArrayBuffer)return s(t,e,r);if("number"==typeof t)throw new TypeError('The "value" argument must not be of type number. Received type number');const f=t.valueOf&&t.valueOf();if(null!=f&&f!==t)return Buffer.from(f,e,r);const i=function(t){if(Buffer.isBuffer(t)){const e=0|h(t.length),r=n(e);return 0===r.length||t.copy(r,0,0,e),r}if(void 0!==t.length)return"number"!=typeof t.length||Number.isNaN(t.length)?n(0):u(t);if("Buffer"===t.type&&Array.isArray(t.data))return u(t.data)}(t);if(i)return i;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof t[Symbol.toPrimitive])return Buffer.from(t[Symbol.toPrimitive]("string"),e,r);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t)}function i(t){if("number"!=typeof t)throw new TypeError('"size" argument must be of type number');if(t<0)throw new RangeError('The value "'+t+'" is invalid for option "size"')}function o(t){return i(t),n(t<0?0:0|h(t))}function u(t){const e=t.length<0?0:0|h(t.length),r=n(e);for(let n=0;n<e;n+=1)r[n]=255&t[n];return r}function s(t,e,r){if(e<0||t.byteLength<e)throw new RangeError('"offset" is outside of buffer bounds');if(t.byteLength<e+(r||0))throw new RangeError('"length" is outside of buffer bounds');let n;return n=void 0===e&&void 0===r?new Uint8Array(t):void 0===r?new Uint8Array(t,e):new Uint8Array(t,e,r),Object.setPrototypeOf(n,Buffer.prototype),n}function h(t){if(t>=**********)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+**********..toString(16)+" bytes");return 0|t}Buffer.poolSize=8192,Buffer.from=function(t,e,r){return f(t,e,r)},Object.setPrototypeOf(Buffer.prototype,Uint8Array.prototype),Object.setPrototypeOf(Buffer,Uint8Array),Buffer.alloc=function(t,e,r){return function(t,e,r){return i(t),t<=0?n(t):void 0!==e?"string"==typeof r?n(t).fill(e,r):n(t).fill(e):n(t)}(t,e,r)},Buffer.allocUnsafe=function(t){return o(t)},Buffer.allocUnsafeSlow=function(t){return o(t)};export function SlowBuffer(t){return+t!=t&&(t=0),Buffer.alloc(+t)}function a(t,e){if(Buffer.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||t instanceof ArrayBuffer)return t.byteLength;if("string"!=typeof t)throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof t);const r=t.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;let f=!1;for(;;)switch(e){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return j(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return z(t).length;default:if(f)return n?-1:j(t).length;e=(""+e).toLowerCase(),f=!0}}function c(t,e,r){let n=!1;if((void 0===e||e<0)&&(e=0),e>this.length)return"";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return"";if((r>>>=0)<=(e>>>=0))return"";for(t||(t="utf8");;)switch(t){case"hex":return A(this,e,r);case"utf8":case"utf-8":return m(this,e,r);case"ascii":return I(this,e,r);case"latin1":case"binary":return U(this,e,r);case"base64":return E(this,e,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return v(this,e,r);default:if(n)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),n=!0}}function l(t,e,r){const n=t[e];t[e]=t[r],t[r]=n}function p(t,e,r,n,f){if(0===t.length)return-1;if("string"==typeof r?(n=r,r=0):r>**********?r=**********:r<-2147483648&&(r=-2147483648),r=+r,Number.isNaN(r)&&(r=f?0:t.length-1),r<0&&(r=t.length+r),r>=t.length){if(f)return-1;r=t.length-1}else if(r<0){if(!f)return-1;r=0}if("string"==typeof e&&(e=Buffer.from(e,n)),Buffer.isBuffer(e))return 0===e.length?-1:g(t,e,r,n,f);if("number"==typeof e)return e&=255,"function"==typeof Uint8Array.prototype.indexOf?f?Uint8Array.prototype.indexOf.call(t,e,r):Uint8Array.prototype.lastIndexOf.call(t,e,r):g(t,[e],r,n,f);throw new TypeError("val must be string, number or Buffer")}function g(t,e,r,n,f){let i,o=1,u=t.length,s=e.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(t.length<2||e.length<2)return-1;o=2,u/=2,s/=2,r/=2}function h(t,e){return 1===o?t[e]:t.readUInt16BE(e*o)}if(f){let n=-1;for(i=r;i<u;i++)if(h(t,i)===h(e,-1===n?0:i-n)){if(-1===n&&(n=i),i-n+1===s)return n*o}else-1!==n&&(i-=i-n),n=-1}else for(r+s>u&&(r=u-s),i=r;i>=0;i--){let r=!0;for(let n=0;n<s;n++)if(h(t,i+n)!==h(e,n)){r=!1;break}if(r)return i}return-1}function y(t,e,r,n){r=Number(r)||0;const f=t.length-r;n?(n=Number(n))>f&&(n=f):n=f;const i=e.length;let o;for(n>i/2&&(n=i/2),o=0;o<n;++o){const n=parseInt(e.substr(2*o,2),16);if(Number.isNaN(n))return o;t[r+o]=n}return o}function B(t,e,r,n){return D(j(e,t.length-r),t,r,n)}function w(t,e,r,n){return D(function(t){const e=[];for(let r=0;r<t.length;++r)e.push(255&t.charCodeAt(r));return e}(e),t,r,n)}function d(t,e,r,n){return D(z(e),t,r,n)}function b(t,e,r,n){return D(function(t,e){let r,n,f;const i=[];for(let o=0;o<t.length&&!((e-=2)<0);++o)r=t.charCodeAt(o),n=r>>8,f=r%256,i.push(f),i.push(n);return i}(e,t.length-r),t,r,n)}function E(e,r,n){return 0===r&&n===e.length?t.fromByteArray(e):t.fromByteArray(e.slice(r,n))}function m(t,e,r){r=Math.min(t.length,r);const n=[];let f=e;for(;f<r;){const e=t[f];let i=null,o=e>239?4:e>223?3:e>191?2:1;if(f+o<=r){let r,n,u,s;switch(o){case 1:e<128&&(i=e);break;case 2:r=t[f+1],128==(192&r)&&(s=(31&e)<<6|63&r,s>127&&(i=s));break;case 3:r=t[f+1],n=t[f+2],128==(192&r)&&128==(192&n)&&(s=(15&e)<<12|(63&r)<<6|63&n,s>2047&&(s<55296||s>57343)&&(i=s));break;case 4:r=t[f+1],n=t[f+2],u=t[f+3],128==(192&r)&&128==(192&n)&&128==(192&u)&&(s=(15&e)<<18|(63&r)<<12|(63&n)<<6|63&u,s>65535&&s<1114112&&(i=s))}}null===i?(i=65533,o=1):i>65535&&(i-=65536,n.push(i>>>10&1023|55296),i=56320|1023&i),n.push(i),f+=o}return function(t){const e=t.length;if(e<=4096)return String.fromCharCode.apply(String,t);let r="",n=0;for(;n<e;)r+=String.fromCharCode.apply(String,t.slice(n,n+=4096));return r}(n)}Buffer.isBuffer=function(t){return null!=t&&!0===t._isBuffer&&t!==Buffer.prototype},Buffer.compare=function(t,e){if(t instanceof Uint8Array&&(t=Buffer.from(t,t.offset,t.byteLength)),e instanceof Uint8Array&&(e=Buffer.from(e,e.offset,e.byteLength)),!Buffer.isBuffer(t)||!Buffer.isBuffer(e))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===e)return 0;let r=t.length,n=e.length;for(let f=0,i=Math.min(r,n);f<i;++f)if(t[f]!==e[f]){r=t[f],n=e[f];break}return r<n?-1:n<r?1:0},Buffer.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},Buffer.concat=function(t,e){if(!Array.isArray(t))throw new TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return Buffer.alloc(0);let r;if(void 0===e)for(e=0,r=0;r<t.length;++r)e+=t[r].length;const n=Buffer.allocUnsafe(e);let f=0;for(r=0;r<t.length;++r){let e=t[r];if(e instanceof Uint8Array)f+e.length>n.length?(Buffer.isBuffer(e)||(e=Buffer.from(e.buffer,e.byteOffset,e.byteLength)),e.copy(n,f)):Uint8Array.prototype.set.call(n,e,f);else{if(!Buffer.isBuffer(e))throw new TypeError('"list" argument must be an Array of Buffers');e.copy(n,f)}f+=e.length}return n},Buffer.byteLength=a,Buffer.prototype._isBuffer=!0,Buffer.prototype.swap16=function(){const t=this.length;if(t%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(let e=0;e<t;e+=2)l(this,e,e+1);return this},Buffer.prototype.swap32=function(){const t=this.length;if(t%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(let e=0;e<t;e+=4)l(this,e,e+3),l(this,e+1,e+2);return this},Buffer.prototype.swap64=function(){const t=this.length;if(t%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(let e=0;e<t;e+=8)l(this,e,e+7),l(this,e+1,e+6),l(this,e+2,e+5),l(this,e+3,e+4);return this},Buffer.prototype.toString=function(){const t=this.length;return 0===t?"":0===arguments.length?m(this,0,t):c.apply(this,arguments)},Buffer.prototype.toLocaleString=Buffer.prototype.toString,Buffer.prototype.equals=function(t){if(!Buffer.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t||0===Buffer.compare(this,t)},Buffer.prototype.inspect=function(){let t="";const e=config.INSPECT_MAX_BYTES;return t=this.toString("hex",0,e).replace(/(.{2})/g,"$1 ").trim(),this.length>e&&(t+=" ... "),"<Buffer "+t+">"},Buffer.prototype[Symbol.for("nodejs.util.inspect.custom")]=Buffer.prototype.inspect,Buffer.prototype.compare=function(t,e,r,n,f){if(t instanceof Uint8Array&&(t=Buffer.from(t,t.offset,t.byteLength)),!Buffer.isBuffer(t))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof t);if(void 0===e&&(e=0),void 0===r&&(r=t?t.length:0),void 0===n&&(n=0),void 0===f&&(f=this.length),e<0||r>t.length||n<0||f>this.length)throw new RangeError("out of range index");if(n>=f&&e>=r)return 0;if(n>=f)return-1;if(e>=r)return 1;if(this===t)return 0;let i=(f>>>=0)-(n>>>=0),o=(r>>>=0)-(e>>>=0);const u=Math.min(i,o),s=this.slice(n,f),h=t.slice(e,r);for(let t=0;t<u;++t)if(s[t]!==h[t]){i=s[t],o=h[t];break}return i<o?-1:o<i?1:0},Buffer.prototype.includes=function(t,e,r){return-1!==this.indexOf(t,e,r)},Buffer.prototype.indexOf=function(t,e,r){return p(this,t,e,r,!0)},Buffer.prototype.lastIndexOf=function(t,e,r){return p(this,t,e,r,!1)},Buffer.prototype.write=function(t,e,r,n){if(void 0===e)n="utf8",r=this.length,e=0;else if(void 0===r&&"string"==typeof e)n=e,r=this.length,e=0;else{if(!isFinite(e))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");e>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0)}const f=this.length-e;if((void 0===r||r>f)&&(r=f),t.length>0&&(r<0||e<0)||e>this.length)throw new RangeError("Attempt to write outside buffer bounds");n||(n="utf8");let i=!1;for(;;)switch(n){case"hex":return y(this,t,e,r);case"utf8":case"utf-8":return B(this,t,e,r);case"ascii":case"latin1":case"binary":return w(this,t,e,r);case"base64":return d(this,t,e,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return b(this,t,e,r);default:if(i)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),i=!0}},Buffer.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function I(t,e,r){let n="";r=Math.min(t.length,r);for(let f=e;f<r;++f)n+=String.fromCharCode(127&t[f]);return n}function U(t,e,r){let n="";r=Math.min(t.length,r);for(let f=e;f<r;++f)n+=String.fromCharCode(t[f]);return n}function A(t,e,r){const n=t.length;(!e||e<0)&&(e=0),(!r||r<0||r>n)&&(r=n);let f="";for(let n=e;n<r;++n)f+=G[t[n]];return f}function v(t,e,r){const n=t.slice(e,r);let f="";for(let t=0;t<n.length-1;t+=2)f+=String.fromCharCode(n[t]+256*n[t+1]);return f}function R(t,e,r){if(t%1!=0||t<0)throw new RangeError("offset is not uint");if(t+e>r)throw new RangeError("Trying to access beyond buffer length")}function T(t,e,r,n,f,i){if(!Buffer.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(e>f||e<i)throw new RangeError('"value" argument is out of bounds');if(r+n>t.length)throw new RangeError("Index out of range")}function L(t,e,r,n,f){k(e,n,f,t,r,7);let i=Number(e&BigInt(4294967295));t[r++]=i,i>>=8,t[r++]=i,i>>=8,t[r++]=i,i>>=8,t[r++]=i;let o=Number(e>>BigInt(32)&BigInt(4294967295));return t[r++]=o,o>>=8,t[r++]=o,o>>=8,t[r++]=o,o>>=8,t[r++]=o,r}function O(t,e,r,n,f){k(e,n,f,t,r,7);let i=Number(e&BigInt(4294967295));t[r+7]=i,i>>=8,t[r+6]=i,i>>=8,t[r+5]=i,i>>=8,t[r+4]=i;let o=Number(e>>BigInt(32)&BigInt(4294967295));return t[r+3]=o,o>>=8,t[r+2]=o,o>>=8,t[r+1]=o,o>>=8,t[r]=o,r+8}function S(t,e,r,n,f,i){if(r+n>t.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function _(t,r,n,f,i){return r=+r,n>>>=0,i||S(t,0,n,4),e.write(t,r,n,f,23,4),n+4}function x(t,r,n,f,i){return r=+r,n>>>=0,i||S(t,0,n,8),e.write(t,r,n,f,52,8),n+8}Buffer.prototype.slice=function(t,e){const r=this.length;(t=~~t)<0?(t+=r)<0&&(t=0):t>r&&(t=r),(e=void 0===e?r:~~e)<0?(e+=r)<0&&(e=0):e>r&&(e=r),e<t&&(e=t);const n=this.subarray(t,e);return Object.setPrototypeOf(n,Buffer.prototype),n},Buffer.prototype.readUintLE=Buffer.prototype.readUIntLE=function(t,e,r){t>>>=0,e>>>=0,r||R(t,e,this.length);let n=this[t],f=1,i=0;for(;++i<e&&(f*=256);)n+=this[t+i]*f;return n},Buffer.prototype.readUintBE=Buffer.prototype.readUIntBE=function(t,e,r){t>>>=0,e>>>=0,r||R(t,e,this.length);let n=this[t+--e],f=1;for(;e>0&&(f*=256);)n+=this[t+--e]*f;return n},Buffer.prototype.readUint8=Buffer.prototype.readUInt8=function(t,e){return t>>>=0,e||R(t,1,this.length),this[t]},Buffer.prototype.readUint16LE=Buffer.prototype.readUInt16LE=function(t,e){return t>>>=0,e||R(t,2,this.length),this[t]|this[t+1]<<8},Buffer.prototype.readUint16BE=Buffer.prototype.readUInt16BE=function(t,e){return t>>>=0,e||R(t,2,this.length),this[t]<<8|this[t+1]},Buffer.prototype.readUint32LE=Buffer.prototype.readUInt32LE=function(t,e){return t>>>=0,e||R(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},Buffer.prototype.readUint32BE=Buffer.prototype.readUInt32BE=function(t,e){return t>>>=0,e||R(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},Buffer.prototype.readBigUInt64LE=function(t){P(t>>>=0,"offset");const e=this[t],r=this[t+7];void 0!==e&&void 0!==r||M(t,this.length-8);const n=e+256*this[++t]+65536*this[++t]+this[++t]*2**24,f=this[++t]+256*this[++t]+65536*this[++t]+r*2**24;return BigInt(n)+(BigInt(f)<<BigInt(32))},Buffer.prototype.readBigUInt64BE=function(t){P(t>>>=0,"offset");const e=this[t],r=this[t+7];void 0!==e&&void 0!==r||M(t,this.length-8);const n=e*2**24+65536*this[++t]+256*this[++t]+this[++t],f=this[++t]*2**24+65536*this[++t]+256*this[++t]+r;return(BigInt(n)<<BigInt(32))+BigInt(f)},Buffer.prototype.readIntLE=function(t,e,r){t>>>=0,e>>>=0,r||R(t,e,this.length);let n=this[t],f=1,i=0;for(;++i<e&&(f*=256);)n+=this[t+i]*f;return f*=128,n>=f&&(n-=Math.pow(2,8*e)),n},Buffer.prototype.readIntBE=function(t,e,r){t>>>=0,e>>>=0,r||R(t,e,this.length);let n=e,f=1,i=this[t+--n];for(;n>0&&(f*=256);)i+=this[t+--n]*f;return f*=128,i>=f&&(i-=Math.pow(2,8*e)),i},Buffer.prototype.readInt8=function(t,e){return t>>>=0,e||R(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},Buffer.prototype.readInt16LE=function(t,e){t>>>=0,e||R(t,2,this.length);const r=this[t]|this[t+1]<<8;return 32768&r?4294901760|r:r},Buffer.prototype.readInt16BE=function(t,e){t>>>=0,e||R(t,2,this.length);const r=this[t+1]|this[t]<<8;return 32768&r?4294901760|r:r},Buffer.prototype.readInt32LE=function(t,e){return t>>>=0,e||R(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},Buffer.prototype.readInt32BE=function(t,e){return t>>>=0,e||R(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},Buffer.prototype.readBigInt64LE=function(t){P(t>>>=0,"offset");const e=this[t],r=this[t+7];void 0!==e&&void 0!==r||M(t,this.length-8);const n=this[t+4]+256*this[t+5]+65536*this[t+6]+(r<<24);return(BigInt(n)<<BigInt(32))+BigInt(e+256*this[++t]+65536*this[++t]+this[++t]*2**24)},Buffer.prototype.readBigInt64BE=function(t){P(t>>>=0,"offset");const e=this[t],r=this[t+7];void 0!==e&&void 0!==r||M(t,this.length-8);const n=(e<<24)+65536*this[++t]+256*this[++t]+this[++t];return(BigInt(n)<<BigInt(32))+BigInt(this[++t]*2**24+65536*this[++t]+256*this[++t]+r)},Buffer.prototype.readFloatLE=function(t,r){return t>>>=0,r||R(t,4,this.length),e.read(this,t,!0,23,4)},Buffer.prototype.readFloatBE=function(t,r){return t>>>=0,r||R(t,4,this.length),e.read(this,t,!1,23,4)},Buffer.prototype.readDoubleLE=function(t,r){return t>>>=0,r||R(t,8,this.length),e.read(this,t,!0,52,8)},Buffer.prototype.readDoubleBE=function(t,r){return t>>>=0,r||R(t,8,this.length),e.read(this,t,!1,52,8)},Buffer.prototype.writeUintLE=Buffer.prototype.writeUIntLE=function(t,e,r,n){if(t=+t,e>>>=0,r>>>=0,!n){T(this,t,e,r,Math.pow(2,8*r)-1,0)}let f=1,i=0;for(this[e]=255&t;++i<r&&(f*=256);)this[e+i]=t/f&255;return e+r},Buffer.prototype.writeUintBE=Buffer.prototype.writeUIntBE=function(t,e,r,n){if(t=+t,e>>>=0,r>>>=0,!n){T(this,t,e,r,Math.pow(2,8*r)-1,0)}let f=r-1,i=1;for(this[e+f]=255&t;--f>=0&&(i*=256);)this[e+f]=t/i&255;return e+r},Buffer.prototype.writeUint8=Buffer.prototype.writeUInt8=function(t,e,r){return t=+t,e>>>=0,r||T(this,t,e,1,255,0),this[e]=255&t,e+1},Buffer.prototype.writeUint16LE=Buffer.prototype.writeUInt16LE=function(t,e,r){return t=+t,e>>>=0,r||T(this,t,e,2,65535,0),this[e]=255&t,this[e+1]=t>>>8,e+2},Buffer.prototype.writeUint16BE=Buffer.prototype.writeUInt16BE=function(t,e,r){return t=+t,e>>>=0,r||T(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=255&t,e+2},Buffer.prototype.writeUint32LE=Buffer.prototype.writeUInt32LE=function(t,e,r){return t=+t,e>>>=0,r||T(this,t,e,4,4294967295,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t,e+4},Buffer.prototype.writeUint32BE=Buffer.prototype.writeUInt32BE=function(t,e,r){return t=+t,e>>>=0,r||T(this,t,e,4,4294967295,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},Buffer.prototype.writeBigUInt64LE=function(t,e=0){return L(this,t,e,BigInt(0),BigInt("0xffffffffffffffff"))},Buffer.prototype.writeBigUInt64BE=function(t,e=0){return O(this,t,e,BigInt(0),BigInt("0xffffffffffffffff"))},Buffer.prototype.writeIntLE=function(t,e,r,n){if(t=+t,e>>>=0,!n){const n=Math.pow(2,8*r-1);T(this,t,e,r,n-1,-n)}let f=0,i=1,o=0;for(this[e]=255&t;++f<r&&(i*=256);)t<0&&0===o&&0!==this[e+f-1]&&(o=1),this[e+f]=(t/i>>0)-o&255;return e+r},Buffer.prototype.writeIntBE=function(t,e,r,n){if(t=+t,e>>>=0,!n){const n=Math.pow(2,8*r-1);T(this,t,e,r,n-1,-n)}let f=r-1,i=1,o=0;for(this[e+f]=255&t;--f>=0&&(i*=256);)t<0&&0===o&&0!==this[e+f+1]&&(o=1),this[e+f]=(t/i>>0)-o&255;return e+r},Buffer.prototype.writeInt8=function(t,e,r){return t=+t,e>>>=0,r||T(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=255&t,e+1},Buffer.prototype.writeInt16LE=function(t,e,r){return t=+t,e>>>=0,r||T(this,t,e,2,32767,-32768),this[e]=255&t,this[e+1]=t>>>8,e+2},Buffer.prototype.writeInt16BE=function(t,e,r){return t=+t,e>>>=0,r||T(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=255&t,e+2},Buffer.prototype.writeInt32LE=function(t,e,r){return t=+t,e>>>=0,r||T(this,t,e,4,**********,-2147483648),this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4},Buffer.prototype.writeInt32BE=function(t,e,r){return t=+t,e>>>=0,r||T(this,t,e,4,**********,-2147483648),t<0&&(t=4294967295+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},Buffer.prototype.writeBigInt64LE=function(t,e=0){return L(this,t,e,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))},Buffer.prototype.writeBigInt64BE=function(t,e=0){return O(this,t,e,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))},Buffer.prototype.writeFloatLE=function(t,e,r){return _(this,t,e,!0,r)},Buffer.prototype.writeFloatBE=function(t,e,r){return _(this,t,e,!1,r)},Buffer.prototype.writeDoubleLE=function(t,e,r){return x(this,t,e,!0,r)},Buffer.prototype.writeDoubleBE=function(t,e,r){return x(this,t,e,!1,r)},Buffer.prototype.copy=function(t,e,r,n){if(!Buffer.isBuffer(t))throw new TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),e>=t.length&&(e=t.length),e||(e=0),n>0&&n<r&&(n=r),n===r)return 0;if(0===t.length||0===this.length)return 0;if(e<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),t.length-e<n-r&&(n=t.length-e+r);const f=n-r;return this===t?this.copyWithin(e,r,n):Uint8Array.prototype.set.call(t,this.subarray(r,n),e),f},Buffer.prototype.fill=function(t,e,r,n){if("string"==typeof t){if("string"==typeof e?(n=e,e=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw new TypeError("encoding must be a string");if("string"==typeof n&&!Buffer.isEncoding(n))throw new TypeError("Unknown encoding: "+n);if(1===t.length){const e=t.charCodeAt(0);("utf8"===n&&e<128||"latin1"===n)&&(t=e)}}else"number"==typeof t?t&=255:"boolean"==typeof t&&(t=Number(t));if(e<0||this.length<e||this.length<r)throw new RangeError("Out of range index");if(r<=e)return this;let f;if(e>>>=0,r=void 0===r?this.length:r>>>0,t||(t=0),"number"==typeof t)for(f=e;f<r;++f)this[f]=t;else{const i=Buffer.isBuffer(t)?t:Buffer.from(t,n),o=i.length;if(0===o)throw new TypeError('The value "'+t+'" is invalid for argument "value"');for(f=0;f<r-e;++f)this[f+e]=i[f%o]}return this};const $={};function N(t,e,r){$[t]=class extends r{constructor(){super(),Object.defineProperty(this,"message",{value:e.apply(this,arguments),writable:!0,configurable:!0}),this.name=`${this.name} [${t}]`,this.stack,delete this.name}get code(){return t}set code(t){Object.defineProperty(this,"code",{configurable:!0,enumerable:!0,value:t,writable:!0})}toString(){return`${this.name} [${t}]: ${this.message}`}}}function C(t){let e="",r=t.length;const n="-"===t[0]?1:0;for(;r>=n+4;r-=3)e=`_${t.slice(r-3,r)}${e}`;return`${t.slice(0,r)}${e}`}function k(t,e,r,n,f,i){if(t>r||t<e){const n="bigint"==typeof e?"n":"";let f;throw f=i>3?0===e||e===BigInt(0)?`>= 0${n} and < 2${n} ** ${8*(i+1)}${n}`:`>= -(2${n} ** ${8*(i+1)-1}${n}) and < 2 ** ${8*(i+1)-1}${n}`:`>= ${e}${n} and <= ${r}${n}`,new $.ERR_OUT_OF_RANGE("value",f,t)}!function(t,e,r){P(e,"offset"),void 0!==t[e]&&void 0!==t[e+r]||M(e,t.length-(r+1))}(n,f,i)}function P(t,e){if("number"!=typeof t)throw new $.ERR_INVALID_ARG_TYPE(e,"number",t)}function M(t,e,r){if(Math.floor(t)!==t)throw P(t,r),new $.ERR_OUT_OF_RANGE(r||"offset","an integer",t);if(e<0)throw new $.ERR_BUFFER_OUT_OF_BOUNDS;throw new $.ERR_OUT_OF_RANGE(r||"offset",`>= ${r?1:0} and <= ${e}`,t)}N("ERR_BUFFER_OUT_OF_BOUNDS",(function(t){return t?`${t} is outside of buffer bounds`:"Attempt to access memory outside buffer bounds"}),RangeError),N("ERR_INVALID_ARG_TYPE",(function(t,e){return`The "${t}" argument must be of type number. Received type ${typeof e}`}),TypeError),N("ERR_OUT_OF_RANGE",(function(t,e,r){let n=`The value of "${t}" is out of range.`,f=r;return Number.isInteger(r)&&Math.abs(r)>2**32?f=C(String(r)):"bigint"==typeof r&&(f=String(r),(r>BigInt(2)**BigInt(32)||r<-(BigInt(2)**BigInt(32)))&&(f=C(f)),f+="n"),n+=` It must be ${e}. Received ${f}`,n}),RangeError);const F=/[^+/0-9A-Za-z-_]/g;function j(t,e){let r;e=e||1/0;const n=t.length;let f=null;const i=[];for(let o=0;o<n;++o){if(r=t.charCodeAt(o),r>55295&&r<57344){if(!f){if(r>56319){(e-=3)>-1&&i.push(239,191,189);continue}if(o+1===n){(e-=3)>-1&&i.push(239,191,189);continue}f=r;continue}if(r<56320){(e-=3)>-1&&i.push(239,191,189),f=r;continue}r=65536+(f-55296<<10|r-56320)}else f&&(e-=3)>-1&&i.push(239,191,189);if(f=null,r<128){if((e-=1)<0)break;i.push(r)}else if(r<2048){if((e-=2)<0)break;i.push(r>>6|192,63&r|128)}else if(r<65536){if((e-=3)<0)break;i.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((e-=4)<0)break;i.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return i}function z(e){return t.toByteArray(function(t){if((t=(t=t.split("=")[0]).trim().replace(F,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(e))}function D(t,e,r,n){let f;for(f=0;f<n&&!(f+r>=e.length||f>=t.length);++f)e[f+r]=t[f];return f}const G=function(){const t="0123456789abcdef",e=new Array(256);for(let r=0;r<16;++r){const n=16*r;for(let f=0;f<16;++f)e[n+f]=t[r]+t[f]}return e}();export default{config,kMaxLength:**********,Buffer,SlowBuffer};
✄
export default e;export{e as EventEmitter,l as once};function e(){e.init.call(this)}e.EventEmitter=e,e.prototype._events=void 0,e.prototype._eventsCount=0,e.prototype._maxListeners=void 0;let t=10;function n(e){if("function"!=typeof e)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function r(t){return void 0===t._maxListeners?e.defaultMaxListeners:t._maxListeners}function i(e,t,i,o){let s;n(i);let u=e._events;if(void 0===u?(u=e._events=Object.create(null),e._eventsCount=0):(void 0!==u.newListener&&(e.emit("newListener",t,i.listener?i.listener:i),u=e._events),s=u[t]),void 0===s)s=u[t]=i,++e._eventsCount;else{"function"==typeof s?s=u[t]=o?[i,s]:[s,i]:o?s.unshift(i):s.push(i);const n=r(e);if(n>0&&s.length>n&&!s.warned){s.warned=!0;const n=new Error("Possible EventEmitter memory leak detected. "+s.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");n.name="MaxListenersExceededWarning",n.emitter=e,n.type=t,n.count=s.length,f=n,console.warn(f)}}var f;return e}function o(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function s(e,t,n){const r={fired:!1,wrapFn:void 0,target:e,type:t,listener:n},i=o.bind(r);return i.listener=n,r.wrapFn=i,i}function u(e,t,n){const r=e._events;if(void 0===r)return[];const i=r[t];return void 0===i?[]:"function"==typeof i?n?[i.listener||i]:[i]:n?function(e){const t=new Array(e.length);for(let n=0;n<t.length;++n)t[n]=e[n].listener||e[n];return t}(i):c(i,i.length)}function f(e){const t=this._events;if(void 0!==t){const n=t[e];if("function"==typeof n)return 1;if(void 0!==n)return n.length}return 0}function c(e,t){const n=new Array(t);for(let r=0;r<t;++r)n[r]=e[r];return n}function l(e,t){return new Promise((function(n,r){function i(n){e.removeListener(t,o),r(n)}function o(){"function"==typeof e.removeListener&&e.removeListener("error",i),n([].slice.call(arguments))}h(e,t,o,{once:!0}),"error"!==t&&function(e,t,n){"function"==typeof e.on&&h(e,"error",t,n)}(e,i,{once:!0})}))}function h(e,t,n,r){if("function"==typeof e.on)r.once?e.once(t,n):e.on(t,n);else{if("function"!=typeof e.addEventListener)throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e);e.addEventListener(t,(function i(o){r.once&&e.removeEventListener(t,i),n(o)}))}}Object.defineProperty(e,"defaultMaxListeners",{enumerable:!0,get:function(){return t},set:function(e){if("number"!=typeof e||e<0||Number.isNaN(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");t=e}}),e.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},e.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||Number.isNaN(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},e.prototype.getMaxListeners=function(){return r(this)},e.prototype.emit=function(e){const t=[];for(let e=1;e<arguments.length;e++)t.push(arguments[e]);let n="error"===e;const r=this._events;if(void 0!==r)n=n&&void 0===r.error;else if(!n)return!1;if(n){let e;if(t.length>0&&(e=t[0]),e instanceof Error)throw e;const n=new Error("Unhandled error."+(e?" ("+e.message+")":""));throw n.context=e,n}const i=r[e];if(void 0===i)return!1;if("function"==typeof i)Reflect.apply(i,this,t);else{const e=i.length,n=c(i,e);for(let r=0;r<e;++r)Reflect.apply(n[r],this,t)}return!0},e.prototype.addListener=function(e,t){return i(this,e,t,!1)},e.prototype.on=e.prototype.addListener,e.prototype.prependListener=function(e,t){return i(this,e,t,!0)},e.prototype.once=function(e,t){return n(t),this.on(e,s(this,e,t)),this},e.prototype.prependOnceListener=function(e,t){return n(t),this.prependListener(e,s(this,e,t)),this},e.prototype.removeListener=function(e,t){n(t);const r=this._events;if(void 0===r)return this;const i=r[e];if(void 0===i)return this;if(i===t||i.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete r[e],r.removeListener&&this.emit("removeListener",e,i.listener||t));else if("function"!=typeof i){let n,o=-1;for(let e=i.length-1;e>=0;e--)if(i[e]===t||i[e].listener===t){n=i[e].listener,o=e;break}if(o<0)return this;0===o?i.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(i,o),1===i.length&&(r[e]=i[0]),void 0!==r.removeListener&&this.emit("removeListener",e,n||t)}return this},e.prototype.off=e.prototype.removeListener,e.prototype.removeAllListeners=function(e){const t=this._events;if(void 0===t)return this;if(void 0===t.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==t[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete t[e]),this;if(0===arguments.length){const e=Object.keys(t);for(let t=0;t<e.length;++t){const n=e[t];"removeListener"!==n&&this.removeAllListeners(n)}return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}const n=t[e];if("function"==typeof n)this.removeListener(e,n);else if(void 0!==n)for(let t=n.length-1;t>=0;t--)this.removeListener(e,n[t]);return this},e.prototype.listeners=function(e){return u(this,e,!0)},e.prototype.rawListeners=function(e){return u(this,e,!1)},e.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):f.call(e,t)},e.prototype.listenerCount=f,e.prototype.eventNames=function(){return this._eventsCount>0?Reflect.ownKeys(this._events):[]};
✄
/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */
export function read(t,o,a,e,h){let r,M;const p=8*h-e-1,n=(1<<p)-1,w=n>>1;let f=-7,l=a?h-1:0;const s=a?-1:1;let i=t[o+l];for(l+=s,r=i&(1<<-f)-1,i>>=-f,f+=p;f>0;)r=256*r+t[o+l],l+=s,f-=8;for(M=r&(1<<-f)-1,r>>=-f,f+=e;f>0;)M=256*M+t[o+l],l+=s,f-=8;if(0===r)r=1-w;else{if(r===n)return M?NaN:1/0*(i?-1:1);M+=Math.pow(2,e),r-=w}return(i?-1:1)*M*Math.pow(2,r-e)}export function write(t,o,a,e,h,r){let M,p,n,w=8*r-h-1;const f=(1<<w)-1,l=f>>1,s=23===h?Math.pow(2,-24)-Math.pow(2,-77):0;let i=e?0:r-1;const N=e?1:-1,c=o<0||0===o&&1/o<0?1:0;for(o=Math.abs(o),isNaN(o)||o===1/0?(p=isNaN(o)?1:0,M=f):(M=Math.floor(Math.log(o)/Math.LN2),o*(n=Math.pow(2,-M))<1&&(M--,n*=2),(o+=M+l>=1?s/n:s*Math.pow(2,1-l))*n>=2&&(M++,n/=2),M+l>=f?(p=0,M=f):M+l>=1?(p=(o*n-1)*Math.pow(2,h),M+=l):(p=o*Math.pow(2,l-1)*Math.pow(2,h),M=0));h>=8;)t[a+i]=255&p,i+=N,p/=256,h-=8;for(M=M<<h|p,w+=h;w>0;)t[a+i]=255&M,i+=N,M/=256,w-=8;t[a+i-N]|=128*c}
✄
import e from"process";const t="win32"===e.platform;function r(e){return 47===e||92===e}function o(e){return 47===e}function n(e){return e>=65&&e<=90||e>=97&&e<=122}function l(e,t,r,o){let n="",l=0,i=-1,c=0,s=0;for(let a=0;a<=e.length;++a){if(a<e.length)s=e.charCodeAt(a);else{if(o(s))break;s=47}if(o(s)){if(i===a-1||1===c);else if(2===c){if(n.length<2||2!==l||46!==n.charCodeAt(n.length-1)||46!==n.charCodeAt(n.length-2)){if(n.length>2){const e=n.lastIndexOf(r);-1===e?(n="",l=0):(n=n.slice(0,e),l=n.length-1-n.lastIndexOf(r)),i=a,c=0;continue}if(0!==n.length){n="",l=0,i=a,c=0;continue}}t&&(n+=n.length>0?`${r}..`:"..",l=2)}else n.length>0?n+=`${r}${e.slice(i+1,a)}`:n=e.slice(i+1,a),l=a-i-1;i=a,c=0}else 46===s&&-1!==c?++c:c=-1}return n}function i(e,t){const r=t.dir||t.root,o=t.base||`${t.name||""}${t.ext||""}`;return r?r===t.root?`${r}${o}`:`${r}${e}${o}`:o}const c={resolve(...t){let o="",i="",c=!1;for(let l=t.length-1;l>=-1;l--){let s;if(l>=0){if(s=t[l],0===s.length)continue}else 0===o.length?s=e.cwd():(s=e.env[`=${o}`]||e.cwd(),(void 0===s||s.slice(0,2).toLowerCase()!==o.toLowerCase()&&92===s.charCodeAt(2))&&(s=`${o}\\`));const a=s.length;let h=0,f="",d=!1;const C=s.charCodeAt(0);if(1===a)r(C)&&(h=1,d=!0);else if(r(C))if(d=!0,r(s.charCodeAt(1))){let e=2,t=e;for(;e<a&&!r(s.charCodeAt(e));)e++;if(e<a&&e!==t){const o=s.slice(t,e);for(t=e;e<a&&r(s.charCodeAt(e));)e++;if(e<a&&e!==t){for(t=e;e<a&&!r(s.charCodeAt(e));)e++;e!==a&&e===t||(f=`\\\\${o}\\${s.slice(t,e)}`,h=e)}}}else h=1;else n(C)&&58===s.charCodeAt(1)&&(f=s.slice(0,2),h=2,a>2&&r(s.charCodeAt(2))&&(d=!0,h=3));if(f.length>0)if(o.length>0){if(f.toLowerCase()!==o.toLowerCase())continue}else o=f;if(c){if(o.length>0)break}else if(i=`${s.slice(h)}\\${i}`,c=d,d&&o.length>0)break}return i=l(i,!c,"\\",r),c?`${o}\\${i}`:`${o}${i}`||"."},normalize(e){const t=e.length;if(0===t)return".";let i,c=0,s=!1;const a=e.charCodeAt(0);if(1===t)return o(a)?"\\":e;if(r(a))if(s=!0,r(e.charCodeAt(1))){let o=2,n=o;for(;o<t&&!r(e.charCodeAt(o));)o++;if(o<t&&o!==n){const l=e.slice(n,o);for(n=o;o<t&&r(e.charCodeAt(o));)o++;if(o<t&&o!==n){for(n=o;o<t&&!r(e.charCodeAt(o));)o++;if(o===t)return`\\\\${l}\\${e.slice(n)}\\`;o!==n&&(i=`\\\\${l}\\${e.slice(n,o)}`,c=o)}}}else c=1;else n(a)&&58===e.charCodeAt(1)&&(i=e.slice(0,2),c=2,t>2&&r(e.charCodeAt(2))&&(s=!0,c=3));let h=c<t?l(e.slice(c),!s,"\\",r):"";return 0!==h.length||s||(h="."),h.length>0&&r(e.charCodeAt(t-1))&&(h+="\\"),void 0===i?s?`\\${h}`:h:s?`${i}\\${h}`:`${i}${h}`},isAbsolute(e){const t=e.length;if(0===t)return!1;const o=e.charCodeAt(0);return r(o)||t>2&&n(o)&&58===e.charCodeAt(1)&&r(e.charCodeAt(2))},join(...e){if(0===e.length)return".";let t,o;for(let r=0;r<e.length;++r){const n=e[r];n.length>0&&(void 0===t?t=o=n:t+=`\\${n}`)}if(void 0===t)return".";let n=!0,l=0;if(r(o.charCodeAt(0))){++l;const e=o.length;e>1&&r(o.charCodeAt(1))&&(++l,e>2&&(r(o.charCodeAt(2))?++l:n=!1))}if(n){for(;l<t.length&&r(t.charCodeAt(l));)l++;l>=2&&(t=`\\${t.slice(l)}`)}return c.normalize(t)},relative(e,t){if(e===t)return"";const r=c.resolve(e),o=c.resolve(t);if(r===o)return"";if((e=r.toLowerCase())===(t=o.toLowerCase()))return"";let n=0;for(;n<e.length&&92===e.charCodeAt(n);)n++;let l=e.length;for(;l-1>n&&92===e.charCodeAt(l-1);)l--;const i=l-n;let s=0;for(;s<t.length&&92===t.charCodeAt(s);)s++;let a=t.length;for(;a-1>s&&92===t.charCodeAt(a-1);)a--;const h=a-s,f=i<h?i:h;let d=-1,C=0;for(;C<f;C++){const r=e.charCodeAt(n+C);if(r!==t.charCodeAt(s+C))break;92===r&&(d=C)}if(C!==f){if(-1===d)return o}else{if(h>f){if(92===t.charCodeAt(s+C))return o.slice(s+C+1);if(2===C)return o.slice(s+C)}i>f&&(92===e.charCodeAt(n+C)?d=C:2===C&&(d=3)),-1===d&&(d=0)}let A="";for(C=n+d+1;C<=l;++C)C!==l&&92!==e.charCodeAt(C)||(A+=0===A.length?"..":"\\..");return s+=d,A.length>0?`${A}${o.slice(s,a)}`:(92===o.charCodeAt(s)&&++s,o.slice(s,a))},toNamespacedPath(e){if("string"!=typeof e||0===e.length)return e;const t=c.resolve(e);if(t.length<=2)return e;if(92===t.charCodeAt(0)){if(92===t.charCodeAt(1)){const e=t.charCodeAt(2);if(63!==e&&46!==e)return`\\\\?\\UNC\\${t.slice(2)}`}}else if(n(t.charCodeAt(0))&&58===t.charCodeAt(1)&&92===t.charCodeAt(2))return`\\\\?\\${t}`;return e},dirname(e){const t=e.length;if(0===t)return".";let o=-1,l=0;const i=e.charCodeAt(0);if(1===t)return r(i)?e:".";if(r(i)){if(o=l=1,r(e.charCodeAt(1))){let n=2,i=n;for(;n<t&&!r(e.charCodeAt(n));)n++;if(n<t&&n!==i){for(i=n;n<t&&r(e.charCodeAt(n));)n++;if(n<t&&n!==i){for(i=n;n<t&&!r(e.charCodeAt(n));)n++;if(n===t)return e;n!==i&&(o=l=n+1)}}}}else n(i)&&58===e.charCodeAt(1)&&(o=t>2&&r(e.charCodeAt(2))?3:2,l=o);let c=-1,s=!0;for(let o=t-1;o>=l;--o)if(r(e.charCodeAt(o))){if(!s){c=o;break}}else s=!1;if(-1===c){if(-1===o)return".";c=o}return e.slice(0,c)},basename(e,t){let o=0,l=-1,i=!0;if(e.length>=2&&n(e.charCodeAt(0))&&58===e.charCodeAt(1)&&(o=2),void 0!==t&&t.length>0&&t.length<=e.length){if(t===e)return"";let n=t.length-1,c=-1;for(let s=e.length-1;s>=o;--s){const a=e.charCodeAt(s);if(r(a)){if(!i){o=s+1;break}}else-1===c&&(i=!1,c=s+1),n>=0&&(a===t.charCodeAt(n)?-1==--n&&(l=s):(n=-1,l=c))}return o===l?l=c:-1===l&&(l=e.length),e.slice(o,l)}for(let t=e.length-1;t>=o;--t)if(r(e.charCodeAt(t))){if(!i){o=t+1;break}}else-1===l&&(i=!1,l=t+1);return-1===l?"":e.slice(o,l)},extname(e){let t=0,o=-1,l=0,i=-1,c=!0,s=0;e.length>=2&&58===e.charCodeAt(1)&&n(e.charCodeAt(0))&&(t=l=2);for(let n=e.length-1;n>=t;--n){const t=e.charCodeAt(n);if(r(t)){if(!c){l=n+1;break}}else-1===i&&(c=!1,i=n+1),46===t?-1===o?o=n:1!==s&&(s=1):-1!==o&&(s=-1)}return-1===o||-1===i||0===s||1===s&&o===i-1&&o===l+1?"":e.slice(o,i)},format:i.bind(null,"\\"),parse(e){const t={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return t;const o=e.length;let l=0,i=e.charCodeAt(0);if(1===o)return r(i)?(t.root=t.dir=e,t):(t.base=t.name=e,t);if(r(i)){if(l=1,r(e.charCodeAt(1))){let t=2,n=t;for(;t<o&&!r(e.charCodeAt(t));)t++;if(t<o&&t!==n){for(n=t;t<o&&r(e.charCodeAt(t));)t++;if(t<o&&t!==n){for(n=t;t<o&&!r(e.charCodeAt(t));)t++;t===o?l=t:t!==n&&(l=t+1)}}}}else if(n(i)&&58===e.charCodeAt(1)){if(o<=2)return t.root=t.dir=e,t;if(l=2,r(e.charCodeAt(2))){if(3===o)return t.root=t.dir=e,t;l=3}}l>0&&(t.root=e.slice(0,l));let c=-1,s=l,a=-1,h=!0,f=e.length-1,d=0;for(;f>=l;--f)if(i=e.charCodeAt(f),r(i)){if(!h){s=f+1;break}}else-1===a&&(h=!1,a=f+1),46===i?-1===c?c=f:1!==d&&(d=1):-1!==c&&(d=-1);return-1!==a&&(-1===c||0===d||1===d&&c===a-1&&c===s+1?t.base=t.name=e.slice(s,a):(t.name=e.slice(s,c),t.base=e.slice(s,a),t.ext=e.slice(c,a))),t.dir=s>0&&s!==l?e.slice(0,s-1):t.root,t},sep:"\\",delimiter:";",win32:null,posix:null},s=(()=>{if(t){const t=/\\/g;return()=>{const r=e.cwd().replace(t,"/");return r.slice(r.indexOf("/"))}}return()=>e.cwd()})(),a={resolve(...e){let t="",r=!1;for(let o=e.length-1;o>=-1&&!r;o--){const n=o>=0?e[o]:s();0!==n.length&&(t=`${n}/${t}`,r=47===n.charCodeAt(0))}return t=l(t,!r,"/",o),r?`/${t}`:t.length>0?t:"."},normalize(e){if(0===e.length)return".";const t=47===e.charCodeAt(0),r=47===e.charCodeAt(e.length-1);return 0===(e=l(e,!t,"/",o)).length?t?"/":r?"./":".":(r&&(e+="/"),t?`/${e}`:e)},isAbsolute:e=>e.length>0&&47===e.charCodeAt(0),join(...e){if(0===e.length)return".";let t;for(let r=0;r<e.length;++r){const o=e[r];o.length>0&&(void 0===t?t=o:t+=`/${o}`)}return void 0===t?".":a.normalize(t)},relative(e,t){if(e===t)return"";if((e=a.resolve(e))===(t=a.resolve(t)))return"";const r=e.length,o=r-1,n=t.length-1,l=o<n?o:n;let i=-1,c=0;for(;c<l;c++){const r=e.charCodeAt(1+c);if(r!==t.charCodeAt(1+c))break;47===r&&(i=c)}if(c===l)if(n>l){if(47===t.charCodeAt(1+c))return t.slice(1+c+1);if(0===c)return t.slice(1+c)}else o>l&&(47===e.charCodeAt(1+c)?i=c:0===c&&(i=0));let s="";for(c=1+i+1;c<=r;++c)c!==r&&47!==e.charCodeAt(c)||(s+=0===s.length?"..":"/..");return`${s}${t.slice(1+i)}`},toNamespacedPath:e=>e,dirname(e){if(0===e.length)return".";const t=47===e.charCodeAt(0);let r=-1,o=!0;for(let t=e.length-1;t>=1;--t)if(47===e.charCodeAt(t)){if(!o){r=t;break}}else o=!1;return-1===r?t?"/":".":t&&1===r?"//":e.slice(0,r)},basename(e,t){let r=0,o=-1,n=!0;if(void 0!==t&&t.length>0&&t.length<=e.length){if(t===e)return"";let l=t.length-1,i=-1;for(let c=e.length-1;c>=0;--c){const s=e.charCodeAt(c);if(47===s){if(!n){r=c+1;break}}else-1===i&&(n=!1,i=c+1),l>=0&&(s===t.charCodeAt(l)?-1==--l&&(o=c):(l=-1,o=i))}return r===o?o=i:-1===o&&(o=e.length),e.slice(r,o)}for(let t=e.length-1;t>=0;--t)if(47===e.charCodeAt(t)){if(!n){r=t+1;break}}else-1===o&&(n=!1,o=t+1);return-1===o?"":e.slice(r,o)},extname(e){let t=-1,r=0,o=-1,n=!0,l=0;for(let i=e.length-1;i>=0;--i){const c=e.charCodeAt(i);if(47!==c)-1===o&&(n=!1,o=i+1),46===c?-1===t?t=i:1!==l&&(l=1):-1!==t&&(l=-1);else if(!n){r=i+1;break}}return-1===t||-1===o||0===l||1===l&&t===o-1&&t===r+1?"":e.slice(t,o)},format:i.bind(null,"/"),parse(e){const t={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return t;const r=47===e.charCodeAt(0);let o;r?(t.root="/",o=1):o=0;let n=-1,l=0,i=-1,c=!0,s=e.length-1,a=0;for(;s>=o;--s){const t=e.charCodeAt(s);if(47!==t)-1===i&&(c=!1,i=s+1),46===t?-1===n?n=s:1!==a&&(a=1):-1!==n&&(a=-1);else if(!c){l=s+1;break}}if(-1!==i){const o=0===l&&r?1:l;-1===n||0===a||1===a&&n===i-1&&n===l+1?t.base=t.name=e.slice(o,i):(t.name=e.slice(o,n),t.base=e.slice(o,i),t.ext=e.slice(n,i))}return l>0?t.dir=e.slice(0,l-1):r&&(t.dir="/"),t},sep:"/",delimiter:":",win32:null,posix:null};a.win32=c.win32=c,a.posix=c.posix=a;const h=t?c:a;export default h;const{resolve:f,normalize:d,isAbsolute:C,join:A,relative:u,toNamespacedPath:g,dirname:$,basename:m,extname:b,format:p,parse:v,sep:x,delimiter:w,win32:k,posix:L}=h;export{f as resolve,d as normalize,C as isAbsolute,A as join,u as relative,g as toNamespacedPath,$ as dirname,m as basename,b as extname,p as format,v as parse,x as sep,w as delimiter,k as win32,L as posix};
✄
export function nextTick(e,...r){Script.nextTick(e,...r)}export const title="Frida";export const browser=!1;export const platform=function(){const e=Process.platform;return"windows"===e?"win32":e}();export const pid=Process.id;export const env={FRIDA_COMPILE:"1"};export const argv=[];export const version=Frida.version;export const versions={};function e(){}export const on=e;export const addListener=e;export const once=e;export const off=e;export const removeListener=e;export const removeAllListeners=e;export const emit=e;export const prependListener=e;export const prependOnceListener=e;export const listeners=function(e){return[]};export function binding(e){throw new Error("process.binding is not supported")}export function cwd(){return"windows"===Process.platform?"C:\\":"/"}export function chdir(e){throw new Error("process.chdir is not supported")}export function umask(){return 0}export default{nextTick,title,browser:false,platform,pid,env,argv,version,versions,on,addListener,once,off,removeListener,removeAllListeners,emit,prependListener,prependOnceListener,listeners,binding,cwd,chdir,umask};
✄
import{format as r}from"util";const e=new Map;export const codes={};export function aggregateTwoErrors(r,e){if(r&&e&&r!==e){if(Array.isArray(e.errors))return e.errors.push(r),e;const t=new AggregateError([e,r],e.message);return t.code=e.code,t}return r||e}function t(t,E){return function(...o){const n=new t,a=function(t,E,o){const n=e.get(t);if("function"==typeof n)return Reflect.apply(n,o,E);(n.match(/%[dfijoOs]/g)||[]).length;return 0===E.length?n:(E.unshift(n),Reflect.apply(r,null,E))}(E,o,n);return Object.defineProperties(n,{message:{value:a,enumerable:!1,writable:!0,configurable:!0},toString:{value(){return`${this.name} [${E}]: ${this.message}`},enumerable:!1,writable:!0,configurable:!0}}),n.code=E,n}}function E(r,E,o,...n){e.set(r,E),o=t(o,r),0!==n.length&&n.forEach((e=>{o[e.name]=t(e,r)})),codes[r]=o}export class AbortError extends Error{constructor(){super("The operation was aborted"),this.code="ABORT_ERR",this.name="AbortError"}}E("ERR_EVENT_RECURSION",'The event "%s" is already being dispatched',Error),E("ERR_ILLEGAL_CONSTRUCTOR","Illegal constructor",TypeError),E("ERR_INVALID_ARG_TYPE","Invalid argument type",TypeError),E("ERR_INVALID_ARG_VALUE","Invalid argument value",TypeError,RangeError),E("ERR_INVALID_RETURN_VALUE","Invalid return value",TypeError,RangeError),E("ERR_INVALID_THIS",'Value of "this" must be of type %s',TypeError),E("ERR_METHOD_NOT_IMPLEMENTED","The %s method is not implemented",Error),E("ERR_MISSING_ARGS","Missing argument",TypeError),E("ERR_MULTIPLE_CALLBACK","Callback called multiple times",Error),E("ERR_OUT_OF_RANGE","Out of range",RangeError),E("ERR_STREAM_ALREADY_FINISHED","Cannot call %s after a stream was finished",Error),E("ERR_STREAM_CANNOT_PIPE","Cannot pipe, not readable",Error),E("ERR_STREAM_DESTROYED","Cannot call %s after a stream was destroyed",Error),E("ERR_STREAM_NULL_VALUES","May not write null values to stream",TypeError),E("ERR_STREAM_PREMATURE_CLOSE","Premature close",Error),E("ERR_STREAM_PUSH_AFTER_EOF","stream.push() after EOF",Error),E("ERR_STREAM_UNSHIFT_AFTER_END_EVENT","stream.unshift() after end event",Error),E("ERR_STREAM_WRITE_AFTER_END","write after end",Error),E("ERR_UNKNOWN_ENCODING","Unknown encoding: %s",TypeError);
✄
import{codes as t}from"../errors.js";import{defineEventHandler as r,EventTarget as o,Event as e,kTrustEvent as n}from"./event_target.js";import{inspect as i}from"util";const{ERR_ILLEGAL_CONSTRUCTOR:b,ERR_INVALID_THIS:l}=t;export const kAborted=Symbol("kAborted");function s(t,r,o,e){if(o<0)return t;const n=Object.assign({},e,{depth:null===e.depth?null:e.depth-1});return`${t.constructor.name} ${i(r,n)}`}export class AbortSignal extends o{constructor(){throw new b}get aborted(){return function(t){if(void 0===t?.[kAborted])throw new l("AbortSignal")}(this),!!this[kAborted]}[i.custom](t,r){return s(this,{aborted:this.aborted},t,r)}static abort(){return a(!0)}}function a(t=!1){const r=new o;return Object.setPrototypeOf(r,AbortSignal.prototype),r[kAborted]=t,r}Object.defineProperties(AbortSignal.prototype,{aborted:{enumerable:!0}}),Object.defineProperty(AbortSignal.prototype,Symbol.toStringTag,{writable:!1,enumerable:!1,configurable:!0,value:"AbortSignal"}),r(AbortSignal.prototype,"abort");const c=Symbol("signal");function u(t){if(void 0===t?.[c])throw new l("AbortController")}export class AbortController{constructor(){this[c]=a()}get signal(){return u(this),this[c]}abort(){u(this),function(t){if(t[kAborted])return;t[kAborted]=!0;const r=new e("abort",{[n]:!0});t.dispatchEvent(r)}(this[c])}[i.custom](t,r){return s(this,{signal:this.signal},t,r)}}Object.defineProperties(AbortController.prototype,{signal:{enumerable:!0},abort:{enumerable:!0}}),Object.defineProperty(AbortController.prototype,Symbol.toStringTag,{writable:!1,enumerable:!1,configurable:!0,value:"AbortController"});
✄
import t from"./end-of-stream.js";import{AbortError as e,codes as o}from"../errors.js";const{ERR_INVALID_ARG_TYPE:r}=o;export function addAbortSignal(t,e){if(((t,e)=>{if("object"!=typeof t||!("aborted"in t))throw new r(e,"AbortSignal",t)})(t,"signal"),!(o=e)||"function"!=typeof o.pipe)throw new r("stream","stream.Stream",e);var o;return module.exports.addAbortSignalNoValidate(t,e)}export function addAbortSignalNoValidate(o,r){if("object"!=typeof o||!("aborted"in o))return r;const n=()=>{r.destroy(new e)};return o.aborted?n():(o.addEventListener("abort",n),t(r,(()=>o.removeEventListener("abort",n)))),r}
✄
import{Buffer as t}from"buffer";import{inspect as e}from"util";export default class h{constructor(){this.head=null,this.tail=null,this.length=0}push(t){const e={data:t,next:null};this.length>0?this.tail.next=e:this.head=e,this.tail=e,++this.length}unshift(t){const e={data:t,next:this.head};0===this.length&&(this.tail=e),this.head=e,++this.length}shift(){if(0===this.length)return;const t=this.head.data;return 1===this.length?this.head=this.tail=null:this.head=this.head.next,--this.length,t}clear(){this.head=this.tail=null,this.length=0}join(t){if(0===this.length)return"";let e=this.head,h=""+e.data;for(;e=e.next;)h+=t+e.data;return h}concat(e){if(0===this.length)return t.alloc(0);const h=t.allocUnsafe(e>>>0);let i=this.head,s=0;for(;i;)h.set(i.data,s),s+=i.data.length,i=i.next;return h}consume(t,e){const h=this.head.data;if(t<h.length){const e=h.slice(0,t);return this.head.data=h.slice(t),e}return t===h.length?this.shift():e?this._getString(t):this._getBuffer(t)}first(){return this.head.data}*[Symbol.iterator](){for(let t=this.head;t;t=t.next)yield t.data}_getString(t){let e="",h=this.head,i=0;do{const s=h.data;if(!(t>s.length)){t===s.length?(e+=s,++i,h.next?this.head=h.next:this.head=this.tail=null):(e+=s.slice(0,t),this.head=h,h.data=s.slice(t));break}e+=s,t-=s.length,++i}while(h=h.next);return this.length-=i,e}_getBuffer(e){const h=t.allocUnsafe(e),i=e;let s=this.head,n=0;do{const t=s.data;if(!(e>t.length)){e===t.length?(h.set(t,i-e),++n,s.next?this.head=s.next:this.head=this.tail=null):(h.set(new Uint8Array(t.buffer,t.byteOffset,e),i-e),this.head=s,s.data=t.slice(e));break}h.set(t,i-e),e-=t.length,++n}while(s=s.next);return this.length-=n,h}[e.custom](t,h){return e(this,{...h,depth:0,customInspect:!1})}}
✄
import{destroyer as t}from"./destroy.js";import e from"./duplex.js";import{AbortError as n,codes as r}from"../errors.js";import{pipeline as i}from"./pipeline.js";import{isNodeStream as o,isReadable as l,isWritable as a}from"./utils.js";const{ERR_INVALID_ARG_VALUE:s,ERR_MISSING_ARGS:f}=r;class d extends e{constructor(t){super(t),!1===t?.readable&&(this._readableState.readable=!1,this._readableState.ended=!0,this._readableState.endEmitted=!0),!1===t?.writable&&(this._writableState.writable=!1,this._writableState.ending=!0,this._writableState.ended=!0,this._writableState.finished=!0)}}export default function u(...r){if(0===r.length)throw new f("streams");if(1===r.length)return e.from(r[0]);const u=[...r];if("function"==typeof r[0]&&(r[0]=e.from(r[0])),"function"==typeof r[r.length-1]){const t=r.length-1;r[t]=e.from(r[t])}for(let t=0;t<r.length;++t)if(o(r[t])){if(t<r.length-1&&!l(r[t]))throw new s(`streams[${t}]`,u[t],"must be readable");if(t>0&&!a(r[t]))throw new s(`streams[${t}]`,u[t],"must be writable")}let c,b,w,h,m;const _=r[0],p=i(r,(function(t){const e=h;h=null,e?e(t):t?m.destroy(t):j||S||m.destroy()})),S=!!a(_),j=!!l(p);return m=new d({writableObjectMode:!!_?.writableObjectMode,readableObjectMode:!!p?.writableObjectMode,writable:S,readable:j}),S&&(m._write=function(t,e,n){_.write(t,e)?n():c=n},m._final=function(t){_.end(),b=t},_.on("drain",(function(){if(c){const t=c;c=null,t()}})),p.on("finish",(function(){if(b){const t=b;b=null,t()}}))),j&&(p.on("readable",(function(){if(w){const t=w;w=null,t()}})),p.on("end",(function(){m.push(null)})),m._read=function(){for(;;){const t=p.read();if(null===t)return void(w=m._read);if(!m.push(t))return}}),m._destroy=function(e,r){e||null===h||(e=new n),w=null,c=null,b=null,null===h?r(e):(h=r,t(p,e))},m}
✄
import{aggregateTwoErrors as t,codes as e,AbortError as r}from"../errors.js";import{kDestroyed as o,isDestroyed as n,isFinished as c,isServerRequest as i}from"./utils.js";import s from"process";const{ERR_MULTIPLE_CALLBACK:d}=e,l=Symbol("kDestroy"),a=Symbol("kConstruct");function u(t,e,r){t&&(t.stack,e&&!e.errored&&(e.errored=t),r&&!r.errored&&(r.errored=t))}export function destroy(e,r){const o=this._readableState,n=this._writableState,c=n||o;return n&&n.destroyed||o&&o.destroyed?("function"==typeof r&&r(),this):(u(e,n,o),n&&(n.destroyed=!0),o&&(o.destroyed=!0),c.constructed?f(this,e,r):this.once(l,(function(o){f(this,t(o,e),r)})),this)}function f(t,e,r){let o=!1;function n(e){if(o)return;o=!0;const n=t._readableState,c=t._writableState;u(e,c,n),c&&(c.closed=!0),n&&(n.closed=!0),"function"==typeof r&&r(e),e?s.nextTick(y,t,e):s.nextTick(m,t)}try{const r=t._destroy(e||null,n);if(null!=r){const t=r.then;"function"==typeof t&&t.call(r,(function(){s.nextTick(n,null)}),(function(t){s.nextTick(n,t)}))}}catch(e){n(e)}}function y(t,e){b(t,e),m(t)}function m(t){const e=t._readableState,r=t._writableState;r&&(r.closeEmitted=!0),e&&(e.closeEmitted=!0),(r&&r.emitClose||e&&e.emitClose)&&t.emit("close")}function b(t,e){const r=t._readableState,o=t._writableState;o&&o.errorEmitted||r&&r.errorEmitted||(o&&(o.errorEmitted=!0),r&&(r.errorEmitted=!0),t.emit("error",e))}export function undestroy(){const t=this._readableState,e=this._writableState;t&&(t.constructed=!0,t.closed=!1,t.closeEmitted=!1,t.destroyed=!1,t.errored=null,t.errorEmitted=!1,t.reading=!1,t.ended=!1===t.readable,t.endEmitted=!1===t.readable),e&&(e.constructed=!0,e.destroyed=!1,e.closed=!1,e.closeEmitted=!1,e.errored=null,e.errorEmitted=!1,e.finalCalled=!1,e.prefinished=!1,e.ended=!1===e.writable,e.ending=!1===e.writable,e.finished=!1===e.writable)}export function errorOrDestroy(t,e,r){const o=t._readableState,n=t._writableState;if(n&&n.destroyed||o&&o.destroyed)return this;o&&o.autoDestroy||n&&n.autoDestroy?t.destroy(e):e&&(e.stack,n&&!n.errored&&(n.errored=e),o&&!o.errored&&(o.errored=e),r?s.nextTick(b,t,e):b(t,e))}export function construct(t,e){if("function"!=typeof t._construct)return;const r=t._readableState,o=t._writableState;r&&(r.constructed=!1),o&&(o.constructed=!1),t.once(a,e),t.listenerCount(a)>1||s.nextTick(_,t)}function _(t){let e=!1;function r(r){if(e)return void errorOrDestroy(t,r??new d);e=!0;const o=t._readableState,n=t._writableState,c=n||o;o&&(o.constructed=!0),n&&(n.constructed=!0),c.destroyed?t.emit(l,r):r?errorOrDestroy(t,r,!0):s.nextTick(p,t)}try{const e=t._construct(r);if(null!=e){const t=e.then;"function"==typeof t&&t.call(e,(function(){s.nextTick(r,null)}),(function(t){s.nextTick(r,t)}))}}catch(t){r(t)}}function p(t){t.emit(a)}function S(t){return t&&t.setHeader&&"function"==typeof t.abort}function k(t){t.emit("close")}function x(t,e){t.emit("error",e),s.nextTick(k,t)}export function destroyer(t,e){t&&!n(t)&&(e||c(t)||(e=new r),i(t)?(t.socket=null,t.destroy(e)):S(t)?t.abort():S(t.req)?t.req.abort():"function"==typeof t.destroy?t.destroy(e):"function"==typeof t.close?t.close():e?s.nextTick(x,t):s.nextTick(k,t),t.destroyed||(t[o]=!0))}
✄
import{AbortController as e}from"./abort_controller.js";import{destroyer as t}from"./destroy.js";import r from"./end-of-stream.js";import{AbortError as a,codes as o}from"../errors.js";import i from"./from.js";import n from"./readable.js";import{isReadable as l,isWritable as b,isIterable as d,isNodeStream as s,isReadableNodeStream as c,isWritableNodeStream as w,isDuplexNodeStream as p}from"./utils.js";import f from"./writable.js";import u from"process";const{ERR_INVALID_ARG_TYPE:y,ERR_INVALID_RETURN_VALUE:h}=o;Object.setPrototypeOf(j.prototype,n.prototype),Object.setPrototypeOf(j,n);for(const g of Object.keys(f.prototype))j.prototype[g]||(j.prototype[g]=f.prototype[g]);export default function j(e){if(!(this instanceof j))return new j(e);n.call(this,e),f.call(this,e),e?(this.allowHalfOpen=!1!==e.allowHalfOpen,!1===e.readable&&(this._readableState.readable=!1,this._readableState.ended=!0,this._readableState.endEmitted=!0),!1===e.writable&&(this._writableState.writable=!1,this._writableState.ending=!0,this._writableState.ended=!0,this._writableState.finished=!0)):this.allowHalfOpen=!0}Object.defineProperties(j.prototype,{writable:Object.getOwnPropertyDescriptor(f.prototype,"writable"),writableHighWaterMark:Object.getOwnPropertyDescriptor(f.prototype,"writableHighWaterMark"),writableObjectMode:Object.getOwnPropertyDescriptor(f.prototype,"writableObjectMode"),writableBuffer:Object.getOwnPropertyDescriptor(f.prototype,"writableBuffer"),writableLength:Object.getOwnPropertyDescriptor(f.prototype,"writableLength"),writableFinished:Object.getOwnPropertyDescriptor(f.prototype,"writableFinished"),writableCorked:Object.getOwnPropertyDescriptor(f.prototype,"writableCorked"),writableEnded:Object.getOwnPropertyDescriptor(f.prototype,"writableEnded"),writableNeedDrain:Object.getOwnPropertyDescriptor(f.prototype,"writableNeedDrain"),destroyed:{get(){return void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed&&this._writableState.destroyed)},set(e){this._readableState&&this._writableState&&(this._readableState.destroyed=e,this._writableState.destroyed=e)}}}),j.from=function(e){return O(e,"body")};class _ extends j{constructor(e){super(e),!1===e?.readable&&(this._readableState.readable=!1,this._readableState.ended=!0,this._readableState.endEmitted=!0),!1===e?.writable&&(this._writableState.writable=!1,this._writableState.ending=!0,this._writableState.ended=!0,this._writableState.finished=!0)}}function O(r,o){if(p(r))return r;if(c(r))return m({readable:r});if(w(r))return m({writable:r});if(s(r))return m({writable:!1,readable:!1});if("function"==typeof r){const{value:n,write:l,final:b,destroy:s}=function(t){let{promise:r,resolve:o}=S();const i=new e,n=i.signal;return{value:t(async function*(){for(;;){const{chunk:e,done:t,cb:i}=await r;if(u.nextTick(i),t)return;if(n.aborted)throw new a;yield e,({promise:r,resolve:o}=S())}}(),{signal:n}),write(e,t,r){o({chunk:e,done:!1,cb:r})},final(e){o({done:!0,cb:e})},destroy(e,t){i.abort(),t(e)}}}(r);if(d(n))return i(_,n,{objectMode:!0,write:l,final:b,destroy:s});const c=n?.then;if("function"==typeof c){let e;const r=c.call(n,(e=>{if(null!=e)throw new h("nully","body",e)}),(r=>{t(e,r)}));return e=new _({objectMode:!0,readable:!1,write:l,final(e){b((async()=>{try{await r,u.nextTick(e,null)}catch(t){u.nextTick(e,t)}}))},destroy:s})}throw new h("Iterable, AsyncIterable or AsyncFunction",o,n)}if(d(r))return i(_,r,{objectMode:!0,writable:!1});if("object"==typeof r?.writable||"object"==typeof r?.readable){return m({readable:r?.readable?c(r?.readable)?r?.readable:O(r.readable):void 0,writable:r?.writable?w(r?.writable)?r?.writable:O(r.writable):void 0})}const n=r?.then;if("function"==typeof n){let e;return n.call(r,(t=>{null!=t&&e.push(t),e.push(null)}),(r=>{t(e,r)})),e=new _({objectMode:!0,writable:!1,read(){}})}throw new y(o,["Blob","ReadableStream","WritableStream","Stream","Iterable","AsyncIterable","Function","{ readable, writable } pair","Promise"],r)}function m(e){const o=e.readable&&"function"!=typeof e.readable.read?n.wrap(e.readable):e.readable,i=e.writable;let d,s,c,w,p,f=!!l(o),u=!!b(i);function y(e){const t=w;w=null,t?t(e):e?p.destroy(e):f||u||p.destroy()}return p=new _({readableObjectMode:!!o?.readableObjectMode,writableObjectMode:!!i?.writableObjectMode,readable:f,writable:u}),u&&(r(i,(e=>{u=!1,e&&t(o,e),y(e)})),p._write=function(e,t,r){i.write(e,t)?r():d=r},p._final=function(e){i.end(),s=e},i.on("drain",(function(){if(d){const e=d;d=null,e()}})),i.on("finish",(function(){if(s){const e=s;s=null,e()}}))),f&&(r(o,(e=>{f=!1,e&&t(o,e),y(e)})),o.on("readable",(function(){if(c){const e=c;c=null,e()}})),o.on("end",(function(){p.push(null)})),p._read=function(){for(;;){const e=o.read();if(null===e)return void(c=p._read);if(!p.push(e))return}}),p._destroy=function(e,r){e||null===w||(e=new a),c=null,d=null,s=null,null===w?r(e):(w=r,t(i,e),t(o,e))},p}function S(){let e,t;return{promise:new Promise(((r,a)=>{e=r,t=a})),resolve:e,reject:t}}
✄
import{AbortError as e,codes as r}from"../errors.js";import o from"./once.js";import{isClosed as t,isReadable as n,isReadableNodeStream as s,isReadableFinished as i,isWritable as l,isWritableNodeStream as a,isWritableFinished as c,isNodeStream as d,willEmitClose as m}from"./utils.js";import b from"process";const{ERR_STREAM_PREMATURE_CLOSE:f}=r;function v(e){return e.setHeader&&"function"==typeof e.abort}const L=()=>{};export default function p(r,p,u){2===arguments.length?(u=p,p={}):null==p&&(p={}),u=o(u);const E=p.readable||!1!==p.readable&&s(r),w=p.writable||!1!==p.writable&&a(r);d(r);const q=r._writableState,T=r._readableState,x=()=>{r.writable||h()};let y=m(r)&&s(r)===E&&a(r)===w,g=c(r,!1);const h=()=>{g=!0,r.destroyed&&(y=!1),(!y||r.readable&&!E)&&(E&&!k||u.call(r))};let k=i(r,!1);const R=()=>{k=!0,r.destroyed&&(y=!1),(!y||r.writable&&!w)&&(w&&!g||u.call(r))},_=e=>{u.call(r,e)};let S=t(r);const j=()=>{S=!0;const e=q?.errored||T?.errored;return e&&"boolean"!=typeof e?u.call(r,e):(!E||k||i(r,!1))&&(!w||g||c(r,!1))?void u.call(r):u.call(r,new f)},A=()=>{r.req.on("finish",h)};v(r)?(r.on("complete",h),y||r.on("abort",j),r.req?A():r.on("request",A)):w&&!q&&(r.on("end",x),r.on("close",x)),y||"boolean"!=typeof r.aborted||r.on("aborted",j),r.on("end",R),r.on("finish",h),!1!==p.error&&r.on("error",_),r.on("close",j),S?b.nextTick(j):q?.errorEmitted||T?.errorEmitted?y||b.nextTick(j):(E||y&&!n(r)||!g&&l(r))&&(w||y&&!l(r)||!k&&n(r))?T&&r.req&&r.aborted&&b.nextTick(j):b.nextTick(j);const C=()=>{u=L,r.removeListener("aborted",j),r.removeListener("complete",h),r.removeListener("abort",j),r.removeListener("request",A),r.req&&r.req.removeListener("finish",h),r.removeListener("end",x),r.removeListener("close",x),r.removeListener("finish",h),r.removeListener("end",R),r.removeListener("error",_),r.removeListener("close",j)};if(p.signal&&!S){const t=()=>{const o=u;C(),o.call(r,new e)};if(p.signal.aborted)b.nextTick(t);else{const e=u;u=o(((...o)=>{p.signal.removeEventListener("abort",t),e.apply(r,o)})),p.signal.addEventListener("abort",t)}}return C}
✄
import{codes as e}from"../errors.js";import t from"events";import n from"process";import{inspect as i}from"util";const{ERR_INVALID_ARG_TYPE:r,ERR_EVENT_RECURSION:s,ERR_MISSING_ARGS:o,ERR_INVALID_THIS:a}=e,h=Symbol.for("nodejs.event_target"),l=Symbol("kIsNodeEventTarget"),{kMaxEventTargetListeners:c,kMaxEventTargetListenersWarned:v}=t;export const kEvents=Symbol("kEvents");const d=Symbol("kIsBeingDispatched"),u=Symbol("kStop"),f=Symbol("kTarget"),p=Symbol("khandlers");export const kWeakHandler=Symbol("kWeak");const E=Symbol.for("nodejs.internal.kHybridDispatch");export const kCreateEvent=Symbol("kCreateEvent");export const kNewListener=Symbol("kNewListener");export const kRemoveListener=Symbol("kRemoveListener");const g=Symbol("kIsNodeStyleListener");export const kTrustEvent=Symbol("kTrustEvent");const w=Symbol("type"),m=Symbol("defaultPrevented"),b=Symbol("cancelable"),k=Symbol("timestamp"),y=Symbol("bubbles"),T=Symbol("composed"),L=Symbol("propagationStopped"),S=new WeakSet,x=Object.getOwnPropertyDescriptor({get isTrusted(){return S.has(this)}},"isTrusted").get;function N(e){return"string"==typeof e?.[w]}export class Event{constructor(e,t=null){if(0===arguments.length)throw new o("type");const{cancelable:n,bubbles:i,composed:r}={...t};this[b]=!!n,this[y]=!!i,this[T]=!!r,this[w]=`${e}`,this[m]=!1,this[k]=Date.now(),this[L]=!1,t?.[kTrustEvent]&&S.add(this),Object.defineProperty(this,"isTrusted",{get:x,enumerable:!0,configurable:!1}),this[f]=null,this[d]=!1}[i.custom](e,t){if(!N(this))throw new a("Event");const n=this.constructor.name;if(e<0)return n;const r=Object.assign({},t,{depth:Number.isInteger(t.depth)?t.depth-1:t.depth});return`${n} ${i({type:this[w],defaultPrevented:this[m],cancelable:this[b],timeStamp:this[k]},r)}`}stopImmediatePropagation(){if(!N(this))throw new a("Event");this[u]=!0}preventDefault(){if(!N(this))throw new a("Event");this[m]=!0}get target(){if(!N(this))throw new a("Event");return this[f]}get currentTarget(){if(!N(this))throw new a("Event");return this[f]}get srcElement(){if(!N(this))throw new a("Event");return this[f]}get type(){if(!N(this))throw new a("Event");return this[w]}get cancelable(){if(!N(this))throw new a("Event");return this[b]}get defaultPrevented(){if(!N(this))throw new a("Event");return this[b]&&this[m]}get timeStamp(){if(!N(this))throw new a("Event");return this[k]}composedPath(){if(!N(this))throw new a("Event");return this[d]?[this[f]]:[]}get returnValue(){if(!N(this))throw new a("Event");return!this.defaultPrevented}get bubbles(){if(!N(this))throw new a("Event");return this[y]}get composed(){if(!N(this))throw new a("Event");return this[T]}get eventPhase(){if(!N(this))throw new a("Event");return this[d]?Event.AT_TARGET:Event.NONE}get cancelBubble(){if(!N(this))throw new a("Event");return this[L]}set cancelBubble(e){if(!N(this))throw new a("Event");e&&this.stopPropagation()}stopPropagation(){if(!N(this))throw new a("Event");this[L]=!0}static NONE=0;static CAPTURING_PHASE=1;static AT_TARGET=2;static BUBBLING_PHASE=3}const P=Object.create(null);P.enumerable=!0,Object.defineProperties(Event.prototype,{[Symbol.toStringTag]:{writable:!1,enumerable:!1,configurable:!0,value:"Event"},stopImmediatePropagation:P,preventDefault:P,target:P,currentTarget:P,srcElement:P,type:P,cancelable:P,defaultPrevented:P,timeStamp:P,composedPath:P,returnValue:P,bubbles:P,composed:P,eventPhase:P,cancelBubble:P,stopPropagation:P});class R extends Event{constructor(e,t){super(e,t),t?.detail&&(this.detail=t.detail)}}let I=null,A=null;function M(){return null===I&&(I=new FinalizationRegistry((e=>e.remove()))),null===A&&(A=new WeakMap),{registry:I,map:A}}class O{constructor(e,t,n,i,r,s,o){this.next=void 0,void 0!==e&&(e.next=this),this.previous=e,this.listener=t,this.once=n,this.capture=i,this.passive=r,this.isNodeStyleListener=s,this.removed=!1,this.weak=Boolean(o),this.weak?(this.callback=new WeakRef(t),M().registry.register(t,this,this),M().map.set(o,t),this.listener=this.callback):"function"==typeof t?(this.callback=t,this.listener=t):(this.callback=t.handleEvent.bind(t),this.listener=t)}same(e,t){return(this.weak?this.listener.deref():this.listener)===e&&this.capture===t}remove(){void 0!==this.previous&&(this.previous.next=this.next),void 0!==this.next&&(this.next.previous=this.previous),this.removed=!0,this.weak&&M().registry.unregister(this)}}export function initEventTarget(e){e[kEvents]=new Map,e[c]=t.defaultMaxListeners,e[v]=!1}export class EventTarget{static[h]=!0;constructor(){initEventTarget(this)}[kNewListener](e,t,r,s,o,a){if(this[c]>0&&e>this[c]&&!this[v]){this[v]=!0;const r=new Error(`Possible EventTarget memory leak detected. ${e} ${t} listeners added to ${i(this,{depth:-1})}. Use events.setMaxListeners() to increase limit`);r.name="MaxListenersExceededWarning",r.target=this,r.type=t,r.count=e,n.emitWarning(r)}}[kRemoveListener](e,t,n,i){}addEventListener(e,t,i={}){if(!isEventTarget(this))throw new a("EventTarget");if(arguments.length<2)throw new o("type","listener");const{once:r,capture:s,passive:h,signal:l,isNodeStyleListener:c,weak:v}=function(e){return"boolean"==typeof e?{capture:e}:null===e?{}:{once:Boolean(e.once),capture:Boolean(e.capture),passive:Boolean(e.passive),signal:e.signal,weak:e[kWeakHandler],isNodeStyleListener:Boolean(e[g])}}(i);if(!z(t)){const i=new Error(`addEventListener called with ${t} which has no effect.`);return i.name="AddEventListenerArgumentTypeWarning",i.target=this,i.type=e,void n.emitWarning(i)}if(e=String(e),l){if(l.aborted)return;l.addEventListener("abort",(()=>{this.removeEventListener(e,t,i)}),{once:!0,[kWeakHandler]:this})}let d=this[kEvents].get(e);if(void 0===d)return d={size:1,next:void 0},new O(d,t,r,s,h,c,v),this[kNewListener](d.size,e,t,r,s,h),void this[kEvents].set(e,d);let u=d.next,f=d;for(;void 0!==u&&!u.same(t,s);)f=u,u=u.next;void 0===u&&(new O(f,t,r,s,h,c,v),d.size++,this[kNewListener](d.size,e,t,r,s,h))}removeEventListener(e,t,n={}){if(!isEventTarget(this))throw new a("EventTarget");if(!z(t))return;e=String(e);const i=!0===n?.capture,r=this[kEvents].get(e);if(void 0===r||void 0===r.next)return;let s=r.next;for(;void 0!==s;){if(s.same(t,i)){s.remove(),r.size--,0===r.size&&this[kEvents].delete(e),this[kRemoveListener](r.size,e,t,i);break}s=s.next}}dispatchEvent(e){if(!isEventTarget(this))throw new a("EventTarget");if(!(e instanceof Event))throw new r("event","Event",e);if(e[d])throw new s(e.type);return this[E](e,e.type,e),!0!==e.defaultPrevented}[E](e,t,n){const i=()=>(void 0===n&&((n=this[kCreateEvent](e,t))[f]=this,n[d]=!0),n);void 0!==n&&(n[f]=this,n[d]=!0);const r=this[kEvents].get(t);if(void 0===r||void 0===r.next)return void 0!==n&&(n[d]=!1),!0;let s,o=r.next;for(;void 0!==o&&(o.passive||!0!==n?.[u]);)if(s=o.next,o.removed)o=s;else{if(o.once){o.remove(),r.size--;const{listener:e,capture:n}=o;this[kRemoveListener](r.size,t,e,n)}try{let t;t=o.isNodeStyleListener?e:i();const n=o.weak?o.callback.deref():o.callback;let r;n&&(r=n.call(this,t),o.isNodeStyleListener||(t[d]=!1)),null!=r&&_(r)}catch(e){B(e)}o=s}void 0!==n&&(n[d]=!1)}[kCreateEvent](e,t){return new R(t,{detail:e})}[i.custom](e,t){if(!isEventTarget(this))throw new a("EventTarget");const n=this.constructor.name;if(e<0)return n;const r=Object.assign({},t,{depth:Number.isInteger(t.depth)?t.depth-1:t.depth});return`${n} ${i({},r)}`}}Object.defineProperties(EventTarget.prototype,{addEventListener:P,removeEventListener:P,dispatchEvent:P,[Symbol.toStringTag]:{writable:!1,enumerable:!1,configurable:!0,value:"EventTarget"}});export function initNodeEventTarget(e){initEventTarget(e)}export class NodeEventTarget extends EventTarget{static[l]=!0;static defaultMaxListeners=10;constructor(){super(),initNodeEventTarget(this)}setMaxListeners(e){if(!j(this))throw new a("NodeEventTarget");t.setMaxListeners(e,this)}getMaxListeners(){if(!j(this))throw new a("NodeEventTarget");return this[c]}eventNames(){if(!j(this))throw new a("NodeEventTarget");return Array.from(this[kEvents].keys())}listenerCount(e){if(!j(this))throw new a("NodeEventTarget");const t=this[kEvents].get(String(e));return void 0!==t?t.size:0}off(e,t,n){if(!j(this))throw new a("NodeEventTarget");return this.removeEventListener(e,t,n),this}removeListener(e,t,n){if(!j(this))throw new a("NodeEventTarget");return this.removeEventListener(e,t,n),this}on(e,t){if(!j(this))throw new a("NodeEventTarget");return this.addEventListener(e,t,{[g]:!0}),this}addListener(e,t){if(!j(this))throw new a("NodeEventTarget");return this.addEventListener(e,t,{[g]:!0}),this}emit(e,t){if(!j(this))throw new a("NodeEventTarget");const n=this.listenerCount(e)>0;return this[E](t,e),n}once(e,t){if(!j(this))throw new a("NodeEventTarget");return this.addEventListener(e,t,{once:!0,[g]:!0}),this}removeAllListeners(e){if(!j(this))throw new a("NodeEventTarget");return void 0!==e?this[kEvents].delete(String(e)):this[kEvents].clear(),this}}function z(e){if("function"==typeof e||"function"==typeof e?.handleEvent)return!0;if(null==e)return!1;throw new r("listener","EventListener",e)}Object.defineProperties(NodeEventTarget.prototype,{setMaxListeners:P,getMaxListeners:P,eventNames:P,listenerCount:P,off:P,removeListener:P,on:P,addListener:P,once:P,emit:P,removeAllListeners:P});export function isEventTarget(e){return e?.constructor?.[h]}function j(e){return e?.constructor?.[l]}function _(e){const t=e.then;"function"==typeof t&&t.call(e,void 0,(function(e){B(e)}))}function B(e){n.nextTick((()=>{throw e}))}export function defineEventHandler(e,t){Object.defineProperty(e,`on${t}`,{get(){return this[p]?.get(t)?.handler},set(e){this[p]||(this[p]=new Map);let n=this[p]?.get(t);if(n){if("function"==typeof n.handler){this[kEvents].get(t).size--;const e=this[kEvents].get(t).size;this[kRemoveListener](e,t,n.handler,!1)}if(n.handler=e,"function"==typeof n.handler){this[kEvents].get(t).size++;const n=this[kEvents].get(t).size;this[kNewListener](n,t,e,!1,!1,!1)}}else n=function(e){function t(...e){if("function"==typeof t.handler)return Reflect.apply(t.handler,this,e)}return t.handler=e,t}(e),this.addEventListener(t,n);this[p].set(t,n)},configurable:!0,enumerable:!0})}export const EventEmitterMixin=e=>{class n extends e{constructor(...e){super(...e),t.call(this)}}const i=Object.getOwnPropertyDescriptors(t.prototype);return delete i.constructor,Object.defineProperties(n.prototype,i),n};
✄
import{codes as t}from"../errors.js";import{Buffer as e}from"buffer";import n from"process";const{ERR_INVALID_ARG_TYPE:o,ERR_STREAM_NULL_VALUES:r}=t;export default function i(t,i,a){let c,f;if("string"==typeof i||i instanceof e)return new t({objectMode:!0,...a,read(){this.push(i),this.push(null)}});if(i&&i[Symbol.asyncIterator])f=!0,c=i[Symbol.asyncIterator]();else{if(!i||!i[Symbol.iterator])throw new o("iterable",["Iterable"],i);f=!1,c=i[Symbol.iterator]()}const s=new t({objectMode:!0,highWaterMark:1,...a});let u=!1;return s._read=function(){u||(u=!0,async function(){for(;;){try{const{value:t,done:e}=f?await c.next():c.next();if(e)s.push(null);else{const e=t&&"function"==typeof t.then?await t:t;if(null===e)throw u=!1,new r;if(s.push(e))continue;u=!1}}catch(t){s.destroy(t)}break}}())},s._destroy=function(t,e){(async function(t){const e=null!=t,n="function"==typeof c.throw;if(e&&n){const{value:e,done:n}=await c.throw(t);if(await e,n)return}if("function"==typeof c.return){const{value:t}=await c.return();await t}})(t).then((()=>n.nextTick(e,t)),(o=>n.nextTick(e,o||t)))},s}
✄
import e from"events";export function Stream(r){e.call(this,r)}Object.setPrototypeOf(Stream.prototype,e.prototype),Object.setPrototypeOf(Stream,e),Stream.prototype.pipe=function(r,t){const n=this;function o(e){r.writable&&!1===r.write(e)&&n.pause&&n.pause()}function i(){n.readable&&n.resume&&n.resume()}n.on("data",o),r.on("drain",i),r._isStdio||t&&!1===t.end||(n.on("end",p),n.on("close",c));let s=!1;function p(){s||(s=!0,r.end())}function c(){s||(s=!0,"function"==typeof r.destroy&&r.destroy())}function d(r){m(),0===e.listenerCount(this,"error")&&this.emit("error",r)}function m(){n.removeListener("data",o),r.removeListener("drain",i),n.removeListener("end",p),n.removeListener("close",c),n.removeListener("error",d),r.removeListener("error",d),n.removeListener("end",m),n.removeListener("close",m),r.removeListener("close",m)}return prependListener(n,"error",d),prependListener(r,"error",d),n.on("end",m),n.on("close",m),r.on("close",m),r.emit("pipe",n),r};export function prependListener(e,r,t){if("function"==typeof e.prependListener)return e.prependListener(r,t);e._events&&e._events[r]?Array.isArray(e._events[r])?e._events[r].unshift(t):e._events[r]=[t,e._events[r]]:e.on(r,t)}
✄
export default function t(t){let e=!1;return function(...n){e||(e=!0,Reflect.apply(t,this,n))}}
✄
import t from"./transform.js";Object.setPrototypeOf(o.prototype,t.prototype),Object.setPrototypeOf(o,t);export default function o(e){if(!(this instanceof o))return new o(e);t.call(this,e)}o.prototype._transform=function(t,o,e){e(null,t)};
✄
import{AbortController as e}from"./abort_controller.js";import*as t from"./destroy.js";import r from"./duplex.js";import o from"./end-of-stream.js";import{aggregateTwoErrors as n,codes as i,AbortError as s}from"../errors.js";import a from"./once.js";import l from"./passthrough.js";import f from"./readable.js";import{isIterable as c,isReadableNodeStream as p,isNodeStream as d}from"./utils.js";import m from"process";const{ERR_INVALID_ARG_TYPE:u,ERR_INVALID_RETURN_VALUE:R,ERR_MISSING_ARGS:E,ERR_STREAM_DESTROYED:w}=i;function b(e,r,n,i){i=a(i);let s=!1;return e.on("close",(()=>{s=!0})),o(e,{readable:r,writable:n},(t=>{s=!t;const o=e._readableState;t&&"ERR_STREAM_PREMATURE_CLOSE"===t.code&&r&&o&&o.ended&&!o.errored&&!o.errorEmitted?e.once("end",i).once("error",i):i(t)})),r=>{s||(s=!0,t.destroyer(e,r),i(r||new w("pipe")))}}function y(e){if(c(e))return e;if(p(e))return async function*(e){yield*f.prototype[Symbol.asyncIterator].call(e)}(e);throw new u("val",["Readable","Iterable","AsyncIterable"],e)}async function _(e,t,r){let i,s=null;const a=e=>{if(e&&(i=e),s){const e=s;s=null,e()}},l=()=>new Promise(((e,t)=>{i?t(i):s=()=>{i?t(i):e()}}));t.on("drain",a);const f=o(t,{readable:!1},a);try{t.writableNeedDrain&&await l();for await(const r of e)t.write(r)||await l();t.end(),await l(),r()}catch(e){r(i!==e?n(i,e):e)}finally{f(),t.off("drain",a)}}export default pipeline;export function pipeline(...e){const t=a(function(e){return e.pop()}(e));return Array.isArray(e[0])&&1===e.length&&(e=e[0]),pipelineImpl(e,t)}export function pipelineImpl(t,o,n){if(t.length<2)throw new E("streams");const i=new e,a=i.signal,f=n?.signal;function u(){j(new s)}let w,h;f?.addEventListener("abort",u);const A=[];let I,S=0;function g(e){j(e,0==--S)}function j(e,t){if(!e||w&&"ERR_STREAM_PREMATURE_CLOSE"!==w.code||(w=e),w||t){for(;A.length;)A.shift()(w);f?.removeEventListener("abort",u),i.abort(),t&&o(w,h)}}for(let e=0;e<t.length;e++){const o=t[e],n=e<t.length-1,i=e>0;if(d(o)&&(S++,A.push(b(o,n,i,g))),0===e)if("function"==typeof o){if(I=o({signal:a}),!c(I))throw new R("Iterable, AsyncIterable or Stream","source",I)}else I=c(o)||p(o)?o:r.from(o);else if("function"==typeof o)if(I=y(I),I=o(I,{signal:a}),n){if(!c(I,!0))throw new R("AsyncIterable",`transform[${e-1}]`,I)}else{const e=new l({objectMode:!0}),t=I?.then;if("function"==typeof t)t.call(I,(t=>{h=t,e.end(t)}),(t=>{e.destroy(t)}));else{if(!c(I,!0))throw new R("AsyncIterable or Promise","destination",I);S++,_(I,e,g)}I=e,S++,A.push(b(I,!1,!0,g))}else d(o)?(p(I)?(I.pipe(o),o!==m.stdout&&o!==m.stderr||I.on("end",(()=>o.end()))):(I=y(I),S++,_(I,o,g)),I=o):I=r.from(o)}return(a?.aborted||f?.aborted)&&m.nextTick(u),I}
✄
import e from"./end-of-stream.js";import{pipelineImpl as i}from"./pipeline.js";import{isIterable as n,isNodeStream as o}from"./utils.js";export function pipeline(...e){return new Promise(((t,p)=>{let r;const s=e[e.length-1];if(s&&"object"==typeof s&&!o(s)&&!n(s)){r=e.pop().signal}i(e,((e,i)=>{e?p(e):t(i)}),{signal:r})}))}export function finished(i,n){return new Promise(((o,t)=>{e(i,n,(e=>{e?t(e):o()}))}))}
✄
import{addAbortSignal as e}from"./add-abort-signal.js";import t from"./buffer_list.js";import*as r from"./destroy.js";import n from"./end-of-stream.js";import{aggregateTwoErrors as a,codes as i}from"../errors.js";import o from"./from.js";import{Stream as d,prependListener as s}from"./legacy.js";import{getHighWaterMark as l,getDefaultHighWaterMark as u}from"./state.js";import{Buffer as h}from"buffer";import c from"events";import f from"process";import{StringDecoder as b}from"string_decoder";export default Readable;const{ERR_INVALID_ARG_TYPE:p,ERR_METHOD_NOT_IMPLEMENTED:g,ERR_OUT_OF_RANGE:m,ERR_STREAM_PUSH_AFTER_EOF:y,ERR_STREAM_UNSHIFT_AFTER_END_EVENT:_}=i,w=Symbol("kPaused");Object.setPrototypeOf(Readable.prototype,d.prototype),Object.setPrototypeOf(Readable,d);const R=()=>{},{errorOrDestroy:S}=r;export function ReadableState(e,r,n){"boolean"!=typeof n&&(n=r instanceof d.Duplex),this.objectMode=!(!e||!e.objectMode),n&&(this.objectMode=this.objectMode||!(!e||!e.readableObjectMode)),this.highWaterMark=e?l(this,e,"readableHighWaterMark",n):u(!1),this.buffer=new t,this.length=0,this.pipes=[],this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.constructed=!0,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this[w]=null,this.errorEmitted=!1,this.emitClose=!e||!1!==e.emitClose,this.autoDestroy=!e||!1!==e.autoDestroy,this.destroyed=!1,this.errored=null,this.closed=!1,this.closeEmitted=!1,this.defaultEncoding=e&&e.defaultEncoding||"utf8",this.awaitDrainWriters=null,this.multiAwaitDrain=!1,this.readingMore=!1,this.dataEmitted=!1,this.decoder=null,this.encoding=null,e&&e.encoding&&(this.decoder=new b(e.encoding),this.encoding=e.encoding)}export function Readable(t){if(!(this instanceof Readable))return new Readable(t);const n=this instanceof d.Duplex;this._readableState=new ReadableState(t,this,n),t&&("function"==typeof t.read&&(this._read=t.read),"function"==typeof t.destroy&&(this._destroy=t.destroy),"function"==typeof t.construct&&(this._construct=t.construct),t.signal&&!n&&e(t.signal,this)),d.call(this,t),r.construct(this,(()=>{this._readableState.needReadable&&L(this,this._readableState)}))}function E(e,t,r,n){const a=e._readableState;let i;if(a.objectMode||("string"==typeof t?(r=r||a.defaultEncoding,a.encoding!==r&&(n&&a.encoding?t=h.from(t,r).toString(a.encoding):(t=h.from(t,r),r=""))):t instanceof h?r="":d._isUint8Array(t)?(t=d._uint8ArrayToBuffer(t),r=""):null!=t&&(i=new p("chunk",["string","Buffer","Uint8Array"],t))),i)S(e,i);else if(null===t)a.reading=!1,function(e,t){if(t.ended)return;if(t.decoder){const e=t.decoder.end();e&&e.length&&(t.buffer.push(e),t.length+=t.objectMode?1:e.length)}t.ended=!0,t.sync?j(e):(t.needReadable=!1,t.emittedReadable=!0,W(e))}(e,a);else if(a.objectMode||t&&t.length>0)if(n)if(a.endEmitted)S(e,new _);else{if(a.destroyed||a.errored)return!1;M(e,a,t,!0)}else if(a.ended)S(e,new y);else{if(a.destroyed||a.errored)return!1;a.reading=!1,a.decoder&&!r?(t=a.decoder.write(t),a.objectMode||0!==t.length?M(e,a,t,!1):L(e,a)):M(e,a,t,!1)}else n||(a.reading=!1,L(e,a));return!a.ended&&(a.length<a.highWaterMark||0===a.length)}function M(e,t,r,n){t.flowing&&0===t.length&&!t.sync&&e.listenerCount("data")>0?(t.multiAwaitDrain?t.awaitDrainWriters.clear():t.awaitDrainWriters=null,t.dataEmitted=!0,e.emit("data",r)):(t.length+=t.objectMode?1:r.length,n?t.buffer.unshift(r):t.buffer.push(r),t.needReadable&&j(e)),L(e,t)}Readable.prototype.destroy=r.destroy,Readable.prototype._undestroy=r.undestroy,Readable.prototype._destroy=function(e,t){t(e)},Readable.prototype[c.captureRejectionSymbol]=function(e){this.destroy(e)},Readable.prototype.push=function(e,t){return E(this,e,t,!1)},Readable.prototype.unshift=function(e,t){return E(this,e,t,!0)},Readable.prototype.isPaused=function(){const e=this._readableState;return!0===e[w]||!1===e.flowing},Readable.prototype.setEncoding=function(e){const t=new b(e);this._readableState.decoder=t,this._readableState.encoding=this._readableState.decoder.encoding;const r=this._readableState.buffer;let n="";for(const e of r)n+=t.write(e);return r.clear(),""!==n&&r.push(n),this._readableState.length=n.length,this};function D(e,t){return e<=0||0===t.length&&t.ended?0:t.objectMode?1:Number.isNaN(e)?t.flowing&&t.length?t.buffer.first().length:t.length:e<=t.length?e:t.ended?t.length:0}function j(e){const t=e._readableState;t.needReadable=!1,t.emittedReadable||(t.emittedReadable=!0,f.nextTick(W,e))}function W(e){const t=e._readableState;t.destroyed||t.errored||!t.length&&!t.ended||(e.emit("readable"),t.emittedReadable=!1),t.needReadable=!t.flowing&&!t.ended&&t.length<=t.highWaterMark,O(e)}function L(e,t){!t.readingMore&&t.constructed&&(t.readingMore=!0,f.nextTick(k,e,t))}function k(e,t){for(;!t.reading&&!t.ended&&(t.length<t.highWaterMark||t.flowing&&0===t.length);){const r=t.length;if(e.read(0),r===t.length)break}t.readingMore=!1}function v(e){const t=e._readableState;t.readableListening=e.listenerCount("readable")>0,t.resumeScheduled&&!1===t[w]?t.flowing=!0:e.listenerCount("data")>0?e.resume():t.readableListening||(t.flowing=null)}function T(e){e.read(0)}function A(e,t){t.reading||e.read(0),t.resumeScheduled=!1,e.emit("resume"),O(e),t.flowing&&!t.reading&&e.read(0)}function O(e){const t=e._readableState;for(;t.flowing&&null!==e.read(););}function x(e,t){"function"!=typeof e.read&&(e=Readable.wrap(e,{objectMode:!0}));const i=async function*(e,t){let i,o=R;function d(t){this===e?(o(),o=R):o=t}e.on("readable",d),n(e,{writable:!1},(e=>{i=e?a(i,e):null,o(),o=R}));try{for(;;){const t=e.destroyed?null:e.read();if(null!==t)yield t;else{if(i)throw i;if(null===i)return;await new Promise(d)}}}catch(e){throw i=a(i,e),i}finally{!i&&!1===t?.destroyOnReturn||void 0!==i&&!e._readableState.autoDestroy||r.destroyer(e,null)}}(e,t);return i.stream=e,i}function N(e,t){if(0===t.length)return null;let r;return t.objectMode?r=t.buffer.shift():!e||e>=t.length?(r=t.decoder?t.buffer.join(""):1===t.buffer.length?t.buffer.first():t.buffer.concat(t.length),t.buffer.clear()):r=t.buffer.consume(e,t.decoder),r}function P(e){const t=e._readableState;t.endEmitted||(t.ended=!0,f.nextTick(C,t,e))}function C(e,t){if(!e.errored&&!e.closeEmitted&&!e.endEmitted&&0===e.length)if(e.endEmitted=!0,t.emit("end"),t.writable&&!1===t.allowHalfOpen)f.nextTick(U,t);else if(e.autoDestroy){const e=t._writableState;(!e||e.autoDestroy&&(e.finished||!1===e.writable))&&t.destroy()}}function U(e){e.writable&&!e.writableEnded&&!e.destroyed&&e.end()}Readable.prototype.read=function(e){void 0===e?e=NaN:Number.isInteger(e)||(e=Number.parseInt(e,10));const t=this._readableState,r=e;if(e>t.highWaterMark&&(t.highWaterMark=function(e){if(e>1073741824)throw new m("size","<= 1GiB",e);return e--,e|=e>>>1,e|=e>>>2,e|=e>>>4,e|=e>>>8,e|=e>>>16,++e}(e)),0!==e&&(t.emittedReadable=!1),0===e&&t.needReadable&&((0!==t.highWaterMark?t.length>=t.highWaterMark:t.length>0)||t.ended))return 0===t.length&&t.ended?P(this):j(this),null;if(0===(e=D(e,t))&&t.ended)return 0===t.length&&P(this),null;let n,a=t.needReadable;if((0===t.length||t.length-e<t.highWaterMark)&&(a=!0),t.ended||t.reading||t.destroyed||t.errored||!t.constructed)a=!1;else if(a){t.reading=!0,t.sync=!0,0===t.length&&(t.needReadable=!0);try{const e=this._read(t.highWaterMark);if(null!=e){const t=e.then;"function"==typeof t&&t.call(e,R,(function(e){S(this,e)}))}}catch(e){S(this,e)}t.sync=!1,t.reading||(e=D(r,t))}return n=e>0?N(e,t):null,null===n?(t.needReadable=t.length<=t.highWaterMark,e=0):(t.length-=e,t.multiAwaitDrain?t.awaitDrainWriters.clear():t.awaitDrainWriters=null),0===t.length&&(t.ended||(t.needReadable=!0),r!==e&&t.ended&&P(this)),null===n||t.errorEmitted||t.closeEmitted||(t.dataEmitted=!0,this.emit("data",n)),n},Readable.prototype._read=function(e){throw new g("_read()")},Readable.prototype.pipe=function(e,t){const r=this,n=this._readableState;1===n.pipes.length&&(n.multiAwaitDrain||(n.multiAwaitDrain=!0,n.awaitDrainWriters=new Set(n.awaitDrainWriters?[n.awaitDrainWriters]:[]))),n.pipes.push(e);const a=(!t||!1!==t.end)&&e!==f.stdout&&e!==f.stderr?o:m;function i(t,a){t===r&&a&&!1===a.hasUnpiped&&(a.hasUnpiped=!0,function(){e.removeListener("close",p),e.removeListener("finish",g),d&&e.removeListener("drain",d);e.removeListener("error",b),e.removeListener("unpipe",i),r.removeListener("end",o),r.removeListener("end",m),r.removeListener("data",h),l=!0,d&&n.awaitDrainWriters&&(!e._writableState||e._writableState.needDrain)&&d()}())}function o(){e.end()}let d;n.endEmitted?f.nextTick(a):r.once("end",a),e.on("unpipe",i);let l=!1;function u(){l||(1===n.pipes.length&&n.pipes[0]===e?(n.awaitDrainWriters=e,n.multiAwaitDrain=!1):n.pipes.length>1&&n.pipes.includes(e)&&n.awaitDrainWriters.add(e),r.pause()),d||(d=function(e,t){return function(){const r=e._readableState;r.awaitDrainWriters===t?r.awaitDrainWriters=null:r.multiAwaitDrain&&r.awaitDrainWriters.delete(t),r.awaitDrainWriters&&0!==r.awaitDrainWriters.size||!c.listenerCount(e,"data")||(r.flowing=!0,O(e))}}(r,e),e.on("drain",d))}function h(t){!1===e.write(t)&&u()}function b(t){if(m(),e.removeListener("error",b),0===c.listenerCount(e,"error")){const r=e._writableState||e._readableState;r&&!r.errorEmitted?S(e,t):e.emit("error",t)}}function p(){e.removeListener("finish",g),m()}function g(){e.removeListener("close",p),m()}function m(){r.unpipe(e)}return r.on("data",h),s(e,"error",b),e.once("close",p),e.once("finish",g),e.emit("pipe",r),!0===e.writableNeedDrain?n.flowing&&u():n.flowing||r.resume(),e},Readable.prototype.unpipe=function(e){const t=this._readableState;if(0===t.pipes.length)return this;if(!e){const e=t.pipes;t.pipes=[],this.pause();for(let t=0;t<e.length;t++)e[t].emit("unpipe",this,{hasUnpiped:!1});return this}const r=t.pipes.indexOf(e);return-1===r||(t.pipes.splice(r,1),0===t.pipes.length&&this.pause(),e.emit("unpipe",this,{hasUnpiped:!1})),this},Readable.prototype.on=function(e,t){const r=d.prototype.on.call(this,e,t),n=this._readableState;return"data"===e?(n.readableListening=this.listenerCount("readable")>0,!1!==n.flowing&&this.resume()):"readable"===e&&(n.endEmitted||n.readableListening||(n.readableListening=n.needReadable=!0,n.flowing=!1,n.emittedReadable=!1,n.length?j(this):n.reading||f.nextTick(T,this))),r},Readable.prototype.addListener=Readable.prototype.on,Readable.prototype.removeListener=function(e,t){const r=d.prototype.removeListener.call(this,e,t);return"readable"===e&&f.nextTick(v,this),r},Readable.prototype.off=Readable.prototype.removeListener,Readable.prototype.removeAllListeners=function(e){const t=d.prototype.removeAllListeners.apply(this,arguments);return"readable"!==e&&void 0!==e||f.nextTick(v,this),t},Readable.prototype.resume=function(){const e=this._readableState;return e.flowing||(e.flowing=!e.readableListening,function(e,t){t.resumeScheduled||(t.resumeScheduled=!0,f.nextTick(A,e,t))}(this,e)),e[w]=!1,this},Readable.prototype.pause=function(){return!1!==this._readableState.flowing&&(this._readableState.flowing=!1,this.emit("pause")),this._readableState[w]=!0,this},Readable.prototype.wrap=function(e){let t=!1;e.on("data",(r=>{!this.push(r)&&e.pause&&(t=!0,e.pause())})),e.on("end",(()=>{this.push(null)})),e.on("error",(e=>{S(this,e)})),e.on("close",(()=>{this.destroy()})),e.on("destroy",(()=>{this.destroy()})),this._read=()=>{t&&e.resume&&(t=!1,e.resume())};const r=Object.keys(e);for(let t=1;t<r.length;t++){const n=r[t];void 0===this[n]&&"function"==typeof e[n]&&(this[n]=e[n].bind(e))}return this},Readable.prototype[Symbol.asyncIterator]=function(){return x(this)},Readable.prototype.iterator=function(e){return x(this,e)},Object.defineProperties(Readable.prototype,{readable:{get(){const e=this._readableState;return!(!e||!1===e.readable||e.destroyed||e.errorEmitted||e.endEmitted)},set(e){this._readableState&&(this._readableState.readable=!!e)}},readableDidRead:{enumerable:!1,get:function(){return this._readableState.dataEmitted}},readableAborted:{enumerable:!1,get:function(){return!(!this._readableState.destroyed&&!this._readableState.errored||this._readableState.endEmitted)}},readableHighWaterMark:{enumerable:!1,get:function(){return this._readableState.highWaterMark}},readableBuffer:{enumerable:!1,get:function(){return this._readableState&&this._readableState.buffer}},readableFlowing:{enumerable:!1,get:function(){return this._readableState.flowing},set:function(e){this._readableState&&(this._readableState.flowing=e)}},readableLength:{enumerable:!1,get(){return this._readableState.length}},readableObjectMode:{enumerable:!1,get(){return!!this._readableState&&this._readableState.objectMode}},readableEncoding:{enumerable:!1,get(){return this._readableState?this._readableState.encoding:null}},destroyed:{enumerable:!1,get(){return void 0!==this._readableState&&this._readableState.destroyed},set(e){this._readableState&&(this._readableState.destroyed=e)}},readableEnded:{enumerable:!1,get(){return!!this._readableState&&this._readableState.endEmitted}}}),Object.defineProperties(ReadableState.prototype,{pipesCount:{get(){return this.pipes.length}},paused:{get(){return!1!==this[w]},set(e){this[w]=!!e}}}),Readable._fromList=N,Readable.from=function(e,t){return o(Readable,e,t)},Readable.wrap=function(e,t){return new Readable({objectMode:e.readableObjectMode??e.objectMode??!0,...t,destroy(t,n){r.destroyer(e,t),n(t)}}).wrap(e)};
✄
import{codes as r}from"../errors.js";const{ERR_INVALID_ARG_VALUE:t}=r;export function getDefaultHighWaterMark(r){return r?16:16384}export function getHighWaterMark(r,e,o,n){const a=function(r,t,e){return null!=r.highWaterMark?r.highWaterMark:t?r[e]:null}(e,n,o);if(null!=a){if(!Number.isInteger(a)||a<0){throw new t(n?`options.${o}`:"options.highWaterMark",a)}return Math.floor(a)}return getDefaultHighWaterMark(r.objectMode)}
✄
import t from"./duplex.js";import{codes as n}from"../errors.js";import s from"process";const{ERR_METHOD_NOT_IMPLEMENTED:e}=n;Object.setPrototypeOf(o.prototype,t.prototype),Object.setPrototypeOf(o,t);const i=Symbol("kCallback");export default function o(n){if(!(this instanceof o))return new o(n);t.call(this,n),this._readableState.sync=!1,this[i]=null,n&&("function"==typeof n.transform&&(this._transform=n.transform),"function"==typeof n.flush&&(this._flush=n.flush)),this.on("prefinish",l)}function h(t){let n=!1;if("function"!=typeof this._flush||this.destroyed)this.push(null),t&&t();else{const e=this._flush(((s,e)=>{n=!0,s?t?t(s):this.destroy(s):(null!=e&&this.push(e),this.push(null),t&&t())}));if(null!=e)try{const i=e.then;"function"==typeof i&&i.call(e,(e=>{n||(null!=e&&this.push(e),this.push(null),t&&s.nextTick(t))}),(n=>{t?s.nextTick(t,n):s.nextTick((()=>this.destroy(n)))}))}catch(t){s.nextTick((()=>this.destroy(t)))}}}function l(){this._final!==h&&h.call(this)}o.prototype._final=h,o.prototype._transform=function(t,n,s){throw new e("_transform()")},o.prototype._write=function(t,n,e){const o=this._readableState,h=this._writableState,l=o.length;let r=!1;const f=this._transform(t,n,((t,n)=>{r=!0,t?e(t):(null!=n&&this.push(n),h.ended||l===o.length||o.length<o.highWaterMark||0===o.length?e():this[i]=e)}));if(void 0!==f&&null!=f)try{const t=f.then;"function"==typeof t&&t.call(f,(t=>{r||(null!=t&&this.push(t),h.ended||l===o.length||o.length<o.highWaterMark||0===o.length?s.nextTick(e):this[i]=e)}),(t=>{s.nextTick(e,t)}))}catch(t){s.nextTick(e,t)}},o.prototype._read=function(){if(this[i]){const t=this[i];this[i]=null,t()}};
✄
export const kDestroyed=Symbol("kDestroyed");export const kIsDisturbed=Symbol("kIsDisturbed");export function isReadableNodeStream(e){return!(!e||"function"!=typeof e.pipe||"function"!=typeof e.on||e._writableState&&!1===e._readableState?.readable||e._writableState&&!e._readableState)}export function isWritableNodeStream(e){return!(!e||"function"!=typeof e.write||"function"!=typeof e.on||e._readableState&&!1===e._writableState?.writable)}export function isDuplexNodeStream(e){return!(!e||"function"!=typeof e.pipe||!e._readableState||"function"!=typeof e.on||"function"!=typeof e.write)}export function isNodeStream(e){return e&&(e._readableState||e._writableState||"function"==typeof e.write&&"function"==typeof e.on||"function"==typeof e.pipe&&"function"==typeof e.on)}export function isIterable(e,t){return null!=e&&(!0===t?"function"==typeof e[Symbol.asyncIterator]:!1===t?"function"==typeof e[Symbol.iterator]:"function"==typeof e[Symbol.asyncIterator]||"function"==typeof e[Symbol.iterator])}export function isDestroyed(e){if(!isNodeStream(e))return null;const t=e._writableState,o=e._readableState,n=t||o;return!!(e.destroyed||e[kDestroyed]||n?.destroyed)}export function isWritableEnded(e){if(!isWritableNodeStream(e))return null;if(!0===e.writableEnded)return!0;const t=e._writableState;return!t?.errored&&("boolean"!=typeof t?.ended?null:t.ended)}export function isWritableFinished(e,t){if(!isWritableNodeStream(e))return null;if(!0===e.writableFinished)return!0;const o=e._writableState;return!o?.errored&&("boolean"!=typeof o?.finished?null:!!(o.finished||!1===t&&!0===o.ended&&0===o.length))}export function isReadableEnded(e){if(!isReadableNodeStream(e))return null;if(!0===e.readableEnded)return!0;const t=e._readableState;return!(!t||t.errored)&&("boolean"!=typeof t?.ended?null:t.ended)}export function isReadableFinished(e,t){if(!isReadableNodeStream(e))return null;const o=e._readableState;return!o?.errored&&("boolean"!=typeof o?.endEmitted?null:!!(o.endEmitted||!1===t&&!0===o.ended&&0===o.length))}export function isReadable(e){const t=isReadableNodeStream(e);return null===t||"boolean"!=typeof e?.readable?null:!isDestroyed(e)&&(t&&e.readable&&!isReadableFinished(e))}export function isWritable(e){const t=isWritableNodeStream(e);return null===t||"boolean"!=typeof e?.writable?null:!isDestroyed(e)&&(t&&e.writable&&!isWritableEnded(e))}export function isFinished(e,t){return isNodeStream(e)?!!isDestroyed(e)||(!1===t?.readable||!isReadable(e))&&(!1===t?.writable||!isWritable(e)):null}export function isClosed(t){if(!isNodeStream(t))return null;const o=t._writableState,n=t._readableState;return"boolean"==typeof o?.closed||"boolean"==typeof n?.closed?o?.closed||n?.closed:"boolean"==typeof t._closed&&e(t)?t._closed:null}function e(e){return"boolean"==typeof e._closed&&"boolean"==typeof e._defaultKeepAlive&&"boolean"==typeof e._removedConnection&&"boolean"==typeof e._removedContLen}export function isServerResponse(t){return"boolean"==typeof t._sent100&&e(t)}export function isServerRequest(e){return"boolean"==typeof e._consuming&&"boolean"==typeof e._dumped&&void 0===e.req?.upgradeOrConnect}export function willEmitClose(e){if(!isNodeStream(e))return null;const t=e._writableState,o=e._readableState,n=t||o;return!n&&isServerResponse(e)||!!(n&&n.autoDestroy&&n.emitClose&&!1===n.closed)}export function isDisturbed(e){return!(!e||!(e.readableDidRead||e.readableAborted||e[kIsDisturbed]))}
✄
import{addAbortSignal as t}from"./add-abort-signal.js";import*as e from"./destroy.js";import{codes as i}from"../errors.js";import{Stream as r}from"./legacy.js";import{getHighWaterMark as n,getDefaultHighWaterMark as o}from"./state.js";import{Buffer as s}from"buffer";import l from"events";import f from"process";export default Writable;const{ERR_INVALID_ARG_TYPE:c,ERR_METHOD_NOT_IMPLEMENTED:a,ERR_MULTIPLE_CALLBACK:d,ERR_STREAM_CANNOT_PIPE:u,ERR_STREAM_DESTROYED:h,ERR_STREAM_ALREADY_FINISHED:b,ERR_STREAM_NULL_VALUES:w,ERR_STREAM_WRITE_AFTER_END:p,ERR_UNKNOWN_ENCODING:g}=i,{errorOrDestroy:y}=e;function _(){}Object.setPrototypeOf(Writable.prototype,r.prototype),Object.setPrototypeOf(Writable,r);const S=Symbol("kOnFinished");export function WritableState(t,e,i){"boolean"!=typeof i&&(i=e instanceof r.Duplex),this.objectMode=!(!t||!t.objectMode),i&&(this.objectMode=this.objectMode||!(!t||!t.writableObjectMode)),this.highWaterMark=t?n(this,t,"writableHighWaterMark",i):o(!1),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;const s=!(!t||!1!==t.decodeStrings);this.decodeStrings=!s,this.defaultEncoding=t&&t.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=R.bind(void 0,e),this.writecb=null,this.writelen=0,this.afterWriteTickInfo=null,k(this),this.pendingcb=0,this.constructed=!0,this.prefinished=!1,this.errorEmitted=!1,this.emitClose=!t||!1!==t.emitClose,this.autoDestroy=!t||!1!==t.autoDestroy,this.errored=null,this.closed=!1,this.closeEmitted=!1,this[S]=[]}function k(t){t.buffered=[],t.bufferedIndex=0,t.allBuffers=!0,t.allNoop=!0}WritableState.prototype.getBuffer=function(){return this.buffered.slice(this.bufferedIndex)},Object.defineProperty(WritableState.prototype,"bufferedRequestCount",{get(){return this.buffered.length-this.bufferedIndex}});const E=Function.prototype[Symbol.hasInstance];export function Writable(i){const n=this instanceof r.Duplex;if(!n&&!E.call(Writable,this))return new Writable(i);this._writableState=new WritableState(i,this,n),i&&("function"==typeof i.write&&(this._write=i.write),"function"==typeof i.writev&&(this._writev=i.writev),"function"==typeof i.destroy&&(this._destroy=i.destroy),"function"==typeof i.final&&(this._final=i.final),"function"==typeof i.construct&&(this._construct=i.construct),i.signal&&t(i.signal,this)),r.call(this,i),e.construct(this,(()=>{const t=this._writableState;t.writing||D(this,t),A(this,t)}))}function W(t,e,i,n){const o=t._writableState;if("function"==typeof i)n=i,i=o.defaultEncoding;else{if(i){if("buffer"!==i&&!s.isEncoding(i))throw new g(i)}else i=o.defaultEncoding;"function"!=typeof n&&(n=_)}if(null===e)throw new w;if(!o.objectMode)if("string"==typeof e)!1!==o.decodeStrings&&(e=s.from(e,i),i="buffer");else if(e instanceof s)i="buffer";else{if(!r._isUint8Array(e))throw new c("chunk",["string","Buffer","Uint8Array"],e);e=r._uint8ArrayToBuffer(e),i="buffer"}let l;return o.ending?l=new p:o.destroyed&&(l=new h("write")),l?(f.nextTick(n,l),y(t,l,!0),l):(o.pendingcb++,function(t,e,i,r,n){const o=e.objectMode?1:i.length;e.length+=o;const s=e.length<e.highWaterMark;s||(e.needDrain=!0);e.writing||e.corked||e.errored||!e.constructed?(e.buffered.push({chunk:i,encoding:r,callback:n}),e.allBuffers&&"buffer"!==r&&(e.allBuffers=!1),e.allNoop&&n!==_&&(e.allNoop=!1)):(e.writelen=o,e.writecb=n,e.writing=!0,e.sync=!0,t._write(i,r,e.onwrite),e.sync=!1);return s&&!e.errored&&!e.destroyed}(t,o,e,i,n))}function m(t,e,i,r,n,o,s){e.writelen=r,e.writecb=s,e.writing=!0,e.sync=!0,e.destroyed?e.onwrite(new h("write")):i?t._writev(n,e.onwrite):t._write(n,o,e.onwrite),e.sync=!1}function T(t,e,i,r){--e.pendingcb,r(i),x(e),y(t,i)}function R(t,e){const i=t._writableState,r=i.sync,n=i.writecb;"function"==typeof n?(i.writing=!1,i.writecb=null,i.length-=i.writelen,i.writelen=0,e?(e.stack,i.errored||(i.errored=e),t._readableState&&!t._readableState.errored&&(t._readableState.errored=e),r?f.nextTick(T,t,i,e,n):T(t,i,e,n)):(i.buffered.length>i.bufferedIndex&&D(t,i),r?null!==i.afterWriteTickInfo&&i.afterWriteTickInfo.cb===n?i.afterWriteTickInfo.count++:(i.afterWriteTickInfo={count:1,cb:n,stream:t,state:i},f.nextTick(I,i.afterWriteTickInfo)):M(t,i,1,n))):y(t,new d)}function I({stream:t,state:e,count:i,cb:r}){return e.afterWriteTickInfo=null,M(t,e,i,r)}function M(t,e,i,r){for(!e.ending&&!t.destroyed&&0===e.length&&e.needDrain&&(e.needDrain=!1,t.emit("drain"));i-- >0;)e.pendingcb--,r();e.destroyed&&x(e),A(t,e)}function x(t){if(t.writing)return;for(let e=t.bufferedIndex;e<t.buffered.length;++e){const{chunk:i,callback:r}=t.buffered[e],n=t.objectMode?1:i.length;t.length-=n,r(t.errored??new h("write"))}const e=t[S].splice(0);for(let i=0;i<e.length;i++)e[i](t.errored??new h("end"));k(t)}function D(t,e){if(e.corked||e.bufferProcessing||e.destroyed||!e.constructed)return;const{buffered:i,bufferedIndex:r,objectMode:n}=e,o=i.length-r;if(!o)return;let s=r;if(e.bufferProcessing=!0,o>1&&t._writev){e.pendingcb-=o-1;const r=e.allNoop?_:t=>{for(let e=s;e<i.length;++e)i[e].callback(t)},n=e.allNoop&&0===s?i:i.slice(s);n.allBuffers=e.allBuffers,m(t,e,!0,e.length,n,"",r),k(e)}else{do{const{chunk:r,encoding:o,callback:l}=i[s];i[s++]=null;m(t,e,!1,n?1:r.length,r,o,l)}while(s<i.length&&!e.writing);s===i.length?k(e):s>256?(i.splice(0,s),e.bufferedIndex=0):e.bufferedIndex=s}e.bufferProcessing=!1}function j(t){return t.ending&&t.constructed&&0===t.length&&!t.errored&&0===t.buffered.length&&!t.finished&&!t.writing&&!t.errorEmitted&&!t.closeEmitted}function N(t,e){e.prefinished||e.finalCalled||("function"!=typeof t._final||e.destroyed?(e.prefinished=!0,t.emit("prefinish")):(e.finalCalled=!0,function(t,e){let i=!1;function r(r){if(i)y(t,r??d());else if(i=!0,e.pendingcb--,r){const i=e[S].splice(0);for(let t=0;t<i.length;t++)i[t](r);y(t,r,e.sync)}else j(e)&&(e.prefinished=!0,t.emit("prefinish"),e.pendingcb++,f.nextTick(O,t,e))}e.sync=!0,e.pendingcb++;try{const e=t._final(r);if(null!=e){const t=e.then;"function"==typeof t&&t.call(e,(function(){f.nextTick(r,null)}),(function(t){f.nextTick(r,t)}))}}catch(e){r(t)}e.sync=!1}(t,e)))}function A(t,e,i){j(e)&&(N(t,e),0===e.pendingcb&&j(e)&&(e.pendingcb++,i?f.nextTick(O,t,e):O(t,e)))}function O(t,e){e.pendingcb--,e.finished=!0;const i=e[S].splice(0);for(let t=0;t<i.length;t++)i[t]();if(t.emit("finish"),e.autoDestroy){const e=t._readableState;(!e||e.autoDestroy&&(e.endEmitted||!1===e.readable))&&t.destroy()}}Object.defineProperty(Writable,Symbol.hasInstance,{value:function(t){return!!E.call(this,t)||this===Writable&&(t&&t._writableState instanceof WritableState)}}),Writable.prototype.pipe=function(){y(this,new u)},Writable.prototype.write=function(t,e,i){return!0===W(this,t,e,i)},Writable.prototype.cork=function(){this._writableState.corked++},Writable.prototype.uncork=function(){const t=this._writableState;t.corked&&(t.corked--,t.writing||D(this,t))},Writable.prototype.setDefaultEncoding=function(t){if("string"==typeof t&&(t=t.toLowerCase()),!s.isEncoding(t))throw new g(t);return this._writableState.defaultEncoding=t,this},Writable.prototype._write=function(t,e,i){if(!this._writev)throw new a("_write()");this._writev([{chunk:t,encoding:e}],i)},Writable.prototype._writev=null,Writable.prototype.end=function(t,e,i){const r=this._writableState;let n;if("function"==typeof t?(i=t,t=null,e=null):"function"==typeof e&&(i=e,e=null),null!=t){const i=W(this,t,e);i instanceof Error&&(n=i)}return r.corked&&(r.corked=1,this.uncork()),n||(r.errored||r.ending?r.finished?n=new b("end"):r.destroyed&&(n=new h("end")):(r.ending=!0,A(this,r,!0),r.ended=!0)),"function"==typeof i&&(n||r.finished?f.nextTick(i,n):r[S].push(i)),this},Object.defineProperties(Writable.prototype,{destroyed:{get(){return!!this._writableState&&this._writableState.destroyed},set(t){this._writableState&&(this._writableState.destroyed=t)}},writable:{get(){const t=this._writableState;return!(!t||!1===t.writable||t.destroyed||t.errored||t.ending||t.ended)},set(t){this._writableState&&(this._writableState.writable=!!t)}},writableFinished:{get(){return!!this._writableState&&this._writableState.finished}},writableObjectMode:{get(){return!!this._writableState&&this._writableState.objectMode}},writableBuffer:{get(){return this._writableState&&this._writableState.getBuffer()}},writableEnded:{get(){return!!this._writableState&&this._writableState.ending}},writableNeedDrain:{get(){const t=this._writableState;return!!t&&(!t.destroyed&&!t.ending&&t.needDrain)}},writableHighWaterMark:{get(){return this._writableState&&this._writableState.highWaterMark}},writableCorked:{get(){return this._writableState?this._writableState.corked:0}},writableLength:{get(){return this._writableState&&this._writableState.length}}});const P=e.destroy;Writable.prototype.destroy=function(t,e){const i=this._writableState;return!i.destroyed&&(i.bufferedIndex<i.buffered.length||i[S].length)&&f.nextTick(x,i),P.call(this,t,e),this},Writable.prototype._undestroy=e.undestroy,Writable.prototype._destroy=function(t,e){e(t)},Writable.prototype[l.captureRejectionSymbol]=function(t){this.destroy(t)};
✄
import{addAbortSignal as r}from"./lib/add-abort-signal.js";import e from"./lib/compose.js";import{destroyer as i}from"./lib/destroy.js";import o from"./lib/duplex.js";import t from"./lib/end-of-stream.js";import{Stream as m}from"./lib/legacy.js";import s from"./lib/passthrough.js";import a from"./lib/pipeline.js";import*as p from"./lib/promises.js";import l from"./lib/readable.js";import f from"./lib/transform.js";import{isDisturbed as b}from"./lib/utils.js";import n from"./lib/writable.js";import{Buffer as d}from"buffer";import{promisify as u,types as j}from"util";export default l;export{b as isDisturbed,l as Stream,m as LegacyStream,l as Readable,n as Writable,o as Duplex,f as Transform,s as PassThrough,a as pipeline,r as addAbortSignal,t as finished,i as destroy,e as compose,p as promises};m.isDisturbed=b,m.Readable=l,m.Writable=n,m.Duplex=o,m.Transform=f,m.PassThrough=s,m.pipeline=a,m.addAbortSignal=r,m.finished=t,m.destroy=i,m.compose=e,Object.defineProperty(m,"promises",{configurable:!0,enumerable:!0,get:()=>p}),Object.defineProperty(a,u.custom,{enumerable:!0,get:()=>p.pipeline}),Object.defineProperty(t,u.custom,{enumerable:!0,get:()=>p.finished}),m.Stream=m,m._isUint8Array=j.isUint8Array,m._uint8ArrayToBuffer=d.from;
✄
import{isDisturbed as r,LegacyStream as a,Readable as e,Writable as t,Duplex as m,Transform as o,PassThrough as d,pipeline as f,addAbortSignal as p,finished as i,destroy as l,compose as s,promises as x}from"@frida/readable-stream";export default a;export{r as isDisturbed,a as Stream,e as Readable,t as Writable,m as Duplex,o as Transform,d as PassThrough,f as pipeline,p as addAbortSignal,i as finished,l as destroy,s as compose,x as promises};
✄
import{Buffer as t}from"buffer";export default{StringDecoder};const e=t.isEncoding;export function StringDecoder(s){let u;switch(this.encoding=function(s){const i=function(t){if(!t)return"utf8";let e=!1;for(;;)switch(t){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return t;default:if(e)return;t=(""+t).toLowerCase(),e=!0}}(s);if(void 0===i&&(t.isEncoding===e||!e(s)))throw new Error("Unknown encoding: "+s);return i||s}(s),this.encoding){case"utf16le":this.text=n,this.end=r,u=4;break;case"utf8":this.fillLast=i,u=4;break;case"base64":this.text=l,this.end=a,u=3;break;default:return this.write=h,void(this.end=o)}this.lastNeed=0,this.lastTotal=0,this.lastChar=t.allocUnsafe(u)}function s(t){return t<=127?0:t>>5==6?2:t>>4==14?3:t>>3==30?4:t>>6==2?-1:-2}function i(t){const e=this.lastTotal-this.lastNeed,s=function(t,e,s){if(128!=(192&e[0]))return t.lastNeed=0,"�";if(t.lastNeed>1&&e.length>1){if(128!=(192&e[1]))return t.lastNeed=1,"�";if(t.lastNeed>2&&e.length>2&&128!=(192&e[2]))return t.lastNeed=2,"�"}}(this,t);return void 0!==s?s:this.lastNeed<=t.length?(t.copy(this.lastChar,e,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal)):(t.copy(this.lastChar,e,0,t.length),void(this.lastNeed-=t.length))}function n(t,e){if((t.length-e)%2==0){const s=t.toString("utf16le",e);if(s){const e=s.charCodeAt(s.length-1);if(e>=55296&&e<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=t[t.length-2],this.lastChar[1]=t[t.length-1],s.slice(0,-1)}return s}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=t[t.length-1],t.toString("utf16le",e,t.length-1)}function r(t){const e=t&&t.length?this.write(t):"";if(this.lastNeed){const t=this.lastTotal-this.lastNeed;return e+this.lastChar.toString("utf16le",0,t)}return e}function l(t,e){const s=(t.length-e)%3;return 0===s?t.toString("base64",e):(this.lastNeed=3-s,this.lastTotal=3,1===s?this.lastChar[0]=t[t.length-1]:(this.lastChar[0]=t[t.length-2],this.lastChar[1]=t[t.length-1]),t.toString("base64",e,t.length-s))}function a(t){const e=t&&t.length?this.write(t):"";return this.lastNeed?e+this.lastChar.toString("base64",0,3-this.lastNeed):e}function h(t){return t.toString(this.encoding)}function o(t){return t&&t.length?this.write(t):""}StringDecoder.prototype.write=function(t){if(0===t.length)return"";let e,s;if(this.lastNeed){if(e=this.fillLast(t),void 0===e)return"";s=this.lastNeed,this.lastNeed=0}else s=0;return s<t.length?e?e+this.text(t,s):this.text(t,s):e||""},StringDecoder.prototype.end=function(t){const e=t&&t.length?this.write(t):"";return this.lastNeed?e+"�":e},StringDecoder.prototype.text=function(t,e){const i=function(t,e,i){let n=e.length-1;if(n<i)return 0;let r=s(e[n]);if(r>=0)return r>0&&(t.lastNeed=r-1),r;if(--n<i||-2===r)return 0;if(r=s(e[n]),r>=0)return r>0&&(t.lastNeed=r-2),r;if(--n<i||-2===r)return 0;if(r=s(e[n]),r>=0)return r>0&&(2===r?r=0:t.lastNeed=r-3),r;return 0}(this,t,e);if(!this.lastNeed)return t.toString("utf8",e);this.lastTotal=i;const n=t.length-(i-this.lastNeed);return t.copy(this.lastChar,0,n),t.toString("utf8",e,n)},StringDecoder.prototype.fillLast=function(t){if(this.lastNeed<=t.length)return t.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);t.copy(this.lastChar,this.lastTotal-this.lastNeed,0,t.length),this.lastNeed-=t.length};
✄
const t=s(Object.prototype.toString),r=s(Number.prototype.valueOf),e=s(String.prototype.valueOf),n=s(Boolean.prototype.valueOf),o=s(BigInt.prototype.valueOf),i=s(Symbol.prototype.valueOf),u=Object.getPrototypeOf((function*(){})),c=Object.getPrototypeOf(Int8Array);export function isArgumentsObject(r){return(null===r||"object"!=typeof r||!(Symbol.toStringTag in r))&&"[object Arguments]"===t(r)}export function isGeneratorFunction(t){return Object.getPrototypeOf(t)===u}export function isTypedArray(t){return t instanceof c}export function isPromise(t){return t instanceof Promise}export function isArrayBufferView(t){return ArrayBuffer.isView(t)}export function isUint8Array(t){return t instanceof Uint8Array}export function isUint8ClampedArray(t){return t instanceof Uint8ClampedArray}export function isUint16Array(t){return t instanceof Uint16Array}export function isUint32Array(t){return t instanceof Uint32Array}export function isInt8Array(t){return t instanceof Int8Array}export function isInt16Array(t){return t instanceof Int16Array}export function isInt32Array(t){return t instanceof Int32Array}export function isFloat32Array(t){return t instanceof Float32Array}export function isFloat64Array(t){return t instanceof Float64Array}export function isBigInt64Array(t){return t instanceof BigInt64Array}export function isBigUint64Array(t){return t instanceof BigUint64Array}export function isMap(r){return"[object Map]"===t(r)}export function isSet(r){return"[object Set]"===t(r)}export function isWeakMap(r){return"[object WeakMap]"===t(r)}export function isWeakSet(r){return"[object WeakSet]"===t(r)}export function isArrayBuffer(r){return"[object ArrayBuffer]"===t(r)}export function isDataView(r){return"[object DataView]"===t(r)}export function isSharedArrayBuffer(r){return"[object SharedArrayBuffer]"===t(r)}export function isAsyncFunction(r){return"[object AsyncFunction]"===t(r)}export function isMapIterator(r){return"[object Map Iterator]"===t(r)}export function isSetIterator(r){return"[object Set Iterator]"===t(r)}export function isGeneratorObject(r){return"[object Generator]"===t(r)}export function isWebAssemblyCompiledModule(r){return"[object WebAssembly.Module]"===t(r)}export function isNumberObject(t){return a(t,r)}export function isStringObject(t){return a(t,e)}export function isBooleanObject(t){return a(t,n)}export function isBigIntObject(t){return a(t,o)}export function isSymbolObject(t){return a(t,i)}function a(t,r){if("object"!=typeof t)return!1;try{return r(t),!0}catch(t){return!1}}export function isBoxedPrimitive(t){return isNumberObject(t)||isStringObject(t)||isBooleanObject(t)||isBigIntObject(t)||isSymbolObject(t)}export function isAnyArrayBuffer(t){return isArrayBuffer(t)||isSharedArrayBuffer(t)}export function isProxy(t){f("isProxy")}export function isExternal(t){f("isExternal")}export function isModuleNamespaceObject(t){f("isModuleNamespaceObject")}function f(t){throw new Error(`${t} is not supported in userland`)}function s(t){return t.call.bind(t)}
✄
import*as e from"./support/types.js";import t from"process";export const types={...e,isRegExp,isDate,isNativeError:isError};export default{format,deprecate,debuglog,inspect,types,isArray,isBoolean,isNull,isNullOrUndefined,isNumber,isString,isSymbol,isUndefined,isRegExp,isObject,isDate,isError,isFunction,isPrimitive,isBuffer,log,inherits,_extend,promisify,callbackify};const n=/%[sdj%]/g;export function format(e){if(!isString(e)){const e=[];for(let t=0;t<arguments.length;t++)e.push(inspect(arguments[t]));return e.join(" ")}let t=1;const r=arguments,i=r.length;let o=String(e).replace(n,(function(e){if("%%"===e)return"%";if(t>=i)return e;switch(e){case"%s":return String(r[t++]);case"%d":return Number(r[t++]);case"%j":try{return JSON.stringify(r[t++])}catch(e){return"[Circular]"}default:return e}}));for(let e=r[t];t<i;e=r[++t])isNull(e)||!isObject(e)?o+=" "+e:o+=" "+inspect(e);return o}export function deprecate(e,n){if(!0===t.noDeprecation)return e;let r=!1;return function(){if(!r){if(t.throwDeprecation)throw new Error(n);t.traceDeprecation?console.trace(n):console.error(n),r=!0}return e.apply(this,arguments)}}const r={};let i=/^$/;if(t.env.NODE_DEBUG){let m=t.env.NODE_DEBUG;m=m.replace(/[|\\{}()[\]^$+?.]/g,"\\$&").replace(/\*/g,".*").replace(/,/g,"$|^").toUpperCase(),i=new RegExp("^"+m+"$","i")}export function debuglog(e){if(e=e.toUpperCase(),!r[e])if(i.test(e)){const n=t.pid;r[e]=function(){const t=format.apply(null,arguments);console.error("%s %d: %s",e,n,t)}}else r[e]=function(){};return r[e]}export function inspect(e,t){const n={seen:[],stylize:s};return arguments.length>=3&&(n.depth=arguments[2]),arguments.length>=4&&(n.colors=arguments[3]),isBoolean(t)?n.showHidden=t:t&&_extend(n,t),isUndefined(n.showHidden)&&(n.showHidden=!1),isUndefined(n.depth)&&(n.depth=2),isUndefined(n.colors)&&(n.colors=!1),isUndefined(n.customInspect)&&(n.customInspect=!0),n.colors&&(n.stylize=o),c(n,e,n.depth)}function o(e,t){const n=inspect.styles[t];return n?"["+inspect.colors[n][0]+"m"+e+"["+inspect.colors[n][1]+"m":e}function s(e,t){return e}function c(e,t,n){if(e.customInspect&&t&&isFunction(t.inspect)&&t.inspect!==inspect&&(!t.constructor||t.constructor.prototype!==t)){let r=t.inspect(n,e);return isString(r)||(r=c(e,r,n)),r}const r=function(e,t){if(isUndefined(t))return e.stylize("undefined","undefined");if(isString(t)){const n="'"+JSON.stringify(t).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return e.stylize(n,"string")}if(isNumber(t))return e.stylize(""+t,"number");if(isBoolean(t))return e.stylize(""+t,"boolean");if(isNull(t))return e.stylize("null","null")}(e,t);if(r)return r;let i=Object.keys(t);const o=function(e){const t={};return e.forEach((function(e,n){t[e]=!0})),t}(i);if(e.showHidden&&(i=Object.getOwnPropertyNames(t)),isError(t)&&(i.indexOf("message")>=0||i.indexOf("description")>=0))return u(t);if(0===i.length){if(isFunction(t)){const n=t.name?": "+t.name:"";return e.stylize("[Function"+n+"]","special")}if(isRegExp(t))return e.stylize(RegExp.prototype.toString.call(t),"regexp");if(isDate(t))return e.stylize(Date.prototype.toString.call(t),"date");if(isError(t))return u(t)}let s,p="",f=!1,a=["{","}"];if(isArray(t)&&(f=!0,a=["[","]"]),isFunction(t)){p=" [Function"+(t.name?": "+t.name:"")+"]"}return isRegExp(t)&&(p=" "+RegExp.prototype.toString.call(t)),isDate(t)&&(p=" "+Date.prototype.toUTCString.call(t)),isError(t)&&(p=" "+u(t)),0!==i.length||f&&0!=t.length?n<0?isRegExp(t)?e.stylize(RegExp.prototype.toString.call(t),"regexp"):e.stylize("[Object]","special"):(e.seen.push(t),s=f?function(e,t,n,r,i){const o=[];for(let i=0,s=t.length;i<s;++i)g(t,String(i))?o.push(l(e,t,n,r,String(i),!0)):o.push("");return i.forEach((function(i){i.match(/^\d+$/)||o.push(l(e,t,n,r,i,!0))})),o}(e,t,n,o,i):i.map((function(r){return l(e,t,n,o,r,f)})),e.seen.pop(),function(e,t,n){if(e.reduce((function(e,t){return t.indexOf("\n")>=0&&0,e+t.replace(/\u001b\[\d\d?m/g,"").length+1}),0)>60)return n[0]+(""===t?"":t+"\n ")+" "+e.join(",\n  ")+" "+n[1];return n[0]+t+" "+e.join(", ")+" "+n[1]}(s,p,a)):a[0]+p+a[1]}function u(e){return"["+Error.prototype.toString.call(e)+"]"}function l(e,t,n,r,i,o){let s,u,l;if(l=Object.getOwnPropertyDescriptor(t,i)||{value:t[i]},l.get?u=l.set?e.stylize("[Getter/Setter]","special"):e.stylize("[Getter]","special"):l.set&&(u=e.stylize("[Setter]","special")),g(r,i)||(s="["+i+"]"),u||(e.seen.indexOf(l.value)<0?(u=isNull(n)?c(e,l.value,null):c(e,l.value,n-1),u.indexOf("\n")>-1&&(u=o?u.split("\n").map((function(e){return"  "+e})).join("\n").substr(2):"\n"+u.split("\n").map((function(e){return"   "+e})).join("\n"))):u=e.stylize("[Circular]","special")),isUndefined(s)){if(o&&i.match(/^\d+$/))return u;s=JSON.stringify(""+i),s.match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(s=s.substr(1,s.length-2),s=e.stylize(s,"name")):(s=s.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),s=e.stylize(s,"string"))}return s+": "+u}inspect.custom=Symbol.for("nodejs.util.inspect.custom"),inspect.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},inspect.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"};export function isArray(e){return Array.isArray(e)}export function isBoolean(e){return"boolean"==typeof e}export function isNull(e){return null===e}export function isNullOrUndefined(e){return null==e}export function isNumber(e){return"number"==typeof e}export function isString(e){return"string"==typeof e}export function isSymbol(e){return"symbol"==typeof e}export function isUndefined(e){return void 0===e}export function isRegExp(e){return isObject(e)&&"[object RegExp]"===p(e)}export function isObject(e){return"object"==typeof e&&null!==e}export function isDate(e){return isObject(e)&&"[object Date]"===p(e)}export function isError(e){return isObject(e)&&("[object Error]"===p(e)||e instanceof Error)}export function isFunction(e){return"function"==typeof e}export function isPrimitive(e){return null===e||"boolean"==typeof e||"number"==typeof e||"string"==typeof e||"symbol"==typeof e||void 0===e}export function isBuffer(e){return e instanceof Buffer}function p(e){return Object.prototype.toString.call(e)}function f(e){return e<10?"0"+e.toString(10):e.toString(10)}const a=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function y(){const e=new Date,t=[f(e.getHours()),f(e.getMinutes()),f(e.getSeconds())].join(":");return[e.getDate(),a[e.getMonth()],t].join(" ")}export function log(){console.log("%s - %s",y(),format.apply(null,arguments))}export function inherits(e,t){Object.defineProperty(e,"super_",{value:t,writable:!0,configurable:!0}),Object.setPrototypeOf(e.prototype,t.prototype)}export function _extend(e,t){if(!t||!isObject(t))return e;const n=Object.keys(t);let r=n.length;for(;r--;)e[n[r]]=t[n[r]];return e}function g(e,t){return Object.prototype.hasOwnProperty.call(e,t)}const d=Symbol("util.promisify.custom");export function promisify(e){if("function"!=typeof e)throw new TypeError('The "original" argument must be of type Function');if(d&&e[d]){const t=e[d];if("function"!=typeof t)throw new TypeError('The "util.promisify.custom" argument must be of type Function');return Object.defineProperty(t,d,{value:t,enumerable:!1,writable:!1,configurable:!0}),t}function t(){let t,n;const r=new Promise((function(e,r){t=e,n=r})),i=[];for(let e=0;e<arguments.length;e++)i.push(arguments[e]);i.push((function(e,r){e?n(e):t(r)}));try{e.apply(this,i)}catch(e){n(e)}return r}return Object.setPrototypeOf(t,Object.getPrototypeOf(e)),d&&Object.defineProperty(t,d,{value:t,enumerable:!1,writable:!1,configurable:!0}),Object.defineProperties(t,Object.getOwnPropertyDescriptors(e))}function b(e,t){if(!e){const t=new Error("Promise was rejected with a falsy value");t.reason=e,e=t}return t(e)}promisify.custom=d;export function callbackify(e){if("function"!=typeof e)throw new TypeError('The "original" argument must be of type Function');function n(){const n=[];for(let e=0;e<arguments.length;e++)n.push(arguments[e]);const r=n.pop();if("function"!=typeof r)throw new TypeError("The last argument must be of type Function");const i=this,o=function(){return r.apply(i,arguments)};e.apply(this,n).then((function(e){t.nextTick(o.bind(null,null,e))}),(function(e){t.nextTick(b.bind(null,e,o))}))}return Object.setPrototypeOf(n,Object.getPrototypeOf(e)),Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)),n}
✄
import{Buffer as t}from"buffer";import e from"path";import n from"process";import r from"stream";const i=at((function(){const t=SystemFunction,e=NativeFunction;return ot([["CreateFileW",t,"pointer",["pointer","uint","uint","pointer","uint","uint","pointer"]],["DeleteFileW",t,"uint",["pointer"]],["GetFileSizeEx",t,"uint",["pointer","pointer"]],["ReadFile",t,"uint",["pointer","pointer","uint","pointer","pointer"]],["RemoveDirectoryW",t,"uint",["pointer"]],["CloseHandle",e,"uint",["pointer"]],["FindFirstFileW",t,"pointer",["pointer","pointer"]],["FindNextFileW",e,"uint",["pointer","pointer"]],["FindClose",e,"uint",["pointer"]],["GetFileAttributesExW",t,"uint",["pointer","uint","pointer"]],["GetFinalPathNameByHandleW",t,"uint",["pointer","pointer","uint","uint"]],["FormatMessageW",e,"uint",["uint","pointer","uint","uint","pointer","uint","pointer"]]])})),o=at((function(){const t=SystemFunction,e=NativeFunction;return ot([["open",t,"int",["pointer","int","...","int"]],["close",e,"int",["int"]],["lseek",e,rt,["int",rt,"int"]],["read",t,et,["int","pointer",nt]],["opendir",t,"pointer",["pointer"]],["opendir$INODE64",t,"pointer",["pointer"]],["closedir",e,"int",["pointer"]],["readdir",e,"pointer",["pointer"]],["readdir$INODE64",e,"pointer",["pointer"]],["readlink",t,et,["pointer","pointer",nt]],["rmdir",t,"int",["pointer"]],["unlink",t,"int",["pointer"]],["stat",t,"int",["pointer","pointer"]],["stat64",t,"int",["pointer","pointer"]],["__xstat64",t,"int",["int","pointer","pointer"],it],["lstat",t,"int",["pointer","pointer"]],["lstat64",t,"int",["pointer","pointer"]],["__lxstat64",t,"int",["int","pointer","pointer"],it],["strerror",e,"pointer",["int"]]])})),l=Process.platform,s=Process.pointerSize,a="windows"===l,u={...{S_IFMT:61440,S_IFREG:32768,S_IFDIR:16384,S_IFCHR:8192,S_IFBLK:24576,S_IFIFO:4096,S_IFLNK:40960,S_IFSOCK:49152,S_IRWXU:448,S_IRUSR:256,S_IWUSR:128,S_IXUSR:64,S_IRWXG:56,S_IRGRP:32,S_IWGRP:16,S_IXGRP:8,S_IRWXO:7,S_IROTH:4,S_IWOTH:2,S_IXOTH:1,DT_UNKNOWN:0,DT_FIFO:1,DT_CHR:2,DT_DIR:4,DT_BLK:6,DT_REG:8,DT_LNK:10,DT_SOCK:12,DT_WHT:14},...{darwin:{O_RDONLY:0,O_WRONLY:1,O_RDWR:2,O_CREAT:512,O_EXCL:2048,O_NOCTTY:131072,O_TRUNC:1024,O_APPEND:8,O_DIRECTORY:1048576,O_NOFOLLOW:256,O_SYNC:128,O_DSYNC:4194304,O_SYMLINK:2097152,O_NONBLOCK:4},linux:{O_RDONLY:0,O_WRONLY:1,O_RDWR:2,O_CREAT:64,O_EXCL:128,O_NOCTTY:256,O_TRUNC:512,O_APPEND:1024,O_DIRECTORY:65536,O_NOATIME:262144,O_NOFOLLOW:131072,O_SYNC:1052672,O_DSYNC:4096,O_DIRECT:16384,O_NONBLOCK:2048}}[l]};class c extends r.Readable{#t=null;#e=null;constructor(t){if(super({highWaterMark:4194304}),a){const e=i().CreateFileW(Memory.allocUtf16String(t),2147483648,1,NULL,3,1073741824,NULL),r=e.value;if(r.equals(-1))return void n.nextTick((()=>{this.destroy(V(e.lastError))}));this.#t=new Win32InputStream(r,{autoClose:!0})}else{const e=o().open(Memory.allocUtf8String(t),u.O_RDONLY,0),r=e.value;if(-1===r)return void n.nextTick((()=>{this.destroy(Z(e.errno))}));this.#t=new UnixInputStream(r,{autoClose:!0})}}_destroy(t,e){this.#t?.close(),this.#t=null,e(t)}_read(e){null===this.#e&&(this.#e=this.#t.read(e).then((n=>{this.#e=null,0!==n.byteLength?this.push(t.from(n))&&this._read(e):this.push(null)})).catch((t=>{this.#e=null,this.destroy(t)})))}}class d extends r.Writable{#n=null;#r=null;constructor(t){if(super({highWaterMark:4194304}),a){const e=i().CreateFileW(Memory.allocUtf16String(t),1073741824,0,NULL,2,1073741952,NULL),r=e.value;if(r.equals(-1))return void n.nextTick((()=>{this.destroy(V(e.lastError))}));this.#n=new Win32OutputStream(r,{autoClose:!0})}else{const e=o(),r=Memory.allocUtf8String(t),i=u.O_WRONLY|u.O_CREAT|u.O_TRUNC,l=u.S_IRUSR|u.S_IWUSR|u.S_IRGRP|u.S_IROTH,s=e.open(r,i,l),a=s.value;if(-1===a)return void n.nextTick((()=>{this.destroy(Z(s.errno))}));this.#n=new UnixOutputStream(a,{autoClose:!0})}}_destroy(t,e){this.#n?.close(),this.#n=null,e(t)}_write(t,e,n){null===this.#r&&(this.#r=this.#n.writeAll(t).then((t=>{this.#r=null,n()})).catch((t=>{this.#r=null,n(t)})))}}const m={enumerateDirectoryEntries(t,e){f(t+"\\*",e)},readFileSync(t,e={}){"string"==typeof e&&(e={encoding:e});const{encoding:n=null}=e,{CreateFileW:r,GetFileSizeEx:o,ReadFile:l,CloseHandle:s}=i(),a=r(Memory.allocUtf16String(t),2147483648,1,NULL,3,0,NULL),u=a.value;u.equals(-1)&&J(a.lastError);try{const t=Memory.alloc(8),e=t,r=o(u,e);0===r.value&&J(r.lastError);const i=e.readU64().valueOf(),a=Memory.alloc(i),c=t,d=l(u,a,i,c,NULL);0===d.value&&J(d.lastError);if(c.readU32()!==i)throw new Error("Short read");return U(a,i,n)}finally{s(u)}},readlinkSync(t){const{CreateFileW:e,GetFinalPathNameByHandleW:n,CloseHandle:r}=i(),o=e(Memory.allocUtf16String(t),0,7,NULL,3,33554432,NULL),l=o.value;l.equals(-1)&&J(o.lastError);try{let t=256;for(;;){const e=Memory.alloc(2*t),{value:r,lastError:i}=n(l,e,t,0);if(0===r&&J(i),8!==i)return e.readUtf16String().substring(4);t*=2}}finally{r(l)}},rmdirSync(t){const e=i().RemoveDirectoryW(Memory.allocUtf16String(t));0===e.value&&J(e.lastError)},unlinkSync(t){const e=i().DeleteFileW(Memory.allocUtf16String(t));0===e.value&&J(e.lastError)},statSync(t){const e=m.lstatSync(t);if(!e.isSymbolicLink())return e;const n=m.readlinkSync(t);return m.lstatSync(n)},lstatSync(t){const e=Memory.alloc(36),n=i().GetFileAttributesExW(Memory.allocUtf16String(t),0,e);if(0===n.value){if(32===n.lastError){let e;return f(t,(t=>{e=Memory.dup(t,36)})),Y(t,e)}J(n.lastError)}return Y(t,e)}};function f(t,e){const{FindFirstFileW:n,FindNextFileW:r,FindClose:o}=i(),l=Memory.alloc(592),s=n(Memory.allocUtf16String(t),l),a=s.value;a.equals(-1)&&J(s.lastError);try{do{e(l)}while(0!==r(a,l))}finally{o(a)}}const S={enumerateDirectoryEntries(t,e){const{opendir:n,opendir$INODE64:r,closedir:i,readdir:l,readdir$INODE64:s}=o(),a=s||l,u=(r||n)(Memory.allocUtf8String(t)),c=u.value;c.isNull()&&Q(u.errno);try{let t;for(;!(t=a(c)).isNull();)e(t)}finally{i(c)}},readFileSync(t,e={}){"string"==typeof e&&(e={encoding:e});const{encoding:n=null}=e,{open:r,close:i,lseek:l,read:s}=o(),a=r(Memory.allocUtf8String(t),u.O_RDONLY,0),c=a.value;-1===c&&Q(a.errno);try{const t=l(c,0,2).valueOf();l(c,0,0);const e=Memory.alloc(t);let r,o,a;do{r=s(c,e,t),o=r.value.valueOf(),a=-1===o}while(a&&4===r.errno);if(a&&Q(r.errno),o!==t.valueOf())throw new Error("Short read");return U(e,t,n)}finally{i(c)}},readlinkSync(t){const e=Memory.allocUtf8String(t),n=S.lstatSync(t).size.valueOf(),r=Memory.alloc(n),i=o().readlink(e,r,n),l=i.value.valueOf();return-1===l&&Q(i.errno),r.readUtf8String(l)},rmdirSync(t){const e=o().rmdir(Memory.allocUtf8String(t));-1===e.value&&Q(e.errno)},unlinkSync(t){const e=o().unlink(Memory.allocUtf8String(t));-1===e.value&&Q(e.errno)},statSync:t=>y(z()._stat,t),lstatSync:t=>y(z()._lstat,t)};function p(e,n,r={}){"string"==typeof r&&(r={encoding:r});const{encoding:i=null}=r;let o;o="string"==typeof n?null===i||_(i)?n:t.from(n,i).buffer:n.buffer;const l=new File(e,"wb");try{l.write(o)}finally{l.close()}}function y(t,e){const n=Memory.alloc(T),r=t(Memory.allocUtf8String(e),n);return 0!==r.value&&Q(r.errno),Y(e,n)}function U(e,n,r){if(_(r))return e.readUtf8String(n);const i=t.from(e.readByteArray(n));return null!==r?i.toString(r):i}function _(t){return"utf8"===t||"utf-8"===t}const O=a?m:S,{enumerateDirectoryEntries:h,readFileSync:v,readlinkSync:g,rmdirSync:R,unlinkSync:k,statSync:w,lstatSync:F}=O,N={windows:{d_name:[44,"Utf16String"],d_type:[0,B],atime:[12,H],mtime:[20,H],ctime:[4,H],size:[28,A]},"linux-32":{d_name:[11,"Utf8String"],d_type:[10,"U8"]},"linux-64":{d_name:[19,"Utf8String"],d_type:[18,"U8"]},"darwin-32":{d_name:[21,"Utf8String"],d_type:[20,"U8"]},"darwin-64":{d_name:[21,"Utf8String"],d_type:[20,"U8"]}},b=a?N.windows:N[`${l}-${8*s}`];function I(t){const e=[];return h(t,(t=>{const n=L(t,"d_name");e.push(n)})),e}function x(t){const n=Object.keys(b).filter((t=>!t.startsWith("d_"))),r=[];return h(t,(i=>{const o=L(i,"d_name"),l=L(i,"d_type",e.join(t,o)),s={};for(const t of n)s[t]=L(i,t);r.push({name:o,type:l,...s})})),r}function L(t,e,...n){const r=b[e],[i,o]=r,l=("string"==typeof o?NativePointer.prototype["read"+o]:o).call(t.add(i),...n);return l instanceof Int64||l instanceof UInt64?l.valueOf():l}const M=new Set(["dev","mode","nlink","uid","gid","rdev","blksize","ino","size","blocks","atimeMs","mtimeMs","ctimeMs","birthtimeMs","atime","mtime","ctime","birthtime"]),W={size:88,fields:{dev:[0,"U64"],mode:[16,"U32"],nlink:[20,"U32"],ino:[12,"U32"],uid:[24,"U32"],gid:[28,"U32"],rdev:[32,"U64"],atime:[56,K],mtime:[64,K],ctime:[72,K],size:[44,"S32"],blocks:[52,"S32"],blksize:[48,"S32"]}},E={windows:{size:36,fields:{dev:[0,$],mode:[0,B],nlink:[0,j],ino:[0,$],uid:[0,$],gid:[0,$],rdev:[0,$],atime:[12,H],mtime:[20,H],ctime:[20,H],birthtime:[4,H],size:[28,A],blocks:[28,A],blksize:[0,j]}},"darwin-32":{size:108,fields:{dev:[0,"S32"],mode:[4,"U16"],nlink:[6,"U16"],ino:[8,"U64"],uid:[16,"U32"],gid:[20,"U32"],rdev:[24,"S32"],atime:[28,K],mtime:[36,K],ctime:[44,K],birthtime:[52,K],size:[60,"S64"],blocks:[68,"S64"],blksize:[76,"S32"]}},"darwin-64":{size:144,fields:{dev:[0,"S32"],mode:[4,"U16"],nlink:[6,"U16"],ino:[8,"U64"],uid:[16,"U32"],gid:[20,"U32"],rdev:[24,"S32"],atime:[32,X],mtime:[48,X],ctime:[64,X],birthtime:[80,X],size:[96,"S64"],blocks:[104,"S64"],blksize:[112,"S32"]}},"linux-ia32":W,"linux-ia32-stat64":{size:96,fields:{dev:[0,"U64"],mode:[16,"U32"],nlink:[20,"U32"],ino:[88,"U64"],uid:[24,"U32"],gid:[28,"U32"],rdev:[32,"U64"],atime:[64,K],mtime:[72,K],ctime:[80,K],size:[44,"S64"],blocks:[56,"S64"],blksize:[52,"S32"]}},"linux-x64":{size:144,fields:{dev:[0,"U64"],mode:[24,"U32"],nlink:[16,"U64"],ino:[8,"U64"],uid:[28,"U32"],gid:[32,"U32"],rdev:[40,"U64"],atime:[72,X],mtime:[88,X],ctime:[104,X],size:[48,"S64"],blocks:[64,"S64"],blksize:[56,"S64"]}},"linux-arm":W,"linux-arm-stat64":{size:104,fields:{dev:[0,"U64"],mode:[16,"U32"],nlink:[20,"U32"],ino:[96,"U64"],uid:[24,"U32"],gid:[28,"U32"],rdev:[32,"U64"],atime:[72,K],mtime:[80,K],ctime:[88,K],size:[48,"S64"],blocks:[64,"S64"],blksize:[56,"S32"]}},"linux-arm64":{size:128,fields:{dev:[0,"U64"],mode:[16,"U32"],nlink:[20,"U32"],ino:[8,"U64"],uid:[24,"U32"],gid:[28,"U32"],rdev:[32,"U64"],atime:[72,X],mtime:[88,X],ctime:[104,X],size:[48,"S64"],blocks:[64,"S64"],blksize:[56,"S32"]}}},D={ia32:3,x64:1,arm:3,arm64:0,mips:3}[Process.arch];let C=null;const T=256;function z(){if(null!==C)return C;let t;if(a)t=E.windows;else{const e=o(),n=e.stat64??e.__xstat64;let r;if("darwin"===l?r="darwin-"+8*s:(r=`${l}-${Process.arch}`,4===s&&void 0!==n&&(r+="-stat64")),t=E[r],void 0===t)throw new Error("Current OS/arch combo is not yet supported; please open a PR");t._stat=n??e.stat,t._lstat=e.lstat64??e.__lxstat64??e.lstat}return C=t,t}class P{dev;mode;nlink;uid;gid;rdev;blksize;ino;size;blocks;atimeMs;mtimeMs;ctimeMs;birthtimeMs;atime;mtime;ctime;birthtime;buffer;isFile(){return 32768==(61440&this.mode)}isDirectory(){return 16384==(61440&this.mode)}isCharacterDevice(){return 8192==(61440&this.mode)}isBlockDevice(){return 24576==(61440&this.mode)}isFIFO(){return 4096==(61440&this.mode)}isSymbolicLink(){return 40960==(61440&this.mode)}isSocket(){return 49152==(61440&this.mode)}}function Y(t,e){return new Proxy(new P,{has:(t,e)=>"symbol"==typeof e?e in t:q(e),get(n,r,i){switch(r){case"prototype":return;case"constructor":case"toString":return n[r];case"hasOwnProperty":return q;case"valueOf":return i;case"buffer":return e;default:{let e;return"symbol"==typeof r||void 0!==(e=n[r])?e:G.call(i,r,t)}}},set:(t,e,n,r)=>!1,ownKeys:t=>Array.from(M),getOwnPropertyDescriptor:(t,e)=>({writable:!1,configurable:!0,enumerable:!0})})}function q(t){return M.has(t)}function G(t,e){let n=z().fields[t];if(void 0===n){if("birthtime"===t)return G.call(this,"ctime",e);const n=t.lastIndexOf("Ms");return n===t.length-2?G.call(this,t.substring(0,n),e).getTime():void 0}const[r,i]=n,o=("string"==typeof i?NativePointer.prototype["read"+i]:i).call(this.buffer.add(r),e);return o instanceof Int64||o instanceof UInt64?o.valueOf():o}function B(t){const e=this.readU32();let n=!1;0!=(1024&e)&&f(t,(t=>{const e=t.add(36).readU32();n=2684354563===e||2684354572===e}));const r=0!=(16&e);let i;return i=n?40960:r?16384:32768,i|=r?493:420,i}function H(){const t=BigInt(this.readU64().toString()).valueOf();return new Date(parseInt((t/10000n-11644473600000n).toString()))}function A(){const t=this.readU32(),e=this.add(4).readU32();return uint64(t).shl(32).or(e)}function K(){const t=this.readU32(),e=this.add(4).readU32();return new Date(1e3*t+e/1e6)}function X(){const t=this.readU64().valueOf(),e=this.add(8).readU64().valueOf();return new Date(1e3*t+e/1e6)}function $(){return 0}function j(){return 1}function J(t){throw V(t)}function Q(t){throw Z(t)}function V(t){const e=Memory.alloc(512);return i().FormatMessageW(4608,NULL,t,0,e,256,NULL),new Error(e.readUtf16String())}function Z(t){const e=o().strerror(t).readUtf8String();return new Error(e)}function tt(t){return function(...e){const r=e.length-1,i=e.slice(0,r),o=e[r];n.nextTick((function(){try{const e=t(...i);o(null,e)}catch(t){o(t)}}))}}const et=8===s?"int64":"int32",nt="u"+et,rt="darwin"===l||8===s?"int64":"int32";function it(t,e,n){return t(D,e,n)}function ot(t){return t.reduce(((t,e)=>(function(t,e){const[n]=e;Object.defineProperty(t,n,{configurable:!0,get(){const[,r,i,o,l]=e;let s=null;const u=a?lt.findExportByName(n):Module.findGlobalExportByName(n);return null!==u&&(s=new r(u,i,o,st)),void 0!==l&&(s=l.bind(null,s)),Object.defineProperty(t,n,{value:s}),s}})}(t,e),t)),{})}const lt=a?Process.getModuleByName("kernel32.dll"):null,st=a&&4===s?{abi:"stdcall"}:{};export function createReadStream(t){return new c(t)}export function createWriteStream(t){return new d(t)}export const readdir=tt(I);export const readFile=tt(v);export const writeFile=tt(p);export const readlink=tt(g);export const rmdir=tt(R);export const unlink=tt(k);export const stat=tt(w);export const lstat=tt(F);function at(t){let e,n=!1;return function(...r){return n||(e=t(...r),n=!0),e}}export{u as constants,I as readdirSync,x as list,v as readFileSync,p as writeFileSync,g as readlinkSync,R as rmdirSync,k as unlinkSync,w as statSync,F as lstatSync,P as Stats};export default{constants:u,createReadStream,createWriteStream,readdir,readdirSync:I,list:x,readFile,readFileSync:v,writeFile,writeFileSync:p,readlink,readlinkSync:g,rmdir,rmdirSync:R,unlink,unlinkSync:k,stat,statSync:w,lstat,lstatSync:F,Stats:P};
✄
import e from"events";import{Readable as t,Writable as s}from"stream";export class Controller{constructor(){this.events=new e,this.sources=new Map,this.nextEndpointId=1,this.requests=new Map,this.nextRequestId=1,this.onCreate=e=>{const t=e.endpoint,s=new n(t);this.sources.set(t.id,s),this.events.emit("stream",s)},this.onFinish=e=>{const t=e.endpoint.id,s=this.sources.get(t);if(void 0===s)throw new Error("Invalid endpoint ID");this.sources.delete(t),s.push(null)},this.onWrite=(e,t)=>{const s=e.endpoint.id,n=this.sources.get(s);if(void 0===n)throw new Error("Invalid endpoint ID");if(null===t)throw new Error("Invalid request: missing data");return n.deliver(t)},this.handlers={".create":this.onCreate,".finish":this.onFinish,".write":this.onWrite}}open(e,t={}){const s={id:this.nextEndpointId++,label:e,details:t};return new i(this,s)}receive(e){const t=e.stanza,{id:s,name:n,payload:i}=t,r=n[0];if("."===r)this.onRequest(s,n,i,e.data);else{if("+"!==r)throw new Error("Unknown stanza: "+n);this.onNotification(s,n,i)}}_request(e,t,s){return new Promise(((n,i)=>{const r=this.nextRequestId++;this.requests.set(r,{resolve:n,reject:i});const o={id:r,name:e,payload:t};this.events.emit("send",{stanza:o,data:s})}))}onRequest(e,t,s,n){const i=this.handlers[t];if(void 0===i)throw new Error(`Invalid request: ${t}`);let r;try{r=i(s,n)}catch(t){return void this.reject(e,t)}r instanceof Promise?r.then((t=>this.resolve(e,t))).catch((t=>this.reject(e,t))):this.resolve(e,r)}resolve(e,t){const s={id:e,name:"+result",payload:t};this.events.emit("send",{stanza:s,data:null})}reject(e,t){const s={id:e,name:"+error",payload:{message:t.toString()}};this.events.emit("send",{stanza:s,data:null})}onNotification(e,t,s){const n=this.requests.get(e);if(void 0===n)throw new Error("Invalid request ID");if(this.requests.delete(e),"+result"===t)n.resolve(s);else{if("+error"!==t)throw new Error("Unknown notification: "+t);{const e=s;n.reject(new Error(e.message))}}}}export default Controller;class n extends t{constructor({label:e,details:t}){super(),this.onReadComplete=null,this.delivery=null,this.label=e,this.details=t}_read(e){null===this.onReadComplete&&(this.onReadComplete=t=>(this.onReadComplete=null,0===t.length?(this.push(null),!1):(this.push(t)&&this._read(e),!0)),this.tryComplete())}deliver(e){return new Promise(((t,s)=>{if(null!==this.delivery)throw new Error("Protocol violation");this.delivery={chunk:e,resolve:t,reject:s},this.tryComplete()}))}tryComplete(){const{onReadComplete:e,delivery:t}=this;null!==e&&null!==t&&(this.onReadComplete=null,this.delivery=null,e(t.chunk)?t.resolve():t.reject(new Error("Stream closed")))}}class i extends s{constructor(e,t){super(),this.controller=e,this.endpoint=t,this.controller._request(".create",{endpoint:this.endpoint},null),this.once("finish",this._onFinish.bind(this))}_write(e,t,s){this.controller._request(".write",{endpoint:this.endpoint},e).then((e=>s())).catch((e=>s(e)))}_onFinish(){this.controller._request(".finish",{endpoint:this.endpoint},null)}}