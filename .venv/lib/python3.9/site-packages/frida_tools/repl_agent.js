📦
1528 /agent.js
✄
const e=new class{#e=new Map;registerQuickCommand(e,n){this.#e.set(e,n)}unregisterQuickCommand(e){this.#e.delete(e)}_invokeQuickCommand(e){const n=e[0],o=this.#e.get(n);if(void 0!==o){const{minArity:i,onInvoke:r}=o;if(e.length-1<i)throw Error(`${n} needs at least ${i} arg${1===i?"":"s"}`);return r(...e.slice(1))}throw Error(`Unknown command ${n}`)}};function n(e){Object.defineProperty(globalThis,e,{enumerable:!0,configurable:!0,get:()=>function(e){let n;return send({type:"frida:load-bridge",name:e}),recv("frida:bridge-loaded",(o=>{n=Script.evaluate(`/frida/bridges/${o.filename}`,"(function () { "+[o.source,`Object.defineProperty(globalThis, '${e}', { value: bridge });`,"return bridge;"].join("\n")+" })();")})).wait(),n}(e)})}globalThis.REPL=e,globalThis.cm=null,globalThis.cs={},n("ObjC"),n("Swift"),n("Java");const o={fridaEvaluateExpression:e=>i((()=>globalThis.eval(e))),fridaEvaluateQuickCommand:n=>i((()=>e._invokeQuickCommand(n))),fridaLoadCmodule(e,n){const o=globalThis.cs;void 0===o._frida_log&&(o._frida_log=new NativeCallback(r,"void",["pointer"]));let i=e;null===e&&recv("frida:cmodule-payload",((e,n)=>{i=n})),globalThis.cm=new CModule(i,o,{toolchain:n})}};function i(e){try{const n=e();if(n instanceof ArrayBuffer)return n;return[null===n?"null":typeof n,n]}catch(e){const n=e;return["error",{name:n.name,message:n.message,stack:n.stack}]}}function r(e){const n=e.readUtf8String();console.log(n)}Object.defineProperty(rpc,"exports",{get:()=>o,set(e){for(const[n,i]of Object.entries(e))o[n]=i}});export{};