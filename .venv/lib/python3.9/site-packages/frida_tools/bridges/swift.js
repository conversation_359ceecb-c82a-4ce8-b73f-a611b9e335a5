var bridge=function(){const t=["pointer","pointer"];let e=null,s=null;function n(){if("arm64"!==Process.arch||"darwin"!==Process.platform)throw new Error("Only arm64(e) Darwin is currently supported");if(null!==e)return e;e=i([{module:"libswiftCore.dylib",functions:{swift_demangle:["pointer",["pointer","size_t","pointer","pointer","int32"]]}}]);const t=i([{module:"libswiftCore.dylib",functions:{swift_allocBox:[["pointer","pointer"],["pointer"]]}}]);return e=Object.assign(e,t),e}function r(){if(null!==s)return s;if(Process.getModuleByName("CoreFoundation").ensureInitialized(),null===Process.findModuleByName("CoreSymbolication"))try{Module.load("/System/Library/PrivateFrameworks/CoreSymbolication.framework/CoreSymbolication")}catch(t){Module.load("/System/Library/PrivateFrameworks/CoreSymbolication.framework/Versions/A/CoreSymbolication")}return s=i([{module:"libmacho.dylib",functions:{getsectiondata:["pointer",["pointer","pointer","pointer","pointer"]]}},{module:"CoreSymbolication",functions:{CSSymbolicatorCreateWithPid:[t,["int"]],CSSymbolicatorCreateWithTask:[t,["uint"]],CSSymbolicatorGetSymbolWithAddressAtTime:[t,[t,"pointer","uint64"]],CSIsNull:["bool",[t]],CSSymbolGetMangledName:["pointer",[t]],CSRelease:["void",[t]]}},{module:"libsystem_kernel.dylib",functions:{mach_task_self:["uint",[]]}}]),s}function i(t){const e={};for(const s of t){const t=Process.getModuleByName(s.module);t.ensureInitialized();for(const[n,[r,i]]of Object.entries(s.functions)){const s=t.getExportByName(n);e[n]=new NativeFunction(s,r,i)}}return e}var a,o,l,c,u,d,h,f,m;!function(t){t[t.AlignmentMask=255]="AlignmentMask",t[t.IsNonPOD=65536]="IsNonPOD",t[t.IsNonInline=131072]="IsNonInline",t[t.HasSpareBits=524288]="HasSpareBits",t[t.IsNonBitwiseTakable=1048576]="IsNonBitwiseTakable",t[t.HasEnumWitnesses=2097152]="HasEnumWitnesses",t[t.Incomplete=4194304]="Incomplete"}(a||(a={}));class p{data;constructor(t){this.data=t}get isInlineStorage(){return!(this.data&a.IsNonInline)}get isPOD(){return!(this.data&a.IsNonPOD)}get isBitwiseTakable(){return!(this.data&a.IsNonBitwiseTakable)}getAlignmentMask(){return this.data&a.AlignmentMask}}!function(t){t[t.Class=0]="Class",t[t.Struct=512]="Struct",t[t.Enum=513]="Enum",t[t.LastEnumerated=2047]="LastEnumerated"}(o||(o={})),function(t){t[t.Module=0]="Module",t[t.Extension=1]="Extension",t[t.Anonymous=2]="Anonymous",t[t.Protocol=3]="Protocol",t[t.OpaqueType=4]="OpaqueType",t[t.TypeFirst=16]="TypeFirst",t[t.Class=16]="Class",t[t.Struct=17]="Struct",t[t.Enum=18]="Enum"}(l||(l={})),function(t){t[t.MetadataInitialization=0]="MetadataInitialization",t[t.MetadataInitialization_width=2]="MetadataInitialization_width",t[t.Class_ResilientSuperclassReferenceKind=9]="Class_ResilientSuperclassReferenceKind",t[t.Class_HasResilientSuperclass=13]="Class_HasResilientSuperclass",t[t.Class_HasOverrideTable=14]="Class_HasOverrideTable",t[t.Class_HasVTable=15]="Class_HasVTable"}(c||(c={})),function(t){t[t.NoMetadataInitialization=0]="NoMetadataInitialization",t[t.SingletonMetadataInitialization=1]="SingletonMetadataInitialization",t[t.ForeignMetadataInitialization=2]="ForeignMetadataInitialization"}(u||(u={}));class g{value;constructor(t){this.value=t}class_hasVTable(){return!!(this.value&1<<c.Class_HasVTable)}class_hasResilientSuperClass(){return!!(this.value&1<<c.Class_HasResilientSuperclass)}class_hasOverrideTable(){return!!(this.value&1<<c.Class_HasOverrideTable)}getMetadataInitialization(){return t=this.value,e=c.MetadataInitialization,s=c.MetadataInitialization_width,t>>>e&~(-1<<s);var t,e,s}hasSingletonMetadataInitialization(){return this.getMetadataInitialization()===u.SingletonMetadataInitialization}hasForeignMetadataInitialization(){return this.getMetadataInitialization()===u.ForeignMetadataInitialization}}!function(t){t[t.Method=0]="Method",t[t.Init=1]="Init",t[t.Getter=2]="Getter",t[t.Setter=3]="Setter",t[t.ModifyCoroutine=4]="ModifyCoroutine",t[t.ReadCoroutine=5]="ReadCoroutine"}(d||(d={}));class F{value;static KindMask=15;constructor(t){this.value=t}getKind(){return this.value&F.KindMask}}!function(t){t[t.DirectTypeDescriptor=0]="DirectTypeDescriptor",t[t.IndirectTypeDescriptor=1]="IndirectTypeDescriptor",t[t.DirectObjCClassName=2]="DirectObjCClassName",t[t.IndirectObjCClass=3]="IndirectObjCClass"}(h||(h={})),function(t){t[t.TypeMetadataKindMask=56]="TypeMetadataKindMask",t[t.TypeMetadataKindShift=3]="TypeMetadataKindShift"}(f||(f={}));class S{value;constructor(t){this.value=t}getTypeReferenceKind(){return(this.value&f.TypeMetadataKindMask)>>f.TypeMetadataKindShift}}class T{static Class=!1;static Any=!0}!function(t){t[t.HasClassConstratint=0]="HasClassConstratint",t[t.HasClassConstratint_width=1]="HasClassConstratint_width"}(m||(m={}));class O{bits;constructor(t){this.bits=t}getClassConstraint(){return!!(this.bits&1<<m.HasClassConstratint)}}class E{handle;offset;static sizeOf=4;static From(t){const e=t.readS32();return 0===e?null:new E(t,e)}constructor(t,e){this.handle=t,this.offset=e}get(){return this.handle.add(this.offset)}}class y{handle;offset;static From(t){const e=t.readS32();return 0===e?null:new y(t,e)}constructor(t,e){this.handle=t,this.offset=e}get(){const t=this.handle.add(-2&this.offset);return 1&this.offset?t.readPointer():t}}class w{handle;static SIZEOF=2*Process.pointerSize;metadata;refCounts;constructor(t){this.handle=t}getMetadata(t){return new t(this.handle.readPointer())}}class C{object;buffer;constructor(t){this.object=new w(t[0]),this.buffer=t[1]}}class _{privateData;constructor(t){this.privateData=t}}class b{handle;static OFFSETOF_KIND=0;#t;constructor(t){this.handle=t,this.#t=this.handle.add(b.OFFSETOF_KIND).readU32()}getKind(){return(t=this.#t)>o.LastEnumerated?o.Class:t;var t}isClassObject(){return this.getKind()==o.Class}getValueWitnesses(){const t=this.getKind();if(t!==o.Enum&&t!==o.Struct)throw new Error(`Kind does not have a VWT: ${t}`);const e=this.handle.sub(Process.pointerSize).readPointer();return new A(e)}getTypeLayout(){const t=this.getValueWitnesses();return{size:t.size,stride:t.stride,flags:t.flags.data,extraInhabitantCount:t.extraInhabitantCount}}vw_initializeWithCopy(t,e){return this.getValueWitnesses().initializeWithCopy(t,e,this.handle)}vw_getEnumTag(t){return this.getValueWitnesses().asEVWT().getEnumTag(t)}vw_destructiveInjectEnumTag(t,e){return this.getValueWitnesses().asEVWT().destructiveInjectEnumTag(t,e)}allocateBoxForExistentialIn(t){if(this.getValueWitnesses().isValueInline())return t.privateData;const e=n(),s=new C(e.swift_allocBox(this.handle));return t.privateData.writePointer(s.object.handle),s.buffer}getFullTypeName(){return this.getDescription().getFullTypeName()}static from(t){switch(new I(t).getKind()){case o.Class:return new v(t);case o.Struct:return new N(t);case o.Enum:return new P(t);default:throw new Error("Unknown metadata kind")}}toJSON(){return{handle:this.handle,name:this.getFullTypeName()}}}class I extends b{static OFFSETOF_DESCRIPTION=Process.pointerSize;#e;get description(){return void 0===this.#e&&(this.#e=this.handle.add(I.OFFSETOF_DESCRIPTION).readPointer()),this.#e}getDescription(){return new $(this.description)}}class v extends b{static OFFSTETOF_DESCRIPTION=8*Process.pointerSize;#e;get description(){return void 0===this.#e&&(this.#e=this.handle.add(v.OFFSTETOF_DESCRIPTION).readPointer()),this.#e}getDescription(){return new D(this.description)}}class N extends I{getDescription(){return new U(this.description)}}class P extends I{getDescription(){return new V(this.description)}}class A{handle;static OFFSETOF_INTIALIZE_WITH_COPY=16;static OFFSETOF_SIZE=64;static OFFSETOF_STRIDE=72;static OFFSETOF_FLAGS=80;static OFFSETOF_EXTRA_INHABITANT_COUNT=84;initializeWithCopy;size;stride;flags;extraInhabitantCount;constructor(t){this.handle=t;const e=this.handle.add(A.OFFSETOF_INTIALIZE_WITH_COPY).readPointer(),s=new NativeFunction(e,"pointer",["pointer","pointer","pointer"]);this.initializeWithCopy=(t,e,n)=>s(t,e,n),this.size=this.getSize(),this.stride=this.getStride(),this.flags=this.getFlags(),this.extraInhabitantCount=this.getExtraInhabitantCount()}isValueInline(){return this.flags.isInlineStorage}getSize(){return this.handle.add(A.OFFSETOF_SIZE).readU64().toNumber()}getStride(){return this.handle.add(A.OFFSETOF_STRIDE).readU64().toNumber()}getAlignmentMask(){return this.flags.getAlignmentMask()}getFlags(){const t=this.handle.add(A.OFFSETOF_FLAGS).readU32();return new p(t)}getExtraInhabitantCount(){return this.handle.add(A.OFFSETOF_EXTRA_INHABITANT_COUNT).readU32()}asEVWT(){return new M(this.handle)}}class M extends A{static OFFSETOF_GET_ENUM_TAG=88;static OFFSETOF_DESTRUCTIVE_INJECT_ENUM_TAG=104;getEnumTag;destructiveInjectEnumTag;constructor(t){super(t);let e=this.handle.add(M.OFFSETOF_GET_ENUM_TAG).readPointer();const s=new NativeFunction(e,"uint32",["pointer","pointer"]);this.getEnumTag=t=>s(t,this.handle),e=this.handle.add(M.OFFSETOF_DESTRUCTIVE_INJECT_ENUM_TAG).readPointer();const n=new NativeFunction(e,"void",["pointer","uint32","pointer"]);this.destructiveInjectEnumTag=(t,e)=>n(t,e,this.handle)}}class z{handle;static OFFSETOF_FLAGS=0;static OFFSETOF_PARENT=4;#s;#n;constructor(t){this.handle=t}get flags(){if(null!=this.#s)return this.#s;const t=this.handle.add(z.OFFSETOF_FLAGS).readU32();return new K(t)}get parent(){return void 0!==this.#n||(this.#n=y.From(this.handle.add(z.OFFSETOF_PARENT))),this.#n}isGeneric(){return this.flags.isGeneric()}getKind(){return this.flags.getKind()}getModuleContext(){let t=new R(this.parent.get());for(;t.flags.getKind()!==l.Module;)t=new R(t.parent.get());return t}}class R extends z{static OFFSETOF_NAME=8;#r;get name(){if(void 0!==this.#r)return this.#r;const t=this.handle.add(R.OFFSETOF_NAME),e=E.From(t).get();return this.#r=e.readCString(),this.#r}}class x extends z{static OFFSETOF_NAME=8;static OFFSETOF_ACCESS_FUNCTION_PTR=12;static OFFSETOF_FIELDS=16;#r;#i;#a;getTypeContextDescriptorFlags(){return new g(this.flags.getKindSpecificFlags())}get name(){if(void 0!==this.#r)return this.#r;const t=E.From(this.handle.add(x.OFFSETOF_NAME)).get();return this.#r=t.readUtf8String(),this.#r}get accessFunctionPointer(){return void 0!==this.#i?this.#i:E.From(this.handle.add(x.OFFSETOF_ACCESS_FUNCTION_PTR)).get()}get fields(){return void 0!==this.#a?this.#a:E.From(this.handle.add(x.OFFSETOF_FIELDS))}isReflectable(){return null!==this.fields}getAccessFunction(){return new NativeFunction(this.accessFunctionPointer,"pointer",[])}getFullTypeName(){return`${this.getModuleContext().name}.${this.name}`}}class $ extends x{}class D extends x{static OFFSETOF_TARGET_VTABLE_DESCRIPTOR_HEADER=44;static OFFSETOF_METHOD_DESCRIPTORS=52;hasVTable(){return this.getTypeContextDescriptorFlags().class_hasVTable()}hasResilientSuperClass(){return this.getTypeContextDescriptorFlags().class_hasResilientSuperClass()}hasOverrideTable(){return this.getTypeContextDescriptorFlags().class_hasOverrideTable()}hasSingletonMetadataInitialization(){return this.getTypeContextDescriptorFlags().hasSingletonMetadataInitialization()}hasForeignMetadataInitialization(){return this.getTypeContextDescriptorFlags().hasForeignMetadataInitialization()}getVTableDescriptor(){if(!this.hasVTable())return null;const t=this.handle.add(D.OFFSETOF_TARGET_VTABLE_DESCRIPTOR_HEADER);return new L(t)}getMethodDescriptors(){const t=[];if(!this.hasVTable()||this.isGeneric()||this.hasResilientSuperClass()||this.hasOverrideTable()||this.hasSingletonMetadataInitialization()||this.hasForeignMetadataInitialization())return t;const e=this.getVTableDescriptor().vtableSize;let s=this.handle.add(D.OFFSETOF_METHOD_DESCRIPTORS);const n=s.add(e*k.sizeof);for(;!s.equals(n);s=s.add(k.sizeof)){const e=new k(s);null!==e.impl&&t.push(e)}return t}}class L{handle;static OFFSETOF_VTABLE_OFFSET=0;static OFFSETOF_VTABLE_SIZE=4;#o;constructor(t){this.handle=t}get vtableSize(){return void 0!==this.#o?this.#o:this.handle.add(L.OFFSETOF_VTABLE_SIZE).readU32()}}class k{handle;static OFFSETOF_FLAGS=0;static OFFSETOF_IMPL=4;static sizeof=8;#s;#l;constructor(t){this.handle=t}get flags(){if(void 0!==this.#s)return this.#s;const t=this.handle.add(k.OFFSETOF_FLAGS).readU32();return new F(t)}get impl(){if(void 0!==this.#l)return this.#l;const t=this.handle.add(k.OFFSETOF_IMPL);return E.From(t)}}class U extends x{static OFFSETOF_NUM_FIELDS=24;static OFFSETOF_FIELD_OFFSET_VECTOR_OFFSET=28;#c;#u;hasFieldOffsetVector(){return 0!==this.fieldOffsetVectorOffset}get numFields(){return void 0!==this.#c?this.#c:this.handle.add(U.OFFSETOF_NUM_FIELDS).readU32()}get fieldOffsetVectorOffset(){return void 0!==this.#u?this.#u:this.handle.add(U.OFFSETOF_FIELD_OFFSET_VECTOR_OFFSET).readU32()}}class V extends x{static OFFSETOF_NUM_PAYLOAD_CASES_AND_PAYLOAD_SIZE_OFFSET=20;static OFFSETOF_NUM_EMPTY_CASES=24;#d;#h;get numPayloadCasesAndPayloaadSizeOffset(){if(void 0===this.#d){const t=this.handle.add(V.OFFSETOF_NUM_PAYLOAD_CASES_AND_PAYLOAD_SIZE_OFFSET).readU32();this.#d=t}return this.#d}get numEmptyCases(){return void 0===this.#h&&(this.#h=this.handle.add(V.OFFSETOF_NUM_EMPTY_CASES).readU32()),this.#h}getNumPayloadCases(){return 16777215&this.numPayloadCasesAndPayloaadSizeOffset}getNumEmptyCases(){return this.numEmptyCases}getNumCases(){return this.getNumPayloadCases()+this.numEmptyCases}isPayloadTag(t){return this.getNumCases()>0&&t<this.getNumPayloadCases()}}class j extends z{static OFFSETOF_NAME=8;static OFFSETOF_NUM_REQUIREMENTS=16;#r;#f;constructor(t){super(t)}get name(){if(void 0===this.#r){const t=E.From(this.handle.add(j.OFFSETOF_NAME)).get();this.#r=t.readCString()}return this.#r}get numRequirements(){if(void 0===this.#f){const t=this.handle.add(j.OFFSETOF_NUM_REQUIREMENTS);this.#f=t.readU32()}return this.#f}getProtocolContextDescriptorFlags(){return new O(this.flags.getKindSpecificFlags())}getFullProtocolName(){return this.getModuleContext().name+"."+this.name}}class B{handle;constructor(t){this.handle=t}getTypeDescriptor(t){let e=null;switch(t){case h.DirectTypeDescriptor:e=E.From(this.handle).get();break;case h.IndirectTypeDescriptor:e=E.From(this.handle).get(),e=e.readPointer();case h.DirectObjCClassName:case h.IndirectObjCClass:}return e}}class W{handle;static OFFSETOF_PROTOTCOL=0;static OFFSETOF_TYPE_REF=4;static OFFSTEOF_WITNESS_TABLE_PATTERN=8;static OFFSETOF_FLAGS=12;static OFFSETOF_WITNESS_TABLE_PATTERN=16;#m;#p;#g;#s;constructor(t){this.handle=t}get protocol(){return void 0===this.#m&&(this.#m=y.From(this.handle.add(W.OFFSETOF_PROTOTCOL)).get()),this.#m}get typeRef(){if(void 0===this.#p){const t=this.handle.add(W.OFFSETOF_TYPE_REF);this.#p=new B(t)}return this.#p}get witnessTablePattern(){if(void 0===this.#g){const t=E.From(this.handle.add(W.OFFSTEOF_WITNESS_TABLE_PATTERN));this.#g=t?t.get():null}return this.#g}get flags(){if(void 0===this.#s){const t=this.handle.add(W.OFFSETOF_FLAGS);this.#s=new S(t.readU32())}return this.#s}getTypeKind(){return this.flags.getTypeReferenceKind()}getTypeDescriptor(){return this.typeRef.getTypeDescriptor(this.getTypeKind())}}class K{value;constructor(t){this.value=t}getKind(){return 31&this.value}isGeneric(){return!!(128&this.value)}getIntValue(){return this.value}getKindSpecificFlags(){return this.value>>>16&65535}}const G=new Map;let q=null;function H(t){const e=r(),s=e.CSSymbolicatorGetSymbolWithAddressAtTime(Q(),t,0x8000000000000000);if(e.CSIsNull(s))return;const n=e.CSSymbolGetMangledName(s).readCString();return null!==n?Z(n):void 0}function Z(t){if(!function(t){if(0==t.length)return!1;const e=["_T0","$S","_$S","$s","_$s"];for(const s of e)if(t.startsWith(s))return!0;return!1}(t))return;const e=G.get(t);if(void 0!==e)return e;const s=n();try{const e=Memory.allocUtf8String(t),n=s.swift_demangle(e,t.length,ptr(0),ptr(0),0).readUtf8String();return G.set(t,n),n}catch(t){return}}function J(t){const e=/(\w+): ([\w.]+)(?:, )*|\(([\w.]+)\)/g,s=/([a-zA-Z_]\w+)(<.+>)*\(.*\) -> ([\w.]+(?: & [\w.]+)*|\([\w.]*\))$/g.exec(t);if(null===s)throw new Error("Couldn't parse function with signature: "+t);const n=s[1],r=s[3]||"void";if(void 0===n)throw new Error("Couldn't parse function with signature: "+t);const i=[],a=[];let o;for(;null!==(o=e.exec(t));){const t=o[3];void 0!==t?(i.push(""),a.push(t)):(i.push(o[1]),a.push(o[2]))}if(i.length!==a.length)throw new Error("Couldn't parse function with signature: "+t);let l=n;return i.length>0&&(l+="$"+i.join("_")+"_"),{methodName:n,argNames:i,argTypeNames:a,retTypeName:r,jsSignature:l}}function Y(t){try{return J(t)}catch(t){return}}function X(t){const e=/(\w+).(getter|setter) : ([\w.]+)$/g.exec(t);if(null===e)throw new Error("Couldn't parse accessor signature "+t);const s=e[2];if("getter"!==s&&"setter"!==s)throw new Error("Couldn't parse accessor signature "+t);return{accessorType:s,memberName:e[1],memberTypeName:e[3]}}function Q(){if(null!==q)return q;const t=r();let e=t.CSSymbolicatorCreateWithPid(Process.id);if(t.CSIsNull(e)&&(e=t.CSSymbolicatorCreateWithTask(t.mach_task_self()),t.CSIsNull(e)))throw new Error("Failed to create symbolicator");return q=e,Script.bindWeak(q,et),e}function tt(t){const e=/protocol conformance descriptor for \S+ : \S+\.(\S+) in \S+/g.exec(t);return null===e?null:e[1]}function et(){r().CSRelease(q)}class st{handle;numWitnessTables;static INITIAL_SIZE=4*Process.pointerSize;static OFFSETOF={buffer:0,type:3*Process.pointerSize,wintessTable:4*Process.pointerSize};#F;#S;constructor(t,e){this.handle=t,this.numWitnessTables=e}static alloc(t){const e=st.INITIAL_SIZE+t*Process.pointerSize,s=Memory.alloc(e);return new st(s,t)}static makeFromRaw(t,e){const s=new st(t,e),n=t.add(st.OFFSETOF.type).readPointer(),r=new I(n);return s.#S=r.isClassObject()?new v(n):r,s}set type(t){this.handle.add(st.OFFSETOF.type).writePointer(t.handle),this.#S=t}get buffer(){return void 0===this.#F&&(this.#F=new _(this.handle)),this.#F}get type(){return this.#S}getWitnessTables(){return this.handle.add(st.OFFSETOF.wintessTable)}isValueInline(){return this.type.getValueWitnesses().isValueInline()}projectValue(){const t=this.type.getValueWitnesses();if(t.isValueInline())return this.buffer.privateData;const e=this.buffer.privateData.readPointer(),s=t.getAlignmentMask(),n=w.SIZEOF+s&~s;return e.add(n)}get sizeof(){return st.INITIAL_SIZE+this.numWitnessTables*Process.pointerSize}}class nt{handle;numWitnessTables;static INITIAL_SIZE=Process.pointerSize;static OFFSETOF={value:0,witnessTables:Process.pointerSize};#T;constructor(t,e){this.handle=t,this.numWitnessTables=e}static alloc(t){const e=nt.INITIAL_SIZE+t*Process.pointerSize,s=Memory.alloc(e);return new nt(s,t)}static makeFromRaw(t,e){const s=new nt(t,e);return s.#T=t.add(nt.OFFSETOF.value).readPointer(),s}get value(){return this.#T}set value(t){this.handle.add(nt.OFFSETOF.value).writePointer(t),this.#T=t}getWitnessTables(){return this.handle.add(nt.OFFSETOF.witnessTables)}get sizeof(){return nt.INITIAL_SIZE+this.numWitnessTables*Process.pointerSize}}const rt=new ModuleMap,it={},at={},ot=new Map;if("arm64"===Process.arch&&"darwin"===Process.platform){for(const t of rt.values()){for(const e of ht(t))at[e.getFullTypeName()]={descriptor:e,conformances:{}};for(const e of ft(t))it[e.getFullProtocolName()]=e}for(const t of rt.values())mt(t)}function lt(t){const e=at[t];if(void 0===e)throw new Error("Type not found: "+t);if(void 0!==e.metadata)return at[t].metadata;const s=e.descriptor.getAccessFunction().call(),n=b.from(s);return at[t].metadata=n,n}function ct(t,e){const s=at[t];if(void 0===s)throw new Error("Type not found: "+t);if(void 0!==s.metadata)return at[t].metadata;const n=new e(s.descriptor.getAccessFunction().call());return at[t].metadata=n,n}function ut(t){const e=at[t];if(void 0===e)throw new Error("Type not found: "+t);return e.conformances}function dt(t){const e=it[t];if(void 0===e)throw new Error(`Can't find protocol descriptor for: "${t}"`);return e}function ht(t){const e=[],s=function(t){return pt(t,"__swift5_types")}(t),n=s.size/E.sizeOf;for(let t=0;t<n;t++){const n=s.vmAddress.add(t*E.sizeOf),r=E.From(n).get(),i=new x(r);if(i.isGeneric())continue;let a;switch(i.getKind()){case l.Class:a=new D(r);break;case l.Struct:a=new U(r);break;case l.Enum:a=new V(r);break;default:continue}e.push(a)}return e}function ft(t){const e=[],s=function(t){return pt(t,"__swift5_protos")}(t),n=s.size/E.sizeOf;for(let t=0;t<n;t++){const n=s.vmAddress.add(t*E.sizeOf),r=E.From(n).get(),i=new j(r);e.push(i)}return e}function mt(t){const e=function(t){return pt(t,"__swift5_proto")}(t),s=e.size/E.sizeOf;for(let t=0;t<s;t++){const s=e.vmAddress.add(t*E.sizeOf),n=E.From(s).get(),r=new W(n),i=r.getTypeDescriptor(),a=new x(i);if(null===i||a.isGeneric()||a.getKind()===l.Protocol)continue;const o=at[a.getFullTypeName()];if(void 0!==o)if(r.protocol.isNull()){const t=H(n),e=tt(t);if(null===e){console.warn(`Failed to parse protocol name from conformance descriptor '${t}'. Please file a bug.`);continue}o.conformances[e]={protocol:null,witnessTable:null}}else{const t=new j(r.protocol);o.conformances[t.name]={protocol:t,witnessTable:r.witnessTablePattern}}}}function pt(t,e,s="__TEXT"){const n=t.base,i=Memory.allocUtf8String(s),a=Memory.allocUtf8String(e),o=Memory.alloc(Process.pointerSize);return{vmAddress:r().getsectiondata(n,i,a,o),size:o.readU32()}}function gt(t){if(null===rt.find(t))return;const e=t.toString(),s=ot.get(e);if(void 0!==s)return s;const n=H(t);return void 0!==n?(ot.set(e,n),n):void 0}function Ft(t){Array.isArray(t)||(t=[t]);const e=Process.pointerSize*t.length,s=Memory.alloc(e);for(let n=0,r=0;r<e;n++,r+=Process.pointerSize){const e=t[n],i=s.add(r);e instanceof NativePointer?i.writePointer(e):i.writeU64(e)}return s}function St(t,e){const s=[];for(let n=0;n<e;n+=8)s.push(t.add(n).readU64());return s}function Tt(t){return(t=t<8?8:t)/8}const Ot=4*Process.pointerSize;class Et{static pages;static currentSlot;static get currentPage(){return Et.pages[Et.pages.length-1]}static _initialize(){Et.pages=[Memory.alloc(Process.pageSize)],Et.currentSlot=Et.currentPage}static allocateTrampoline(t){void 0===Et.pages&&Et._initialize();let e=Et.currentPage;const s=e.add(Process.pageSize);Et.currentSlot.add(t).compare(s)>0&&(e=Memory.alloc(Process.pageSize),Et.pages.push(e));const n=Et.currentSlot;return Et.currentSlot=Et.currentSlot.add(t),n}}function yt(t,e,s,n,r){const i=s.map(t=>wt(t)),a=wt(e),l=new It(t,a,i,n).wrapper;return Object.assign(function(...t){const n=[];for(const[e,r]of t.entries()){const t=s[e];if("string"==typeof t||Array.isArray(t)){n.push(r);continue}if(t instanceof b){n.push(Ct(r));continue}const i=t,a=r.$metadata;let o;if(i.isClassOnly)o=nt.alloc(i.numProtocols),o.value=r.handle;else if(o=st.alloc(i.numProtocols),o.type=a,a.isClassObject())o.buffer.privateData.writePointer(r.handle);else{const t=a.allocateBoxForExistentialIn(o.buffer);a.vw_initializeWithCopy(t,r.handle)}const l=o.getWitnessTables();for(const[t,e]of i.protocols.entries()){const s=a.getFullTypeName(),n=ut(s)[e.name];if(void 0===n)throw new Error(`Type ${s} does not conform to protocol ${e.name}`);const r=n.witnessTable;l.add(t*Process.pointerSize).writePointer(r)}n.push(Ct(o))}const r=l(...n);if("string"==typeof e||Array.isArray(e))return r;if(e instanceof b)switch(e.getKind()){case o.Struct:return new Ut(e,{raw:r});case o.Enum:return new Vt(e,{raw:r});case o.Class:return new jt(r);default:throw new Error("Unimplemented kind: "+e.getKind())}const i=Ft(r);return kt.fromExistentialContainer(i,e)},{address:t})}function wt(t){if("string"==typeof t||Array.isArray(t))return t;if(t instanceof Dt){const e=Array(t.numProtocols).fill("pointer");return t.isClassOnly?["pointer",...e]:["pointer","pointer","pointer","pointer",...e]}if(t.getKind()===o.Class||_t(t))return"pointer";let e=t.getTypeLayout().stride/8;return e=e>1?e:1,Array(e).fill("uint64")}function Ct(t){if(t instanceof jt)return t.handle;if(t instanceof st)return St(t.handle,t.sizeof);if(t instanceof nt){const e=t,s=[];for(let t=0;t!=e.sizeof;t+=8)s.push(e.handle.add(t).readPointer());return s}return _t(t.$metadata)?t.handle:St(t.handle,t.$metadata.getTypeLayout().stride)}function _t(t){return!t.getValueWitnesses().flags.isBitwiseTakable}class bt{#O={};#E=0;get length(){return Object.keys(this.#O).length-this.#E}enqueue(t){const e=Object.keys(this.#O).length;this.#O[e]=t}dequeue(){if(0===Object.keys(this.#O).length)return;return this.#O[this.#E++]}resetCursor(){this.#E=0}toJSON(){return this.#O}}class It{#y;#w;#C;#_;#b;#I;constructor(t,e,s,n,r){let i;this.#y=new bt,s=s.map(t=>{if(Array.isArray(t)&&t.length>4){const e=Memory.alloc(Process.pointerSize*t.length);return this.#y.enqueue(e),"pointer"}return t}).flat(),this.#w=e,Array.isArray(e)?(this.#C=Process.pointerSize*e.length,this.#_=Memory.alloc(this.#C),e.length>4&&(i=this.#_)):"void"===e?this.#C=0:(this.#C=Process.pointerSize,this.#_=Memory.alloc(this.#C)),this.#b=Memory.alloc(2*Process.pointerSize);const a=Et.allocateTrampoline(76);Memory.patchCode(a,76,e=>{const s=new Arm64Writer(e,{pc:a});if(s.putLdrRegAddress("x15",this.#b),s.putStpRegRegRegOffset("x29","x30","x15",0,"post-adjust"),void 0!==n&&s.putLdrRegAddress("x20",n),void 0!==r&&s.putLdrRegAddress("x21",r),void 0!==i&&s.putLdrRegAddress("x8",i),s.putLdrRegAddress("x14",t),s.putBlrRegNoAuth("x14"),void 0===i&&this.#C>0){s.putLdrRegAddress("x15",this.#_);let t=0,e=0;for(;e<this.#C;t++,e+=8){const n=`x${t}`;s.putStrRegRegOffset(n,"x15",e)}}s.putLdrRegAddress("x15",this.#b),s.putLdpRegRegRegOffset("x29","x30","x15",0,"post-adjust"),s.putRet(),s.flush()}),this.#I=new NativeFunction(a,"pointer",s)}wrapper=(...t)=>{this.#y.resetCursor(),t=t.map(t=>{if(Array.isArray(t)&&t.length>4){const e=this.#y.dequeue();return function(t,e){const s=Process.pointerSize*t.length;for(let n=0,r=0;r<s;n++,r+=Process.pointerSize)e.add(r).writeU64(t[n])}(t,e),e}return t}).flat();if((0,this.#I)(...t),0===this.#C)return;const e=[];if(!Array.isArray(this.#w))return this.#_.readValue(this.#w);for(let t=0,s=0;t<this.#C;t+=8,s++){const n=this.#w[s];e.push(this.#_.add(t).readValue(n))}return e};call(...t){return this.wrapper(t)}}NativePointer.prototype.readValue=function(t){switch(t){case"pointer":return this.readPointer();case"string":return this.readCString();case"int":return this.readInt();case"uint":return this.readUInt();case"long":return this.readLong();case"ulong":return this.readULong();case"int8":return this.readS8();case"uint8":return this.readU8();case"int16":return this.readS16();case"uint16":return this.readU16();case"int32":return this.readS32();case"uint32":return this.readU32();case"int64":return this.readS64();case"uint64":return this.readU64();default:throw new Error(`Unimplemented type: ${t}`)}};class vt{handle;static SIZE=16;static OFFSETOF_NUMFIELDS=12;#c;constructor(t){this.handle=t}getFieldRecordBuffer(){return this.handle.add(vt.SIZE)}get numFields(){return void 0!==this.#c||(this.#c=this.handle.add(vt.OFFSETOF_NUMFIELDS).readU32()),this.#c}getFields(){const t=[];let e,s=this.getFieldRecordBuffer();for(let n=0;n<this.numFields;n++)e=new Nt(s),t.push(e),s=s.add(Nt.SIZE);return t}}class Nt{handle;static SIZE=12;static OFFSETOF_FLAGS=0;static OFFSETOF_MANGLED_TYPE_NAME=4;static OFFSETOF_FIELD_NAME=8;#s;#v;#N;constructor(t){this.handle=t}get flags(){return void 0!==this.#s||(this.#s=this.handle.add(Nt.OFFSETOF_FLAGS).readU32()),this.#s}get mangledTypeName(){return void 0!==this.#v||(this.#v=E.From(this.handle.add(Nt.OFFSETOF_MANGLED_TYPE_NAME))),this.#v}get fieldName(){return void 0!==this.#N||(this.#N=E.From(this.handle.add(Nt.OFFSETOF_FIELD_NAME)).get().readUtf8String()),this.#N}get isIndirectCase(){return!!(this.flags&Pt.IsIndirectCase)}get isVar(){return!!(this.flags&Pt.IsVar)}}var Pt,At;!function(t){t[t.IsIndirectCase=1]="IsIndirectCase",t[t.IsVar=2]="IsVar"}(Pt||(Pt={}));class Mt{kind;descriptor;$conformances;$name;$fields;$moduleName;constructor(t,e,s){this.kind=t,this.descriptor=e,this.$conformances=s,this.$name=e.name,this.$fields=Bt(e),this.$moduleName=e.getModuleContext().name}get $metadataPointer(){return this.$metadata.handle}toJSON(){return{$fields:this.$fields,$conformances:Object.keys(this.$conformances)}}}class zt extends Mt{$methods;constructor(t,e){super("Class",t,e),this.$methods=Wt(t);for(const t of this.$methods)if("Init"===t.type){const e=Y(t.name);if(void 0===e)continue;Object.defineProperty(this,e.jsSignature,{configurable:!0,get(){const s=e.argTypeNames.map(t=>lt(t)),n=yt(t.address,this.$metadata,s,this.$metadataPointer);return Object.defineProperty(this,e.jsSignature,{configurable:!0,value:n}),n}})}}get $metadata(){return ct(this.descriptor.getFullTypeName(),v)}toJSON(){const t=super.toJSON();return Object.assign(t,{$methods:this.$methods})}}class Rt extends Mt{constructor(t,e){super("Struct",t,e)}get $metadata(){return ct(this.descriptor.getFullTypeName(),N)}}class xt extends Mt{constructor(t,e){if(super("Enum",t,e),void 0!==this.$fields)for(const[e,s]of this.$fields.entries()){const n=e;if(t.isPayloadTag(n)){const t=t=>{if(void 0===t)throw new Error("Case requires an associated value");return new Vt(this.$metadata,{tag:n,payload:t})};Object.defineProperty(this,s.name,{configurable:!1,enumerable:!0,value:t,writable:!1})}else Object.defineProperty(this,s.name,{configurable:!0,enumerable:!0,get:()=>{const t=new Vt(this.$metadata,{tag:n});return Object.defineProperty(this,s.name,{value:t}),t}})}}get $metadata(){return ct(this.descriptor.getFullTypeName(),P)}}class $t{descriptor;name;numRequirements;isClassOnly;moduleName;constructor(t){this.descriptor=t,this.name=t.name,this.numRequirements=t.numRequirements,this.isClassOnly=t.getProtocolContextDescriptorFlags().getClassConstraint()==T.Class,this.moduleName=t.getModuleContext().name}toJSON(){return{numRequirements:this.descriptor.numRequirements,isClassOnly:this.isClassOnly}}}class Dt{protocols;numProtocols;isClassOnly;constructor(...t){this.protocols=[...t],this.numProtocols=t.length,this.isClassOnly=!1;for(const e of t)if(e.isClassOnly){this.isClassOnly=!0;break}}get sizeofExistentialContainer(){return(this.isClassOnly?1*Process.pointerSize:4*Process.pointerSize)+Process.pointerSize*this.numProtocols}static fromSignature(t){const e=[],s=t.split("&").map(t=>t.trim());for(const t of s){const s=dt(t),n=new $t(s);e.push(n)}return new Dt(...e)}}class Lt{equals(t){return this.handle.equals(t.handle)}toJSON(){return{handle:this.handle}}static fromAdopted(t,e){return e.getKind()===o.Class?new jt(t):kt.fromAdopted(t,e)}static fromExistentialContainer(t,e){if(e.isClassOnly){const s=nt.makeFromRaw(t,e.numProtocols);return new jt(s.value)}{const s=st.makeFromRaw(t,e.numProtocols),n=s.type;if(n.isClassObject())return new jt(s.buffer.privateData.readPointer());{const t=s.projectValue();return kt.fromCopy(t,n)}}}}class kt extends Lt{$metadata;static fromCopy(t,e){const s=Memory.alloc(e.getTypeLayout().stride);return e.vw_initializeWithCopy(s,t),e.getKind()===o.Struct?new Ut(e,{handle:s}):new Vt(e,{handle:s})}static fromAdopted(t,e){const s=e.getKind();if(s===o.Struct)return new Ut(e,{handle:t});if(s===o.Enum)return new Vt(e,{handle:t});throw new Error("Non-value kind: "+s)}static fromRaw(t,e){const s=e.getKind();if(s===o.Struct)return new Ut(e,{raw:t});if(s===o.Enum)return new Vt(e,{raw:t});throw new Error("Non-value kind: "+s)}}class Ut{$metadata;handle;constructor(t,e){if(void 0===e.handle&&void 0===e.raw)throw new Error("Either a handle or raw fields must be provided");this.$metadata=t instanceof Rt?t.$metadata:t,this.handle=e.handle||Ft(e.raw)}equals(t){return this.handle.equals(t.handle)}toJSON(){return{handle:this.handle}}}class Vt{$metadata;handle;descriptor;#P;#A;constructor(t,e){this.$metadata=t instanceof xt?t.$metadata:t,this.descriptor=this.$metadata.getDescription();const s=Bt(this.descriptor);if(void 0===e.tag&&void 0===e.handle&&void 0===e.raw)throw new Error("Either a tag, handle or raw fields must be provided");if(void 0!==e.tag){const t=e.tag,n=e.payload,r=this.$metadata.getTypeLayout().stride,i=r<Process.pointerSize?Process.pointerSize:r;if(this.handle=Memory.alloc(i),void 0===t||t>=this.descriptor.getNumCases())throw new Error("Invalid tag for an enum of this type");if(this.descriptor.isPayloadTag(t)){if(void 0===n)throw new Error("Payload must be provided for this tag");const e=s[t].typeName;if(n.$metadata.getFullTypeName()!==e)throw new Error("Payload must be of type "+e);n instanceof jt?(this.handle.writePointer(n.handle),this.#A=n):(this.#A=kt.fromAdopted(this.handle,n.$metadata),this.$metadata.vw_initializeWithCopy(this.handle,n.handle))}this.$metadata.vw_destructiveInjectEnumTag(this.handle,t),this.#P=t}else{this.handle=e.handle||Ft(e.raw);const t=this.$metadata.vw_getEnumTag(this.handle);let n;if(t>=this.descriptor.getNumCases())throw new Error("Invalid pointer for an enum of this type");if(this.descriptor.isPayloadTag(t)){const e=ct(s[t].typeName,I);n=Lt.fromAdopted(this.handle,e)}this.#P=t,this.#A=n}}get $tag(){return this.#P}get $payload(){return this.#A}equals(t){let e=!1;return void 0!==this.$tag&&void 0!==t.$tag&&(e=this.$tag===t.$tag),void 0!==this.$payload&&void 0!==t.$payload&&(e&&=this.$payload.handle.equals(t.$payload.handle)),e}toJSON(){return{handle:this.handle,$tag:this.#P,$payload:this.#A}}}class jt extends Lt{handle;$metadata;#M;constructor(t){super(),this.handle=t,this.#M=new w(t),this.$metadata=this.#M.getMetadata(v);const e=this.$metadata.getDescription();for(const t of Wt(e))switch(t.type){case"Getter":{const e=X(t.name),s=lt(e.memberTypeName),n=yt(t.address,s,[],this.handle);Object.defineProperty(this,e.memberName,{configurable:!0,enumerable:!0,get:n});break}case"Setter":{const e=X(t.name),s=lt(e.memberTypeName),n=yt(t.address,"void",[s],this.handle);Object.defineProperty(this,e.memberName,{configurable:!0,enumerable:!0,set:n});break}case"Method":{const e=J(t.name),s="()"===e.retTypeName?"void":lt(e.retTypeName),n=e.argTypeNames.map(t=>lt(t)),r=yt(t.address,s,n,this.handle);Object.defineProperty(this,e.jsSignature,{configurable:!0,enumerable:!0,value:r});break}}}}function Bt(t){const e=[];if(!t.isReflectable())return;const s=new vt(t.fields.get());if(0===s.numFields)return;const n=s.getFields();for(const t of n)e.push({name:t.fieldName,typeName:null===t.mangledTypeName?void 0:Kt(t.mangledTypeName.get()),isVar:t.isVar});return e}function Wt(t){const e=[];for(const s of t.getMethodDescriptors()){const t=s.impl.get(),n=gt(t),r=s.flags.getKind();let i;switch(r){case d.Init:i="Init";break;case d.Getter:i="Getter";break;case d.Setter:i="Setter";break;case d.ReadCoroutine:i="ReadCoroutine";break;case d.ModifyCoroutine:i="ModifyCoroutine";break;case d.Method:i="Method";break;default:throw new Error(`Invalid method descriptor kind: ${r}`)}e.push({address:t,name:n,type:i})}return e}function Kt(t){let e=t,s=e.readU8(),n=null;for(;0!==s;){if(s>=1&&s<=23){if(e=e.add(1),1===s)n=new x(E.From(e).get());else if(2===s){let t=E.From(e).get().readPointer();t=t.and(8796093022207),n=new x(t)}break}if(s>=24&&s<=31)throw new Error("UNIMPLEMENTED 0x18 - 0x1F");e=e.add(1),s=e.readU8()}return null!==n?n.name:Z("_$s"+t.readCString())}class Gt{static sharedInstance;modules={};classes={};structs={};enums={};protocols={};cachedTypes={};static shared(){return void 0===Gt.sharedInstance&&(Gt.sharedInstance=new Gt),Gt.sharedInstance}constructor(){for(const t of Object.values(at)){const e=t.descriptor,s=t.conformances;switch(t.descriptor.getKind()){case l.Class:{const t=new zt(e,s);this.classes[t.$name]=t,this.getModule(t.$moduleName).addClass(t);break}case l.Struct:{const t=new Rt(e,s);this.structs[t.$name]=t,this.getModule(t.$moduleName).addStruct(t);break}case l.Enum:{const t=new xt(e,s);this.enums[t.$name]=t,this.getModule(t.$moduleName).addEnum(t);break}}}for(const t of Object.values(it)){const e=new $t(t);this.protocols[t.name]=e,this.getModule(e.moduleName).addProtocol(e)}}getModule(t){if(t in this.modules)return this.modules[t];const e=new qt(t);return this.modules[t]=e,e}}class qt{name;classes={};structs={};enums={};protocols={};constructor(t){this.name=t}addClass(t){this.classes[t.$name]=t}addStruct(t){this.structs[t.$name]=t}addEnum(t){this.enums[t.$name]=t}addProtocol(t){this.protocols[t.name]=t}toJSON(){return{classes:Object.keys(this.classes).length,structs:Object.keys(this.structs).length,enums:Object.keys(this.enums).length,protocols:Object.keys(this.protocols).length}}}function Ht(t){return t.indexOf("&")>-1||it[t]}function Zt(t,e,s){const n=[];for(let r=e;r!=s;r++)n.push(t[r]);return n}!function(t){t.attach=function(t,e){const s=J(function(t){const e=gt(t);if(void 0===e)throw new Error("Can't find symbol at "+t.toString());return e}(t));let n,r;return void 0!==e.onLeave&&(r=function(t){const r=s.retTypeName;let i;if(Ht(r)){const t=Dt.fromSignature(r),e=t.sizeofExistentialContainer;let s;if(e<=Ot){const t=Tt(e),n=[];for(let e=0;e!=t;e++)n.push(this.context[`x${e}`]);s=Ft(n)}else s=n;i=kt.fromExistentialContainer(s,t)}else{const e=lt(s.retTypeName);if(e.isClassObject())i=new jt(t);else{if(e.getTypeLayout().stride<=Ot&&!_t(e)){const t=Tt(e.getTypeLayout().stride),s=[];for(let e=0;e<t;e++)s.push(this.context[`x${e}`]);i=kt.fromRaw(s,e)}else i=kt.fromCopy(n,e)}}e.onLeave.bind(this)(i)}),Interceptor.attach(t,{onEnter:function(t){if(n=this.context.x8,void 0!==e.onEnter){const n=[];let r,i=0;for(const e of s.argTypeNames){if(Ht(e)){const s=Dt.fromSignature(e),a=s.sizeofExistentialContainer;let o;if(a<=Ot){const e=Tt(a);o=Ft(Zt(t,i,i+e)),i+=e}else o=t[i++];r=kt.fromExistentialContainer(o,s),n.push(r);continue}const s=lt(e);if(s.isClassObject())r=new jt(t[i++]);else{const e=Tt(s.getTypeLayout().stride),n=s.getKind(),a=Zt(t,i,i+e);if(n===o.Struct){r=new Ut(s,{raw:a})}else{if(n!==o.Enum)throw new Error("Unhandled metadata kind: "+n);r=new Vt(s,{raw:a})}i+=e}n.push(r)}e.onEnter.bind(this)(n)}},onLeave:r})}}(At||(At={}));return new class{#z=null;#R=null;constructor(){try{this.tryInitialize()}catch(t){}}get available(){try{return this.tryInitialize()}catch(t){return!1}}get api(){return this.#z}get modules(){return Gt.shared().modules}get classes(){return Gt.shared().classes}get structs(){return Gt.shared().structs}get enums(){return Gt.shared().enums}get protocols(){return Gt.shared().protocols}Object=jt;Struct=Ut;Enum=Vt;ProtocolComposition=Dt;Interceptor=At;NativeFunction(t,e,s,n,r){function i(t){return t instanceof Mt?t.$metadata:t instanceof $t?new Dt(t):t}return yt(t,i(e),s.map(t=>i(t)),n)}tryInitialize(){if(null!==this.#z)return!0;if(null!==this.#R)throw this.#R;try{this.#z=n(),r(),Q()}catch(t){throw this.#R=t,t}return null!==this.#z}}}();
