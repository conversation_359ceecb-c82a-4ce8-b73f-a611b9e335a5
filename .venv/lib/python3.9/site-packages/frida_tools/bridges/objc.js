var bridge=function(){let cachedApi=null;const defaultInvocationOptions={exceptions:"propagate"};function getApi(){if(null!==cachedApi)return cachedApi;const e={};let t=0;return[{module:"libsystem_malloc.dylib",functions:{free:["void",["pointer"]]}},{module:"libobjc.A.dylib",functions:{objc_msgSend:function(e){this.objc_msgSend=e},objc_msgSend_stret:function(e){this.objc_msgSend_stret=e},objc_msgSend_fpret:function(e){this.objc_msgSend_fpret=e},objc_msgSendSuper:function(e){this.objc_msgSendSuper=e},objc_msgSendSuper_stret:function(e){this.objc_msgSendSuper_stret=e},objc_msgSendSuper_fpret:function(e){this.objc_msgSendSuper_fpret=e},objc_getClassList:["int",["pointer","int"]],objc_lookUpClass:["pointer",["pointer"]],objc_allocateClassPair:["pointer",["pointer","pointer","pointer"]],objc_disposeClassPair:["void",["pointer"]],objc_registerClassPair:["void",["pointer"]],class_isMetaClass:["bool",["pointer"]],class_getName:["pointer",["pointer"]],class_getImageName:["pointer",["pointer"]],class_copyProtocolList:["pointer",["pointer","pointer"]],class_copyMethodList:["pointer",["pointer","pointer"]],class_getClassMethod:["pointer",["pointer","pointer"]],class_getInstanceMethod:["pointer",["pointer","pointer"]],class_getSuperclass:["pointer",["pointer"]],class_addProtocol:["bool",["pointer","pointer"]],class_addMethod:["bool",["pointer","pointer","pointer","pointer"]],class_copyIvarList:["pointer",["pointer","pointer"]],objc_getProtocol:["pointer",["pointer"]],objc_copyProtocolList:["pointer",["pointer"]],objc_allocateProtocol:["pointer",["pointer"]],objc_registerProtocol:["void",["pointer"]],protocol_getName:["pointer",["pointer"]],protocol_copyMethodDescriptionList:["pointer",["pointer","bool","bool","pointer"]],protocol_copyPropertyList:["pointer",["pointer","pointer"]],protocol_copyProtocolList:["pointer",["pointer","pointer"]],protocol_addProtocol:["void",["pointer","pointer"]],protocol_addMethodDescription:["void",["pointer","pointer","pointer","bool","bool"]],ivar_getName:["pointer",["pointer"]],ivar_getTypeEncoding:["pointer",["pointer"]],ivar_getOffset:["pointer",["pointer"]],object_isClass:["bool",["pointer"]],object_getClass:["pointer",["pointer"]],object_getClassName:["pointer",["pointer"]],method_getName:["pointer",["pointer"]],method_getTypeEncoding:["pointer",["pointer"]],method_getImplementation:["pointer",["pointer"]],method_setImplementation:["pointer",["pointer","pointer"]],property_getName:["pointer",["pointer"]],property_copyAttributeList:["pointer",["pointer","pointer"]],sel_getName:["pointer",["pointer"]],sel_registerName:["pointer",["pointer"]],class_getInstanceSize:["pointer",["pointer"]]},optionals:{objc_msgSend_stret:"ABI",objc_msgSend_fpret:"ABI",objc_msgSendSuper_stret:"ABI",objc_msgSendSuper_fpret:"ABI",object_isClass:"iOS8"}},{module:"libdispatch.dylib",functions:{dispatch_async_f:["void",["pointer","pointer","pointer"]]},variables:{_dispatch_main_q:function(e){this._dispatch_main_q=e}}}].forEach(function(r){const n="libobjc.A.dylib"===r.module,o=r.functions||{},i=r.variables||{},s=r.optionals||{};t+=Object.keys(o).length+Object.keys(i).length;const a=(Process.findModuleByName(r.module)?.enumerateExports()??[]).reduce(function(e,t){return e[t.name]=t,e},{});Object.keys(o).forEach(function(r){const i=a[r];if(void 0!==i&&"function"===i.type){const s=o[r];"function"==typeof s?(s.call(e,i.address),n&&s.call(e,i.address)):(e[r]=new NativeFunction(i.address,s[0],s[1],defaultInvocationOptions),n&&(e[r]=e[r])),t--}else{s[r]&&t--}}),Object.keys(i).forEach(function(r){const n=a[r];if(void 0!==n&&"variable"===n.type){i[r].call(e,n.address),t--}})}),0===t&&(e.objc_msgSend_stret||(e.objc_msgSend_stret=e.objc_msgSend),e.objc_msgSend_fpret||(e.objc_msgSend_fpret=e.objc_msgSend),e.objc_msgSendSuper_stret||(e.objc_msgSendSuper_stret=e.objc_msgSendSuper),e.objc_msgSendSuper_fpret||(e.objc_msgSendSuper_fpret=e.objc_msgSendSuper),cachedApi=e),cachedApi}const code="#include <glib.h>\n#include <ptrauth.h>\n\n#define KERN_SUCCESS 0\n#define MALLOC_PTR_IN_USE_RANGE_TYPE 1\n#if defined (HAVE_I386) && GLIB_SIZEOF_VOID_P == 8\n# define OBJC_ISA_MASK 0x7ffffffffff8ULL\n#elif defined (HAVE_ARM64)\n# define OBJC_ISA_MASK 0xffffffff8ULL\n#endif\n\ntypedef struct _ChooseContext ChooseContext;\n\ntypedef struct _malloc_zone_t malloc_zone_t;\ntypedef struct _malloc_introspection_t malloc_introspection_t;\ntypedef struct _vm_range_t vm_range_t;\n\ntypedef gpointer Class;\ntypedef int kern_return_t;\ntypedef guint mach_port_t;\ntypedef mach_port_t task_t;\ntypedef guintptr vm_offset_t;\ntypedef guintptr vm_size_t;\ntypedef vm_offset_t vm_address_t;\n\nstruct _ChooseContext\n{\n  GHashTable * classes;\n  GArray * matches;\n};\n\nstruct _malloc_zone_t\n{\n  void * reserved1;\n  void * reserved2;\n  size_t (* size) (struct _malloc_zone_t * zone, const void * ptr);\n  void * (* malloc) (struct _malloc_zone_t * zone, size_t size);\n  void * (* calloc) (struct _malloc_zone_t * zone, size_t num_items, size_t size);\n  void * (* valloc) (struct _malloc_zone_t * zone, size_t size);\n  void (* free) (struct _malloc_zone_t * zone, void * ptr);\n  void * (* realloc) (struct _malloc_zone_t * zone, void * ptr, size_t size);\n  void (* destroy) (struct _malloc_zone_t * zone);\n  const char * zone_name;\n\n  unsigned (* batch_malloc) (struct _malloc_zone_t * zone, size_t size, void ** results, unsigned num_requested);\n  void (* batch_free) (struct _malloc_zone_t * zone, void ** to_be_freed, unsigned num_to_be_freed);\n\n  malloc_introspection_t * introspect;\n};\n\ntypedef kern_return_t (* memory_reader_t) (task_t remote_task, vm_address_t remote_address, vm_size_t size, void ** local_memory);\ntypedef void (* vm_range_recorder_t) (task_t task, void * user_data, unsigned type, vm_range_t * ranges, unsigned count);\ntypedef kern_return_t (* enumerator_func) (task_t task, void * user_data, unsigned type_mask, vm_address_t zone_address, memory_reader_t reader,\n      vm_range_recorder_t recorder);\n\nstruct _malloc_introspection_t\n{\n  enumerator_func enumerator;\n};\n\nstruct _vm_range_t\n{\n  vm_address_t address;\n  vm_size_t size;\n};\n\nextern int objc_getClassList (Class * buffer, int buffer_count);\nextern Class class_getSuperclass (Class cls);\nextern size_t class_getInstanceSize (Class cls);\nextern kern_return_t malloc_get_all_zones (task_t task, memory_reader_t reader, vm_address_t ** addresses, unsigned * count);\n\nstatic void collect_subclasses (Class klass, GHashTable * result);\nstatic void collect_matches_in_ranges (task_t task, void * user_data, unsigned type, vm_range_t * ranges, unsigned count);\nstatic kern_return_t read_local_memory (task_t remote_task, vm_address_t remote_address, vm_size_t size, void ** local_memory);\n\nextern mach_port_t selfTask;\n\ngpointer *\nchoose (Class * klass,\n        gboolean consider_subclasses,\n        guint * count)\n{\n  ChooseContext ctx;\n  GHashTable * classes;\n  vm_address_t * malloc_zone_addresses;\n  unsigned malloc_zone_count, i;\n\n  classes = g_hash_table_new_full (NULL, NULL, NULL, NULL);\n  ctx.classes = classes;\n  ctx.matches = g_array_new (FALSE, FALSE, sizeof (gpointer));\n  if (consider_subclasses)\n    collect_subclasses (klass, classes);\n  else\n    g_hash_table_insert (classes, klass, GSIZE_TO_POINTER (class_getInstanceSize (klass)));\n\n  malloc_zone_count = 0;\n  malloc_get_all_zones (selfTask, read_local_memory, &malloc_zone_addresses, &malloc_zone_count);\n\n  for (i = 0; i != malloc_zone_count; i++)\n  {\n    vm_address_t zone_address = malloc_zone_addresses[i];\n    malloc_zone_t * zone = (malloc_zone_t *) zone_address;\n    enumerator_func enumerator;\n\n    if (zone != NULL && zone->introspect != NULL &&\n        (enumerator = (ptrauth_strip (zone->introspect, ptrauth_key_asda))->enumerator) != NULL)\n    {\n      enumerator = ptrauth_sign_unauthenticated (\n          ptrauth_strip (enumerator, ptrauth_key_asia),\n          ptrauth_key_asia, 0);\n\n      enumerator (selfTask, &ctx, MALLOC_PTR_IN_USE_RANGE_TYPE, zone_address, read_local_memory,\n          collect_matches_in_ranges);\n    }\n  }\n\n  g_hash_table_unref (classes);\n\n  *count = ctx.matches->len;\n\n  return (gpointer *) g_array_free (ctx.matches, FALSE);\n}\n\nvoid\ndestroy (gpointer mem)\n{\n  g_free (mem);\n}\n\nstatic void\ncollect_subclasses (Class klass,\n                    GHashTable * result)\n{\n  Class * classes;\n  int count, i;\n\n  count = objc_getClassList (NULL, 0);\n  classes = g_malloc (count * sizeof (gpointer));\n  count = objc_getClassList (classes, count);\n\n  for (i = 0; i != count; i++)\n  {\n    Class candidate = classes[i];\n    Class c;\n\n    c = candidate;\n    do\n    {\n      if (c == klass)\n      {\n        g_hash_table_insert (result, candidate, GSIZE_TO_POINTER (class_getInstanceSize (candidate)));\n        break;\n      }\n\n      c = class_getSuperclass (c);\n    }\n    while (c != NULL);\n  }\n\n  g_free (classes);\n}\n\nstatic void\ncollect_matches_in_ranges (task_t task,\n                           void * user_data,\n                           unsigned type,\n                           vm_range_t * ranges,\n                           unsigned count)\n{\n  ChooseContext * ctx = user_data;\n  GHashTable * classes = ctx->classes;\n  unsigned i;\n\n  for (i = 0; i != count; i++)\n  {\n    const vm_range_t * range = &ranges[i];\n    gconstpointer candidate = GSIZE_TO_POINTER (range->address);\n    gconstpointer isa;\n    guint instance_size;\n\n    isa = *(gconstpointer *) candidate;\n#ifdef OBJC_ISA_MASK\n    isa = GSIZE_TO_POINTER (GPOINTER_TO_SIZE (isa) & OBJC_ISA_MASK);\n#endif\n\n    instance_size = GPOINTER_TO_UINT (g_hash_table_lookup (classes, isa));\n    if (instance_size != 0 && range->size >= instance_size)\n    {\n      g_array_append_val (ctx->matches, candidate);\n    }\n  }\n}\n\nstatic kern_return_t\nread_local_memory (task_t remote_task,\n                   vm_address_t remote_address,\n                   vm_size_t size,\n                   void ** local_memory)\n{\n  *local_memory = (void *) remote_address;\n\n  return KERN_SUCCESS;\n}\n",{pointerSize}=Process;let cachedModule=null;function get(){return null===cachedModule&&(cachedModule=compileModule()),cachedModule}function compileModule(){const{objc_getClassList:e,class_getSuperclass:t,class_getInstanceSize:r}=getApi(),n=Memory.alloc(4);n.writeU32(Module.getGlobalExportByName("mach_task_self_").readU32());const o=new CModule(code,{objc_getClassList:e,class_getSuperclass:t,class_getInstanceSize:r,malloc_get_all_zones:Process.getModuleByName("/usr/lib/system/libsystem_malloc.dylib").getExportByName("malloc_get_all_zones"),selfTask:n}),i=new NativeFunction(o.choose,"pointer",["pointer","bool","pointer"]),s=new NativeFunction(o.destroy,"void",["pointer"]);return{handle:o,choose(e,t){const r=[],n=Memory.alloc(4),o=i(e,t?1:0,n);try{const e=n.readU32();for(let t=0;t!==e;t++)r.push(o.add(t*pointerSize).readPointer())}finally{s(o)}return r}}}function Runtime(){const pointerSize=Process.pointerSize;let api=null,apiError=null;const realizedClasses=new Set,classRegistry=new ClassRegistry,protocolRegistry=new ProtocolRegistry,replacedMethods=new Map,scheduledWork=new Map;let nextId=1,workCallback=null,NSAutoreleasePool=null;const bindings=new Map;let readObjectIsa=null;const msgSendBySignatureId=new Map,msgSendSuperBySignatureId=new Map;let cachedNSString=null,cachedNSStringCtor=null,cachedNSNumber=null,cachedNSNumberCtor=null,singularTypeById=null,modifiers=null;try{tryInitialize()}catch(e){}function tryInitialize(){if(null!==api)return!0;if(null!==apiError)throw apiError;try{api=getApi()}catch(e){throw apiError=e,e}return null!==api}function dispose(){for(const[e,t]of replacedMethods.entries()){const r=ptr(e),[n,o]=t;api.method_getImplementation(r).equals(o)&&api.method_setImplementation(r,n)}replacedMethods.clear()}function performScheduledWorkItem(e){const t=e.toString(),r=scheduledWork.get(t);scheduledWork.delete(t),null===NSAutoreleasePool&&(NSAutoreleasePool=classRegistry.NSAutoreleasePool);const n=NSAutoreleasePool.alloc().init();let o=null;try{r()}catch(e){o=e}n.release(),setImmediate(performScheduledWorkCleanup,o)}function performScheduledWorkCleanup(e){if(Script.unpin(),null!==e)throw e}function selector(e){return api.sel_registerName(Memory.allocUtf8String(e))}function selectorAsString(e){return api.sel_getName(e).readUtf8String()}Script.bindWeak(this,dispose),Object.defineProperty(this,"available",{enumerable:!0,get:()=>tryInitialize()}),Object.defineProperty(this,"api",{enumerable:!0,get:()=>getApi()}),Object.defineProperty(this,"classes",{enumerable:!0,value:classRegistry}),Object.defineProperty(this,"protocols",{enumerable:!0,value:protocolRegistry}),Object.defineProperty(this,"Object",{enumerable:!0,value:ObjCObject}),Object.defineProperty(this,"Protocol",{enumerable:!0,value:ObjCProtocol}),Object.defineProperty(this,"Block",{enumerable:!0,value:Block}),Object.defineProperty(this,"mainQueue",{enumerable:!0,get:()=>api?._dispatch_main_q??null}),Object.defineProperty(this,"registerProxy",{enumerable:!0,value:registerProxy}),Object.defineProperty(this,"registerClass",{enumerable:!0,value:registerClass}),Object.defineProperty(this,"registerProtocol",{enumerable:!0,value:registerProtocol}),Object.defineProperty(this,"bind",{enumerable:!0,value:bind}),Object.defineProperty(this,"unbind",{enumerable:!0,value:unbind}),Object.defineProperty(this,"getBoundData",{enumerable:!0,value:getBoundData}),Object.defineProperty(this,"enumerateLoadedClasses",{enumerable:!0,value:enumerateLoadedClasses}),Object.defineProperty(this,"enumerateLoadedClassesSync",{enumerable:!0,value:enumerateLoadedClassesSync}),Object.defineProperty(this,"choose",{enumerable:!0,value:choose}),Object.defineProperty(this,"chooseSync",{enumerable:!0,value(e){const t=[];return choose(e,{onMatch(e){t.push(e)},onComplete(){}}),t}}),this.schedule=function(e,t){const r=ptr(nextId++);scheduledWork.set(r.toString(),t),null===workCallback&&(workCallback=new NativeCallback(performScheduledWorkItem,"void",["pointer"])),Script.pin(),api.dispatch_async_f(e,r,workCallback)},this.implement=function(e,t){return new NativeCallback(t,e.returnType,e.argumentTypes)},this.selector=selector,this.selectorAsString=selectorAsString;const registryBuiltins=new Set(["prototype","constructor","hasOwnProperty","toJSON","toString","valueOf"]);function ClassRegistry(){const e={};let t=0;const r=new Proxy(this,{has:(e,t)=>n(t),get(e,t,r){switch(t){case"prototype":return e.prototype;case"constructor":return e.constructor;case"hasOwnProperty":return n;case"toJSON":return i;case"toString":return s;case"valueOf":return a;default:const r=o(t);return null!==r?r:void 0}},set:(e,t,r,n)=>!1,ownKeys(r){if(null===api)return[];let n=api.objc_getClassList(NULL,0);if(n!==t){const r=Memory.alloc(n*pointerSize);n=api.objc_getClassList(r,n);for(let t=0;t!==n;t++){const n=r.add(t*pointerSize).readPointer(),o=api.class_getName(n).readUtf8String();e[o]=n}t=n}return Object.keys(e)},getOwnPropertyDescriptor:(e,t)=>({writable:!1,configurable:!0,enumerable:!0})});function n(e){return!!registryBuiltins.has(e)||null!==o(e)}function o(r){let n=e[r];if(void 0===n){if(n=api.objc_lookUpClass(Memory.allocUtf8String(r)),n.isNull())return null;e[r]=n,t++}return new ObjCObject(n,void 0,!0)}function i(){return Object.keys(r).reduce(function(e,t){return e[t]=function(e){const t=o(e);if(null===t)throw new Error("Unable to find class '"+e+"'");return t}(t).toJSON(),e},{})}function s(){return"ClassRegistry"}function a(){return"ClassRegistry"}return r}function ProtocolRegistry(){let e={},t=0;const r=new Proxy(this,{has:(e,t)=>n(t),get(e,t,r){switch(t){case"prototype":return e.prototype;case"constructor":return e.constructor;case"hasOwnProperty":return n;case"toJSON":return i;case"toString":return s;case"valueOf":return a;default:const r=o(t);return null!==r?r:void 0}},set:(e,t,r,n)=>!1,ownKeys(r){if(null===api)return[];const n=Memory.alloc(pointerSize),o=api.objc_copyProtocolList(n);try{const r=n.readUInt();if(r!==t){e={};for(let t=0;t!==r;t++){const r=o.add(t*pointerSize).readPointer(),n=api.protocol_getName(r).readUtf8String();e[n]=r}t=r}}finally{api.free(o)}return Object.keys(e)},getOwnPropertyDescriptor:(e,t)=>({writable:!1,configurable:!0,enumerable:!0})});function n(e){return!!registryBuiltins.has(e)||null!==o(e)}function o(r){let n=e[r];if(void 0===n){if(n=api.objc_getProtocol(Memory.allocUtf8String(r)),n.isNull())return null;e[r]=n,t++}return new ObjCProtocol(n)}function i(){return Object.keys(r).reduce(function(t,r){return t[r]={handle:e[r]},t},{})}function s(){return"ProtocolRegistry"}function a(){return"ProtocolRegistry"}return r}const objCObjectBuiltins=new Set(["prototype","constructor","handle","hasOwnProperty","toJSON","toString","valueOf","equals","$kind","$super","$superClass","$class","$className","$moduleName","$protocols","$methods","$ownMethods","$ivars"]);function ObjCObject(e,t,r,n){let o=null,i=null,s=null,a=null,l=null,c=null,u=null,p=null,d=null,f=null,m=null;const g={};let _=null,h=null,y=null;if(e=getHandle(e),void 0===r){const t=api.object_getClass(e),r=t.toString();realizedClasses.has(r)||(api.objc_lookUpClass(api.class_getName(t)),realizedClasses.add(r))}const b=new Proxy(this,{has:(e,t)=>S(t),get(o,d,f){switch(d){case"handle":return e;case"prototype":return o.prototype;case"constructor":return o.constructor;case"hasOwnProperty":return S;case"toJSON":return P;case"toString":case"valueOf":const m=f.description;if(void 0!==m){const e=m.call(f);if(null!==e)return e.UTF8String.bind(e)}return function(){return f.$className};case"equals":return T;case"$kind":return null===i&&(i=N()?api.class_isMetaClass(e)?"meta-class":"class":"instance"),i;case"$super":if(null===s){const t=api.class_getSuperclass(v());if(t.isNull())s=[null];else{const n=Memory.alloc(2*pointerSize);n.writePointer(e),n.add(pointerSize).writePointer(t),s=[new ObjCObject(e,void 0,r,n)]}}return s[0];case"$superClass":if(null===a){const e=api.class_getSuperclass(v());a=e.isNull()?[null]:[new ObjCObject(e)]}return a[0];case"$class":return null===l&&(l=new ObjCObject(api.object_getClass(e),void 0,!0)),l;case"$className":return null===c&&(c=n?api.class_getName(n.add(pointerSize).readPointer()).readUtf8String():N()?api.class_getName(e).readUtf8String():api.object_getClassName(e).readUtf8String()),c;case"$moduleName":return null===u&&(u=api.class_getImageName(v()).readUtf8String()),u;case"$protocols":if(null===p){p={};const e=Memory.alloc(pointerSize),t=api.class_copyProtocolList(v(),e);if(!t.isNull())try{const r=e.readUInt();for(let e=0;e!==r;e++){const r=new ObjCProtocol(t.add(e*pointerSize).readPointer());p[r.name]=r}}finally{api.free(t)}}return p;case"$methods":if(null===_){const e=n?n.add(pointerSize).readPointer():v(),t=api.object_getClass(e),r=new Set;let o=t;do{for(let e of collectMethodNames(o,"+ "))r.add(e);o=api.class_getSuperclass(o)}while(!o.isNull());o=e;do{for(let e of collectMethodNames(o,"- "))r.add(e);o=api.class_getSuperclass(o)}while(!o.isNull());_=Array.from(r)}return _;case"$ownMethods":if(null===h){const e=n?n.add(pointerSize).readPointer():v(),t=collectMethodNames(api.object_getClass(e),"+ "),r=collectMethodNames(e,"- ");h=t.concat(r)}return h;case"$ivars":return null===y&&(y=N()?{}:new ObjCIvars(b,v())),y;default:if("symbol"==typeof d)return o[d];if(t){const e=O(d);if(null===e||!e.implemented)return}const g=z(d);if(null===g)return;return g}},set:(e,t,r,n)=>!1,ownKeys(r){if(null===d)if(t){const e=[],t=j();Object.keys(t).forEach(function(r){if("+"!==r[0]&&"-"!==r[0]){t[r].implemented&&e.push(r)}}),d=e}else{const t={},r={};let n=api.object_getClass(e);do{const e=Memory.alloc(pointerSize),o=api.class_copyMethodList(n,e),i=N()?"+ ":"- ";try{const n=e.readUInt();for(let e=0;e!==n;e++){const n=o.add(e*pointerSize).readPointer(),s=api.method_getName(n),a=api.sel_getName(s).readUtf8String();if(void 0!==r[a])continue;r[a]=a;const l=jsMethodName(a);let c=2,u=l;for(;void 0!==t[u];)c++,u=l+c;t[u]=!0;const p=i+a;if(void 0===g[p]){const e={sel:s,handle:n,wrapper:null};g[p]=e,g[u]=e}}}finally{api.free(o)}n=api.class_getSuperclass(n)}while(!n.isNull());d=Object.keys(t)}return["handle"].concat(d)},getOwnPropertyDescriptor:(e,t)=>({writable:!1,configurable:!0,enumerable:!0})});return t&&(m=N()?null:z("- respondsToSelector:")),b;function S(e){if(objCObjectBuiltins.has(e))return!0;if(t){const t=O(e);return!(null===t||!t.implemented)}return null!==C(e)}function v(){return null===o&&(o=N()?e:api.object_getClass(e)),o}function N(){return void 0===r&&(r=api.object_isClass?!!api.object_isClass(e):!!api.class_isMetaClass(api.object_getClass(e))),r}function C(e){let r=g[e];if(void 0!==r)return r;const n=function(e){const t=/([+\-])\s(\S+)/.exec(e);let r,n;null===t?(n=N()?"+":"-",r=objcMethodName(e)):(n=t[1],r=t[2]);const o=[n,r].join(" ");return[n,r,o]}(e),o=n[2];if(r=g[o],void 0!==r)return g[e]=r,r;const i=n[0],s=n[1],a=selector(s),l=N()?"+":"-";if(t){const e=O(o);null!==e&&(r={sel:a,types:e.types,wrapper:null,kind:i})}if(void 0===r){const e="+"===i?api.class_getClassMethod(v(),a):api.class_getInstanceMethod(v(),a);if(e.isNull()){if(N()||"-"!==i||"forwardingTargetForSelector:"===s||"methodSignatureForSelector:"===s)return null;let e=b;if(!("- forwardingTargetForSelector:"in b))return null;{const t=b.forwardingTargetForSelector_(a);if(null===t||"instance"!==t.$kind)return null;e=t}const t=api.class_getInstanceMethod(api.object_getClass(e.handle),a);if(t.isNull())return null;let n=api.method_getTypeEncoding(t).readUtf8String();if((null===n||""===n)&&(n=w(e,o),null===n&&(n=w(b,o)),null===n))return null;r={sel:a,types:n,wrapper:null,kind:i}}else r={sel:a,handle:e,wrapper:null,kind:i}}return g[o]=r,g[e]=r,i===l&&(g[jsMethodName(s)]=r),r}function w(e,t){const r=Object.keys(e.$protocols).map(t=>k({},e.$protocols[t])).reduce((e,t)=>(Object.assign(e,t),e),{})[t];return void 0===r?null:r.types}function k(e,t){return void 0!==t.methods&&Object.assign(e,t.methods),void 0!==t.protocol&&k(e,t.protocol),e}function O(e){const t=j()[e];return void 0!==t?t:null}function j(){if(null===f){const e={},r=collectProtocols(t),n=N()?"+":"-";Object.keys(r).forEach(function(t){const o=r[t].methods;Object.keys(o).forEach(function(t){const r=o[t],i=t.substr(2),s=t[0];let a=!1,l=!1;const c={types:r.types};Object.defineProperty(c,"implemented",{get:()=>(a||(l=!!r.required||null!==m&&m.call(b,selector(i)),a=!0),l)}),e[t]=c,s===n&&(e[jsMethodName(i)]=c)})}),f=e}return f}function z(e){const t=C(e);if(null===t)return null;let r=t.wrapper;return null===r&&(r=makeMethodInvocationWrapper(t,b,n,defaultInvocationOptions),t.wrapper=r),r}function P(){return{handle:e.toString()}}function T(t){return e.equals(getHandle(t))}}function getReplacementMethodImplementation(e){const t=replacedMethods.get(e.toString());if(void 0===t)return null;const[,r]=t;return r}function replaceMethodImplementation(e,t){const r=e.toString();let n;const o=replacedMethods.get(r);void 0!==o?[n]=o:n=api.method_getImplementation(e),t.equals(n)?replacedMethods.delete(r):replacedMethods.set(r,[n,t]),api.method_setImplementation(e,t)}function collectMethodNames(e,t){const r=[],n=Memory.alloc(pointerSize),o=api.class_copyMethodList(e,n);try{const e=n.readUInt();for(let n=0;n!==e;n++){const e=o.add(n*pointerSize).readPointer(),i=api.method_getName(e),s=api.sel_getName(i).readUtf8String();r.push(t+s)}}finally{api.free(o)}return r}function ObjCProtocol(e){let t=null,r=null,n=null,o=null;function i(t,r,n){const o=api.protocol_copyMethodDescriptionList(e,n.required?1:0,n.instance?1:0,r);if(!o.isNull())try{const e=r.readUInt();for(let r=0;r!==e;r++){const e=o.add(r*(2*pointerSize)),i=(n.instance?"- ":"+ ")+selectorAsString(e.readPointer()),s=e.add(pointerSize).readPointer().readUtf8String();t[i]={required:n.required,types:s}}}finally{api.free(o)}}Object.defineProperty(this,"handle",{value:e,enumerable:!0}),Object.defineProperty(this,"name",{get:()=>(null===t&&(t=api.protocol_getName(e).readUtf8String()),t),enumerable:!0}),Object.defineProperty(this,"protocols",{get(){if(null===r){r={};const t=Memory.alloc(pointerSize),n=api.protocol_copyProtocolList(e,t);if(!n.isNull())try{const e=t.readUInt();for(let t=0;t!==e;t++){const e=new ObjCProtocol(n.add(t*pointerSize).readPointer());r[e.name]=e}}finally{api.free(n)}}return r},enumerable:!0}),Object.defineProperty(this,"properties",{get(){if(null===n){n={};const t=Memory.alloc(pointerSize),r=api.protocol_copyPropertyList(e,t);if(!r.isNull())try{const e=t.readUInt();for(let o=0;o!==e;o++){const e=r.add(o*pointerSize).readPointer(),i=api.property_getName(e).readUtf8String(),s={},a=api.property_copyAttributeList(e,t);if(!a.isNull())try{const e=t.readUInt();for(let t=0;t!==e;t++){const e=a.add(t*(2*pointerSize)),r=e.readPointer().readUtf8String(),n=e.add(pointerSize).readPointer().readUtf8String();s[r]=n}}finally{api.free(a)}n[i]=s}}finally{api.free(r)}}return n},enumerable:!0}),Object.defineProperty(this,"methods",{get(){if(null===o){o={};const e=Memory.alloc(pointerSize);i(o,e,{required:!0,instance:!1}),i(o,e,{required:!1,instance:!1}),i(o,e,{required:!0,instance:!0}),i(o,e,{required:!1,instance:!0})}return o},enumerable:!0})}const objCIvarsBuiltins=new Set(["prototype","constructor","hasOwnProperty","toJSON","toString","valueOf"]);function ObjCIvars(e,t){const r={};let n=null,o=[],i=t;do{o.unshift(i),i=api.class_getSuperclass(i)}while(!i.isNull());const s=Memory.alloc(pointerSize);o.forEach(e=>{const t=api.class_copyIvarList(e,s);try{const e=s.readUInt();for(let n=0;n!==e;n++){const e=t.add(n*pointerSize).readPointer(),o=api.ivar_getName(e).readUtf8String();r[o]=[e,null]}}finally{api.free(t)}});const a=new Proxy(this,{has:(e,t)=>c(t),get(e,t,r){switch(t){case"prototype":return e.prototype;case"constructor":return e.constructor;case"hasOwnProperty":return c;case"toJSON":return u;case"toString":return p;case"valueOf":return d;default:const r=l(t);if(null===r)return;return r.get()}},set(e,t,r,n){const o=l(t);if(null===o)throw new Error("Unknown ivar");return o.set(r),!0},ownKeys:e=>(null===n&&(n=Object.keys(r)),n),getOwnPropertyDescriptor:(e,t)=>({writable:!0,configurable:!0,enumerable:!0})});return a;function l(t){const n=r[t];if(void 0===n)return null;let o=n[1];if(null===o){const r=n[0],i=api.ivar_getOffset(r).toInt32(),s=e.handle.add(i),a=parseType(api.ivar_getTypeEncoding(r).readUtf8String()),l=a.fromNative||identityTransform,c=a.toNative||identityTransform;let u,p;"isa"===t?(u=readObjectIsa,p=function(){throw new Error("Unable to set the isa instance variable")}):(u=a.read,p=a.write),o={get:()=>l.call(e,u(s)),set(t){p(s,c.call(e,t))}},n[1]=o}return o}function c(e){return!!objCIvarsBuiltins.has(e)||r.hasOwnProperty(e)}function u(){return Object.keys(a).reduce(function(e,t){return e[t]=a[t],e},{})}function p(){return"ObjCIvars"}function d(){return"ObjCIvars"}}let blockDescriptorAllocSize,blockDescriptorDeclaredSize,blockDescriptorOffsets,blockSize,blockOffsets;4===pointerSize?(blockDescriptorAllocSize=16,blockDescriptorDeclaredSize=20,blockDescriptorOffsets={reserved:0,size:4,rest:8},blockSize=20,blockOffsets={isa:0,flags:4,reserved:8,invoke:12,descriptor:16}):(blockDescriptorAllocSize=32,blockDescriptorDeclaredSize=32,blockDescriptorOffsets={reserved:0,size:8,rest:16},blockSize=32,blockOffsets={isa:0,flags:8,reserved:12,invoke:16,descriptor:24});const BLOCK_HAS_COPY_DISPOSE=1<<25,BLOCK_HAS_CTOR=1<<26,BLOCK_IS_GLOBAL=1<<28,BLOCK_HAS_STRET=1<<29,BLOCK_HAS_SIGNATURE=1<<30;function Block(e,t=defaultInvocationOptions){if(this._options=t,e instanceof NativePointer){const t=e.add(blockOffsets.descriptor).readPointer();this.handle=e;const r=e.add(blockOffsets.flags).readU32();if(0!==(r&BLOCK_HAS_SIGNATURE)){const e=0!==(r&BLOCK_HAS_COPY_DISPOSE)?2:0;this.types=t.add(blockDescriptorOffsets.rest+e*pointerSize).readPointer().readCString(),this._signature=parseSignature(this.types)}else this._signature=null}else{this.declare(e);const t=Memory.alloc(blockDescriptorAllocSize+blockSize),r=t.add(blockDescriptorAllocSize),n=Memory.allocUtf8String(this.types);t.add(blockDescriptorOffsets.reserved).writeULong(0),t.add(blockDescriptorOffsets.size).writeULong(blockDescriptorDeclaredSize),t.add(blockDescriptorOffsets.rest).writePointer(n),r.add(blockOffsets.isa).writePointer(classRegistry.__NSGlobalBlock__),r.add(blockOffsets.flags).writeU32(BLOCK_HAS_SIGNATURE|BLOCK_IS_GLOBAL),r.add(blockOffsets.reserved).writeU32(0),r.add(blockOffsets.descriptor).writePointer(t),this.handle=r,this._storage=[t,n],this.implementation=e.implementation}}function collectProtocols(e,t){(t=t||{})[e.name]=e;const r=e.protocols;return Object.keys(r).forEach(function(e){collectProtocols(r[e],t)}),t}function registerProxy(e){const t=e.protocols||[],r=e.methods||{},n=e.events||{},o=new Set(Object.keys(r).filter(e=>null!==/([+\-])\s(\S+)/.exec(e)).map(e=>e.split(" ")[1])),i={"- dealloc":function(){const e=this.data.target;"- release"in e&&e.release(),unbind(this.self),this.super.dealloc();const t=this.data.events.dealloc;void 0!==t&&t.call(this)},"- respondsToSelector:":function(e){const t=selectorAsString(e);return!!o.has(t)||this.data.target.respondsToSelector_(e)},"- forwardingTargetForSelector:":function(e){const t=this.data.events.forward;return void 0!==t&&t.call(this,selectorAsString(e)),this.data.target},"- methodSignatureForSelector:":function(e){return this.data.target.methodSignatureForSelector_(e)},"- forwardInvocation:":function(e){e.invokeWithTarget_(this.data.target)}};for(var s in r)if(r.hasOwnProperty(s)){if(i.hasOwnProperty(s))throw new Error("The '"+s+"' method is reserved");i[s]=r[s]}const a=registerClass({name:e.name,super:classRegistry.NSProxy,protocols:t,methods:i});return function(e,t){e=e instanceof NativePointer?new ObjCObject(e):e,t=t||{};const r=a.alloc().autorelease(),o=getBoundData(r);for(var i in o.target="- retain"in e?e.retain():e,o.events=n,t)if(t.hasOwnProperty(i)){if(o.hasOwnProperty(i))throw new Error("The '"+i+"' property is reserved");o[i]=t[i]}this.handle=r.handle}}function registerClass(e){let t=e.name;void 0===t&&(t=makeClassName());const r=void 0!==e.super?e.super:classRegistry.NSObject,n=e.protocols||[],o=e.methods||{},i=[],s=api.objc_allocateClassPair(null!==r?r.handle:NULL,Memory.allocUtf8String(t),ptr("0"));if(s.isNull())throw new Error("Unable to register already registered class '"+t+"'");const a=api.object_getClass(s);try{n.forEach(function(e){api.class_addProtocol(s,e.handle)}),Object.keys(o).forEach(function(e){const t=/([+\-])\s(\S+)/.exec(e);if(null===t)throw new Error("Invalid method name");const l=t[1],c=t[2];let u;const p=o[e];if("function"==typeof p){let t=null;if(e in r)t=r[e].types;else for(let r of n){const n=r.methods[e];if(void 0!==n){t=n.types;break}}if(null===t)throw new Error("Unable to find '"+e+"' in super-class or any of its protocols");u={types:t,implementation:p}}else u=p;const d="+"===l?a:s;let f=u.types;void 0===f&&(f=unparseSignature(u.retType,["+"===l?"class":"object","selector"].concat(u.argTypes)));const m=parseSignature(f),g=new NativeCallback(makeMethodImplementationWrapper(m,u.implementation),m.retType.type,m.argTypes.map(function(e){return e.type}));i.push(g),api.class_addMethod(d,selector(c),g,Memory.allocUtf8String(f))})}catch(e){throw api.objc_disposeClassPair(s),e}return api.objc_registerClassPair(s),s._methodCallbacks=i,Script.bindWeak(s,makeClassDestructor(ptr(s))),new ObjCObject(s)}function makeClassDestructor(e){return function(){api.objc_disposeClassPair(e)}}function registerProtocol(e){let t=e.name;void 0===t&&(t=makeProtocolName());const r=e.protocols||[],n=e.methods||{};r.forEach(function(e){if(!(e instanceof ObjCProtocol))throw new Error("Expected protocol")});const o=Object.keys(n).map(function(e){const t=n[e],r=/([+\-])\s(\S+)/.exec(e);if(null===r)throw new Error("Invalid method name");const o=r[1],i=r[2];let s=t.types;return void 0===s&&(s=unparseSignature(t.retType,["+"===o?"class":"object","selector"].concat(t.argTypes))),{kind:o,name:i,types:s,optional:t.optional}}),i=api.objc_allocateProtocol(Memory.allocUtf8String(t));if(i.isNull())throw new Error("Unable to register already registered protocol '"+t+"'");return r.forEach(function(e){api.protocol_addProtocol(i,e.handle)}),o.forEach(function(e){const t=e.optional?0:1,r="-"===e.kind?1:0;api.protocol_addMethodDescription(i,selector(e.name),Memory.allocUtf8String(e.types),t,r)}),api.objc_registerProtocol(i),new ObjCProtocol(i)}function getHandle(e){if(e instanceof NativePointer)return e;if("object"==typeof e&&e.hasOwnProperty("handle"))return e.handle;throw new Error("Expected NativePointer or ObjC.Object instance")}function bind(e,t){const r=getHandle(e),n=e instanceof ObjCObject?e:new ObjCObject(r);bindings.set(r.toString(),{self:n,super:n.$super,data:t})}function unbind(e){const t=getHandle(e);bindings.delete(t.toString())}function getBoundData(e){return getBinding(e).data}function getBinding(e){const t=getHandle(e),r=t.toString();let n=bindings.get(r);if(void 0===n){const o=e instanceof ObjCObject?e:new ObjCObject(t);n={self:o,super:o.$super,data:{}},bindings.set(r,n)}return n}function enumerateLoadedClasses(...e){const t=new ModuleMap;let r,n,o=!1;if(1===e.length)r=e[0];else{r=e[1];n=e[0].ownedBy}void 0===n&&(n=t,o=!0);const i=api.class_getName,s=r.onMatch.bind(r),a=(8===pointerSize?8:11)*pointerSize,l=api.objc_getClassList(NULL,0),c=Memory.alloc(l*pointerSize);api.objc_getClassList(c,l);for(let e=0;e!==l;e++){const r=c.add(e*pointerSize).readPointer(),l=i(r);let u=null,p=n.findPath(l);if(null===p&&(o||null===t.findPath(l))){u=l.readCString();if(-1!==u.indexOf(".")){const e=r.add(a).readPointer();p=n.findPath(e)}}null!==p&&(null===u&&(u=l.readUtf8String()),s(u,p))}r.onComplete()}function enumerateLoadedClassesSync(e={}){const t={};return enumerateLoadedClasses(e,{onMatch(e,r){let n=t[r];void 0===n&&(n=[],t[r]=n),n.push(e)},onComplete(){}}),t}function choose(e,t){let r=e,n=!0;if(e instanceof ObjCObject||"object"!=typeof e||(r=e.class,e.hasOwnProperty("subclasses")&&(n=e.subclasses)),!(r instanceof ObjCObject)||"class"!==r.$kind&&"meta-class"!==r.$kind)throw new Error("Expected an ObjC.Object for a class or meta-class");const o=get().choose(r,n).map(e=>new ObjCObject(e));for(const e of o){if("stop"===t.onMatch(e))break}t.onComplete()}function makeMethodInvocationWrapper(method,owner,superSpecifier,invocationOptions){const sel=method.sel;let handle=method.handle,types;void 0===handle?(handle=null,types=method.types):types=api.method_getTypeEncoding(handle).readUtf8String();const signature=parseSignature(types),retType=signature.retType,argTypes=signature.argTypes.slice(2),objc_msgSend=superSpecifier?getMsgSendSuperImpl(signature,invocationOptions):getMsgSendImpl(signature,invocationOptions),argVariableNames=argTypes.map(function(e,t){return"a"+(t+1)}),callArgs=[superSpecifier?"superSpecifier":"this","sel"].concat(argTypes.map(function(e,t){return e.toNative?"argTypes["+t+"].toNative.call(this, "+argVariableNames[t]+")":argVariableNames[t]}));let returnCaptureLeft,returnCaptureRight;"void"===retType.type?(returnCaptureLeft="",returnCaptureRight=""):retType.fromNative?(returnCaptureLeft="return retType.fromNative.call(this, ",returnCaptureRight=")"):(returnCaptureLeft="return ",returnCaptureRight="");const m=eval("var m = function ("+argVariableNames.join(", ")+") { "+returnCaptureLeft+"objc_msgSend("+callArgs.join(", ")+")"+returnCaptureRight+"; }; m;");function getMethodHandle(){if(null===handle){if("instance"===owner.$kind){let e=owner;do{if(!("- forwardingTargetForSelector:"in e))break;{const t=e.forwardingTargetForSelector_(sel);if(null===t)break;if("instance"!==t.$kind)break;const r=api.class_getInstanceMethod(t.$class.handle,sel);r.isNull()?e=t:handle=r}}while(null===handle)}if(null===handle)throw new Error("Unable to find method handle of proxied function")}return handle}return Object.defineProperty(m,"handle",{enumerable:!0,get:getMethodHandle}),m.selector=sel,Object.defineProperty(m,"implementation",{enumerable:!0,get(){const e=getMethodHandle(),t=new NativeFunction(api.method_getImplementation(e),m.returnType,m.argumentTypes,invocationOptions),r=getReplacementMethodImplementation(e);return null!==r&&(t._callback=r),t},set(e){replaceMethodImplementation(getMethodHandle(),e)}}),m.returnType=retType.type,m.argumentTypes=signature.argTypes.map(e=>e.type),m.types=types,Object.defineProperty(m,"symbol",{enumerable:!0,get:()=>`${method.kind}[${owner.$className} ${selectorAsString(sel)}]`}),m.clone=function(e){return makeMethodInvocationWrapper(method,owner,superSpecifier,e)},m}function makeMethodImplementationWrapper(signature,implementation){const retType=signature.retType,argTypes=signature.argTypes,argVariableNames=argTypes.map(function(e,t){return 0===t?"handle":1===t?"sel":"a"+(t-1)}),callArgs=argTypes.slice(2).map(function(e,t){const r=argVariableNames[2+t];return e.fromNative?"argTypes["+(2+t)+"].fromNative.call(self, "+r+")":r});let returnCaptureLeft,returnCaptureRight;"void"===retType.type?(returnCaptureLeft="",returnCaptureRight=""):retType.toNative?(returnCaptureLeft="return retType.toNative.call(self, ",returnCaptureRight=")"):(returnCaptureLeft="return ",returnCaptureRight="");const m=eval("var m = function ("+argVariableNames.join(", ")+") { var binding = getBinding(handle);var self = binding.self;"+returnCaptureLeft+"implementation.call(binding"+(callArgs.length>0?", ":"")+callArgs.join(", ")+")"+returnCaptureRight+"; }; m;");return m}function makeBlockInvocationWrapper(block,signature,implementation){const retType=signature.retType,argTypes=signature.argTypes.slice(1),argVariableNames=argTypes.map(function(e,t){return"a"+(t+1)}),callArgs=argTypes.map(function(e,t){return e.toNative?"argTypes["+t+"].toNative.call(this, "+argVariableNames[t]+")":argVariableNames[t]});let returnCaptureLeft,returnCaptureRight;"void"===retType.type?(returnCaptureLeft="",returnCaptureRight=""):retType.fromNative?(returnCaptureLeft="return retType.fromNative.call(this, ",returnCaptureRight=")"):(returnCaptureLeft="return ",returnCaptureRight="");const f=eval("var f = function ("+argVariableNames.join(", ")+") { "+returnCaptureLeft+"implementation(this"+(callArgs.length>0?", ":"")+callArgs.join(", ")+")"+returnCaptureRight+"; }; f;");return f.bind(block)}function makeBlockImplementationWrapper(block,signature,implementation){const retType=signature.retType,argTypes=signature.argTypes,argVariableNames=argTypes.map(function(e,t){return 0===t?"handle":"a"+t}),callArgs=argTypes.slice(1).map(function(e,t){const r=argVariableNames[1+t];return e.fromNative?"argTypes["+(1+t)+"].fromNative.call(this, "+r+")":r});let returnCaptureLeft,returnCaptureRight;"void"===retType.type?(returnCaptureLeft="",returnCaptureRight=""):retType.toNative?(returnCaptureLeft="return retType.toNative.call(this, ",returnCaptureRight=")"):(returnCaptureLeft="return ",returnCaptureRight="");const f=eval("var f = function ("+argVariableNames.join(", ")+") { if (!this.handle.equals(handle))this.handle = handle;"+returnCaptureLeft+"implementation.call(block"+(callArgs.length>0?", ":"")+callArgs.join(", ")+")"+returnCaptureRight+"; }; f;");return f.bind(block)}function rawFridaType(e){return"object"===e?"pointer":e}function makeClassName(){for(let e=1;;e++){const t="FridaAnonymousClass"+e;if(!(t in classRegistry))return t}}function makeProtocolName(){for(let e=1;;e++){const t="FridaAnonymousProtocol"+e;if(!(t in protocolRegistry))return t}}function objcMethodName(e){return e.replace(/_/g,":")}function jsMethodName(e){let t=e.replace(/:/g,"_");return objCObjectBuiltins.has(t)&&(t+="2"),t}Object.defineProperties(Block.prototype,{implementation:{enumerable:!0,get(){const e=this.handle.add(blockOffsets.invoke).readPointer().strip(),t=this._getSignature();return makeBlockInvocationWrapper(this,t,new NativeFunction(e.sign(),t.retType.type,t.argTypes.map(function(e){return e.type}),this._options))},set(e){const t=this._getSignature(),r=new NativeCallback(makeBlockImplementationWrapper(this,t,e),t.retType.type,t.argTypes.map(function(e){return e.type}));this._callback=r;const n=this.handle.add(blockOffsets.invoke),o=Memory.queryProtection(n),i=o.includes("w");i||Memory.protect(n,Process.pointerSize,"rw-"),n.writePointer(r.strip().sign("ia",n)),i||Memory.protect(n,Process.pointerSize,o)}},declare:{value(e){let t=e.types;void 0===t&&(t=unparseSignature(e.retType,["block"].concat(e.argTypes))),this.types=t,this._signature=parseSignature(t)}},_getSignature:{value(){const e=this._signature;if(null===e)throw new Error("block is missing signature; call declare()");return e}}});const isaMasks={x64:"0x7ffffffffff8",arm64:"0xffffffff8"},rawMask=isaMasks[Process.arch];if(void 0!==rawMask){const e=ptr(rawMask);readObjectIsa=function(t){return t.readPointer().and(e)}}else readObjectIsa=function(e){return e.readPointer()};function getMsgSendImpl(e,t){return resolveMsgSendImpl(msgSendBySignatureId,e,t,!1)}function getMsgSendSuperImpl(e,t){return resolveMsgSendImpl(msgSendSuperBySignatureId,e,t,!0)}function resolveMsgSendImpl(e,t,r,n){if(r!==defaultInvocationOptions)return makeMsgSendImpl(t,r,n);const{id:o}=t;let i=e.get(o);return void 0===i&&(i=makeMsgSendImpl(t,r,n),e.set(o,i)),i}function makeMsgSendImpl(e,t,r){const n=e.retType.type,o=e.argTypes.map(function(e){return e.type}),i=["objc_msgSend"];r&&i.push("Super");n instanceof Array&&!typeFitsInRegisters(n)?i.push("_stret"):"float"!==n&&"double"!==n||i.push("_fpret");const s=i.join("");return new NativeFunction(api[s],n,o,t)}function typeFitsInRegisters(e){if("x64"!==Process.arch)return!1;return sizeOfTypeOnX64(e)<=16}function sizeOfTypeOnX64(e){if(e instanceof Array)return e.reduce((e,t)=>e+sizeOfTypeOnX64(t),0);switch(e){case"bool":case"char":case"uchar":return 1;case"int16":case"uint16":return 2;case"int":case"int32":case"uint":case"uint32":case"float":return 4;default:return 8}}function unparseSignature(e,t){const r=typeIdFromAlias(e),n=t.map(typeIdFromAlias),o=n.map(e=>singularTypeById[e].size),i=o.reduce((e,t)=>e+t,0);let s=0;return r+i+n.map((e,t)=>{const r=e+s;return s+=o[t],r}).join("")}function parseSignature(e){const t=[e,0];parseQualifiers(t);const r=readType(t);readNumber(t);const n=[];let o=JSON.stringify(r.type);for(;dataAvailable(t);){parseQualifiers(t);const e=readType(t);readNumber(t),n.push(e),o+=JSON.stringify(e.type)}return{id:o,retType:r,argTypes:n}}function parseType(e){return readType([e,0])}function readType(e){let t=readChar(e);if("@"===t){let r=peekChar(e);"?"===r?(t+=r,skipChar(e),"<"===peekChar(e)&&skipExtendedBlock(e)):'"'===r&&(skipChar(e),readUntil('"',e))}else if("^"===t){let r=peekChar(e);"@"===r&&(t+=r,skipChar(e))}const r=singularTypeById[t];if(void 0!==r)return r;if("["===t){const t=readNumber(e),r=readType(e);return skipChar(e),arrayType(t,r)}if("{"===t){if(!tokenExistsAhead("=","}",e))return readUntil("}",e),structType([]);readUntil("=",e);const t=[];let r;for(;"}"!==(r=peekChar(e));)'"'===r&&(skipChar(e),readUntil('"',e)),t.push(readType(e));return skipChar(e),structType(t)}if("("===t){readUntil("=",e);const t=[];for(;")"!==peekChar(e);)t.push(readType(e));return skipChar(e),unionType(t)}if("b"===t)return readNumber(e),singularTypeById.i;if("^"===t)return readType(e),singularTypeById["?"];if(modifiers.has(t))return readType(e);throw new Error("Unable to handle type "+t)}function skipExtendedBlock(e){let t;for(skipChar(e);">"!==(t=peekChar(e));)"<"===peekChar(e)?skipExtendedBlock(e):(skipChar(e),'"'===t&&readUntil('"',e));skipChar(e)}function readNumber(e){let t="";for(;dataAvailable(e);){const r=peekChar(e),n=r.charCodeAt(0);if(!(n>=48&&n<=57))break;t+=r,skipChar(e)}return parseInt(t)}function readUntil(e,t){const r=t[0],n=t[1],o=r.indexOf(e,n);if(-1===o)throw new Error("Expected token '"+e+"' not found");const i=r.substring(n,o);return t[1]=o+1,i}function readChar(e){return e[0][e[1]++]}function peekChar(e){return e[0][e[1]]}function tokenExistsAhead(e,t,r){const[n,o]=r,i=n.indexOf(e,o);if(-1===i)return!1;const s=n.indexOf(t,o);if(-1===s)throw new Error("Expected to find terminator: "+t);return i<s}function skipChar(e){e[1]++}function dataAvailable(e){return e[1]!==e[0].length}const qualifierById={r:"const",n:"in",N:"inout",o:"out",O:"bycopy",R:"byref",V:"oneway"};function parseQualifiers(e){const t=[];for(;;){const r=qualifierById[peekChar(e)];if(void 0===r)break;t.push(r),skipChar(e)}return t}const idByAlias={char:"c",int:"i",int16:"s",int32:"i",int64:"q",uchar:"C",uint:"I",uint16:"S",uint32:"I",uint64:"Q",float:"f",double:"d",bool:"B",void:"v",string:"*",object:"@",block:"@?",class:"#",selector:":",pointer:"^v"};function typeIdFromAlias(e){if("object"==typeof e&&null!==e)return`@"${e.type}"`;const t=idByAlias[e];if(void 0===t)throw new Error("No known encoding for type "+e);return t}const fromNativeId=function(e){return e.isNull()?null:e.toString(16)===this.handle.toString(16)?this:new ObjCObject(e)},toNativeId=function(e){if(null===e)return NULL;const t=typeof e;return"string"===t?(null===cachedNSStringCtor&&(cachedNSString=classRegistry.NSString,cachedNSStringCtor=cachedNSString.stringWithUTF8String_),cachedNSStringCtor.call(cachedNSString,Memory.allocUtf8String(e))):"number"===t?(null===cachedNSNumberCtor&&(cachedNSNumber=classRegistry.NSNumber,cachedNSNumberCtor=cachedNSNumber.numberWithDouble_),cachedNSNumberCtor.call(cachedNSNumber,e)):e},fromNativeBlock=function(e){return e.isNull()?null:e.toString(16)===this.handle.toString(16)?this:new Block(e)},toNativeBlock=function(e){return null!==e?e:NULL},toNativeObjectArray=function(e){if(e instanceof Array){const t=e.length,r=Memory.alloc(t*pointerSize);for(let n=0;n!==t;n++)r.add(n*pointerSize).writePointer(toNativeId(e[n]));return r}return e};function arrayType(e,t){return{type:"pointer",read(r){const n=[],o=t.size;for(let i=0;i!==e;i++)n.push(t.read(r.add(i*o)));return n},write(e,r){const n=t.size;r.forEach((r,o)=>{t.write(e.add(o*n),r)})}}}function structType(e){let t,r;if(e.some(function(e){return!!e.fromNative})){const r=e.map(function(e){return e.fromNative?e.fromNative:identityTransform});t=function(e){return e.map(function(e,t){return r[t].call(this,e)})}}else t=identityTransform;if(e.some(function(e){return!!e.toNative})){const t=e.map(function(e){return e.toNative?e.toNative:identityTransform});r=function(e){return e.map(function(e,r){return t[r].call(this,e)})}}else r=identityTransform;const[n,o]=e.reduce(function(e,t){const[r,n]=e,{size:o}=t,i=align(r,o);return n.push(i),[i+o,n]},[0,[]]);return{type:e.map(e=>e.type),size:n,read:t=>e.map((e,r)=>e.read(t.add(o[r]))),write(t,r){r.forEach((r,n)=>{e[n].write(t.add(o[n]),r)})},fromNative:t,toNative:r}}function unionType(e){const t=e.reduce(function(e,t){return t.size>e.size?t:e},e[0]);let r,n;if(t.fromNative){const e=t.fromNative;r=function(t){return e.call(this,t[0])}}else r=function(e){return e[0]};if(t.toNative){const e=t.toNative;n=function(t){return[e.call(this,t)]}}else n=function(e){return[e]};return{type:[t.type],size:t.size,read:t.read,write:t.write,fromNative:r,toNative:n}}const longBits=8==pointerSize&&"windows"!==Process.platform?64:32;function identityTransform(e){return e}function align(e,t){const r=e%t;return 0===r?e:e+(t-r)}modifiers=new Set(["j","A","r","n","N","o","O","R","V","+"]),singularTypeById={c:{type:"char",size:1,read:e=>e.readS8(),write:(e,t)=>{e.writeS8(t)},toNative:e=>"boolean"==typeof e?e?1:0:e},i:{type:"int",size:4,read:e=>e.readInt(),write:(e,t)=>{e.writeInt(t)}},s:{type:"int16",size:2,read:e=>e.readS16(),write:(e,t)=>{e.writeS16(t)}},l:{type:"int32",size:4,read:e=>e.readS32(),write:(e,t)=>{e.writeS32(t)}},q:{type:"int64",size:8,read:e=>e.readS64(),write:(e,t)=>{e.writeS64(t)}},C:{type:"uchar",size:1,read:e=>e.readU8(),write:(e,t)=>{e.writeU8(t)}},I:{type:"uint",size:4,read:e=>e.readUInt(),write:(e,t)=>{e.writeUInt(t)}},S:{type:"uint16",size:2,read:e=>e.readU16(),write:(e,t)=>{e.writeU16(t)}},L:{type:"uint"+longBits,size:longBits/8,read:e=>e.readULong(),write:(e,t)=>{e.writeULong(t)}},Q:{type:"uint64",size:8,read:e=>e.readU64(),write:(e,t)=>{e.writeU64(t)}},f:{type:"float",size:4,read:e=>e.readFloat(),write:(e,t)=>{e.writeFloat(t)}},d:{type:"double",size:8,read:e=>e.readDouble(),write:(e,t)=>{e.writeDouble(t)}},B:{type:"bool",size:1,read:e=>e.readU8(),write:(e,t)=>{e.writeU8(t)},fromNative:e=>!!e,toNative:e=>e?1:0},v:{type:"void",size:0},"*":{type:"pointer",size:pointerSize,read:e=>e.readPointer(),write:(e,t)=>{e.writePointer(t)},fromNative:e=>e.readUtf8String()},"@":{type:"pointer",size:pointerSize,read:e=>e.readPointer(),write:(e,t)=>{e.writePointer(t)},fromNative:fromNativeId,toNative:toNativeId},"@?":{type:"pointer",size:pointerSize,read:e=>e.readPointer(),write:(e,t)=>{e.writePointer(t)},fromNative:fromNativeBlock,toNative:toNativeBlock},"^@":{type:"pointer",size:pointerSize,read:e=>e.readPointer(),write:(e,t)=>{e.writePointer(t)},toNative:toNativeObjectArray},"^v":{type:"pointer",size:pointerSize,read:e=>e.readPointer(),write:(e,t)=>{e.writePointer(t)}},"#":{type:"pointer",size:pointerSize,read:e=>e.readPointer(),write:(e,t)=>{e.writePointer(t)},fromNative:fromNativeId,toNative:toNativeId},":":{type:"pointer",size:pointerSize,read:e=>e.readPointer(),write:(e,t)=>{e.writePointer(t)}},"?":{type:"pointer",size:pointerSize,read:e=>e.readPointer(),write:(e,t)=>{e.writePointer(t)}}}}const runtime=new Runtime;return runtime}();
