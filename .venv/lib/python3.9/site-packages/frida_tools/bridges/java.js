var bridge=function(){const{pageSize:e,pointerSize:t}=Process;class n{constructor(t){this.sliceSize=t,this.slicesPerPage=e/t,this.pages=[],this.free=[]}allocateSlice(t,n){const r=void 0===t.near,o=1===n;if(r&&o){const e=this.free.pop();if(void 0!==e)return e}else if(n<e){const{free:e}=this,i=e.length,a=o?null:ptr(n-1);for(let n=0;n!==i;n++){const i=e[n],s=r||this._isSliceNear(i,t),l=o||i.and(a).isNull();if(s&&l)return e.splice(n,1)[0]}}return this._allocatePage(t)}_allocatePage(t){const n=Memory.alloc(e,t),{sliceSize:r,slicesPerPage:o}=this;for(let e=1;e!==o;e++){const t=n.add(e*r);this.free.push(t)}return this.pages.push(n),n}_isSliceNear(e,t){const n=e.add(this.sliceSize),{near:o,maxDistance:i}=t,a=r(o.sub(e)),s=r(o.sub(n));return a.compare(i)<=0&&s.compare(i)<=0}freeSlice(e){this.free.push(e)}}function r(e){const n=4===t?31:63,r=ptr(1).shl(n).not();return e.and(r)}function o(e,t){if(0!==t)throw new Error(e+" failed: "+t)}const i=805371904,a=805372416,s=1,{pointerSize:l}=Process,c={exceptions:"propagate"};function d(e,t){this.handle=e,this.vm=t,this.vtable=e.readPointer()}function u(e,t,n,r){let o=null;return function(){null===o&&(o=new NativeFunction(this.vtable.add((e-1)*l).readPointer(),t,n,c));let i=[o];return i=i.concat.apply(i,arguments),r.apply(this,i)}}function h(e,t,{limit:n}){let r=e,o=null;for(let e=0;e!==n;e++){const e=Instruction.parse(r),n=t(e,o);if(null!==n)return n;r=e.next,o=e}return null}function p(e){let t=null,n=!1;return function(...r){return n||(t=e(...r),n=!0),t}}function f(e,t){this.handle=e,this.vm=t}d.prototype.deallocate=u(47,"int32",["pointer","pointer"],function(e,t){return e(this.handle,t)}),d.prototype.getLoadedClasses=u(78,"int32",["pointer","pointer","pointer"],function(e,t,n){o("EnvJvmti::getLoadedClasses",e(this.handle,t,n))}),d.prototype.iterateOverInstancesOfClass=u(112,"int32",["pointer","pointer","int","pointer","pointer"],function(e,t,n,r,i){o("EnvJvmti::iterateOverInstancesOfClass",e(this.handle,t,n,r,i))}),d.prototype.getObjectsWithTags=u(114,"int32",["pointer","int","pointer","pointer","pointer","pointer"],function(e,t,n,r,i,a){o("EnvJvmti::getObjectsWithTags",e(this.handle,t,n,r,i,a))}),d.prototype.addCapabilities=u(142,"int32",["pointer","pointer"],function(e,t){return e(this.handle,t)});const _=Process.pointerSize,m={pointer:34,uint8:37,int8:40,uint16:43,int16:46,int32:49,int64:52,float:55,double:58,void:61},g={pointer:64,uint8:67,int8:70,uint16:73,int16:76,int32:79,int64:82,float:85,double:88,void:91},b={pointer:114,uint8:117,int8:120,uint16:123,int16:126,int32:129,int64:132,float:135,double:138,void:141},y={pointer:95,uint8:96,int8:97,uint16:98,int16:99,int32:100,int64:101,float:102,double:103},v={pointer:104,uint8:105,int8:106,uint16:107,int16:108,int32:109,int64:110,float:111,double:112},w={pointer:145,uint8:146,int8:147,uint16:148,int16:149,int32:150,int64:151,float:152,double:153},E={pointer:154,uint8:155,int8:156,uint16:157,int16:158,int32:159,int64:160,float:161,double:162},S={exceptions:"propagate"};let N=null,L=[];function C(e){return L.push(e),e}function k(e){return null===N&&(N=e.handle.readPointer()),N}function M(e,t,n,r){let o=null;return function(){null===o&&(o=new NativeFunction(k(this).add(e*_).readPointer(),t,n,S));let i=[o];return i=i.concat.apply(i,arguments),r.apply(this,i)}}f.dispose=function(e){L.forEach(e.deleteGlobalRef,e),L=[]},f.prototype.getVersion=M(4,"int32",["pointer"],function(e){return e(this.handle)}),f.prototype.findClass=M(6,"pointer",["pointer","pointer"],function(e,t){const n=e(this.handle,Memory.allocUtf8String(t));return this.throwIfExceptionPending(),n}),f.prototype.throwIfExceptionPending=function(){const e=this.exceptionOccurred();if(e.isNull())return;this.exceptionClear();const t=this.newGlobalRef(e);this.deleteLocalRef(e);const n=this.vaMethod("pointer",[])(this.handle,t,this.javaLangObject().toString),r=this.stringFromJni(n);this.deleteLocalRef(n);const o=new Error(r);throw o.$h=t,Script.bindWeak(o,function(e,t){return function(){e.perform(e=>{e.deleteGlobalRef(t)})}}(this.vm,t)),o},f.prototype.fromReflectedMethod=M(7,"pointer",["pointer","pointer"],function(e,t){return e(this.handle,t)}),f.prototype.fromReflectedField=M(8,"pointer",["pointer","pointer"],function(e,t){return e(this.handle,t)}),f.prototype.toReflectedMethod=M(9,"pointer",["pointer","pointer","pointer","uint8"],function(e,t,n,r){return e(this.handle,t,n,r)}),f.prototype.getSuperclass=M(10,"pointer",["pointer","pointer"],function(e,t){return e(this.handle,t)}),f.prototype.isAssignableFrom=M(11,"uint8",["pointer","pointer","pointer"],function(e,t,n){return!!e(this.handle,t,n)}),f.prototype.toReflectedField=M(12,"pointer",["pointer","pointer","pointer","uint8"],function(e,t,n,r){return e(this.handle,t,n,r)}),f.prototype.throw=M(13,"int32",["pointer","pointer"],function(e,t){return e(this.handle,t)}),f.prototype.exceptionOccurred=M(15,"pointer",["pointer"],function(e){return e(this.handle)}),f.prototype.exceptionDescribe=M(16,"void",["pointer"],function(e){e(this.handle)}),f.prototype.exceptionClear=M(17,"void",["pointer"],function(e){e(this.handle)}),f.prototype.pushLocalFrame=M(19,"int32",["pointer","int32"],function(e,t){return e(this.handle,t)}),f.prototype.popLocalFrame=M(20,"pointer",["pointer","pointer"],function(e,t){return e(this.handle,t)}),f.prototype.newGlobalRef=M(21,"pointer",["pointer","pointer"],function(e,t){return e(this.handle,t)}),f.prototype.deleteGlobalRef=M(22,"void",["pointer","pointer"],function(e,t){e(this.handle,t)}),f.prototype.deleteLocalRef=M(23,"void",["pointer","pointer"],function(e,t){e(this.handle,t)}),f.prototype.isSameObject=M(24,"uint8",["pointer","pointer","pointer"],function(e,t,n){return!!e(this.handle,t,n)}),f.prototype.newLocalRef=M(25,"pointer",["pointer","pointer"],function(e,t){return e(this.handle,t)}),f.prototype.allocObject=M(27,"pointer",["pointer","pointer"],function(e,t){return e(this.handle,t)}),f.prototype.getObjectClass=M(31,"pointer",["pointer","pointer"],function(e,t){return e(this.handle,t)}),f.prototype.isInstanceOf=M(32,"uint8",["pointer","pointer","pointer"],function(e,t,n){return!!e(this.handle,t,n)}),f.prototype.getMethodId=M(33,"pointer",["pointer","pointer","pointer","pointer"],function(e,t,n,r){return e(this.handle,t,Memory.allocUtf8String(n),Memory.allocUtf8String(r))}),f.prototype.getFieldId=M(94,"pointer",["pointer","pointer","pointer","pointer"],function(e,t,n,r){return e(this.handle,t,Memory.allocUtf8String(n),Memory.allocUtf8String(r))}),f.prototype.getIntField=M(100,"int32",["pointer","pointer","pointer"],function(e,t,n){return e(this.handle,t,n)}),f.prototype.getStaticMethodId=M(113,"pointer",["pointer","pointer","pointer","pointer"],function(e,t,n,r){return e(this.handle,t,Memory.allocUtf8String(n),Memory.allocUtf8String(r))}),f.prototype.getStaticFieldId=M(144,"pointer",["pointer","pointer","pointer","pointer"],function(e,t,n,r){return e(this.handle,t,Memory.allocUtf8String(n),Memory.allocUtf8String(r))}),f.prototype.getStaticIntField=M(150,"int32",["pointer","pointer","pointer"],function(e,t,n){return e(this.handle,t,n)}),f.prototype.getStringLength=M(164,"int32",["pointer","pointer"],function(e,t){return e(this.handle,t)}),f.prototype.getStringChars=M(165,"pointer",["pointer","pointer","pointer"],function(e,t){return e(this.handle,t,NULL)}),f.prototype.releaseStringChars=M(166,"void",["pointer","pointer","pointer"],function(e,t,n){e(this.handle,t,n)}),f.prototype.newStringUtf=M(167,"pointer",["pointer","pointer"],function(e,t){const n=Memory.allocUtf8String(t);return e(this.handle,n)}),f.prototype.getStringUtfChars=M(169,"pointer",["pointer","pointer","pointer"],function(e,t){return e(this.handle,t,NULL)}),f.prototype.releaseStringUtfChars=M(170,"void",["pointer","pointer","pointer"],function(e,t,n){e(this.handle,t,n)}),f.prototype.getArrayLength=M(171,"int32",["pointer","pointer"],function(e,t){return e(this.handle,t)}),f.prototype.newObjectArray=M(172,"pointer",["pointer","int32","pointer","pointer"],function(e,t,n,r){return e(this.handle,t,n,r)}),f.prototype.getObjectArrayElement=M(173,"pointer",["pointer","pointer","int32"],function(e,t,n){return e(this.handle,t,n)}),f.prototype.setObjectArrayElement=M(174,"void",["pointer","pointer","int32","pointer"],function(e,t,n,r){e(this.handle,t,n,r)}),f.prototype.newBooleanArray=M(175,"pointer",["pointer","int32"],function(e,t){return e(this.handle,t)}),f.prototype.newByteArray=M(176,"pointer",["pointer","int32"],function(e,t){return e(this.handle,t)}),f.prototype.newCharArray=M(177,"pointer",["pointer","int32"],function(e,t){return e(this.handle,t)}),f.prototype.newShortArray=M(178,"pointer",["pointer","int32"],function(e,t){return e(this.handle,t)}),f.prototype.newIntArray=M(179,"pointer",["pointer","int32"],function(e,t){return e(this.handle,t)}),f.prototype.newLongArray=M(180,"pointer",["pointer","int32"],function(e,t){return e(this.handle,t)}),f.prototype.newFloatArray=M(181,"pointer",["pointer","int32"],function(e,t){return e(this.handle,t)}),f.prototype.newDoubleArray=M(182,"pointer",["pointer","int32"],function(e,t){return e(this.handle,t)}),f.prototype.getBooleanArrayElements=M(183,"pointer",["pointer","pointer","pointer"],function(e,t){return e(this.handle,t,NULL)}),f.prototype.getByteArrayElements=M(184,"pointer",["pointer","pointer","pointer"],function(e,t){return e(this.handle,t,NULL)}),f.prototype.getCharArrayElements=M(185,"pointer",["pointer","pointer","pointer"],function(e,t){return e(this.handle,t,NULL)}),f.prototype.getShortArrayElements=M(186,"pointer",["pointer","pointer","pointer"],function(e,t){return e(this.handle,t,NULL)}),f.prototype.getIntArrayElements=M(187,"pointer",["pointer","pointer","pointer"],function(e,t){return e(this.handle,t,NULL)}),f.prototype.getLongArrayElements=M(188,"pointer",["pointer","pointer","pointer"],function(e,t){return e(this.handle,t,NULL)}),f.prototype.getFloatArrayElements=M(189,"pointer",["pointer","pointer","pointer"],function(e,t){return e(this.handle,t,NULL)}),f.prototype.getDoubleArrayElements=M(190,"pointer",["pointer","pointer","pointer"],function(e,t){return e(this.handle,t,NULL)}),f.prototype.releaseBooleanArrayElements=M(191,"pointer",["pointer","pointer","pointer","int32"],function(e,t,n){e(this.handle,t,n,2)}),f.prototype.releaseByteArrayElements=M(192,"pointer",["pointer","pointer","pointer","int32"],function(e,t,n){e(this.handle,t,n,2)}),f.prototype.releaseCharArrayElements=M(193,"pointer",["pointer","pointer","pointer","int32"],function(e,t,n){e(this.handle,t,n,2)}),f.prototype.releaseShortArrayElements=M(194,"pointer",["pointer","pointer","pointer","int32"],function(e,t,n){e(this.handle,t,n,2)}),f.prototype.releaseIntArrayElements=M(195,"pointer",["pointer","pointer","pointer","int32"],function(e,t,n){e(this.handle,t,n,2)}),f.prototype.releaseLongArrayElements=M(196,"pointer",["pointer","pointer","pointer","int32"],function(e,t,n){e(this.handle,t,n,2)}),f.prototype.releaseFloatArrayElements=M(197,"pointer",["pointer","pointer","pointer","int32"],function(e,t,n){e(this.handle,t,n,2)}),f.prototype.releaseDoubleArrayElements=M(198,"pointer",["pointer","pointer","pointer","int32"],function(e,t,n){e(this.handle,t,n,2)}),f.prototype.getByteArrayRegion=M(200,"void",["pointer","pointer","int","int","pointer"],function(e,t,n,r,o){e(this.handle,t,n,r,o)}),f.prototype.setBooleanArrayRegion=M(207,"void",["pointer","pointer","int32","int32","pointer"],function(e,t,n,r,o){e(this.handle,t,n,r,o)}),f.prototype.setByteArrayRegion=M(208,"void",["pointer","pointer","int32","int32","pointer"],function(e,t,n,r,o){e(this.handle,t,n,r,o)}),f.prototype.setCharArrayRegion=M(209,"void",["pointer","pointer","int32","int32","pointer"],function(e,t,n,r,o){e(this.handle,t,n,r,o)}),f.prototype.setShortArrayRegion=M(210,"void",["pointer","pointer","int32","int32","pointer"],function(e,t,n,r,o){e(this.handle,t,n,r,o)}),f.prototype.setIntArrayRegion=M(211,"void",["pointer","pointer","int32","int32","pointer"],function(e,t,n,r,o){e(this.handle,t,n,r,o)}),f.prototype.setLongArrayRegion=M(212,"void",["pointer","pointer","int32","int32","pointer"],function(e,t,n,r,o){e(this.handle,t,n,r,o)}),f.prototype.setFloatArrayRegion=M(213,"void",["pointer","pointer","int32","int32","pointer"],function(e,t,n,r,o){e(this.handle,t,n,r,o)}),f.prototype.setDoubleArrayRegion=M(214,"void",["pointer","pointer","int32","int32","pointer"],function(e,t,n,r,o){e(this.handle,t,n,r,o)}),f.prototype.registerNatives=M(215,"int32",["pointer","pointer","pointer","int32"],function(e,t,n,r){return e(this.handle,t,n,r)}),f.prototype.monitorEnter=M(217,"int32",["pointer","pointer"],function(e,t){return e(this.handle,t)}),f.prototype.monitorExit=M(218,"int32",["pointer","pointer"],function(e,t){return e(this.handle,t)}),f.prototype.getDirectBufferAddress=M(230,"pointer",["pointer","pointer"],function(e,t){return e(this.handle,t)}),f.prototype.getObjectRefType=M(232,"int32",["pointer","pointer"],function(e,t){return e(this.handle,t)});const j=new Map;function I(e,t,n,r){return P(this,"p",x,e,t,n,r)}function R(e,t,n,r){return P(this,"v",T,e,t,n,r)}function A(e,t,n,r){return P(this,"n",U,e,t,n,r)}function P(e,t,n,r,o,i,a){if(void 0!==a)return n(e,r,o,i,a);const s=[r,t,o].concat(i).join("|");let l=j.get(s);return void 0===l&&(l=n(e,r,o,i,S),j.set(s,l)),l}function x(e,t,n,r,o){return new NativeFunction(k(e).add(t*_).readPointer(),n,["pointer","pointer","pointer"].concat(r),o)}function T(e,t,n,r,o){return new NativeFunction(k(e).add(t*_).readPointer(),n,["pointer","pointer","pointer","..."].concat(r),o)}function U(e,t,n,r,o){return new NativeFunction(k(e).add(t*_).readPointer(),n,["pointer","pointer","pointer","pointer","..."].concat(r),o)}f.prototype.constructor=function(e,t){return R.call(this,28,"pointer",e,t)},f.prototype.vaMethod=function(e,t,n){const r=m[e];if(void 0===r)throw new Error("Unsupported type: "+e);return R.call(this,r,e,t,n)},f.prototype.nonvirtualVaMethod=function(e,t,n){const r=g[e];if(void 0===r)throw new Error("Unsupported type: "+e);return A.call(this,r,e,t,n)},f.prototype.staticVaMethod=function(e,t,n){const r=b[e];if(void 0===r)throw new Error("Unsupported type: "+e);return R.call(this,r,e,t,n)},f.prototype.getField=function(e){const t=y[e];if(void 0===t)throw new Error("Unsupported type: "+e);return I.call(this,t,e,[])},f.prototype.getStaticField=function(e){const t=w[e];if(void 0===t)throw new Error("Unsupported type: "+e);return I.call(this,t,e,[])},f.prototype.setField=function(e){const t=v[e];if(void 0===t)throw new Error("Unsupported type: "+e);return I.call(this,t,"void",[e])},f.prototype.setStaticField=function(e){const t=E[e];if(void 0===t)throw new Error("Unsupported type: "+e);return I.call(this,t,"void",[e])};let O=null;f.prototype.javaLangClass=function(){if(null===O){const e=this.findClass("java/lang/Class");try{const t=this.getMethodId.bind(this,e);O={handle:C(this.newGlobalRef(e)),getName:t("getName","()Ljava/lang/String;"),getSimpleName:t("getSimpleName","()Ljava/lang/String;"),getGenericSuperclass:t("getGenericSuperclass","()Ljava/lang/reflect/Type;"),getDeclaredConstructors:t("getDeclaredConstructors","()[Ljava/lang/reflect/Constructor;"),getDeclaredMethods:t("getDeclaredMethods","()[Ljava/lang/reflect/Method;"),getDeclaredFields:t("getDeclaredFields","()[Ljava/lang/reflect/Field;"),isArray:t("isArray","()Z"),isPrimitive:t("isPrimitive","()Z"),isInterface:t("isInterface","()Z"),getComponentType:t("getComponentType","()Ljava/lang/Class;")}}finally{this.deleteLocalRef(e)}}return O};let F=null;f.prototype.javaLangObject=function(){if(null===F){const e=this.findClass("java/lang/Object");try{const t=this.getMethodId.bind(this,e);F={handle:C(this.newGlobalRef(e)),toString:t("toString","()Ljava/lang/String;"),getClass:t("getClass","()Ljava/lang/Class;")}}finally{this.deleteLocalRef(e)}}return F};let D=null;f.prototype.javaLangReflectConstructor=function(){if(null===D){const e=this.findClass("java/lang/reflect/Constructor");try{D={getGenericParameterTypes:this.getMethodId(e,"getGenericParameterTypes","()[Ljava/lang/reflect/Type;")}}finally{this.deleteLocalRef(e)}}return D};let z=null;f.prototype.javaLangReflectMethod=function(){if(null===z){const e=this.findClass("java/lang/reflect/Method");try{const t=this.getMethodId.bind(this,e);z={getName:t("getName","()Ljava/lang/String;"),getGenericParameterTypes:t("getGenericParameterTypes","()[Ljava/lang/reflect/Type;"),getParameterTypes:t("getParameterTypes","()[Ljava/lang/Class;"),getGenericReturnType:t("getGenericReturnType","()Ljava/lang/reflect/Type;"),getGenericExceptionTypes:t("getGenericExceptionTypes","()[Ljava/lang/reflect/Type;"),getModifiers:t("getModifiers","()I"),isVarArgs:t("isVarArgs","()Z")}}finally{this.deleteLocalRef(e)}}return z};let $=null;f.prototype.javaLangReflectField=function(){if(null===$){const e=this.findClass("java/lang/reflect/Field");try{const t=this.getMethodId.bind(this,e);$={getName:t("getName","()Ljava/lang/String;"),getType:t("getType","()Ljava/lang/Class;"),getGenericType:t("getGenericType","()Ljava/lang/reflect/Type;"),getModifiers:t("getModifiers","()I"),toString:t("toString","()Ljava/lang/String;")}}finally{this.deleteLocalRef(e)}}return $};let J=null;f.prototype.javaLangReflectTypeVariable=function(){if(null===J){const e=this.findClass("java/lang/reflect/TypeVariable");try{const t=this.getMethodId.bind(this,e);J={handle:C(this.newGlobalRef(e)),getName:t("getName","()Ljava/lang/String;"),getBounds:t("getBounds","()[Ljava/lang/reflect/Type;"),getGenericDeclaration:t("getGenericDeclaration","()Ljava/lang/reflect/GenericDeclaration;")}}finally{this.deleteLocalRef(e)}}return J};let V=null;f.prototype.javaLangReflectWildcardType=function(){if(null===V){const e=this.findClass("java/lang/reflect/WildcardType");try{const t=this.getMethodId.bind(this,e);V={handle:C(this.newGlobalRef(e)),getLowerBounds:t("getLowerBounds","()[Ljava/lang/reflect/Type;"),getUpperBounds:t("getUpperBounds","()[Ljava/lang/reflect/Type;")}}finally{this.deleteLocalRef(e)}}return V};let B=null;f.prototype.javaLangReflectGenericArrayType=function(){if(null===B){const e=this.findClass("java/lang/reflect/GenericArrayType");try{B={handle:C(this.newGlobalRef(e)),getGenericComponentType:this.getMethodId(e,"getGenericComponentType","()Ljava/lang/reflect/Type;")}}finally{this.deleteLocalRef(e)}}return B};let G=null;f.prototype.javaLangReflectParameterizedType=function(){if(null===G){const e=this.findClass("java/lang/reflect/ParameterizedType");try{const t=this.getMethodId.bind(this,e);G={handle:C(this.newGlobalRef(e)),getActualTypeArguments:t("getActualTypeArguments","()[Ljava/lang/reflect/Type;"),getRawType:t("getRawType","()Ljava/lang/reflect/Type;"),getOwnerType:t("getOwnerType","()Ljava/lang/reflect/Type;")}}finally{this.deleteLocalRef(e)}}return G};let Z=null;f.prototype.javaLangString=function(){if(null===Z){const e=this.findClass("java/lang/String");try{Z={handle:C(this.newGlobalRef(e))}}finally{this.deleteLocalRef(e)}}return Z},f.prototype.getClassName=function(e){const t=this.vaMethod("pointer",[])(this.handle,e,this.javaLangClass().getName);try{return this.stringFromJni(t)}finally{this.deleteLocalRef(t)}},f.prototype.getObjectClassName=function(e){const t=this.getObjectClass(e);try{return this.getClassName(t)}finally{this.deleteLocalRef(t)}},f.prototype.getActualTypeArgument=function(e){const t=this.vaMethod("pointer",[])(this.handle,e,this.javaLangReflectParameterizedType().getActualTypeArguments);if(this.throwIfExceptionPending(),!t.isNull())try{return this.getTypeNameFromFirstTypeElement(t)}finally{this.deleteLocalRef(t)}},f.prototype.getTypeNameFromFirstTypeElement=function(e){if(!(this.getArrayLength(e)>0))return"java.lang.Object";{const t=this.getObjectArrayElement(e,0);try{return this.getTypeName(t)}finally{this.deleteLocalRef(t)}}},f.prototype.getTypeName=function(e,t){const n=this.vaMethod("pointer",[]);if(this.isInstanceOf(e,this.javaLangClass().handle))return this.getClassName(e);if(this.isInstanceOf(e,this.javaLangReflectGenericArrayType().handle))return this.getArrayTypeName(e);if(this.isInstanceOf(e,this.javaLangReflectParameterizedType().handle)){const r=n(this.handle,e,this.javaLangReflectParameterizedType().getRawType);let o;this.throwIfExceptionPending();try{o=this.getTypeName(r)}finally{this.deleteLocalRef(r)}return t&&(o+="<"+this.getActualTypeArgument(e)+">"),o}return this.isInstanceOf(e,this.javaLangReflectTypeVariable().handle)||this.isInstanceOf(e,this.javaLangReflectWildcardType().handle),"java.lang.Object"},f.prototype.getArrayTypeName=function(e){const t=this.vaMethod("pointer",[]);if(this.isInstanceOf(e,this.javaLangClass().handle))return this.getClassName(e);if(!this.isInstanceOf(e,this.javaLangReflectGenericArrayType().handle))return"[Ljava.lang.Object;";{const n=t(this.handle,e,this.javaLangReflectGenericArrayType().getGenericComponentType);this.throwIfExceptionPending();try{return"[L"+this.getTypeName(n)+";"}finally{this.deleteLocalRef(n)}}},f.prototype.stringFromJni=function(e){const t=this.getStringChars(e);if(t.isNull())throw new Error("Unable to access string");try{const n=this.getStringLength(e);return t.readUtf16String(n)}finally{this.releaseStringChars(e,t)}};const H=Process.pointerSize,q=Process.getCurrentThreadId(),W=new Map,K=new Map;function Q(e){const t=e.vm;let n=null,r=null,i=null;function a(e){const t=K.get(e);return void 0===t?null:t[0]}this.handle=t,this.perform=function(e){const t=Process.getCurrentThreadId(),n=a(t);if(null!==n)return e(n);let r=this._tryGetEnv();const o=null!==r;o||(r=this.attachCurrentThread(),W.set(t,!0)),this.link(t,r);try{return e(r)}finally{const e=t===q;if(e||this.unlink(t),!o&&!e){const e=W.get(t);W.delete(t),e&&this.detachCurrentThread()}}},this.attachCurrentThread=function(){const e=Memory.alloc(H);return o("VM::AttachCurrentThread",n(t,e,NULL)),new f(e.readPointer(),this)},this.detachCurrentThread=function(){o("VM::DetachCurrentThread",r(t))},this.preventDetachDueToClassLoader=function(){const e=Process.getCurrentThreadId();W.has(e)&&W.set(e,!1)},this.getEnv=function(){const e=a(Process.getCurrentThreadId());if(null!==e)return e;const n=Memory.alloc(H),r=i(t,n,65542);if(-2===r)throw new Error("Current thread is not attached to the Java VM; please move this code inside a Java.perform() callback");return o("VM::GetEnv",r),new f(n.readPointer(),this)},this.tryGetEnv=function(){const e=a(Process.getCurrentThreadId());return null!==e?e:this._tryGetEnv()},this._tryGetEnv=function(){const e=this.tryGetEnvHandle(65542);return null===e?null:new f(e,this)},this.tryGetEnvHandle=function(e){const n=Memory.alloc(H);return 0!==i(t,n,e)?null:n.readPointer()},this.makeHandleDestructor=function(e){return()=>{this.perform(t=>{t.deleteGlobalRef(e)})}},this.link=function(e,t){const n=K.get(e);void 0===n?K.set(e,[t,1]):n[1]++},this.unlink=function(e){const t=K.get(e);1===t[1]?K.delete(e):t[1]--},function(){const e=t.readPointer(),o={exceptions:"propagate"};n=new NativeFunction(e.add(4*H).readPointer(),"int32",["pointer","pointer","pointer"],o),r=new NativeFunction(e.add(5*H).readPointer(),"int32",["pointer"],o),i=new NativeFunction(e.add(6*H).readPointer(),"int32",["pointer","pointer","int32"],o)}.call(this)}Q.dispose=function(e){!0===W.get(q)&&(W.delete(q),e.detachCurrentThread())};const X=Process.pointerSize,{readU32:Y,readPointer:ee,writeU32:te,writePointer:ne}=NativePointer.prototype,re=256,oe=ptr(1).not(),ie=17*X,ae=18*X,se=2147483648,le=3*X,ce=3*X,de=p(function(e){const t=e.vm,n=e.artRuntime,r=4===X?200:384,o=r+100*X,i=be(),a=ge(),{isApiLevel34OrApexEquivalent:s}=e;let l=null;for(let e=r;e!==o;e+=X){if(n.add(e).readPointer().equals(t)){let t,r=null;i>=33||"Tiramisu"===a||s?(t=[e-4*X],r=e-X):i>=30||"R"===a?(t=[e-3*X,e-4*X],r=e-X):t=i>=29?[e-2*X]:i>=27?[e-le-3*X]:[e-le-2*X];for(const e of t){const t=e-X,o=t-X;let a;a=s?o-9*X:i>=24?o-8*X:i>=23?o-7*X:o-4*X;const c={offset:{heap:a,threadList:o,internTable:t,classLinker:e,jniIdManager:r}};if(null!==Ve(n,c)){l=c;break}}break}}if(null===l)throw new Error("Unable to determine Runtime field offsets");return l.offset.instrumentation=function(e){const t=e["art::Runtime::DeoptimizeBootImage"];if(void 0===t)return null;return h(t,De[Process.arch],{limit:30})}(e),l.offset.jniIdsIndirection=function(e){const t=e.find("_ZN3art7Runtime12SetJniIdTypeENS_9JniIdTypeE");if(null===t)return null;const n=h(t,$e[Process.arch],{limit:20});if(null===n)throw new Error("Unable to determine Runtime.jni_ids_indirection_ offset");return n}(e),l}),ue=p(function(){const e={"4-21":136,"4-22":136,"4-23":172,"4-24":196,"4-25":196,"4-26":196,"4-27":196,"4-28":212,"4-29":172,"4-30":180,"4-31":180,"8-21":224,"8-22":224,"8-23":296,"8-24":344,"8-25":344,"8-26":352,"8-27":352,"8-28":392,"8-29":328,"8-30":336,"8-31":336}[`${X}-${be()}`];if(void 0===e)throw new Error("Unable to determine Instrumentation field offsets");return{offset:{forcedInterpretOnly:4,deoptimizationEnabled:e}}}),he=p(function(e){const t=Oe();let n;return e.perform(e=>{const r=e.findClass("android/os/Process"),o=vt(e.getStaticMethodId(r,"getElapsedCpuTime","()J"));e.deleteLocalRef(r);const i=Process.getModuleByName("libandroid_runtime.so"),a=i.base,s=a.add(i.size),l=be(),c=l<=21?8:X;let d=null,u=null,h=2;for(let e=0;64!==e&&0!==h;e+=4){const t=o.add(e);if(null===d){const n=t.readPointer();n.compare(a)>=0&&n.compare(s)<0&&(d=e,h--)}if(null===u){281==(2950692863&t.readU32())&&(u=e,h--)}}if(0!==h)throw new Error("Unable to determine ArtMethod field offsets");const p=d+c;n={size:l<=21?p+32:p+X,offset:{jniCode:d,quickCode:p,accessFlags:u}},"artInterpreterToCompiledCodeBridge"in t&&(n.offset.interpreterCode=d-c)}),n}),pe=p(function(e){const t=be();let n;return e.perform(e=>{const r=We(e),o=e.handle;let i=null,a=null,s=null,l=null,c=null,d=null;for(let e=144;256!==e;e+=X){if(r.add(e).readPointer().equals(o)){a=e-6*X,c=e-4*X,d=e+2*X,t<=22&&(a-=X,i=a-X-72-12,s=e+6*X,c-=X,d-=X),l=e+9*X,t<=22&&(l+=2*X+4,8===X&&(l+=4)),t>=23&&(l+=X);break}}if(null===l)throw new Error("Unable to determine ArtThread field offsets");n={offset:{isExceptionReportedToInstrumentation:i,exception:a,throwLocation:s,topHandleScope:l,managedStack:c,self:d}}}),n}),fe=p(function(){return be()>=23?{offset:{topQuickFrame:0,link:X}}:{offset:{topQuickFrame:2*X,link:0}}}),_e=p(function(e,t){const n=new NativeCallback(et,"void",["pointer"]);return Dt(e,t,n)}),me=p(function(){return Xe("ro.build.version.release")}),ge=p(function(){return Xe("ro.build.version.codename")}),be=p(function(){return parseInt(Xe("ro.build.version.sdk"),10)}),ye=p(function(e){let t=NULL;switch(Process.arch){case"ia32":t=dt(32,t=>{t.putMovRegRegOffsetPtr("ecx","esp",4),t.putMovRegRegOffsetPtr("edx","esp",8),t.putCallAddressWithArguments(e,["ecx","edx"]),t.putMovRegReg("esp","ebp"),t.putPopReg("ebp"),t.putRet()});break;case"x64":t=dt(32,t=>{t.putPushReg("rdi"),t.putCallAddressWithArguments(e,["rsi"]),t.putPopReg("rdi"),t.putMovRegPtrReg("rdi","rax"),t.putMovRegOffsetPtrReg("rdi",8,"edx"),t.putRet()});break;case"arm":t=dt(16,t=>{t.putCallAddressWithArguments(e,["r0","r1"]),t.putPopRegs(["r0","lr"]),t.putMovRegReg("pc","lr")});break;case"arm64":t=dt(64,t=>{t.putPushRegReg("x0","lr"),t.putCallAddressWithArguments(e,["x1"]),t.putPopRegReg("x2","lr"),t.putStrRegRegOffset("x0","x2",0),t.putStrRegRegOffset("w1","x2",8),t.putRet()})}return new NativeFunction(t,"void",["pointer","pointer"],we)}),ve="ia32"===Process.arch?function(e,t){const n=new NativeFunction(e,"void",["pointer"].concat(t),we);return function(){const e=Memory.alloc(X);return n(e,...arguments),e.readPointer()}}:function(e,t){return new NativeFunction(e,"pointer",t,we)},we={exceptions:"propagate"},Ee={};let Se=null,Ne=null,Le=null,Ce=null;const ke=[],Me=new Map,je=[];let Ie=null,Re=0,Ae=!1,Pe=!1,xe=null,Te=null,Ue=null;function Oe(){return null===Se&&(Se=function(){const e=Process.enumerateModules().filter(e=>/^lib(art|dvm).so$/.test(e.name)).filter(e=>!/\/system\/fake-libs/.test(e.path));if(0===e.length)return null;const t=e[0],n=-1!==t.name.indexOf("art")?"art":"dalvik",r="art"===n,i={module:t,find(e){const{module:t}=this;let n=t.findExportByName(e);return null===n&&(n=t.findSymbolByName(e)),n},flavor:n,addLocalReference:null};i.isApiLevel34OrApexEquivalent=r&&(null!==i.find("_ZN3art7AppInfo29GetPrimaryApkReferenceProfileEv")||null!==i.find("_ZN3art6Thread15RunFlipFunctionEPS0_"));const a=r?{functions:{JNI_GetCreatedJavaVMs:["JNI_GetCreatedJavaVMs","int",["pointer","int","pointer"]],artInterpreterToCompiledCodeBridge:function(e){this.artInterpreterToCompiledCodeBridge=e},_ZN3art9JavaVMExt12AddGlobalRefEPNS_6ThreadENS_6ObjPtrINS_6mirror6ObjectEEE:["art::JavaVMExt::AddGlobalRef","pointer",["pointer","pointer","pointer"]],_ZN3art9JavaVMExt12AddGlobalRefEPNS_6ThreadEPNS_6mirror6ObjectE:["art::JavaVMExt::AddGlobalRef","pointer",["pointer","pointer","pointer"]],_ZN3art17ReaderWriterMutex13ExclusiveLockEPNS_6ThreadE:["art::ReaderWriterMutex::ExclusiveLock","void",["pointer","pointer"]],_ZN3art17ReaderWriterMutex15ExclusiveUnlockEPNS_6ThreadE:["art::ReaderWriterMutex::ExclusiveUnlock","void",["pointer","pointer"]],_ZN3art22IndirectReferenceTable3AddEjPNS_6mirror6ObjectE:function(e){this["art::IndirectReferenceTable::Add"]=new NativeFunction(e,"pointer",["pointer","uint","pointer"],we)},_ZN3art22IndirectReferenceTable3AddENS_15IRTSegmentStateENS_6ObjPtrINS_6mirror6ObjectEEE:function(e){this["art::IndirectReferenceTable::Add"]=new NativeFunction(e,"pointer",["pointer","uint","pointer"],we)},_ZN3art9JavaVMExt12DecodeGlobalEPv:function(e){let t;t=be()>=26?ve(e,["pointer","pointer"]):new NativeFunction(e,"pointer",["pointer","pointer"],we),this["art::JavaVMExt::DecodeGlobal"]=function(e,n,r){return t(e,r)}},_ZN3art9JavaVMExt12DecodeGlobalEPNS_6ThreadEPv:["art::JavaVMExt::DecodeGlobal","pointer",["pointer","pointer","pointer"]],_ZNK3art6Thread19DecodeGlobalJObjectEP8_jobject:["art::Thread::DecodeJObject","pointer",["pointer","pointer"]],_ZNK3art6Thread13DecodeJObjectEP8_jobject:["art::Thread::DecodeJObject","pointer",["pointer","pointer"]],_ZN3art10ThreadList10SuspendAllEPKcb:["art::ThreadList::SuspendAll","void",["pointer","pointer","bool"]],_ZN3art10ThreadList10SuspendAllEv:function(e){const t=new NativeFunction(e,"void",["pointer"],we);this["art::ThreadList::SuspendAll"]=function(e,n,r){return t(e)}},_ZN3art10ThreadList9ResumeAllEv:["art::ThreadList::ResumeAll","void",["pointer"]],_ZN3art11ClassLinker12VisitClassesEPNS_12ClassVisitorE:["art::ClassLinker::VisitClasses","void",["pointer","pointer"]],_ZN3art11ClassLinker12VisitClassesEPFbPNS_6mirror5ClassEPvES4_:function(e){const t=new NativeFunction(e,"void",["pointer","pointer","pointer"],we);this["art::ClassLinker::VisitClasses"]=function(e,n){t(e,n,NULL)}},_ZNK3art11ClassLinker17VisitClassLoadersEPNS_18ClassLoaderVisitorE:["art::ClassLinker::VisitClassLoaders","void",["pointer","pointer"]],_ZN3art2gc4Heap12VisitObjectsEPFvPNS_6mirror6ObjectEPvES5_:["art::gc::Heap::VisitObjects","void",["pointer","pointer","pointer"]],_ZN3art2gc4Heap12GetInstancesERNS_24VariableSizedHandleScopeENS_6HandleINS_6mirror5ClassEEEiRNSt3__16vectorINS4_INS5_6ObjectEEENS8_9allocatorISB_EEEE:["art::gc::Heap::GetInstances","void",["pointer","pointer","pointer","int","pointer"]],_ZN3art2gc4Heap12GetInstancesERNS_24VariableSizedHandleScopeENS_6HandleINS_6mirror5ClassEEEbiRNSt3__16vectorINS4_INS5_6ObjectEEENS8_9allocatorISB_EEEE:function(e){const t=new NativeFunction(e,"void",["pointer","pointer","pointer","bool","int","pointer"],we);this["art::gc::Heap::GetInstances"]=function(e,n,r,o,i){t(e,n,r,0,o,i)}},_ZN3art12StackVisitorC2EPNS_6ThreadEPNS_7ContextENS0_13StackWalkKindEjb:["art::StackVisitor::StackVisitor","void",["pointer","pointer","pointer","uint","uint","bool"]],_ZN3art12StackVisitorC2EPNS_6ThreadEPNS_7ContextENS0_13StackWalkKindEmb:["art::StackVisitor::StackVisitor","void",["pointer","pointer","pointer","uint","size_t","bool"]],_ZN3art12StackVisitor9WalkStackILNS0_16CountTransitionsE0EEEvb:["art::StackVisitor::WalkStack","void",["pointer","bool"]],_ZNK3art12StackVisitor9GetMethodEv:["art::StackVisitor::GetMethod","pointer",["pointer"]],_ZNK3art12StackVisitor16DescribeLocationEv:function(e){this["art::StackVisitor::DescribeLocation"]=Vt(e,["pointer"])},_ZNK3art12StackVisitor24GetCurrentQuickFrameInfoEv:function(e){var t;this["art::StackVisitor::GetCurrentQuickFrameInfo"]=(t=e,function(e){const n=Memory.alloc(12);return ye(t)(n,e),{frameSizeInBytes:n.readU32(),coreSpillMask:n.add(4).readU32(),fpSpillMask:n.add(8).readU32()}})},_ZN3art6Thread18GetLongJumpContextEv:["art::Thread::GetLongJumpContext","pointer",["pointer"]],_ZN3art6mirror5Class13GetDescriptorEPNSt3__112basic_stringIcNS2_11char_traitsIcEENS2_9allocatorIcEEEE:function(e){this["art::mirror::Class::GetDescriptor"]=e},_ZN3art6mirror5Class11GetLocationEv:function(e){this["art::mirror::Class::GetLocation"]=Vt(e,["pointer"])},_ZN3art9ArtMethod12PrettyMethodEb:function(e){this["art::ArtMethod::PrettyMethod"]=Vt(e,["pointer","bool"])},_ZN3art12PrettyMethodEPNS_9ArtMethodEb:function(e){this["art::ArtMethod::PrettyMethodNullSafe"]=Vt(e,["pointer","bool"])},_ZN3art6Thread14CurrentFromGdbEv:["art::Thread::CurrentFromGdb","pointer",[]],_ZN3art6mirror6Object5CloneEPNS_6ThreadE:function(e){this["art::mirror::Object::Clone"]=new NativeFunction(e,"pointer",["pointer","pointer"],we)},_ZN3art6mirror6Object5CloneEPNS_6ThreadEm:function(e){const t=new NativeFunction(e,"pointer",["pointer","pointer","pointer"],we);this["art::mirror::Object::Clone"]=function(e,n){const r=NULL;return t(e,n,r)}},_ZN3art6mirror6Object5CloneEPNS_6ThreadEj:function(e){const t=new NativeFunction(e,"pointer",["pointer","pointer","uint"],we);this["art::mirror::Object::Clone"]=function(e,n){return t(e,n,0)}},_ZN3art3Dbg14SetJdwpAllowedEb:["art::Dbg::SetJdwpAllowed","void",["bool"]],_ZN3art3Dbg13ConfigureJdwpERKNS_4JDWP11JdwpOptionsE:["art::Dbg::ConfigureJdwp","void",["pointer"]],_ZN3art31InternalDebuggerControlCallback13StartDebuggerEv:["art::InternalDebuggerControlCallback::StartDebugger","void",["pointer"]],_ZN3art3Dbg9StartJdwpEv:["art::Dbg::StartJdwp","void",[]],_ZN3art3Dbg8GoActiveEv:["art::Dbg::GoActive","void",[]],_ZN3art3Dbg21RequestDeoptimizationERKNS_21DeoptimizationRequestE:["art::Dbg::RequestDeoptimization","void",["pointer"]],_ZN3art3Dbg20ManageDeoptimizationEv:["art::Dbg::ManageDeoptimization","void",[]],_ZN3art15instrumentation15Instrumentation20EnableDeoptimizationEv:["art::Instrumentation::EnableDeoptimization","void",["pointer"]],_ZN3art15instrumentation15Instrumentation20DeoptimizeEverythingEPKc:["art::Instrumentation::DeoptimizeEverything","void",["pointer","pointer"]],_ZN3art15instrumentation15Instrumentation20DeoptimizeEverythingEv:function(e){const t=new NativeFunction(e,"void",["pointer"],we);this["art::Instrumentation::DeoptimizeEverything"]=function(e,n){t(e)}},_ZN3art7Runtime19DeoptimizeBootImageEv:["art::Runtime::DeoptimizeBootImage","void",["pointer"]],_ZN3art15instrumentation15Instrumentation10DeoptimizeEPNS_9ArtMethodE:["art::Instrumentation::Deoptimize","void",["pointer","pointer"]],_ZN3art3jni12JniIdManager14DecodeMethodIdEP10_jmethodID:["art::jni::JniIdManager::DecodeMethodId","pointer",["pointer","pointer"]],_ZN3art3jni12JniIdManager13DecodeFieldIdEP9_jfieldID:["art::jni::JniIdManager::DecodeFieldId","pointer",["pointer","pointer"]],_ZN3art11interpreter18GetNterpEntryPointEv:["art::interpreter::GetNterpEntryPoint","pointer",[]],_ZN3art7Monitor17TranslateLocationEPNS_9ArtMethodEjPPKcPi:["art::Monitor::TranslateLocation","void",["pointer","uint32","pointer","pointer"]]},variables:{_ZN3art3Dbg9gRegistryE:function(e){this.isJdwpStarted=()=>!e.readPointer().isNull()},_ZN3art3Dbg15gDebuggerActiveE:function(e){this.isDebuggerActive=()=>!!e.readU8()}},optionals:new Set(["artInterpreterToCompiledCodeBridge","_ZN3art9JavaVMExt12AddGlobalRefEPNS_6ThreadENS_6ObjPtrINS_6mirror6ObjectEEE","_ZN3art9JavaVMExt12AddGlobalRefEPNS_6ThreadEPNS_6mirror6ObjectE","_ZN3art9JavaVMExt12DecodeGlobalEPv","_ZN3art9JavaVMExt12DecodeGlobalEPNS_6ThreadEPv","_ZNK3art6Thread19DecodeGlobalJObjectEP8_jobject","_ZNK3art6Thread13DecodeJObjectEP8_jobject","_ZN3art10ThreadList10SuspendAllEPKcb","_ZN3art10ThreadList10SuspendAllEv","_ZN3art11ClassLinker12VisitClassesEPNS_12ClassVisitorE","_ZN3art11ClassLinker12VisitClassesEPFbPNS_6mirror5ClassEPvES4_","_ZNK3art11ClassLinker17VisitClassLoadersEPNS_18ClassLoaderVisitorE","_ZN3art6mirror6Object5CloneEPNS_6ThreadE","_ZN3art6mirror6Object5CloneEPNS_6ThreadEm","_ZN3art6mirror6Object5CloneEPNS_6ThreadEj","_ZN3art22IndirectReferenceTable3AddEjPNS_6mirror6ObjectE","_ZN3art22IndirectReferenceTable3AddENS_15IRTSegmentStateENS_6ObjPtrINS_6mirror6ObjectEEE","_ZN3art2gc4Heap12VisitObjectsEPFvPNS_6mirror6ObjectEPvES5_","_ZN3art2gc4Heap12GetInstancesERNS_24VariableSizedHandleScopeENS_6HandleINS_6mirror5ClassEEEiRNSt3__16vectorINS4_INS5_6ObjectEEENS8_9allocatorISB_EEEE","_ZN3art2gc4Heap12GetInstancesERNS_24VariableSizedHandleScopeENS_6HandleINS_6mirror5ClassEEEbiRNSt3__16vectorINS4_INS5_6ObjectEEENS8_9allocatorISB_EEEE","_ZN3art12StackVisitorC2EPNS_6ThreadEPNS_7ContextENS0_13StackWalkKindEjb","_ZN3art12StackVisitorC2EPNS_6ThreadEPNS_7ContextENS0_13StackWalkKindEmb","_ZN3art12StackVisitor9WalkStackILNS0_16CountTransitionsE0EEEvb","_ZNK3art12StackVisitor9GetMethodEv","_ZNK3art12StackVisitor16DescribeLocationEv","_ZNK3art12StackVisitor24GetCurrentQuickFrameInfoEv","_ZN3art6Thread18GetLongJumpContextEv","_ZN3art6mirror5Class13GetDescriptorEPNSt3__112basic_stringIcNS2_11char_traitsIcEENS2_9allocatorIcEEEE","_ZN3art6mirror5Class11GetLocationEv","_ZN3art9ArtMethod12PrettyMethodEb","_ZN3art12PrettyMethodEPNS_9ArtMethodEb","_ZN3art3Dbg13ConfigureJdwpERKNS_4JDWP11JdwpOptionsE","_ZN3art31InternalDebuggerControlCallback13StartDebuggerEv","_ZN3art3Dbg15gDebuggerActiveE","_ZN3art15instrumentation15Instrumentation20EnableDeoptimizationEv","_ZN3art15instrumentation15Instrumentation20DeoptimizeEverythingEPKc","_ZN3art15instrumentation15Instrumentation20DeoptimizeEverythingEv","_ZN3art7Runtime19DeoptimizeBootImageEv","_ZN3art15instrumentation15Instrumentation10DeoptimizeEPNS_9ArtMethodE","_ZN3art3Dbg9StartJdwpEv","_ZN3art3Dbg8GoActiveEv","_ZN3art3Dbg21RequestDeoptimizationERKNS_21DeoptimizationRequestE","_ZN3art3Dbg20ManageDeoptimizationEv","_ZN3art3Dbg9gRegistryE","_ZN3art3jni12JniIdManager14DecodeMethodIdEP10_jmethodID","_ZN3art3jni12JniIdManager13DecodeFieldIdEP9_jfieldID","_ZN3art11interpreter18GetNterpEntryPointEv","_ZN3art7Monitor17TranslateLocationEPNS_9ArtMethodEjPPKcPi"])}:{functions:{_Z20dvmDecodeIndirectRefP6ThreadP8_jobject:["dvmDecodeIndirectRef","pointer",["pointer","pointer"]],_Z15dvmUseJNIBridgeP6MethodPv:["dvmUseJNIBridge","void",["pointer","pointer"]],_Z20dvmHeapSourceGetBasev:["dvmHeapSourceGetBase","pointer",[]],_Z21dvmHeapSourceGetLimitv:["dvmHeapSourceGetLimit","pointer",[]],_Z16dvmIsValidObjectPK6Object:["dvmIsValidObject","uint8",["pointer"]],JNI_GetCreatedJavaVMs:["JNI_GetCreatedJavaVMs","int",["pointer","int","pointer"]]},variables:{gDvmJni:function(e){this.gDvmJni=e},gDvm:function(e){this.gDvm=e}}},{functions:s={},variables:l={},optionals:c=new Set}=a,d=[];for(const[e,t]of Object.entries(s)){const n=i.find(e);null!==n?"function"==typeof t?t.call(i,n):i[t[0]]=new NativeFunction(n,t[1],t[2],we):c.has(e)||d.push(e)}for(const[e,t]of Object.entries(l)){const n=i.find(e);null!==n?t.call(i,n):c.has(e)||d.push(e)}if(d.length>0)throw new Error("Java API only partially available; please file a bug. Missing: "+d.join(", "));const u=Memory.alloc(X),h=Memory.alloc(4);if(o("JNI_GetCreatedJavaVMs",i.JNI_GetCreatedJavaVMs(u,1,h)),0===h.readInt())return null;if(i.vm=u.readPointer(),r){const e=be();let t;t=e>=27?33554432:e>=24?16777216:0,i.kAccCompileDontBother=t;const n=i.vm.add(X).readPointer();i.artRuntime=n;const r=de(i),o=r.offset,a=o.instrumentation;i.artInstrumentation=null!==a?n.add(a):null,i.artHeap=n.add(o.heap).readPointer(),i.artThreadList=n.add(o.threadList).readPointer();const s=n.add(o.classLinker).readPointer(),l=function(e,t){const n=Ve(e,t);if(null===n)throw new Error("Unable to determine ClassLinker field offsets");return n}(n,r).offset,c=s.add(l.quickResolutionTrampoline).readPointer(),d=s.add(l.quickImtConflictTrampoline).readPointer(),u=s.add(l.quickGenericJniTrampoline).readPointer(),h=s.add(l.quickToInterpreterBridgeTrampoline).readPointer();i.artClassLinker={address:s,quickResolutionTrampoline:c,quickImtConflictTrampoline:d,quickGenericJniTrampoline:u,quickToInterpreterBridgeTrampoline:h};const p=new Q(i);i.artQuickGenericJniTrampoline=He(u,p),i.artQuickToInterpreterBridge=He(h,p),i.artQuickResolutionTrampoline=He(c,p),void 0===i["art::JavaVMExt::AddGlobalRef"]&&(i["art::JavaVMExt::AddGlobalRef"]=function(e){const t=4===X?{globalsLock:32,globals:72}:{globalsLock:64,globals:112},n=e.vm.add(t.globalsLock),r=e.vm.add(t.globals),o=e["art::IndirectReferenceTable::Add"],i=e["art::ReaderWriterMutex::ExclusiveLock"],a=e["art::ReaderWriterMutex::ExclusiveUnlock"],s=0;return function(e,t,l){i(n,t);try{return o(r,s,l)}finally{a(n,t)}}}(i)),void 0===i["art::JavaVMExt::DecodeGlobal"]&&(i["art::JavaVMExt::DecodeGlobal"]=function(e){const t=e["art::Thread::DecodeJObject"];if(void 0===t)throw new Error("art::Thread::DecodeJObject is not available; please file a bug");return function(e,n,r){return t(n,r)}}(i)),void 0===i["art::ArtMethod::PrettyMethod"]&&(i["art::ArtMethod::PrettyMethod"]=i["art::ArtMethod::PrettyMethodNullSafe"]),void 0!==i["art::interpreter::GetNterpEntryPoint"]?i.artNterpEntryPoint=i["art::interpreter::GetNterpEntryPoint"]():i.artNterpEntryPoint=i.find("ExecuteNterpImpl"),Ce=function(e,t){const n=pe(t).offset,r=fe().offset,o=`\n#include <gum/guminterceptor.h>\n\nextern GMutex lock;\nextern GHashTable * methods;\nextern GHashTable * replacements;\nextern gpointer last_seen_art_method;\n\nextern gpointer get_oat_quick_method_header_impl (gpointer method, gpointer pc);\n\nvoid\ninit (void)\n{\n  g_mutex_init (&lock);\n  methods = g_hash_table_new_full (NULL, NULL, NULL, NULL);\n  replacements = g_hash_table_new_full (NULL, NULL, NULL, NULL);\n}\n\nvoid\nfinalize (void)\n{\n  g_hash_table_unref (replacements);\n  g_hash_table_unref (methods);\n  g_mutex_clear (&lock);\n}\n\ngboolean\nis_replacement_method (gpointer method)\n{\n  gboolean is_replacement;\n\n  g_mutex_lock (&lock);\n\n  is_replacement = g_hash_table_contains (replacements, method);\n\n  g_mutex_unlock (&lock);\n\n  return is_replacement;\n}\n\ngpointer\nget_replacement_method (gpointer original_method)\n{\n  gpointer replacement_method;\n\n  g_mutex_lock (&lock);\n\n  replacement_method = g_hash_table_lookup (methods, original_method);\n\n  g_mutex_unlock (&lock);\n\n  return replacement_method;\n}\n\nvoid\nset_replacement_method (gpointer original_method,\n                        gpointer replacement_method)\n{\n  g_mutex_lock (&lock);\n\n  g_hash_table_insert (methods, original_method, replacement_method);\n  g_hash_table_insert (replacements, replacement_method, original_method);\n\n  g_mutex_unlock (&lock);\n}\n\nvoid\ndelete_replacement_method (gpointer original_method)\n{\n  gpointer replacement_method;\n\n  g_mutex_lock (&lock);\n\n  replacement_method = g_hash_table_lookup (methods, original_method);\n  if (replacement_method != NULL)\n  {\n    g_hash_table_remove (methods, original_method);\n    g_hash_table_remove (replacements, replacement_method);\n  }\n\n  g_mutex_unlock (&lock);\n}\n\ngpointer\ntranslate_method (gpointer method)\n{\n  gpointer translated_method;\n\n  g_mutex_lock (&lock);\n\n  translated_method = g_hash_table_lookup (replacements, method);\n\n  g_mutex_unlock (&lock);\n\n  return (translated_method != NULL) ? translated_method : method;\n}\n\ngpointer\nfind_replacement_method_from_quick_code (gpointer method,\n                                         gpointer thread)\n{\n  gpointer replacement_method;\n  gpointer managed_stack;\n  gpointer top_quick_frame;\n  gpointer link_managed_stack;\n  gpointer * link_top_quick_frame;\n\n  replacement_method = get_replacement_method (method);\n  if (replacement_method == NULL)\n    return NULL;\n\n  /*\n   * Stack check.\n   *\n   * Return NULL to indicate that the original method should be invoked, otherwise\n   * return a pointer to the replacement ArtMethod.\n   *\n   * If the caller is our own JNI replacement stub, then a stack transition must\n   * have been pushed onto the current thread's linked list.\n   *\n   * Therefore, we invoke the original method if the following conditions are met:\n   *   1- The current managed stack is empty.\n   *   2- The ArtMethod * inside the linked managed stack's top quick frame is the\n   *      same as our replacement.\n   */\n  managed_stack = thread + ${n.managedStack};\n  top_quick_frame = *((gpointer *) (managed_stack + ${r.topQuickFrame}));\n  if (top_quick_frame != NULL)\n    return replacement_method;\n\n  link_managed_stack = *((gpointer *) (managed_stack + ${r.link}));\n  if (link_managed_stack == NULL)\n    return replacement_method;\n\n  link_top_quick_frame = GSIZE_TO_POINTER (*((gsize *) (link_managed_stack + ${r.topQuickFrame})) & ~((gsize) 1));\n  if (link_top_quick_frame == NULL || *link_top_quick_frame != replacement_method)\n    return replacement_method;\n\n  return NULL;\n}\n\nvoid\non_interpreter_do_call (GumInvocationContext * ic)\n{\n  gpointer method, replacement_method;\n\n  method = gum_invocation_context_get_nth_argument (ic, 0);\n\n  replacement_method = get_replacement_method (method);\n  if (replacement_method != NULL)\n    gum_invocation_context_replace_nth_argument (ic, 0, replacement_method);\n}\n\ngpointer\non_art_method_get_oat_quick_method_header (gpointer method,\n                                           gpointer pc)\n{\n  if (is_replacement_method (method))\n    return NULL;\n\n  return get_oat_quick_method_header_impl (method, pc);\n}\n\nvoid\non_art_method_pretty_method (GumInvocationContext * ic)\n{\n  const guint this_arg_index = ${"arm64"===Process.arch?0:1};\n  gpointer method;\n\n  method = gum_invocation_context_get_nth_argument (ic, this_arg_index);\n  if (method == NULL)\n    gum_invocation_context_replace_nth_argument (ic, this_arg_index, last_seen_art_method);\n  else\n    last_seen_art_method = method;\n}\n\nvoid\non_leave_gc_concurrent_copying_copying_phase (GumInvocationContext * ic)\n{\n  GHashTableIter iter;\n  gpointer hooked_method, replacement_method;\n\n  g_mutex_lock (&lock);\n\n  g_hash_table_iter_init (&iter, methods);\n  while (g_hash_table_iter_next (&iter, &hooked_method, &replacement_method))\n    *((uint32_t *) replacement_method) = *((uint32_t *) hooked_method);\n\n  g_mutex_unlock (&lock);\n}\n`,i=8,a=X,s=X,l=X,c=Memory.alloc(i+a+s+l),d=c.add(i),u=d.add(a),h=u.add(s),p=e.find(4===X?"_ZN3art9ArtMethod23GetOatQuickMethodHeaderEj":"_ZN3art9ArtMethod23GetOatQuickMethodHeaderEm"),f=new CModule(o,{lock:c,methods:d,replacements:u,last_seen_art_method:h,get_oat_quick_method_header_impl:p??ptr("0xdeadbeef")}),_={exceptions:"propagate",scheduling:"exclusive"};return{handle:f,replacedMethods:{isReplacement:new NativeFunction(f.is_replacement_method,"bool",["pointer"],_),get:new NativeFunction(f.get_replacement_method,"pointer",["pointer"],_),set:new NativeFunction(f.set_replacement_method,"void",["pointer","pointer"],_),delete:new NativeFunction(f.delete_replacement_method,"void",["pointer"],_),translate:new NativeFunction(f.translate_method,"pointer",["pointer"],_),findReplacementFromQuickCode:f.find_replacement_method_from_quick_code},getOatQuickMethodHeaderImpl:p,hooks:{Interpreter:{doCall:f.on_interpreter_do_call},ArtMethod:{getOatQuickMethodHeader:f.on_art_method_get_oat_quick_method_header,prettyMethod:f.on_art_method_pretty_method},Gc:{copyingPhase:{onLeave:f.on_leave_gc_concurrent_copying_copying_phase},runFlip:{onEnter:f.on_leave_gc_concurrent_copying_copying_phase}}}}}(i,p),function(e){const t=e["art::ArtMethod::PrettyMethod"];if(void 0===t)return;Interceptor.attach(t.impl,Ce.hooks.ArtMethod.prettyMethod),Interceptor.flush()}(i);let f=null;Object.defineProperty(i,"jvmti",{get(){return null===f&&(f=[Fe(p,this.artRuntime)]),f[0]}})}const p=t.enumerateImports().filter(e=>0===e.name.indexOf("_Z")).reduce((e,t)=>(e[t.name]=t.address,e),{});return i.$new=new NativeFunction(p._Znwm||p._Znwj,"pointer",["ulong"],we),i.$delete=new NativeFunction(p._ZdlPv,"void",["pointer"],we),Le=r?Mt:Rt,i}()),Se}function Fe(e,t){let n=null;return e.perform(()=>{const r=Oe().find("_ZN3art7Runtime18EnsurePluginLoadedEPKcPNSt3__112basic_stringIcNS3_11char_traitsIcEENS3_9allocatorIcEEEE");if(null===r)return;const o=new NativeFunction(r,"bool",["pointer","pointer","pointer"]),i=Memory.alloc(X);if(!o(t,Memory.allocUtf8String("libopenjdkjvmti.so"),i))return;const l=1073741824|a,c=e.tryGetEnvHandle(l);if(null===c)return;n=new d(c,e);const u=Memory.alloc(8);u.writeU64(s);0!==n.addCapabilities(u)&&(n=null)}),n}const De={ia32:ze,x64:ze,arm:function(e){if("add.w"!==e.mnemonic)return null;const t=e.operands;if(3!==t.length)return null;const n=t[2];if("imm"!==n.type)return null;return n.value},arm64:function(e){if("add"!==e.mnemonic)return null;const t=e.operands;if(3!==t.length)return null;if("sp"===t[0].value||"sp"===t[1].value)return null;const n=t[2];if("imm"!==n.type)return null;const r=n.value.valueOf();if(r<256||r>1024)return null;return r}};function ze(e){if("lea"!==e.mnemonic)return null;const t=e.operands[1].value.disp;return t<256||t>1024?null:t}const $e={ia32:Je,x64:Je,arm:function(e){if("ldr.w"===e.mnemonic)return e.operands[1].value.disp;return null},arm64:function(e,t){if(null===t)return null;const{mnemonic:n}=e,{mnemonic:r}=t;if("cmp"===n&&"ldr"===r||"bl"===n&&"str"===r)return t.operands[1].value.disp;return null}};function Je(e){return"cmp"===e.mnemonic?e.operands[0].value.disp:null}function Ve(e,t){if(null!==Ne)return Ne;const{classLinker:n,internTable:r}=t.offset,o=e.add(n).readPointer(),i=e.add(r).readPointer(),a=4===X?100:200,s=a+100*X,l=be();let c=null;for(let e=a;e!==s;e+=X){if(o.add(e).readPointer().equals(i)){let t;t=l>=30||"R"===ge()?6:l>=29?4:l>=23?3:5;const n=e+t*X;let r;r=l>=23?n-2*X:n-3*X,c={offset:{quickResolutionTrampoline:r,quickImtConflictTrampoline:n-X,quickGenericJniTrampoline:n,quickToInterpreterBridgeTrampoline:n+X}};break}}return null!==c&&(Ne=c),c}function Be(e){let t=null;return e.perform(n=>{const r=Ge(),o=he(e),i={artArrayLengthSize:4,artArrayEntrySize:r.size,artArrayMax:50},a={artArrayLengthSize:X,artArrayEntrySize:o.size,artArrayMax:100},s=(e,t,n)=>{const r=e.add(t).readPointer();if(r.isNull())return null;const o=4===n?r.readU32():r.readU64().valueOf();return o<=0?null:{length:o,data:r.add(n)}},l=(e,t,n,r)=>{try{const o=s(e,t,r.artArrayLengthSize);if(null===o)return!1;const i=Math.min(o.length,r.artArrayMax);for(let e=0;e!==i;e++){if(o.data.add(e*r.artArrayEntrySize).equals(n))return!0}}catch{}return!1},c=n.findClass("java/lang/Thread"),d=n.newGlobalRef(c);try{let r;Ye(e,n,t=>{r=Oe()["art::JavaVMExt::DecodeGlobal"](e,t,d)});const o=wt(n.getFieldId(d,"name","Ljava/lang/String;")),c=wt(n.getStaticFieldId(d,"MAX_PRIORITY","I"));let u=-1,h=-1;for(let e=0;256!==e;e+=4)-1===u&&l(r,e,c,i)&&(u=e),-1===h&&l(r,e,o,i)&&(h=e);if(-1===h||-1===u)throw new Error("Unable to find fields in java/lang/Thread; please file a bug");const p=h!==u?u:0,f=h;let _=-1;const m=vt(n.getMethodId(d,"getName","()Ljava/lang/String;"));for(let e=0;256!==e;e+=4)-1===_&&l(r,e,m,a)&&(_=e);if(-1===_)throw new Error("Unable to find methods in java/lang/Thread; please file a bug");let g=-1;const b=s(r,_,a.artArrayLengthSize).length;for(let e=_;256!==e;e+=4)if(r.add(e).readU16()===b){g=e;break}if(-1===g)throw new Error("Unable to find copied methods in java/lang/Thread; please file a bug");t={offset:{ifields:f,methods:_,sfields:p,copiedMethodsOffset:g}}}finally{n.deleteLocalRef(c),n.deleteGlobalRef(d)}}),t}function Ge(e){const t=be();return t>=23?{size:16,offset:{accessFlags:4}}:t>=21?{size:24,offset:{accessFlags:12}}:null}const Ze={ia32:qe,x64:qe,arm:function(e){if("ldr.w"===e.mnemonic)return e.operands[1].value.disp;return null},arm64:function(e){if("ldr"===e.mnemonic)return e.operands[1].value.disp;return null}};function He(e,t){let n;return t.perform(t=>{const r=We(t),o=(0,Ze[Process.arch])(Instruction.parse(e));n=null!==o?r.add(o).readPointer():e}),n}function qe(e){return"jmp"===e.mnemonic?e.operands[0].value.disp:null}function We(e){return e.handle.add(X).readPointer()}let Ke=null;const Qe=92;function Xe(e){null===Ke&&(Ke=new NativeFunction(Process.getModuleByName("libc.so").getExportByName("__system_property_get"),"int",["pointer","pointer"],we));const t=Memory.alloc(Qe);return Ke(Memory.allocUtf8String(e),t),t.readUtf8String()}function Ye(e,t,n){const r=_e(e,t),o=We(t).toString();if(Ee[o]=n,r(t.handle),void 0!==Ee[o])throw delete Ee[o],new Error("Unable to perform state transition; please file a bug")}function et(e){const t=e.toString(),n=Ee[t];delete Ee[t],n(e)}function tt(e){const t=Oe(),n=t.artThreadList;t["art::ThreadList::SuspendAll"](n,Memory.allocUtf8String("frida"),0);try{e()}finally{t["art::ThreadList::ResumeAll"](n)}}class nt{constructor(e){const t=Memory.alloc(4*X),n=t.add(X);t.writePointer(n);const r=new NativeCallback((t,n)=>!0===e(n)?1:0,"bool",["pointer","pointer"]);n.add(2*X).writePointer(r),this.handle=t,this._onVisit=r}}function rt(e){return Oe()["art::ClassLinker::VisitClasses"]instanceof NativeFunction?new nt(e):new NativeCallback(t=>!0===e(t)?1:0,"bool",["pointer","pointer"])}class ot{constructor(e){const t=Memory.alloc(4*X),n=t.add(X);t.writePointer(n);const r=new NativeCallback((t,n)=>{e(n)},"void",["pointer","pointer"]);n.add(2*X).writePointer(r),this.handle=t,this._onVisit=r}}function it(e){return new ot(e)}const at={"include-inlined-frames":0,"skip-inlined-frames":1};class st{constructor(e){this.handle=e}prettyMethod(e=!0){const t=new Bt;return Oe()["art::ArtMethod::PrettyMethod"](t,this.handle,e?1:0),t.disposeToString()}toString(){return`ArtMethod(handle=${this.handle})`}}const lt={ia32:globalThis.X86Relocator,x64:globalThis.X86Relocator,arm:globalThis.ThumbRelocator,arm64:globalThis.Arm64Relocator},ct={ia32:globalThis.X86Writer,x64:globalThis.X86Writer,arm:globalThis.ThumbWriter,arm64:globalThis.Arm64Writer};function dt(e,t){null===Ie&&(Ie=Memory.alloc(Process.pageSize));const n=Ie.add(Re),r=Process.arch,o=ct[r];return Memory.patchCode(n,e,r=>{const i=new o(r,{pc:n});if(t(i),i.flush(),i.offset>e)throw new Error(`Wrote ${i.offset}, exceeding maximum of ${e}`)}),Re+=e,"arm"===r?n.or(1):n}function ut(e,t){!function(e){if(Pe)return;Pe=!0,function(e){const t=Oe(),n=[t.artQuickGenericJniTrampoline,t.artQuickToInterpreterBridge,t.artQuickResolutionTrampoline];n.forEach(t=>{Memory.protect(t,32,"rwx");const n=new kt(t);n.activate(e),je.push(n)})}(e),function(){const e=Oe(),t=be(),{isApiLevel34OrApexEquivalent:n}=e;let r;if(t<=22)r=/^_ZN3art11interpreter6DoCallILb[0-1]ELb[0-1]EEEbPNS_6mirror9ArtMethodEPNS_6ThreadERNS_11ShadowFrameEPKNS_11InstructionEtPNS_6JValueE$/;else if(t<=33&&!n)r=/^_ZN3art11interpreter6DoCallILb[0-1]ELb[0-1]EEEbPNS_9ArtMethodEPNS_6ThreadERNS_11ShadowFrameEPKNS_11InstructionEtPNS_6JValueE$/;else{if(!n)throw new Error("Unable to find method invocation in ART; please file a bug");r=/^_ZN3art11interpreter6DoCallILb[0-1]EEEbPNS_9ArtMethodEPNS_6ThreadERNS_11ShadowFrameEPKNS_11InstructionEtbPNS_6JValueE$/}const o=e.module,i=[...o.enumerateExports(),...o.enumerateSymbols()].filter(e=>r.test(e.name));if(0===i.length)throw new Error("Unable to find method invocation in ART; please file a bug");for(const e of i)Interceptor.attach(e.address,Ce.hooks.Interpreter.doCall)}()}(t),function(){if(Ae)return;if(Ae=!0,!function(){if(be()<31)return!1;const e=ht[Process.arch];if(void 0===e)return!1;const t=e.signatures.map(({pattern:e,offset:t=0,validateMatch:n=_t})=>({pattern:new MatchPattern(e.join("")),offset:t,validateMatch:n})),n=[];for(const{base:e,size:r}of Oe().module.enumerateRanges("--x"))for(const{pattern:o,offset:i,validateMatch:a}of t){const t=Memory.scanSync(e,r,o).map(({address:e,size:t})=>({address:e.sub(i),size:t+i})).filter(e=>{const t=a(e);return null!==t&&(e.validationResult=t,!0)});n.push(...t)}if(0===n.length)return!1;return n.forEach(e.instrument),!0}()){const{getOatQuickMethodHeaderImpl:e}=Ce;if(null===e)return;try{Interceptor.replace(e,Ce.hooks.ArtMethod.getOatQuickMethodHeader)}catch(e){}}const e=be();let t=null;const n=Oe();e>28?t=n.find("_ZN3art2gc9collector17ConcurrentCopying12CopyingPhaseEv"):e>22&&(t=n.find("_ZN3art2gc9collector17ConcurrentCopying12MarkingPhaseEv"));null!==t&&Interceptor.attach(t,Ce.hooks.Gc.copyingPhase);let r=null;r=n.find("_ZN3art6Thread15RunFlipFunctionEPS0_"),null===r&&(r=n.find("_ZN3art6Thread15RunFlipFunctionEPS0_b"));null!==r&&Interceptor.attach(r,Ce.hooks.Gc.runFlip)}()}const ht={arm:{signatures:[{pattern:["b0 68","01 30","0c d0","1b 98",":","c0 ff","c0 ff","00 ff","00 2f"],validateMatch:pt},{pattern:["d8 f8 08 00","01 30","0c d0","1b 98",":","f0 ff ff 0f","ff ff","00 ff","00 2f"],validateMatch:pt},{pattern:["b0 68","01 30","40 f0 c3 80","00 25",":","c0 ff","c0 ff","c0 fb 00 d0","ff f8"],validateMatch:pt}],instrument:function({address:e,size:t,validationResult:n}){const{methodReg:r,target:o}=n,i=Memory.alloc(Process.pageSize);let a=t;Memory.patchCode(i,256,t=>{const n=new ThumbWriter(t,{pc:i}),s=new ThumbRelocator(e,n);for(let e=0;2!==e;e++)s.readOne();s.writeAll(),s.readOne(),s.skipOne(),n.putBCondLabel("eq","runtime_or_replacement_method");n.putBytes([45,237,16,10]);const l=["r0","r1","r2","r3"];n.putPushRegs(l),n.putCallAddressWithArguments(Ce.replacedMethods.isReplacement,[r]),n.putCmpRegImm("r0",0),n.putPopRegs(l);n.putBytes([189,236,16,10]),n.putBCondLabel("ne","runtime_or_replacement_method"),n.putBLabel("regular_method"),s.readOne();const c=s.input.address.equals(o.whenRegularMethod);for(n.putLabel(c?"regular_method":"runtime_or_replacement_method"),s.writeOne();a<10;){const e=s.readOne();if(0===e){a=10;break}a=e}s.writeAll(),n.putBranchAddress(e.add(a+1)),n.putLabel(c?"runtime_or_replacement_method":"regular_method"),n.putBranchAddress(o.whenTrue),n.flush()}),ke.push(new mt(e,a,i)),Memory.patchCode(e,a,t=>{const n=new ThumbWriter(t,{pc:e});n.putLdrRegAddress("pc",i.or(1)),n.flush()})}},arm64:{signatures:[{pattern:["0a 40 b9","1f 05 00 31","40 01 00 54","88 39 00 f0",":","fc ff ff","1f fc ff ff","1f 00 00 ff","00 00 00 9f"],offset:1,validateMatch:ft},{pattern:["0a 40 b9","1f 05 00 31","01 34 00 54","e0 03 1f aa",":","fc ff ff","1f fc ff ff","1f 00 00 ff","e0 ff ff ff"],offset:1,validateMatch:ft}],instrument:function({address:e,size:t,validationResult:n}){const{methodReg:r,scratchReg:o,target:i}=n,a=Memory.alloc(Process.pageSize);Memory.patchCode(a,256,t=>{const n=new Arm64Writer(t,{pc:a}),o=new Arm64Relocator(e,n);for(let e=0;2!==e;e++)o.readOne();o.writeAll(),o.readOne(),o.skipOne(),n.putBCondLabel("eq","runtime_or_replacement_method");const s=["d0","d1","d2","d3","d4","d5","d6","d7","x0","x1","x2","x3","x4","x5","x6","x7","x8","x9","x10","x11","x12","x13","x14","x15","x16","x17"],l=s.length;for(let e=0;e!==l;e+=2)n.putPushRegReg(s[e],s[e+1]);n.putCallAddressWithArguments(Ce.replacedMethods.isReplacement,[r]),n.putCmpRegReg("x0","xzr");for(let e=l-2;e>=0;e-=2)n.putPopRegReg(s[e],s[e+1]);n.putBCondLabel("ne","runtime_or_replacement_method"),n.putBLabel("regular_method"),o.readOne();const c=o.input,d=c.address.equals(i.whenRegularMethod);n.putLabel(d?"regular_method":"runtime_or_replacement_method"),o.writeOne(),n.putBranchAddress(c.next),n.putLabel(d?"runtime_or_replacement_method":"regular_method"),n.putBranchAddress(i.whenTrue),n.flush()}),ke.push(new mt(e,t,a)),Memory.patchCode(e,t,t=>{const n=new Arm64Writer(t,{pc:e});n.putLdrRegAddress(o,a),n.putBrReg(o),n.flush()})}}};function pt({address:e,size:t}){const n=Instruction.parse(e.or(1)),[r,o]=n.operands,i=o.value.base,a=r.value,s=Instruction.parse(n.next.add(2)),l=ptr(s.operands[0].value),c=s.address.add(s.size);let d,u;return"beq"===s.mnemonic?(d=c,u=l):(d=l,u=c),h(d.or(1),function(e){const{mnemonic:t}=e;if("ldr"!==t&&"ldr.w"!==t)return null;const{base:n,disp:r}=e.operands[1].value;if(n!==i||20!==r)return null;return{methodReg:i,scratchReg:a,target:{whenTrue:l,whenRegularMethod:d,whenRuntimeMethod:u}}},{limit:3})}function ft({address:e,size:t}){const[n,r]=Instruction.parse(e).operands,o=r.value.base,i="x"+n.value.substring(1),a=Instruction.parse(e.add(8)),s=ptr(a.operands[0].value),l=e.add(12);let c,d;return"b.eq"===a.mnemonic?(c=l,d=s):(c=s,d=l),h(c,function(e){if("ldr"!==e.mnemonic)return null;const{base:t,disp:n}=e.operands[1].value;if(t!==o||24!==n)return null;return{methodReg:o,scratchReg:i,target:{whenTrue:s,whenRegularMethod:c,whenRuntimeMethod:d}}},{limit:3})}function _t(){return{}}class mt{constructor(e,t,n){this.address=e,this.size=t,this.originalCode=e.readByteArray(t),this.trampoline=n}revert(){Memory.patchCode(this.address,this.size,e=>{e.writeByteArray(this.originalCode)})}}function gt(e,t={}){const{limit:n=16}=t,r=e.getEnv();return null===xe&&(xe=function(e,t){const n=Oe(),r=Memory.alloc(Process.pointerSize),o=new CModule("\n#include <glib.h>\n#include <stdbool.h>\n#include <string.h>\n#include <gum/gumtls.h>\n#include <json-glib/json-glib.h>\n\ntypedef struct _ArtBacktrace ArtBacktrace;\ntypedef struct _ArtStackFrame ArtStackFrame;\n\ntypedef struct _ArtStackVisitor ArtStackVisitor;\ntypedef struct _ArtStackVisitorVTable ArtStackVisitorVTable;\n\ntypedef struct _ArtClass ArtClass;\ntypedef struct _ArtMethod ArtMethod;\ntypedef struct _ArtThread ArtThread;\ntypedef struct _ArtContext ArtContext;\n\ntypedef struct _JNIEnv JNIEnv;\n\ntypedef struct _StdString StdString;\ntypedef struct _StdTinyString StdTinyString;\ntypedef struct _StdLargeString StdLargeString;\n\ntypedef enum {\n  STACK_WALK_INCLUDE_INLINED_FRAMES,\n  STACK_WALK_SKIP_INLINED_FRAMES,\n} StackWalkKind;\n\nstruct _StdTinyString\n{\n  guint8 unused;\n  gchar data[(3 * sizeof (gpointer)) - 1];\n};\n\nstruct _StdLargeString\n{\n  gsize capacity;\n  gsize size;\n  gchar * data;\n};\n\nstruct _StdString\n{\n  union\n  {\n    guint8 flags;\n    StdTinyString tiny;\n    StdLargeString large;\n  };\n};\n\nstruct _ArtBacktrace\n{\n  GChecksum * id;\n  GArray * frames;\n  gchar * frames_json;\n};\n\nstruct _ArtStackFrame\n{\n  ArtMethod * method;\n  gsize dexpc;\n  StdString description;\n};\n\nstruct _ArtStackVisitorVTable\n{\n  void (* unused1) (void);\n  void (* unused2) (void);\n  bool (* visit) (ArtStackVisitor * visitor);\n};\n\nstruct _ArtStackVisitor\n{\n  ArtStackVisitorVTable * vtable;\n\n  guint8 padding[512];\n\n  ArtStackVisitorVTable vtable_storage;\n\n  ArtBacktrace * backtrace;\n};\n\nstruct _ArtMethod\n{\n  guint32 declaring_class;\n  guint32 access_flags;\n};\n\nextern GumTlsKey current_backtrace;\n\nextern void (* perform_art_thread_state_transition) (JNIEnv * env);\n\nextern ArtContext * art_thread_get_long_jump_context (ArtThread * thread);\n\nextern void art_stack_visitor_init (ArtStackVisitor * visitor, ArtThread * thread, void * context, StackWalkKind walk_kind,\n    size_t num_frames, bool check_suspended);\nextern void art_stack_visitor_walk_stack (ArtStackVisitor * visitor, bool include_transitions);\nextern ArtMethod * art_stack_visitor_get_method (ArtStackVisitor * visitor);\nextern void art_stack_visitor_describe_location (StdString * description, ArtStackVisitor * visitor);\nextern ArtMethod * translate_method (ArtMethod * method);\nextern void translate_location (ArtMethod * method, guint32 pc, const gchar ** source_file, gint32 * line_number);\nextern void get_class_location (StdString * result, ArtClass * klass);\nextern void cxx_delete (void * mem);\nextern unsigned long strtoul (const char * str, char ** endptr, int base);\n\nstatic bool visit_frame (ArtStackVisitor * visitor);\nstatic void art_stack_frame_destroy (ArtStackFrame * frame);\n\nstatic void append_jni_type_name (GString * s, const gchar * name, gsize length);\n\nstatic void std_string_destroy (StdString * str);\nstatic gchar * std_string_get_data (StdString * str);\n\nvoid\ninit (void)\n{\n  current_backtrace = gum_tls_key_new ();\n}\n\nvoid\nfinalize (void)\n{\n  gum_tls_key_free (current_backtrace);\n}\n\nArtBacktrace *\n_create (JNIEnv * env,\n         guint limit)\n{\n  ArtBacktrace * bt;\n\n  bt = g_new (ArtBacktrace, 1);\n  bt->id = g_checksum_new (G_CHECKSUM_SHA1);\n  bt->frames = (limit != 0)\n      ? g_array_sized_new (FALSE, FALSE, sizeof (ArtStackFrame), limit)\n      : g_array_new (FALSE, FALSE, sizeof (ArtStackFrame));\n  g_array_set_clear_func (bt->frames, (GDestroyNotify) art_stack_frame_destroy);\n  bt->frames_json = NULL;\n\n  gum_tls_key_set_value (current_backtrace, bt);\n\n  perform_art_thread_state_transition (env);\n\n  gum_tls_key_set_value (current_backtrace, NULL);\n\n  return bt;\n}\n\nvoid\n_on_thread_state_transition_complete (ArtThread * thread)\n{\n  ArtContext * context;\n  ArtStackVisitor visitor = {\n    .vtable_storage = {\n      .visit = visit_frame,\n    },\n  };\n\n  context = art_thread_get_long_jump_context (thread);\n\n  art_stack_visitor_init (&visitor, thread, context, STACK_WALK_SKIP_INLINED_FRAMES, 0, true);\n  visitor.vtable = &visitor.vtable_storage;\n  visitor.backtrace = gum_tls_key_get_value (current_backtrace);\n\n  art_stack_visitor_walk_stack (&visitor, false);\n\n  cxx_delete (context);\n}\n\nstatic bool\nvisit_frame (ArtStackVisitor * visitor)\n{\n  ArtBacktrace * bt = visitor->backtrace;\n  ArtStackFrame frame;\n  const gchar * description, * dexpc_part;\n\n  frame.method = art_stack_visitor_get_method (visitor);\n\n  art_stack_visitor_describe_location (&frame.description, visitor);\n\n  description = std_string_get_data (&frame.description);\n  if (strstr (description, \" '<\") != NULL)\n    goto skip;\n\n  dexpc_part = strstr (description, \" at dex PC 0x\");\n  if (dexpc_part == NULL)\n    goto skip;\n  frame.dexpc = strtoul (dexpc_part + 13, NULL, 16);\n\n  g_array_append_val (bt->frames, frame);\n\n  g_checksum_update (bt->id, (guchar *) &frame.method, sizeof (frame.method));\n  g_checksum_update (bt->id, (guchar *) &frame.dexpc, sizeof (frame.dexpc));\n\n  return true;\n\nskip:\n  std_string_destroy (&frame.description);\n  return true;\n}\n\nstatic void\nart_stack_frame_destroy (ArtStackFrame * frame)\n{\n  std_string_destroy (&frame->description);\n}\n\nvoid\n_destroy (ArtBacktrace * backtrace)\n{\n  g_free (backtrace->frames_json);\n  g_array_free (backtrace->frames, TRUE);\n  g_checksum_free (backtrace->id);\n  g_free (backtrace);\n}\n\nconst gchar *\n_get_id (ArtBacktrace * backtrace)\n{\n  return g_checksum_get_string (backtrace->id);\n}\n\nconst gchar *\n_get_frames (ArtBacktrace * backtrace)\n{\n  GArray * frames = backtrace->frames;\n  JsonBuilder * b;\n  guint i;\n  JsonNode * root;\n\n  if (backtrace->frames_json != NULL)\n    return backtrace->frames_json;\n\n  b = json_builder_new_immutable ();\n\n  json_builder_begin_array (b);\n\n  for (i = 0; i != frames->len; i++)\n  {\n    ArtStackFrame * frame = &g_array_index (frames, ArtStackFrame, i);\n    gchar * description, * ret_type, * paren_open, * paren_close, * arg_types, * token, * method_name, * class_name;\n    GString * signature;\n    gchar * cursor;\n    ArtMethod * translated_method;\n    StdString location;\n    gsize dexpc;\n    const gchar * source_file;\n    gint32 line_number;\n\n    description = std_string_get_data (&frame->description);\n\n    ret_type = strchr (description, '\\'') + 1;\n\n    paren_open = strchr (ret_type, '(');\n    paren_close = strchr (paren_open, ')');\n    *paren_open = '\\0';\n    *paren_close = '\\0';\n\n    arg_types = paren_open + 1;\n\n    token = strrchr (ret_type, '.');\n    *token = '\\0';\n\n    method_name = token + 1;\n\n    token = strrchr (ret_type, ' ');\n    *token = '\\0';\n\n    class_name = token + 1;\n\n    signature = g_string_sized_new (128);\n\n    append_jni_type_name (signature, class_name, method_name - class_name - 1);\n    g_string_append_c (signature, ',');\n    g_string_append (signature, method_name);\n    g_string_append (signature, \",(\");\n\n    if (arg_types != paren_close)\n    {\n      for (cursor = arg_types; cursor != NULL;)\n      {\n        gsize length;\n        gchar * next;\n\n        token = strstr (cursor, \", \");\n        if (token != NULL)\n        {\n          length = token - cursor;\n          next = token + 2;\n        }\n        else\n        {\n          length = paren_close - cursor;\n          next = NULL;\n        }\n\n        append_jni_type_name (signature, cursor, length);\n\n        cursor = next;\n      }\n    }\n\n    g_string_append_c (signature, ')');\n\n    append_jni_type_name (signature, ret_type, class_name - ret_type - 1);\n\n    translated_method = translate_method (frame->method);\n    dexpc = (translated_method == frame->method) ? frame->dexpc : 0;\n\n    get_class_location (&location, GSIZE_TO_POINTER (translated_method->declaring_class));\n\n    translate_location (translated_method, dexpc, &source_file, &line_number);\n\n    json_builder_begin_object (b);\n\n    json_builder_set_member_name (b, \"signature\");\n    json_builder_add_string_value (b, signature->str);\n\n    json_builder_set_member_name (b, \"origin\");\n    json_builder_add_string_value (b, std_string_get_data (&location));\n\n    json_builder_set_member_name (b, \"className\");\n    json_builder_add_string_value (b, class_name);\n\n    json_builder_set_member_name (b, \"methodName\");\n    json_builder_add_string_value (b, method_name);\n\n    json_builder_set_member_name (b, \"methodFlags\");\n    json_builder_add_int_value (b, translated_method->access_flags);\n\n    json_builder_set_member_name (b, \"fileName\");\n    json_builder_add_string_value (b, source_file);\n\n    json_builder_set_member_name (b, \"lineNumber\");\n    json_builder_add_int_value (b, line_number);\n\n    json_builder_end_object (b);\n\n    std_string_destroy (&location);\n    g_string_free (signature, TRUE);\n  }\n\n  json_builder_end_array (b);\n\n  root = json_builder_get_root (b);\n  backtrace->frames_json = json_to_string (root, FALSE);\n  json_node_unref (root);\n\n  return backtrace->frames_json;\n}\n\nstatic void\nappend_jni_type_name (GString * s,\n                      const gchar * name,\n                      gsize length)\n{\n  gchar shorty = '\\0';\n  gsize i;\n\n  switch (name[0])\n  {\n    case 'b':\n      if (strncmp (name, \"boolean\", length) == 0)\n        shorty = 'Z';\n      else if (strncmp (name, \"byte\", length) == 0)\n        shorty = 'B';\n      break;\n    case 'c':\n      if (strncmp (name, \"char\", length) == 0)\n        shorty = 'C';\n      break;\n    case 'd':\n      if (strncmp (name, \"double\", length) == 0)\n        shorty = 'D';\n      break;\n    case 'f':\n      if (strncmp (name, \"float\", length) == 0)\n        shorty = 'F';\n      break;\n    case 'i':\n      if (strncmp (name, \"int\", length) == 0)\n        shorty = 'I';\n      break;\n    case 'l':\n      if (strncmp (name, \"long\", length) == 0)\n        shorty = 'J';\n      break;\n    case 's':\n      if (strncmp (name, \"short\", length) == 0)\n        shorty = 'S';\n      break;\n    case 'v':\n      if (strncmp (name, \"void\", length) == 0)\n        shorty = 'V';\n      break;\n  }\n\n  if (shorty != '\\0')\n  {\n    g_string_append_c (s, shorty);\n\n    return;\n  }\n\n  if (length > 2 && name[length - 2] == '[' && name[length - 1] == ']')\n  {\n    g_string_append_c (s, '[');\n    append_jni_type_name (s, name, length - 2);\n\n    return;\n  }\n\n  g_string_append_c (s, 'L');\n\n  for (i = 0; i != length; i++)\n  {\n    gchar ch = name[i];\n    if (ch != '.')\n      g_string_append_c (s, ch);\n    else\n      g_string_append_c (s, '/');\n  }\n\n  g_string_append_c (s, ';');\n}\n\nstatic void\nstd_string_destroy (StdString * str)\n{\n  bool is_large = (str->flags & 1) != 0;\n  if (is_large)\n    cxx_delete (str->large.data);\n}\n\nstatic gchar *\nstd_string_get_data (StdString * str)\n{\n  bool is_large = (str->flags & 1) != 0;\n  return is_large ? str->large.data : str->tiny.data;\n}\n",{current_backtrace:Memory.alloc(Process.pointerSize),perform_art_thread_state_transition:r,art_thread_get_long_jump_context:n["art::Thread::GetLongJumpContext"],art_stack_visitor_init:n["art::StackVisitor::StackVisitor"],art_stack_visitor_walk_stack:n["art::StackVisitor::WalkStack"],art_stack_visitor_get_method:n["art::StackVisitor::GetMethod"],art_stack_visitor_describe_location:n["art::StackVisitor::DescribeLocation"],translate_method:Ce.replacedMethods.translate,translate_location:n["art::Monitor::TranslateLocation"],get_class_location:n["art::mirror::Class::GetLocation"],cxx_delete:n.$delete,strtoul:Process.getModuleByName("libc.so").getExportByName("strtoul")}),i=new NativeFunction(o._create,"pointer",["pointer","uint"],we),a=new NativeFunction(o._destroy,"void",["pointer"],we),s={exceptions:"propagate",scheduling:"exclusive"},l=new NativeFunction(o._get_id,"pointer",["pointer"],s),c=new NativeFunction(o._get_frames,"pointer",["pointer"],s),d=Dt(e,t,o._on_thread_state_transition_complete);function u(e){a(e)}return o._performData=d,r.writePointer(d),o.backtrace=(e,t)=>{const n=i(e,t),r=new bt(n);return Script.bindWeak(r,u.bind(null,n)),r},o.getId=e=>l(e).readUtf8String(),o.getFrames=e=>JSON.parse(c(e).readUtf8String()),o}(e,r)),xe.backtrace(r,n)}class bt{constructor(e){this.handle=e}get id(){return xe.getId(this.handle)}get frames(){return xe.getFrames(this.handle)}}function yt(){Me.forEach(e=>{e.vtablePtr.writePointer(e.vtable),e.vtableCountPtr.writeS32(e.vtableCount)}),Me.clear();for(const e of je.splice(0))e.deactivate();for(const e of ke.splice(0))e.revert()}function vt(e){return Et(e,"art::jni::JniIdManager::DecodeMethodId")}function wt(e){return Et(e,"art::jni::JniIdManager::DecodeFieldId")}function Et(e,t){const n=Oe(),r=de(n).offset,o=r.jniIdManager,i=r.jniIdsIndirection;if(null!==o&&null!==i){const r=n.artRuntime;if(0!==r.add(i).readInt()){const i=r.add(o).readPointer();return n[t](i,e)}}return e}const St={ia32:function(e,t,n,r,o){const i=pe(o).offset,a=he(o).offset;let s;return Memory.patchCode(e,128,r=>{const o=new X86Writer(r,{pc:e}),l=new X86Relocator(t,o);o.putPushax(),o.putMovRegReg("ebp","esp"),o.putAndRegU32("esp",4294967280),o.putSubRegImm("esp",512),o.putBytes([15,174,4,36]),o.putMovRegFsU32Ptr("ebx",i.self),o.putCallAddressWithAlignedArguments(Ce.replacedMethods.findReplacementFromQuickCode,["eax","ebx"]),o.putTestRegReg("eax","eax"),o.putJccShortLabel("je","restore_registers","no-hint"),o.putMovRegOffsetPtrReg("ebp",28,"eax"),o.putLabel("restore_registers"),o.putBytes([15,174,12,36]),o.putMovRegReg("esp","ebp"),o.putPopax(),o.putJccShortLabel("jne","invoke_replacement","no-hint");do{s=l.readOne()}while(s<n&&!l.eoi);l.writeAll(),l.eoi||o.putJmpAddress(t.add(s)),o.putLabel("invoke_replacement"),o.putJmpRegOffsetPtr("eax",a.quickCode),o.flush()}),s},x64:function(e,t,n,r,o){const i=pe(o).offset,a=he(o).offset;let s;return Memory.patchCode(e,256,r=>{const o=new X86Writer(r,{pc:e}),l=new X86Relocator(t,o);o.putPushax(),o.putMovRegReg("rbp","rsp"),o.putAndRegU32("rsp",4294967280),o.putSubRegImm("rsp",512),o.putBytes([15,174,4,36]),o.putMovRegGsU32Ptr("rbx",i.self),o.putCallAddressWithAlignedArguments(Ce.replacedMethods.findReplacementFromQuickCode,["rdi","rbx"]),o.putTestRegReg("rax","rax"),o.putJccShortLabel("je","restore_registers","no-hint"),o.putMovRegOffsetPtrReg("rbp",64,"rax"),o.putLabel("restore_registers"),o.putBytes([15,174,12,36]),o.putMovRegReg("rsp","rbp"),o.putPopax(),o.putJccShortLabel("jne","invoke_replacement","no-hint");do{s=l.readOne()}while(s<n&&!l.eoi);l.writeAll(),l.eoi||o.putJmpAddress(t.add(s)),o.putLabel("invoke_replacement"),o.putJmpRegOffsetPtr("rdi",a.quickCode),o.flush()}),s},arm:function(e,t,n,r,o){const i=he(o).offset,a=t.and(oe);let s;return Memory.patchCode(e,128,r=>{const o=new ThumbWriter(r,{pc:e}),l=new ThumbRelocator(a,o);o.putPushRegs(["r1","r2","r3","r5","r6","r7","r8","r10","r11","lr"]),o.putBytes([45,237,16,10]),o.putSubRegRegImm("sp","sp",8),o.putStrRegRegOffset("r0","sp",0),o.putCallAddressWithArguments(Ce.replacedMethods.findReplacementFromQuickCode,["r0","r9"]),o.putCmpRegImm("r0",0),o.putBCondLabel("eq","restore_registers"),o.putStrRegRegOffset("r0","sp",0),o.putLabel("restore_registers"),o.putLdrRegRegOffset("r0","sp",0),o.putAddRegRegImm("sp","sp",8),o.putBytes([189,236,16,10]),o.putPopRegs(["lr","r11","r10","r8","r7","r6","r5","r3","r2","r1"]),o.putBCondLabel("ne","invoke_replacement");do{s=l.readOne()}while(s<n&&!l.eoi);l.writeAll(),l.eoi||o.putLdrRegAddress("pc",t.add(s)),o.putLabel("invoke_replacement"),o.putLdrRegRegOffset("pc","r0",i.quickCode),o.flush()}),s},arm64:function(e,t,n,{availableScratchRegs:r},o){const i=he(o).offset;let a;return Memory.patchCode(e,256,o=>{const s=new Arm64Writer(o,{pc:e}),l=new Arm64Relocator(t,s);s.putPushRegReg("d0","d1"),s.putPushRegReg("d2","d3"),s.putPushRegReg("d4","d5"),s.putPushRegReg("d6","d7"),s.putPushRegReg("x1","x2"),s.putPushRegReg("x3","x4"),s.putPushRegReg("x5","x6"),s.putPushRegReg("x7","x20"),s.putPushRegReg("x21","x22"),s.putPushRegReg("x23","x24"),s.putPushRegReg("x25","x26"),s.putPushRegReg("x27","x28"),s.putPushRegReg("x29","lr"),s.putSubRegRegImm("sp","sp",16),s.putStrRegRegOffset("x0","sp",0),s.putCallAddressWithArguments(Ce.replacedMethods.findReplacementFromQuickCode,["x0","x19"]),s.putCmpRegReg("x0","xzr"),s.putBCondLabel("eq","restore_registers"),s.putStrRegRegOffset("x0","sp",0),s.putLabel("restore_registers"),s.putLdrRegRegOffset("x0","sp",0),s.putAddRegRegImm("sp","sp",16),s.putPopRegReg("x29","lr"),s.putPopRegReg("x27","x28"),s.putPopRegReg("x25","x26"),s.putPopRegReg("x23","x24"),s.putPopRegReg("x21","x22"),s.putPopRegReg("x7","x20"),s.putPopRegReg("x5","x6"),s.putPopRegReg("x3","x4"),s.putPopRegReg("x1","x2"),s.putPopRegReg("d6","d7"),s.putPopRegReg("d4","d5"),s.putPopRegReg("d2","d3"),s.putPopRegReg("d0","d1"),s.putBCondLabel("ne","invoke_replacement");do{a=l.readOne()}while(a<n&&!l.eoi);if(l.writeAll(),!l.eoi){const e=Array.from(r)[0];s.putLdrRegAddress(e,t.add(a)),s.putBrReg(e)}s.putLabel("invoke_replacement"),s.putLdrRegRegOffset("x16","x0",i.quickCode),s.putBrReg("x16"),s.flush()}),a}};const Nt={ia32:Lt,x64:Lt,arm:function(e,t,n){const r=e.and(oe);Memory.patchCode(r,16,e=>{const n=new ThumbWriter(e,{pc:r});n.putLdrRegAddress("pc",t.or(1)),n.flush()})},arm64:function(e,t,n){Memory.patchCode(e,16,r=>{const o=new Arm64Writer(r,{pc:e});16===n?o.putLdrRegAddress("x16",t):o.putAdrpRegAddress("x16",t),o.putBrReg("x16"),o.flush()})}};function Lt(e,t,n){Memory.patchCode(e,16,n=>{const r=new X86Writer(n,{pc:e});r.putJmpAddress(t),r.flush()})}const Ct={ia32:5,x64:16,arm:8,arm64:16};class kt{constructor(e){this.quickCode=e,this.quickCodeAddress="arm"===Process.arch?e.and(oe):e,this.redirectSize=0,this.trampoline=null,this.overwrittenPrologue=null,this.overwrittenPrologueLength=0}_canRelocateCode(e,t){const n=ct[Process.arch],r=lt[Process.arch],{quickCodeAddress:o}=this,i=new r(o,new n(o));let a;if("arm64"===Process.arch){let n=new Set(["x16","x17"]);do{const e=i.readOne(),t=new Set(n),{read:r,written:o}=i.input.regsAccessed;for(const e of[r,o])for(const n of e){let e;e=n.startsWith("w")?"x"+n.substring(1):n,t.delete(e)}if(0===t.size)break;a=e,n=t}while(a<e&&!i.eoi);t.availableScratchRegs=n}else do{a=i.readOne()}while(a<e&&!i.eoi);return a>=e}_allocateTrampoline(){if(null===Ue){Ue=new n(4===X?128:256)}const e=Ct[Process.arch];let t,r,o=1;const i={};if(4===X||this._canRelocateCode(e,i))t=e,r={};else{let e;"x64"===Process.arch?(t=5,e=2147467263):"arm64"===Process.arch&&(t=8,e=4294963200,o=4096),r={near:this.quickCodeAddress,maxDistance:e}}return this.redirectSize=t,this.trampoline=Ue.allocateSlice(r,o),i}_destroyTrampoline(){Ue.freeSlice(this.trampoline)}activate(e){const t=this._allocateTrampoline(),{trampoline:n,quickCode:r,redirectSize:o}=this,i=(0,St[Process.arch])(n,r,o,t,e);this.overwrittenPrologueLength=i,this.overwrittenPrologue=Memory.dup(this.quickCodeAddress,i);(0,Nt[Process.arch])(r,n,o)}deactivate(){const{quickCodeAddress:e,overwrittenPrologueLength:t}=this,n=ct[Process.arch];Memory.patchCode(e,t,r=>{const o=new n(r,{pc:e}),{overwrittenPrologue:i}=this;o.putBytes(i.readByteArray(t)),o.flush()}),this._destroyTrampoline()}}class Mt{constructor(e){const t=vt(e);this.methodId=t,this.originalMethod=null,this.hookedMethodId=t,this.replacementMethodId=null,this.interceptor=null}replace(e,t,n,r,o){const{kAccCompileDontBother:i,artNterpEntryPoint:a}=o;this.originalMethod=jt(this.methodId,r);const s=this.originalMethod.accessFlags;if(268435456&s&&be()<28){const e=this.originalMethod.jniCode;this.hookedMethodId=e.add(2*X).readPointer(),this.originalMethod=jt(this.hookedMethodId,r)}const{hookedMethodId:l}=this,c=function(e,t){const n=Oe();if(be()<23){const t=n["art::Thread::CurrentFromGdb"]();return n["art::mirror::Object::Clone"](e,t)}return Memory.dup(e,he(t).size)}(l,r);this.replacementMethodId=c,It(c,{jniCode:e,accessFlags:(-3670017&s|re|i)>>>0,quickCode:o.artClassLinker.quickGenericJniTrampoline,interpreterCode:o.artInterpreterToCompiledCodeBridge},r);let d=1209008128;0===(s&re)&&(d|=524288),It(l,{accessFlags:(s&~d|i)>>>0},r);const u=this.originalMethod.quickCode;if(null!==a&&u.equals(a)&&It(l,{quickCode:o.artQuickToInterpreterBridge},r),!function(e){const t=Oe(),{module:n,artClassLinker:r}=t;return e.equals(r.quickGenericJniTrampoline)||e.equals(r.quickToInterpreterBridgeTrampoline)||e.equals(r.quickResolutionTrampoline)||e.equals(r.quickImtConflictTrampoline)||e.compare(n.base)>=0&&e.compare(n.base.add(n.size))<0}(u)){const e=new kt(u);e.activate(r),this.interceptor=e}Ce.replacedMethods.set(l,c),ut(0,r)}revert(e){const{hookedMethodId:t,interceptor:n}=this;It(t,this.originalMethod,e),Ce.replacedMethods.delete(t),null!==n&&(n.deactivate(),this.interceptor=null)}resolveTarget(e,t,n,r){return this.hookedMethodId}}function jt(e,t){const n=he(t).offset;return["jniCode","accessFlags","quickCode","interpreterCode"].reduce((t,r)=>{const o=n[r];if(void 0===o)return t;const i=e.add(o),a="accessFlags"===r?Y:ee;return t[r]=a.call(i),t},{})}function It(e,t,n){const r=he(n).offset;Object.keys(t).forEach(n=>{const o=r[n];if(void 0===o)return;const i=e.add(o);("accessFlags"===n?te:ne).call(i,t[n])})}class Rt{constructor(e){this.methodId=e,this.originalMethod=null}replace(e,t,n,r,o){const{methodId:i}=this;this.originalMethod=Memory.dup(i,56);let a=n.reduce((e,t)=>e+t.size,0);t&&a++;const s=(i.add(4).readU32()|re)>>>0,l=a,c=a;i.add(4).writeU32(s),i.add(10).writeU16(l),i.add(12).writeU16(0),i.add(14).writeU16(c),i.add(36).writeU32(function(e){if("ia32"!==Process.arch)return se;const t=e.add(28).readPointer().readCString();if(null===t||0===t.length||t.length>65535)return se;let n;switch(t[0]){case"V":n=0;break;case"F":n=1;break;case"D":n=2;break;case"J":n=3;break;case"Z":case"B":n=7;break;case"C":n=6;break;case"S":n=5;break;default:n=4}let r=0;for(let e=t.length-1;e>0;e--){const n=t[e];r+="D"===n||"J"===n?2:1}return n<<28|r}(i)),o.dvmUseJNIBridge(i,e)}revert(e){Memory.copy(this.methodId,this.originalMethod,56)}resolveTarget(e,t,n,r){const o=n.handle.add(12).readPointer();let i,a;if(t)i=r.dvmDecodeIndirectRef(o,e.$h);else{const t=e.$borrowClassHandle(n);i=r.dvmDecodeIndirectRef(o,t.value),t.unref(n)}a=t?i.add(0).readPointer():i;const s=a.toString(16);let l=Me.get(s);if(void 0===l){const e=a.add(116),t=a.add(112),n=e.readPointer(),r=t.readS32(),o=r*X,i=Memory.alloc(2*o);Memory.copy(i,n,o),e.writePointer(i),l={classObject:a,vtablePtr:e,vtableCountPtr:t,vtable:n,vtableCount:r,shadowVtable:i,shadowVtableCount:r,targetMethods:new Map},Me.set(s,l)}const c=this.methodId.toString(16);let d=l.targetMethods.get(c);if(void 0===d){d=Memory.dup(this.originalMethod,56);const e=l.shadowVtableCount++;l.shadowVtable.add(e*X).writePointer(d),d.add(8).writeU16(e),l.vtableCountPtr.writeS32(l.shadowVtableCount),l.targetMethods.set(c,d)}return d}}function At(e,t,n){Tt(e,t,5,n)}function Pt(e,t){Tt(e,t,3)}function xt(e,t){const n=Oe();if(be()<26)throw new Error("This API is only available on Android >= 8.0");Ye(e,t,e=>{n["art::Runtime::DeoptimizeBootImage"](n.artRuntime)})}function Tt(e,t,n,r){const o=Oe();if(be()<24)throw new Error("This API is only available on Android >= 7.0");Ye(e,t,e=>{if(be()<30){o.isJdwpStarted()||function(e){const t=new Ut;e["art::Dbg::SetJdwpAllowed"](1);const n=function(){const e=be()<28?2:3,t=e,n=0,r=8+le+2,o=Memory.alloc(r);return o.writeU32(t).add(4).writeU8(1).add(1).writeU8(0).add(1).add(le).writeU16(n),o}();e["art::Dbg::ConfigureJdwp"](n);const r=e["art::InternalDebuggerControlCallback::StartDebugger"];void 0!==r?r(NULL):e["art::Dbg::StartJdwp"]()}(o),o.isDebuggerActive()||o["art::Dbg::GoActive"]();const e=Memory.alloc(8+X);switch(e.writeU32(n),n){case 3:break;case 5:e.add(8).writePointer(r);break;default:throw new Error("Unsupported deoptimization kind")}o["art::Dbg::RequestDeoptimization"](e),o["art::Dbg::ManageDeoptimization"]()}else{const e=o.artInstrumentation;if(null===e)throw new Error("Unable to find Instrumentation class in ART; please file a bug");const t=o["art::Instrumentation::EnableDeoptimization"];if(void 0!==t){!!e.add(ue().offset.deoptimizationEnabled).readU8()||t(e)}switch(n){case 3:o["art::Instrumentation::DeoptimizeEverything"](e,Memory.allocUtf8String("frida"));break;case 5:o["art::Instrumentation::Deoptimize"](e,r);break;default:throw new Error("Unsupported deoptimization kind")}}})}class Ut{constructor(){const e=Process.getModuleByName("libart.so"),t=e.getExportByName("_ZN3art4JDWP12JdwpAdbState6AcceptEv"),n=e.getExportByName("_ZN3art4JDWP12JdwpAdbState15ReceiveClientFdEv"),r=Ot(),o=Ot();this._controlFd=r[0],this._clientFd=o[0];let i=null;i=Interceptor.attach(t,function(e){const t=e[0];Memory.scanSync(t.add(8252),256,"00 ff ff ff ff 00")[0].address.add(1).writeS32(r[1]),i.detach()}),Interceptor.replace(n,new NativeCallback(function(e){return Interceptor.revert(n),o[1]},"int",["pointer"])),Interceptor.flush(),this._handshakeRequest=this._performHandshake()}async _performHandshake(){const e=new UnixInputStream(this._clientFd,{autoClose:!1}),t=new UnixOutputStream(this._clientFd,{autoClose:!1}),n=[74,68,87,80,45,72,97,110,100,115,104,97,107,101];try{await t.writeAll(n),await e.readAll(n.length)}catch(e){}}}function Ot(){null===Te&&(Te=new NativeFunction(Process.getModuleByName("libc.so").getExportByName("socketpair"),"int",["int","int","int","pointer"]));const e=Memory.alloc(8);if(-1===Te(1,1,0,e))throw new Error("Unable to create socketpair for JDWP");return[e.readS32(),e.add(4).readS32()]}const Ft={ia32:zt,x64:zt,arm:function(e,t,n,r,o,i,a){const s={},l=new Set,c=ptr(1).not(),d=[n];for(;d.length>0;){let e=d.shift();const t=Object.values(s).some(({begin:t,end:n})=>e.compare(t)>=0&&e.compare(n)<0);if(t)continue;const n=e.and(c),o=n.toString(),i=e.and(1);let a={begin:n},u=null,h=!1,p=0;do{if(e.equals(r)){h=!0;break}const t=Instruction.parse(e),{mnemonic:n}=t;u=t;const f=e.and(c).toString(),_=s[f];if(void 0!==_){delete s[_.begin.toString()],s[o]=_,_.begin=a.begin,a=null;break}const m=0===p;let g=null;switch(n){case"b":g=ptr(t.operands[0].value),h=m;break;case"beq.w":case"beq":case"bne":case"bne.w":case"bgt":g=ptr(t.operands[0].value);break;case"cbz":case"cbnz":g=ptr(t.operands[1].value);break;case"pop.w":m&&(h=1===t.operands.filter(e=>"pc"===e.value).length)}switch(n){case"it":p=1;break;case"itt":p=2;break;case"ittt":p=3;break;case"itttt":p=4;break;default:p>0&&p--}null!==g&&(l.add(g.toString()),d.push(g.or(i)),d.sort((e,t)=>e.compare(t))),e=t.next}while(!h);null!==a&&(a.end=u.address.add(u.size),s[o]=a)}const u=Object.keys(s).map(e=>s[e]);u.sort((e,t)=>e.begin.compare(t.begin));const h=s[n.and(c).toString()];u.splice(u.indexOf(h),1),u.unshift(h);const p=new ThumbWriter(e,{pc:t});let f=!1,_=null,m=null;u.forEach(e=>{const t=new ThumbRelocator(e.begin,p);let n=e.begin;const r=e.end;let s=0;do{if(0===t.readOne())throw new Error("Unexpected end of block");const e=t.input;n=e.address,s=e.size;const{mnemonic:r}=e,c=n.toString();l.has(c)&&p.putLabel(c);let d=!0;switch(r){case"b":p.putBLabel(Jt(e.operands[0])),d=!1;break;case"beq.w":p.putBCondLabelWide("eq",Jt(e.operands[0])),d=!1;break;case"bne.w":p.putBCondLabelWide("ne",Jt(e.operands[0])),d=!1;break;case"beq":case"bne":case"bgt":p.putBCondLabelWide(r.substr(1),Jt(e.operands[0])),d=!1;break;case"cbz":{const t=e.operands;p.putCbzRegLabel(t[0].value,Jt(t[1])),d=!1;break}case"cbnz":{const t=e.operands;p.putCbnzRegLabel(t[0].value,Jt(t[1])),d=!1;break}case"str":case"str.w":{const t=e.operands[1].value,n=t.disp;if(n===o){_=t.base;const e="r4"!==_?"r4":"r5",n=["r0","r1","r2","r3",e,"r9","r12","lr"];p.putPushRegs(n),p.putMrsRegReg(e,"apsr-nzcvq"),p.putCallAddressWithArguments(a,[_]),p.putMsrRegReg("apsr-nzcvq",e),p.putPopRegs(n),f=!0,d=!1}else i.has(n)&&t.base===_&&(d=!1);break}case"ldr":{const[t,n]=e.operands;if("mem"===n.type){const e=n.value;"r"===e.base[0]&&e.disp===ie&&(m=t.value)}break}case"blx":e.operands[0].value===m&&(p.putLdrRegRegOffset("r0","r0",4),p.putCallAddressWithArguments(a,["r0"]),f=!0,m=null,d=!1)}d?t.writeAll():t.skipOne()}while(!n.add(s).equals(r));t.dispose()}),p.dispose(),f||$t();return new NativeFunction(t.or(1),"void",["pointer"],we)},arm64:function(e,t,n,r,o,i,a){const s={},l=new Set,c=[n];for(;c.length>0;){let e=c.shift();if(Object.values(s).some(({begin:t,end:n})=>e.compare(t)>=0&&e.compare(n)<0))continue;const t=e.toString();let n={begin:e},o=null,i=!1;do{if(e.equals(r)){i=!0;break}let a;try{a=Instruction.parse(e)}catch(t){if(0===e.readU32()){i=!0;break}throw t}o=a;const d=s[a.address.toString()];if(void 0!==d){delete s[d.begin.toString()],s[t]=d,d.begin=n.begin,n=null;break}let u=null;switch(a.mnemonic){case"b":u=ptr(a.operands[0].value),i=!0;break;case"b.eq":case"b.ne":case"b.le":case"b.gt":u=ptr(a.operands[0].value);break;case"cbz":case"cbnz":u=ptr(a.operands[1].value);break;case"tbz":case"tbnz":u=ptr(a.operands[2].value);break;case"ret":i=!0}null!==u&&(l.add(u.toString()),c.push(u),c.sort((e,t)=>e.compare(t))),e=a.next}while(!i);null!==n&&(n.end=o.address.add(o.size),s[t]=n)}const d=Object.keys(s).map(e=>s[e]);d.sort((e,t)=>e.begin.compare(t.begin));const u=s[n.toString()];d.splice(d.indexOf(u),1),d.unshift(u);const h=new Arm64Writer(e,{pc:t});h.putBLabel("performTransition");const p=t.add(h.offset);h.putPushAllXRegisters(),h.putCallAddressWithArguments(a,["x0"]),h.putPopAllXRegisters(),h.putRet(),h.putLabel("performTransition");let f=!1,_=null,m=null;d.forEach(e=>{const t=e.end.sub(e.begin).toInt32(),n=new Arm64Relocator(e.begin,h);let r;for(;0!==(r=n.readOne());){const e=n.input,{mnemonic:s}=e,c=e.address.toString();l.has(c)&&h.putLabel(c);let d=!0;switch(s){case"b":h.putBLabel(Jt(e.operands[0])),d=!1;break;case"b.eq":case"b.ne":case"b.le":case"b.gt":h.putBCondLabel(s.substr(2),Jt(e.operands[0])),d=!1;break;case"cbz":{const t=e.operands;h.putCbzRegLabel(t[0].value,Jt(t[1])),d=!1;break}case"cbnz":{const t=e.operands;h.putCbnzRegLabel(t[0].value,Jt(t[1])),d=!1;break}case"tbz":{const t=e.operands;h.putTbzRegImmLabel(t[0].value,t[1].value.valueOf(),Jt(t[2])),d=!1;break}case"tbnz":{const t=e.operands;h.putTbnzRegImmLabel(t[0].value,t[1].value.valueOf(),Jt(t[2])),d=!1;break}case"str":{const t=e.operands,n=t[0].value,r=t[1].value,a=r.disp;"xzr"===n&&a===o?(_=r.base,h.putPushRegReg("x0","lr"),h.putMovRegReg("x0",_),h.putBlImm(p),h.putPopRegReg("x0","lr"),f=!0,d=!1):i.has(a)&&r.base===_&&(d=!1);break}case"ldr":{const t=e.operands,n=t[1].value;"x"===n.base[0]&&n.disp===ie&&(m=t[0].value);break}case"blr":e.operands[0].value===m&&(h.putLdrRegRegOffset("x0","x0",8),h.putCallAddressWithArguments(a,["x0"]),f=!0,m=null,d=!1)}if(d?n.writeAll():n.skipOne(),r===t)break}n.dispose()}),h.dispose(),f||$t();return new NativeFunction(t,"void",["pointer"],we)}};function Dt(e,t,n){const r=Oe(),o=t.handle.readPointer();let i;const a=r.find("_ZN3art3JNIILb1EE14ExceptionClearEP7_JNIEnv");let s;i=null!==a?a:o.add(ie).readPointer();const l=r.find("_ZN3art3JNIILb1EE10FatalErrorEP7_JNIEnvPKc");s=null!==l?l:o.add(ae).readPointer();const c=Ft[Process.arch];if(void 0===c)throw new Error("Not yet implemented for "+Process.arch);let d=null;const u=pe(e).offset,h=u.exception,p=new Set,f=u.isExceptionReportedToInstrumentation;null!==f&&p.add(f);const _=u.throwLocation;null!==_&&(p.add(_),p.add(_+X),p.add(_+2*X));const m=Memory.alloc(65536);return Memory.patchCode(m,65536,e=>{d=c(e,m,i,s,h,p,n)}),d._code=m,d._callback=n,d}function zt(e,t,n,r,o,i,a){const s={},l=new Set,c=[n];for(;c.length>0;){let e=c.shift();if(Object.values(s).some(({begin:t,end:n})=>e.compare(t)>=0&&e.compare(n)<0))continue;const t=e.toString();let n={begin:e},o=null,i=!1;do{if(e.equals(r)){i=!0;break}const a=Instruction.parse(e);o=a;const d=s[a.address.toString()];if(void 0!==d){delete s[d.begin.toString()],s[t]=d,d.begin=n.begin,n=null;break}let u=null;switch(a.mnemonic){case"jmp":u=ptr(a.operands[0].value),i=!0;break;case"je":case"jg":case"jle":case"jne":case"js":u=ptr(a.operands[0].value);break;case"ret":i=!0}null!==u&&(l.add(u.toString()),c.push(u),c.sort((e,t)=>e.compare(t))),e=a.next}while(!i);null!==n&&(n.end=o.address.add(o.size),s[t]=n)}const d=Object.keys(s).map(e=>s[e]);d.sort((e,t)=>e.begin.compare(t.begin));const u=s[n.toString()];d.splice(d.indexOf(u),1),d.unshift(u);const h=new X86Writer(e,{pc:t});let p=!1,f=null;return d.forEach(e=>{const t=e.end.sub(e.begin).toInt32(),n=new X86Relocator(e.begin,h);let r;for(;0!==(r=n.readOne());){const e=n.input,{mnemonic:s}=e,c=e.address.toString();l.has(c)&&h.putLabel(c);let d=!0;switch(s){case"jmp":h.putJmpNearLabel(Jt(e.operands[0])),d=!1;break;case"je":case"jg":case"jle":case"jne":case"js":h.putJccNearLabel(s,Jt(e.operands[0]),"no-hint"),d=!1;break;case"mov":{const[t,n]=e.operands;if("mem"===t.type&&"imm"===n.type){const e=t.value,r=e.disp;if(r===o&&0===n.value.valueOf()){if(f=e.base,h.putPushfx(),h.putPushax(),h.putMovRegReg("xbp","xsp"),4===X)h.putAndRegU32("esp",4294967280);else{const e="rdi"!==f?"rdi":"rsi";h.putMovRegU64(e,uint64("0xfffffffffffffff0")),h.putAndRegReg("rsp",e)}h.putCallAddressWithAlignedArguments(a,[f]),h.putMovRegReg("xsp","xbp"),h.putPopax(),h.putPopfx(),p=!0,d=!1}else i.has(r)&&e.base===f&&(d=!1)}break}case"call":{const t=e.operands[0];"mem"===t.type&&t.value.disp===ie&&(4===X?(h.putPopReg("eax"),h.putMovRegRegOffsetPtr("eax","eax",4),h.putPushReg("eax")):h.putMovRegRegOffsetPtr("rdi","rdi",8),h.putCallAddressWithArguments(a,[]),p=!0,d=!1);break}}if(d?n.writeAll():n.skipOne(),r===t)break}n.dispose()}),h.dispose(),p||$t(),new NativeFunction(t,"void",["pointer"],we)}function $t(){throw new Error("Unable to parse ART internals; please file a bug")}function Jt(e){return ptr(e.value).toString()}function Vt(e,t){const{arch:n}=Process;switch(n){case"ia32":case"arm64":{let r;r="ia32"===n?dt(64,n=>{const r=1+t.length,o=4*r;n.putSubRegImm("esp",o);for(let e=0;e!==r;e++){const t=4*e;n.putMovRegRegOffsetPtr("eax","esp",o+4+t),n.putMovRegOffsetPtrReg("esp",t,"eax")}n.putCallAddress(e),n.putAddRegImm("esp",o-4),n.putRet()}):dt(32,n=>{n.putMovRegReg("x8","x0"),t.forEach((e,t)=>{n.putMovRegReg("x"+t,"x"+(t+1))}),n.putLdrRegAddress("x7",e),n.putBrReg("x7")});const o=new NativeFunction(r,"void",["pointer"].concat(t),we),i=function(...e){o(...e)};return i.handle=r,i.impl=e,i}default:{const n=new NativeFunction(e,"void",["pointer"].concat(t),we);return n.impl=e,n}}}class Bt{constructor(){this.handle=Memory.alloc(le)}dispose(){const[e,t]=this._getData();t||Oe().$delete(e)}disposeToString(){const e=this.toString();return this.dispose(),e}toString(){const[e]=this._getData();return e.readUtf8String()}_getData(){const e=this.handle,t=!(1&e.readU8());return[t?e.add(1):e.add(2*X).readPointer(),t]}}class Gt{$delete(){this.dispose(),Oe().$delete(this)}constructor(e,t){this.handle=e,this._begin=e,this._end=e.add(X),this._storage=e.add(2*X),this._elementSize=t}init(){this.begin=NULL,this.end=NULL,this.storage=NULL}dispose(){Oe().$delete(this.begin)}get begin(){return this._begin.readPointer()}set begin(e){this._begin.writePointer(e)}get end(){return this._end.readPointer()}set end(e){this._end.writePointer(e)}get storage(){return this._storage.readPointer()}set storage(e){this._storage.writePointer(e)}get size(){return this.end.sub(this.begin).toInt32()/this._elementSize}}class Zt extends Gt{static $new(){const e=new Zt(Oe().$new(ce));return e.init(),e}constructor(e){super(e,X)}get handles(){const e=[];let t=this.begin;const n=this.end;for(;!t.equals(n);)e.push(t.readPointer()),t=t.add(X);return e}}const Ht=X,qt=Ht+4;class Wt{$delete(){this.dispose(),Oe().$delete(this)}constructor(e){this.handle=e,this._link=e.add(0),this._numberOfReferences=e.add(Ht)}init(e,t){this.link=e,this.numberOfReferences=t}dispose(){}get link(){return new Wt(this._link.readPointer())}set link(e){this._link.writePointer(e)}get numberOfReferences(){return this._numberOfReferences.readS32()}set numberOfReferences(e){this._numberOfReferences.writeS32(e)}}const Kt=function(e){const t=e%X;if(0!==t)return e+X-t;return e}(qt),Qt=Kt+X,Xt=Qt+X;class Yt extends Wt{static $new(e,t){const n=new Yt(Oe().$new(Xt));return n.init(e,t),n}constructor(e){super(e),this._self=e.add(Kt),this._currentScope=e.add(Qt);const t=(64-X-4-4)/4;this._scopeLayout=en.layoutForCapacity(t),this._topHandleScopePtr=null}init(e,t){const n=e.add(pe(t).offset.topHandleScope);this._topHandleScopePtr=n,super.init(n.readPointer(),-1),this.self=e,this.currentScope=en.$new(this._scopeLayout),n.writePointer(this)}dispose(){let e;for(this._topHandleScopePtr.writePointer(this.link);null!==(e=this.currentScope);){const t=e.link;e.$delete(),this.currentScope=t}}get self(){return this._self.readPointer()}set self(e){this._self.writePointer(e)}get currentScope(){const e=this._currentScope.readPointer();return e.isNull()?null:new en(e,this._scopeLayout)}set currentScope(e){this._currentScope.writePointer(e)}newHandle(e){return this.currentScope.newHandle(e)}}class en extends Wt{static $new(e){const t=new en(Oe().$new(e.size),e);return t.init(),t}constructor(e,t){super(e);const{offset:n}=t;this._refsStorage=e.add(n.refsStorage),this._pos=e.add(n.pos),this._layout=t}init(){super.init(NULL,this._layout.numberOfReferences),this.pos=0}get pos(){return this._pos.readU32()}set pos(e){this._pos.writeU32(e)}newHandle(e){const t=this.pos,n=this._refsStorage.add(4*t);return n.writeS32(e.toInt32()),this.pos=t+1,n}static layoutForCapacity(e){const t=qt+4*e;return{size:t+4,numberOfReferences:e,offset:{refsStorage:qt,pos:t}}}}const tn={arm:function(e,t){const n=Process.pageSize,r=Memory.alloc(n);Memory.protect(r,n,"rwx");const o=new NativeCallback(t,"void",["pointer"]);r._onMatchCallback=o;const i=[26625,18947,17041,53505,19202,18200,18288,48896],a=2*i.length,s=a+4,l=s+4;return Memory.patchCode(r,l,function(t){i.forEach((e,n)=>{t.add(2*n).writeU16(e)}),t.add(a).writeS32(e),t.add(s).writePointer(o)}),r.or(1)},arm64:function(e,t){const n=Process.pageSize,r=Memory.alloc(n);Memory.protect(r,n,"rwx");const o=new NativeCallback(t,"void",["pointer"]);r._onMatchCallback=o;const i=[3107979265,402653378,1795293247,1409286241,1476395139,3592355936,3596551104],a=4*i.length,s=a+4,l=s+8;return Memory.patchCode(r,l,function(t){i.forEach((e,n)=>{t.add(4*n).writeU32(e)}),t.add(a).writeS32(e),t.add(s).writePointer(o)}),r}};function nn(e,t){return(tn[Process.arch]||rn)(e,t)}function rn(e,t){return new NativeCallback(n=>{n.readS32()===e&&t(n)},"void",["pointer","pointer"])}const on=Object.freeze(Object.defineProperty({__proto__:null,ArtMethod:st,ArtStackVisitor:class{constructor(e,t,n,r=0,o=!0){const i=Oe(),a=3*X,s=Memory.alloc(512+a);i["art::StackVisitor::StackVisitor"](s,e,t,at[n],r,o?1:0);const l=s.add(512);s.writePointer(l);const c=new NativeCallback(this._visitFrame.bind(this),"bool",["pointer"]);l.add(2*X).writePointer(c),this.handle=s,this._onVisitFrame=c;const d=s.add(4===X?12:24);this._curShadowFrame=d,this._curQuickFrame=d.add(X),this._curQuickFramePc=d.add(2*X),this._curOatQuickMethodHeader=d.add(3*X),this._getMethodImpl=i["art::StackVisitor::GetMethod"],this._descLocImpl=i["art::StackVisitor::DescribeLocation"],this._getCQFIImpl=i["art::StackVisitor::GetCurrentQuickFrameInfo"]}walkStack(e=!1){Oe()["art::StackVisitor::WalkStack"](this.handle,e?1:0)}_visitFrame(){return this.visitFrame()?1:0}visitFrame(){throw new Error("Subclass must implement visitFrame")}getMethod(){const e=this._getMethodImpl(this.handle);return e.isNull()?null:new st(e)}getCurrentQuickFramePc(){return this._curQuickFramePc.readPointer()}getCurrentQuickFrame(){return this._curQuickFrame.readPointer()}getCurrentShadowFrame(){return this._curShadowFrame.readPointer()}describeLocation(){const e=new Bt;return this._descLocImpl(e,this.handle),e.disposeToString()}getCurrentOatQuickMethodHeader(){return this._curOatQuickMethodHeader.readPointer()}getCurrentQuickFrameInfo(){return this._getCQFIImpl(this.handle)}},DVM_JNI_ENV_OFFSET_SELF:12,HandleVector:Zt,VariableSizedHandleScope:Yt,backtrace:gt,deoptimizeBootImage:xt,deoptimizeEverything:Pt,deoptimizeMethod:At,ensureClassInitialized:function(e,t){"art"===Oe().flavor&&(e.getFieldId(t,"x","Z"),e.exceptionClear())},getAndroidApiLevel:be,getAndroidVersion:me,getApi:Oe,getArtClassSpec:Be,getArtFieldSpec:Ge,getArtMethodSpec:he,getArtThreadFromEnv:We,getArtThreadSpec:pe,makeArtClassLoaderVisitor:it,makeArtClassVisitor:rt,makeMethodMangler:function(e){return new Le(e)},makeObjectVisitorPredicate:nn,revertGlobalPatches:yt,translateMethod:function(e){return Ce.replacedMethods.translate(e)},withAllArtThreadsSuspended:tt,withRunnableArtThread:Ye},Symbol.toStringTag,{value:"Module"})),{pointerSize:an}=Process,sn=33554432,ln=67108864,cn=134217728,dn={exceptions:"propagate"},un=p(function(){const e=bn(),{version:t}=e;let n;n=t>=17?"method:early":t>=9&&t<=16?"const-method":"method:late";const r=e["Method::size"](1)*an,o=4*an,i="method:early"===n?an:0,a=o+i,s=a+4,l=s+4+8,c=l+an,d=0!==i?o:c,u=8+an,h=u+an,p="const-method"===n?an:0,f=h+p;return{getAdapterPointer:0!==p?function(e,t){return t.add(h)}:function(e,t){return e.add(d)},method:{size:r,constMethodOffset:an,methodDataOffset:2*an,methodCountersOffset:3*an,accessFlagsOffset:a,vtableIndexOffset:s,i2iEntryOffset:l,nativeFunctionOffset:r-2*an,signatureHandlerOffset:r-an},constMethod:{constantPoolOffset:8,stackmapDataOffset:u,sizeOffset:f,methodIdnumOffset:f+14},constantPool:{cacheOffset:2*an,instanceKlassOffset:3*an}}}),hn=p(function(){const{version:e,createNewDefaultVtableIndices:t}=bn(),n=jn[Process.arch];if(void 0===n)throw new Error(`Missing vtable offset parser for ${Process.arch}`);const r=h(t,n,{limit:32});if(null===r)throw new Error("Unable to deduce vtable offset");return{vtableOffset:r,methodsOffset:r-7*an,memberNamesOffset:r-17*an,oopMapCacheOffset:r-(e>=10&&e<=11||e>=15?17:18)*an}}),pn=p(function(){const{vtableRedefineClasses:e,redefineClassesDoIt:t,redefineClassesDoItPrologue:n,redefineClassesDoItEpilogue:r,redefineClassesOnError:o,redefineClassesAllow:i,redefineClassesDispose0:a,redefineClassesDispose1:s,"VMThread::execute":l}=bn(),c=e.add(2*an),d=15*an,u=Memory.dup(c,d),h=new NativeCallback(()=>{},"void",["pointer"]);let p,f,_;for(let e=0;e!==d;e+=an){const l=u.add(e),c=l.readPointer();void 0!==o&&c.equals(o)||void 0!==a&&c.equals(a)||void 0!==s&&c.equals(s)?l.writePointer(h):c.equals(t)?p=e:c.equals(n)?(f=e,l.writePointer(i)):c.equals(r)&&(_=e,l.writePointer(h))}return{execute:l,emptyCallback:h,vtable:u,vtableSize:d,doItOffset:p,prologueOffset:f,epilogueOffset:_}});let fn=null,_n=!1;const mn=new Map,gn=new Map;function bn(){return null===fn&&(fn=function(){const e=Process.enumerateModules().filter(e=>/jvm.(dll|dylib|so)$/.test(e.name));if(0===e.length)return null;const t=e[0],n={flavor:"jvm"},r="windows"===Process.platform?[{module:t,functions:{JNI_GetCreatedJavaVMs:["JNI_GetCreatedJavaVMs","int",["pointer","int","pointer"]],JVM_Sleep:["JVM_Sleep","void",["pointer","pointer","long"]],"VMThread::execute":["VMThread::execute","void",["pointer"]],"Method::size":["Method::size","int",["int"]],"Method::set_native_function":["Method::set_native_function","void",["pointer","pointer","int"]],"Method::clear_native_function":["Method::clear_native_function","void",["pointer"]],"Method::jmethod_id":["Method::jmethod_id","pointer",["pointer"]],"ClassLoaderDataGraph::classes_do":["ClassLoaderDataGraph::classes_do","void",["pointer"]],"NMethodSweeper::sweep_code_cache":["NMethodSweeper::sweep_code_cache","void",[]],"OopMapCache::flush_obsolete_entries":["OopMapCache::flush_obsolete_entries","void",["pointer"]]},variables:{"VM_RedefineClasses::`vftable'":function(e){this.vtableRedefineClasses=e},"VM_RedefineClasses::doit":function(e){this.redefineClassesDoIt=e},"VM_RedefineClasses::doit_prologue":function(e){this.redefineClassesDoItPrologue=e},"VM_RedefineClasses::doit_epilogue":function(e){this.redefineClassesDoItEpilogue=e},"VM_RedefineClasses::allow_nested_vm_operations":function(e){this.redefineClassesAllow=e},"NMethodSweeper::_traversals":function(e){this.traversals=e},"NMethodSweeper::_should_sweep":function(e){this.shouldSweep=e}},optionals:[]}]:[{module:t,functions:{JNI_GetCreatedJavaVMs:["JNI_GetCreatedJavaVMs","int",["pointer","int","pointer"]],_ZN6Method4sizeEb:["Method::size","int",["int"]],_ZN6Method19set_native_functionEPhb:["Method::set_native_function","void",["pointer","pointer","int"]],_ZN6Method21clear_native_functionEv:["Method::clear_native_function","void",["pointer"]],_ZN6Method24restore_unshareable_infoEP10JavaThread:["Method::restore_unshareable_info","void",["pointer","pointer"]],_ZN6Method24restore_unshareable_infoEP6Thread:["Method::restore_unshareable_info","void",["pointer","pointer"]],_ZN6Method11link_methodERK12methodHandleP10JavaThread:["Method::link_method","void",["pointer","pointer","pointer"]],_ZN6Method10jmethod_idEv:["Method::jmethod_id","pointer",["pointer"]],_ZN6Method10clear_codeEv:function(e){const t=new NativeFunction(e,"void",["pointer"],dn);this["Method::clear_code"]=function(e){t(e)}},_ZN6Method10clear_codeEb:function(e){const t=new NativeFunction(e,"void",["pointer","int"],dn),n=0;this["Method::clear_code"]=function(e){t(e,n)}},_ZN18VM_RedefineClasses19mark_dependent_codeEP13InstanceKlass:["VM_RedefineClasses::mark_dependent_code","void",["pointer","pointer"]],_ZN18VM_RedefineClasses20flush_dependent_codeEv:["VM_RedefineClasses::flush_dependent_code","void",[]],_ZN18VM_RedefineClasses20flush_dependent_codeEP13InstanceKlassP6Thread:["VM_RedefineClasses::flush_dependent_code","void",["pointer","pointer","pointer"]],_ZN18VM_RedefineClasses20flush_dependent_codeE19instanceKlassHandleP6Thread:["VM_RedefineClasses::flush_dependent_code","void",["pointer","pointer","pointer"]],_ZN19ResolvedMethodTable21adjust_method_entriesEPb:["ResolvedMethodTable::adjust_method_entries","void",["pointer"]],_ZN15MemberNameTable21adjust_method_entriesEP13InstanceKlassPb:["MemberNameTable::adjust_method_entries","void",["pointer","pointer","pointer"]],_ZN17ConstantPoolCache21adjust_method_entriesEPb:function(e){const t=new NativeFunction(e,"void",["pointer","pointer"],dn);this["ConstantPoolCache::adjust_method_entries"]=function(e,n,r){t(e,r)}},_ZN17ConstantPoolCache21adjust_method_entriesEP13InstanceKlassPb:function(e){const t=new NativeFunction(e,"void",["pointer","pointer","pointer"],dn);this["ConstantPoolCache::adjust_method_entries"]=function(e,n,r){t(e,n,r)}},_ZN20ClassLoaderDataGraph10classes_doEP12KlassClosure:["ClassLoaderDataGraph::classes_do","void",["pointer"]],_ZN20ClassLoaderDataGraph22clean_deallocate_listsEb:["ClassLoaderDataGraph::clean_deallocate_lists","void",["int"]],_ZN10JavaThread27thread_from_jni_environmentEP7JNIEnv_:["JavaThread::thread_from_jni_environment","pointer",["pointer"]],_ZN8VMThread7executeEP12VM_Operation:["VMThread::execute","void",["pointer"]],_ZN11OopMapCache22flush_obsolete_entriesEv:["OopMapCache::flush_obsolete_entries","void",["pointer"]],_ZN14NMethodSweeper11force_sweepEv:["NMethodSweeper::force_sweep","void",[]],_ZN14NMethodSweeper16sweep_code_cacheEv:["NMethodSweeper::sweep_code_cache","void",[]],_ZN14NMethodSweeper17sweep_in_progressEv:["NMethodSweeper::sweep_in_progress","bool",[]],JVM_Sleep:["JVM_Sleep","void",["pointer","pointer","long"]]},variables:{_ZN18VM_RedefineClasses14_the_class_oopE:function(e){this.redefineClass=e},_ZN18VM_RedefineClasses10_the_classE:function(e){this.redefineClass=e},_ZN18VM_RedefineClasses25AdjustCpoolCacheAndVtable8do_klassEP5Klass:function(e){this.doKlass=e},_ZN18VM_RedefineClasses22AdjustAndCleanMetadata8do_klassEP5Klass:function(e){this.doKlass=e},_ZTV18VM_RedefineClasses:function(e){this.vtableRedefineClasses=e},_ZN18VM_RedefineClasses4doitEv:function(e){this.redefineClassesDoIt=e},_ZN18VM_RedefineClasses13doit_prologueEv:function(e){this.redefineClassesDoItPrologue=e},_ZN18VM_RedefineClasses13doit_epilogueEv:function(e){this.redefineClassesDoItEpilogue=e},_ZN18VM_RedefineClassesD0Ev:function(e){this.redefineClassesDispose0=e},_ZN18VM_RedefineClassesD1Ev:function(e){this.redefineClassesDispose1=e},_ZNK18VM_RedefineClasses26allow_nested_vm_operationsEv:function(e){this.redefineClassesAllow=e},_ZNK18VM_RedefineClasses14print_on_errorEP12outputStream:function(e){this.redefineClassesOnError=e},_ZN13InstanceKlass33create_new_default_vtable_indicesEiP10JavaThread:function(e){this.createNewDefaultVtableIndices=e},_ZN13InstanceKlass33create_new_default_vtable_indicesEiP6Thread:function(e){this.createNewDefaultVtableIndices=e},_ZN19Abstract_VM_Version19jre_release_versionEv:function(e){const t=new NativeFunction(e,"pointer",[],dn)().readCString();this.version=t.startsWith("1.8")?8:t.startsWith("9.")?9:parseInt(t.slice(0,2),10),this.versionS=t},_ZN14NMethodSweeper11_traversalsE:function(e){this.traversals=e},_ZN14NMethodSweeper21_sweep_fractions_leftE:function(e){this.fractions=e},_ZN14NMethodSweeper13_should_sweepE:function(e){this.shouldSweep=e}},optionals:["_ZN6Method24restore_unshareable_infoEP10JavaThread","_ZN6Method24restore_unshareable_infoEP6Thread","_ZN6Method11link_methodERK12methodHandleP10JavaThread","_ZN6Method10clear_codeEv","_ZN6Method10clear_codeEb","_ZN18VM_RedefineClasses19mark_dependent_codeEP13InstanceKlass","_ZN18VM_RedefineClasses20flush_dependent_codeEv","_ZN18VM_RedefineClasses20flush_dependent_codeEP13InstanceKlassP6Thread","_ZN18VM_RedefineClasses20flush_dependent_codeE19instanceKlassHandleP6Thread","_ZN19ResolvedMethodTable21adjust_method_entriesEPb","_ZN15MemberNameTable21adjust_method_entriesEP13InstanceKlassPb","_ZN17ConstantPoolCache21adjust_method_entriesEPb","_ZN17ConstantPoolCache21adjust_method_entriesEP13InstanceKlassPb","_ZN20ClassLoaderDataGraph22clean_deallocate_listsEb","_ZN10JavaThread27thread_from_jni_environmentEP7JNIEnv_","_ZN14NMethodSweeper11force_sweepEv","_ZN14NMethodSweeper17sweep_in_progressEv","_ZN18VM_RedefineClasses14_the_class_oopE","_ZN18VM_RedefineClasses10_the_classE","_ZN18VM_RedefineClasses25AdjustCpoolCacheAndVtable8do_klassEP5Klass","_ZN18VM_RedefineClasses22AdjustAndCleanMetadata8do_klassEP5Klass","_ZN18VM_RedefineClassesD0Ev","_ZN18VM_RedefineClassesD1Ev","_ZNK18VM_RedefineClasses14print_on_errorEP12outputStream","_ZN13InstanceKlass33create_new_default_vtable_indicesEiP10JavaThread","_ZN13InstanceKlass33create_new_default_vtable_indicesEiP6Thread","_ZN14NMethodSweeper21_sweep_fractions_leftE"]}],a=[];if(r.forEach(function(e){const t=e.module,r=e.functions||{},o=e.variables||{},i=new Set(e.optionals||[]),s=t.enumerateExports().reduce(function(e,t){return e[t.name]=t,e},{}),l=t.enumerateSymbols().reduce(function(e,t){return e[t.name]=t,e},s);Object.keys(r).forEach(function(e){const t=l[e];if(void 0!==t){const o=r[e];"function"==typeof o?o.call(n,t.address):n[o[0]]=new NativeFunction(t.address,o[1],o[2],dn)}else i.has(e)||a.push(e)}),Object.keys(o).forEach(function(e){const t=l[e];if(void 0!==t){o[e].call(n,t.address)}else i.has(e)||a.push(e)})}),a.length>0)throw new Error("Java API only partially available; please file a bug. Missing: "+a.join(", "));const l=Memory.alloc(an),c=Memory.alloc(4);if(o("JNI_GetCreatedJavaVMs",n.JNI_GetCreatedJavaVMs(l,1,c)),0===c.readInt())return null;n.vm=l.readPointer();const u="windows"===Process.platform?{$new:["??2@YAPEAX_K@Z","pointer",["ulong"]],$delete:["??3@YAXPEAX@Z","void",["pointer"]]}:{$new:["_Znwm","pointer",["ulong"]],$delete:["_ZdlPv","void",["pointer"]]};for(const[e,[t,r,o]]of Object.entries(u)){let i=Module.findGlobalExportByName(t);if(null===i&&(i=DebugSymbol.fromName(t).address,i.isNull()))throw new Error(`unable to find C++ allocator API, missing: '${t}'`);n[e]=new NativeFunction(i,r,o,dn)}n.jvmti=function(e){const t=new Q(e);let n;return t.perform(()=>{const e=t.tryGetEnvHandle(i);if(null===e)throw new Error("JVMTI not available");n=new d(e,t);const r=Memory.alloc(8);r.writeU64(s);o("getEnvJvmti::AddCapabilities",n.addCapabilities(r))}),n}(n),void 0===n["JavaThread::thread_from_jni_environment"]&&(n["JavaThread::thread_from_jni_environment"]=function(e){let t=null;const n=yn[Process.arch];if(void 0!==n){const r=new Q(e).perform(e=>e.handle.readPointer().add(6*an).readPointer());t=h(r,n,{limit:11})}if(null===t)return()=>{throw new Error("Unable to make thread_from_jni_environment() helper for the current architecture")};return e=>e.add(t)}(n));return n}()),fn}const yn={x64:function(e){if("lea"!==e.mnemonic)return null;const{base:t,disp:n}=e.operands[1].value;if(!("rdi"===t&&n<0))return null;return n}};function vn(e,t){}class wn{constructor(e){this.methodId=e,this.method=e.readPointer(),this.originalMethod=null,this.newMethod=null,this.resolved=null,this.impl=null,this.key=e.toString(16)}replace(e,t,n,r,o){const{key:i}=this,a=gn.get(i);void 0!==a&&(gn.delete(i),this.method=a.method,this.originalMethod=a.originalMethod,this.newMethod=a.newMethod,this.resolved=a.resolved),this.impl=e,mn.set(i,this),En(r)}revert(e){const{key:t}=this;mn.delete(t),gn.set(t,this),En(e)}resolveTarget(e,t,n,r){const{resolved:o,originalMethod:i,methodId:a}=this;if(null!==o)return o;if(null===i)return a;i.oldMethod.vtableIndexPtr.writeS32(-2);const s=Memory.alloc(an);return s.writePointer(this.method),this.resolved=s,s}}function En(e){_n||(_n=!0,Script.nextTick(Sn,e))}function Sn(e){const t=new Map(mn),n=new Map(gn);mn.clear(),gn.clear(),_n=!1,e.perform(e=>{const r=bn(),o=r["JavaThread::thread_from_jni_environment"](e.handle);let i=!1;Nn(()=>{t.forEach(e=>{const{method:t,originalMethod:n,impl:i,methodId:a,newMethod:s}=e;null===n?(e.originalMethod=kn(t),e.newMethod=function(e,t,n){const r=bn(),o=kn(e);o.constPtr.writePointer(o.const);const i=(256|o.accessFlags|sn|ln|cn)>>>0;if(o.accessFlagsPtr.writeU32(i),o.signatureHandler.writePointer(NULL),o.adapter.writePointer(NULL),o.i2iEntry.writePointer(NULL),r["Method::clear_code"](o.method),o.dataPtr.writePointer(NULL),o.countersPtr.writePointer(NULL),o.stackmapPtr.writePointer(NULL),r["Method::clear_native_function"](o.method),r["Method::set_native_function"](o.method,t,0),r["Method::restore_unshareable_info"](o.method,n),r.version>=17){const e=Memory.alloc(2*an);e.writePointer(o.method),e.add(an).writePointer(n),r["Method::link_method"](o.method,e,n)}return o}(t,i,o),Cn(e.newMethod,a,o)):r["Method::set_native_function"](s.method,i,0)}),n.forEach(e=>{const{originalMethod:t,methodId:n,newMethod:r}=e;if(null!==t){!function(e){const{oldMethod:t}=e;t.accessFlagsPtr.writeU32(t.accessFlags),t.vtableIndexPtr.writeS32(t.vtableIndex)}(t);const e=t.oldMethod;e.oldMethod=r,Cn(e,n,o),i=!0}})}),i&&function(e){const{fractions:t,shouldSweep:n,traversals:r,"NMethodSweeper::sweep_code_cache":o,"NMethodSweeper::sweep_in_progress":i,"NMethodSweeper::force_sweep":a,JVM_Sleep:s}=bn();if(void 0!==a)Thread.sleep(.05),a(),Thread.sleep(.05),a();else{let a=r.readS64();const l=a+2;for(;l>a;){t.writeS32(1),s(e,NULL,50),i()||Nn(()=>{Thread.sleep(.05)});0===n.readU8()&&(t.writeS32(1),o()),a=r.readS64()}}}(e.handle)})}function Nn(e,t,n){const{execute:r,vtable:o,vtableSize:i,doItOffset:a,prologueOffset:s,epilogueOffset:l}=pn(),c=Memory.dup(o,i),d=Memory.alloc(25*an);d.writePointer(c);const u=new NativeCallback(e,"void",["pointer"]);c.add(a).writePointer(u),r(d)}function Ln(e){return new wn(e)}function Cn(e,t,n){const{method:r,oldMethod:o}=e,i=bn();e.methodsArray.add(e.methodIndex*an).writePointer(r),e.vtableIndex>=0&&e.vtable.add(e.vtableIndex*an).writePointer(r),t.writePointer(r),o.accessFlagsPtr.writeU32((196608|o.accessFlags)>>>0);const a=i["OopMapCache::flush_obsolete_entries"];if(void 0!==a){const{oopMapCache:t}=e;t.isNull()||a(t)}const s=i["VM_RedefineClasses::mark_dependent_code"],l=i["VM_RedefineClasses::flush_dependent_code"];void 0!==s?(s(NULL,e.instanceKlass),l()):l(NULL,e.instanceKlass,n);const c=Memory.alloc(1);c.writeU8(1),i["ConstantPoolCache::adjust_method_entries"](e.cache,e.instanceKlass,c);const d=Memory.alloc(3*an),u=Memory.alloc(an);u.writePointer(i.doKlass),d.writePointer(u),d.add(an).writePointer(n),d.add(2*an).writePointer(n),void 0!==i.redefineClass&&i.redefineClass.writePointer(e.instanceKlass),i["ClassLoaderDataGraph::classes_do"](d);const h=i["ResolvedMethodTable::adjust_method_entries"];if(void 0!==h)h(c);else{const{memberNames:t}=e;if(!t.isNull()){const n=i["MemberNameTable::adjust_method_entries"];void 0!==n&&n(t,e.instanceKlass,c)}}const p=i["ClassLoaderDataGraph::clean_deallocate_lists"];void 0!==p&&p(0)}function kn(e){const t=un(),n=e.add(t.method.constMethodOffset).readPointer(),r=n.add(t.constMethod.sizeOffset).readS32()*an,o=Memory.alloc(r+t.method.size);Memory.copy(o,n,r);const i=o.add(r);Memory.copy(i,e,t.method.size);const a=Mn(i,o,r),s=Mn(e,n,r);return a.oldMethod=s,a}function Mn(e,t,n){const r=bn(),o=un(),i=e.add(o.method.constMethodOffset),a=e.add(o.method.methodDataOffset),s=e.add(o.method.methodCountersOffset),l=e.add(o.method.accessFlagsOffset),c=l.readU32(),d=o.getAdapterPointer(e,t),u=e.add(o.method.i2iEntryOffset),h=e.add(o.method.signatureHandlerOffset),p=t.add(o.constMethod.constantPoolOffset).readPointer(),f=t.add(o.constMethod.stackmapDataOffset),_=p.add(o.constantPool.instanceKlassOffset).readPointer(),m=p.add(o.constantPool.cacheOffset).readPointer(),g=hn(),b=_.add(g.methodsOffset).readPointer(),y=b.readS32(),v=b.add(an),w=t.add(o.constMethod.methodIdnumOffset).readU16(),E=e.add(o.method.vtableIndexOffset),S=E.readS32(),N=_.add(g.vtableOffset),L=_.add(g.oopMapCacheOffset).readPointer(),C=r.version>=10?_.add(g.memberNamesOffset).readPointer():NULL;return{method:e,methodSize:o.method.size,const:t,constSize:n,constPtr:i,dataPtr:a,countersPtr:s,stackmapPtr:f,instanceKlass:_,methodsArray:v,methodsCount:y,methodIndex:w,vtableIndex:S,vtableIndexPtr:E,vtable:N,accessFlags:c,accessFlagsPtr:l,adapter:d,i2iEntry:u,signatureHandler:h,memberNames:C,cache:m,oopMapCache:L}}const jn={x64:function(e){if("mov"!==e.mnemonic)return null;const t=e.operands[0];if("mem"!==t.type)return null;const{value:n}=t;if(1!==n.scale)return null;const{disp:r}=n;if(r<256)return null;return r+16}};let In=Oe;try{me()}catch(e){In=bn}const Rn=In,An="#include <json-glib/json-glib.h>\n#include <string.h>\n\n#define kAccStatic 0x0008\n#define kAccConstructor 0x00010000\n\ntypedef struct _Model Model;\ntypedef struct _EnumerateMethodsContext EnumerateMethodsContext;\n\ntypedef struct _JavaApi JavaApi;\ntypedef struct _JavaClassApi JavaClassApi;\ntypedef struct _JavaMethodApi JavaMethodApi;\ntypedef struct _JavaFieldApi JavaFieldApi;\n\ntypedef struct _JNIEnv JNIEnv;\ntypedef guint8 jboolean;\ntypedef gint32 jint;\ntypedef jint jsize;\ntypedef gpointer jobject;\ntypedef jobject jclass;\ntypedef jobject jstring;\ntypedef jobject jarray;\ntypedef jarray jobjectArray;\ntypedef gpointer jfieldID;\ntypedef gpointer jmethodID;\n\ntypedef struct _jvmtiEnv jvmtiEnv;\ntypedef enum\n{\n  JVMTI_ERROR_NONE = 0\n} jvmtiError;\n\ntypedef struct _ArtApi ArtApi;\ntypedef guint32 ArtHeapReference;\ntypedef struct _ArtObject ArtObject;\ntypedef struct _ArtClass ArtClass;\ntypedef struct _ArtClassLinker ArtClassLinker;\ntypedef struct _ArtClassVisitor ArtClassVisitor;\ntypedef struct _ArtClassVisitorVTable ArtClassVisitorVTable;\ntypedef struct _ArtMethod ArtMethod;\ntypedef struct _ArtString ArtString;\n\ntypedef union _StdString StdString;\ntypedef struct _StdStringShort StdStringShort;\ntypedef struct _StdStringLong StdStringLong;\n\ntypedef void (* ArtVisitClassesFunc) (ArtClassLinker * linker, ArtClassVisitor * visitor);\ntypedef const char * (* ArtGetClassDescriptorFunc) (ArtClass * klass, StdString * storage);\ntypedef void (* ArtPrettyMethodFunc) (StdString * result, ArtMethod * method, jboolean with_signature);\n\nstruct _Model\n{\n  GHashTable * members;\n};\n\nstruct _EnumerateMethodsContext\n{\n  GPatternSpec * class_query;\n  GPatternSpec * method_query;\n  jboolean include_signature;\n  jboolean ignore_case;\n  jboolean skip_system_classes;\n  GHashTable * groups;\n};\n\nstruct _JavaClassApi\n{\n  jmethodID get_declared_methods;\n  jmethodID get_declared_fields;\n};\n\nstruct _JavaMethodApi\n{\n  jmethodID get_name;\n  jmethodID get_modifiers;\n};\n\nstruct _JavaFieldApi\n{\n  jmethodID get_name;\n  jmethodID get_modifiers;\n};\n\nstruct _JavaApi\n{\n  JavaClassApi clazz;\n  JavaMethodApi method;\n  JavaFieldApi field;\n};\n\nstruct _JNIEnv\n{\n  gpointer * functions;\n};\n\nstruct _jvmtiEnv\n{\n  gpointer * functions;\n};\n\nstruct _ArtApi\n{\n  gboolean available;\n\n  guint class_offset_ifields;\n  guint class_offset_methods;\n  guint class_offset_sfields;\n  guint class_offset_copied_methods_offset;\n\n  guint method_size;\n  guint method_offset_access_flags;\n\n  guint field_size;\n  guint field_offset_access_flags;\n\n  guint alignment_padding;\n\n  ArtClassLinker * linker;\n  ArtVisitClassesFunc visit_classes;\n  ArtGetClassDescriptorFunc get_class_descriptor;\n  ArtPrettyMethodFunc pretty_method;\n\n  void (* free) (gpointer mem);\n};\n\nstruct _ArtObject\n{\n  ArtHeapReference klass;\n  ArtHeapReference monitor;\n};\n\nstruct _ArtClass\n{\n  ArtObject parent;\n\n  ArtHeapReference class_loader;\n};\n\nstruct _ArtClassVisitor\n{\n  ArtClassVisitorVTable * vtable;\n  gpointer user_data;\n};\n\nstruct _ArtClassVisitorVTable\n{\n  void (* reserved1) (ArtClassVisitor * self);\n  void (* reserved2) (ArtClassVisitor * self);\n  jboolean (* visit) (ArtClassVisitor * self, ArtClass * klass);\n};\n\nstruct _ArtString\n{\n  ArtObject parent;\n\n  gint32 count;\n  guint32 hash_code;\n\n  union\n  {\n    guint16 value[0];\n    guint8 value_compressed[0];\n  };\n};\n\nstruct _StdStringShort\n{\n  guint8 size;\n  gchar data[(3 * sizeof (gpointer)) - sizeof (guint8)];\n};\n\nstruct _StdStringLong\n{\n  gsize capacity;\n  gsize size;\n  gchar * data;\n};\n\nunion _StdString\n{\n  StdStringShort s;\n  StdStringLong l;\n};\n\nstatic void model_add_method (Model * self, const gchar * name, jmethodID id, jint modifiers);\nstatic void model_add_field (Model * self, const gchar * name, jfieldID id, jint modifiers);\nstatic void model_free (Model * model);\n\nstatic jboolean collect_matching_class_methods (ArtClassVisitor * self, ArtClass * klass);\nstatic gchar * finalize_method_groups_to_json (GHashTable * groups);\nstatic GPatternSpec * make_pattern_spec (const gchar * pattern, jboolean ignore_case);\nstatic gchar * class_name_from_signature (const gchar * signature);\nstatic gchar * format_method_signature (const gchar * name, const gchar * signature);\nstatic void append_type (GString * output, const gchar ** type);\n\nstatic gpointer read_art_array (gpointer object_base, guint field_offset, guint length_size, guint * length);\n\nstatic void std_string_destroy (StdString * str);\nstatic gchar * std_string_c_str (StdString * self);\n\nextern GMutex lock;\nextern GArray * models;\nextern JavaApi java_api;\nextern ArtApi art_api;\n\nvoid\ninit (void)\n{\n  g_mutex_init (&lock);\n  models = g_array_new (FALSE, FALSE, sizeof (Model *));\n}\n\nvoid\nfinalize (void)\n{\n  guint n, i;\n\n  n = models->len;\n  for (i = 0; i != n; i++)\n  {\n    Model * model = g_array_index (models, Model *, i);\n    model_free (model);\n  }\n\n  g_array_unref (models);\n  g_mutex_clear (&lock);\n}\n\nModel *\nmodel_new (jclass class_handle,\n           gpointer class_object,\n           JNIEnv * env)\n{\n  Model * model;\n  GHashTable * members;\n  gpointer * funcs = env->functions;\n  jmethodID (* from_reflected_method) (JNIEnv *, jobject) = funcs[7];\n  jfieldID (* from_reflected_field) (JNIEnv *, jobject) = funcs[8];\n  jobject (* to_reflected_method) (JNIEnv *, jclass, jmethodID, jboolean) = funcs[9];\n  jobject (* to_reflected_field) (JNIEnv *, jclass, jfieldID, jboolean) = funcs[12];\n  void (* delete_local_ref) (JNIEnv *, jobject) = funcs[23];\n  jobject (* call_object_method) (JNIEnv *, jobject, jmethodID, ...) = funcs[34];\n  jint (* call_int_method) (JNIEnv *, jobject, jmethodID, ...) = funcs[49];\n  const char * (* get_string_utf_chars) (JNIEnv *, jstring, jboolean *) = funcs[169];\n  void (* release_string_utf_chars) (JNIEnv *, jstring, const char *) = funcs[170];\n  jsize (* get_array_length) (JNIEnv *, jarray) = funcs[171];\n  jobject (* get_object_array_element) (JNIEnv *, jobjectArray, jsize) = funcs[173];\n  jsize n, i;\n\n  model = g_new (Model, 1);\n\n  members = g_hash_table_new_full (g_str_hash, g_str_equal, g_free, g_free);\n  model->members = members;\n\n  if (art_api.available)\n  {\n    gpointer elements;\n    guint n, i;\n    const guint field_arrays[] = {\n      art_api.class_offset_ifields,\n      art_api.class_offset_sfields\n    };\n    guint field_array_cursor;\n    gboolean merged_fields = art_api.class_offset_sfields == 0;\n\n    elements = read_art_array (class_object, art_api.class_offset_methods, sizeof (gsize), NULL);\n    n = *(guint16 *) (class_object + art_api.class_offset_copied_methods_offset);\n    for (i = 0; i != n; i++)\n    {\n      jmethodID id;\n      guint32 access_flags;\n      jboolean is_static;\n      jobject method, name;\n      const char * name_str;\n      jint modifiers;\n\n      id = elements + (i * art_api.method_size);\n\n      access_flags = *(guint32 *) (id + art_api.method_offset_access_flags);\n      if ((access_flags & kAccConstructor) != 0)\n        continue;\n      is_static = (access_flags & kAccStatic) != 0;\n      method = to_reflected_method (env, class_handle, id, is_static);\n      name = call_object_method (env, method, java_api.method.get_name);\n      name_str = get_string_utf_chars (env, name, NULL);\n      modifiers = access_flags & 0xffff;\n\n      model_add_method (model, name_str, id, modifiers);\n\n      release_string_utf_chars (env, name, name_str);\n      delete_local_ref (env, name);\n      delete_local_ref (env, method);\n    }\n\n    for (field_array_cursor = 0; field_array_cursor != G_N_ELEMENTS (field_arrays); field_array_cursor++)\n    {\n      jboolean is_static;\n\n      if (field_arrays[field_array_cursor] == 0)\n        continue;\n\n      if (!merged_fields)\n        is_static = field_array_cursor == 1;\n\n      elements = read_art_array (class_object, field_arrays[field_array_cursor], sizeof (guint32), &n);\n      for (i = 0; i != n; i++)\n      {\n        jfieldID id;\n        guint32 access_flags;\n        jobject field, name;\n        const char * name_str;\n        jint modifiers;\n\n        id = elements + (i * art_api.field_size);\n\n        access_flags = *(guint32 *) (id + art_api.field_offset_access_flags);\n        if (merged_fields)\n          is_static = (access_flags & kAccStatic) != 0;\n        field = to_reflected_field (env, class_handle, id, is_static);\n        name = call_object_method (env, field, java_api.field.get_name);\n        name_str = get_string_utf_chars (env, name, NULL);\n        modifiers = access_flags & 0xffff;\n\n        model_add_field (model, name_str, id, modifiers);\n\n        release_string_utf_chars (env, name, name_str);\n        delete_local_ref (env, name);\n        delete_local_ref (env, field);\n      }\n    }\n  }\n  else\n  {\n    jobject elements;\n\n    elements = call_object_method (env, class_handle, java_api.clazz.get_declared_methods);\n    n = get_array_length (env, elements);\n    for (i = 0; i != n; i++)\n    {\n      jobject method, name;\n      const char * name_str;\n      jmethodID id;\n      jint modifiers;\n\n      method = get_object_array_element (env, elements, i);\n      name = call_object_method (env, method, java_api.method.get_name);\n      name_str = get_string_utf_chars (env, name, NULL);\n      id = from_reflected_method (env, method);\n      modifiers = call_int_method (env, method, java_api.method.get_modifiers);\n\n      model_add_method (model, name_str, id, modifiers);\n\n      release_string_utf_chars (env, name, name_str);\n      delete_local_ref (env, name);\n      delete_local_ref (env, method);\n    }\n    delete_local_ref (env, elements);\n\n    elements = call_object_method (env, class_handle, java_api.clazz.get_declared_fields);\n    n = get_array_length (env, elements);\n    for (i = 0; i != n; i++)\n    {\n      jobject field, name;\n      const char * name_str;\n      jfieldID id;\n      jint modifiers;\n\n      field = get_object_array_element (env, elements, i);\n      name = call_object_method (env, field, java_api.field.get_name);\n      name_str = get_string_utf_chars (env, name, NULL);\n      id = from_reflected_field (env, field);\n      modifiers = call_int_method (env, field, java_api.field.get_modifiers);\n\n      model_add_field (model, name_str, id, modifiers);\n\n      release_string_utf_chars (env, name, name_str);\n      delete_local_ref (env, name);\n      delete_local_ref (env, field);\n    }\n    delete_local_ref (env, elements);\n  }\n\n  g_mutex_lock (&lock);\n  g_array_append_val (models, model);\n  g_mutex_unlock (&lock);\n\n  return model;\n}\n\nstatic void\nmodel_add_method (Model * self,\n                  const gchar * name,\n                  jmethodID id,\n                  jint modifiers)\n{\n  GHashTable * members = self->members;\n  gchar * key, type;\n  const gchar * value;\n\n  if (name[0] == '$')\n    key = g_strdup_printf (\"_%s\", name);\n  else\n    key = g_strdup (name);\n\n  type = (modifiers & kAccStatic) != 0 ? 's' : 'i';\n\n  value = g_hash_table_lookup (members, key);\n  if (value == NULL)\n    g_hash_table_insert (members, key, g_strdup_printf (\"m:%c0x%zx\", type, id));\n  else\n    g_hash_table_insert (members, key, g_strdup_printf (\"%s:%c0x%zx\", value, type, id));\n}\n\nstatic void\nmodel_add_field (Model * self,\n                 const gchar * name,\n                 jfieldID id,\n                 jint modifiers)\n{\n  GHashTable * members = self->members;\n  gchar * key, type;\n\n  if (name[0] == '$')\n    key = g_strdup_printf (\"_%s\", name);\n  else\n    key = g_strdup (name);\n  while (g_hash_table_contains (members, key))\n  {\n    gchar * new_key = g_strdup_printf (\"_%s\", key);\n    g_free (key);\n    key = new_key;\n  }\n\n  type = (modifiers & kAccStatic) != 0 ? 's' : 'i';\n\n  g_hash_table_insert (members, key, g_strdup_printf (\"f:%c0x%zx\", type, id));\n}\n\nstatic void\nmodel_free (Model * model)\n{\n  g_hash_table_unref (model->members);\n\n  g_free (model);\n}\n\ngboolean\nmodel_has (Model * self,\n           const gchar * member)\n{\n  return g_hash_table_contains (self->members, member);\n}\n\nconst gchar *\nmodel_find (Model * self,\n            const gchar * member)\n{\n  return g_hash_table_lookup (self->members, member);\n}\n\ngchar *\nmodel_list (Model * self)\n{\n  GString * result;\n  GHashTableIter iter;\n  guint i;\n  const gchar * name;\n\n  result = g_string_sized_new (128);\n\n  g_string_append_c (result, '[');\n\n  g_hash_table_iter_init (&iter, self->members);\n  for (i = 0; g_hash_table_iter_next (&iter, (gpointer *) &name, NULL); i++)\n  {\n    if (i > 0)\n      g_string_append_c (result, ',');\n\n    g_string_append_c (result, '\"');\n    g_string_append (result, name);\n    g_string_append_c (result, '\"');\n  }\n\n  g_string_append_c (result, ']');\n\n  return g_string_free (result, FALSE);\n}\n\ngchar *\nenumerate_methods_art (const gchar * class_query,\n                       const gchar * method_query,\n                       jboolean include_signature,\n                       jboolean ignore_case,\n                       jboolean skip_system_classes)\n{\n  gchar * result;\n  EnumerateMethodsContext ctx;\n  ArtClassVisitor visitor;\n  ArtClassVisitorVTable visitor_vtable = { NULL, };\n\n  ctx.class_query = make_pattern_spec (class_query, ignore_case);\n  ctx.method_query = make_pattern_spec (method_query, ignore_case);\n  ctx.include_signature = include_signature;\n  ctx.ignore_case = ignore_case;\n  ctx.skip_system_classes = skip_system_classes;\n  ctx.groups = g_hash_table_new_full (NULL, NULL, NULL, NULL);\n\n  visitor.vtable = &visitor_vtable;\n  visitor.user_data = &ctx;\n\n  visitor_vtable.visit = collect_matching_class_methods;\n\n  art_api.visit_classes (art_api.linker, &visitor);\n\n  result = finalize_method_groups_to_json (ctx.groups);\n\n  g_hash_table_unref (ctx.groups);\n  g_pattern_spec_free (ctx.method_query);\n  g_pattern_spec_free (ctx.class_query);\n\n  return result;\n}\n\nstatic jboolean\ncollect_matching_class_methods (ArtClassVisitor * self,\n                                ArtClass * klass)\n{\n  EnumerateMethodsContext * ctx = self->user_data;\n  const char * descriptor;\n  StdString descriptor_storage = { 0, };\n  gchar * class_name = NULL;\n  gchar * class_name_copy = NULL;\n  const gchar * normalized_class_name;\n  JsonBuilder * group;\n  size_t class_name_length;\n  GHashTable * seen_method_names;\n  gpointer elements;\n  guint n, i;\n\n  if (ctx->skip_system_classes && klass->class_loader == 0)\n    goto skip_class;\n\n  descriptor = art_api.get_class_descriptor (klass, &descriptor_storage);\n  if (descriptor[0] != 'L')\n    goto skip_class;\n\n  class_name = class_name_from_signature (descriptor);\n\n  if (ctx->ignore_case)\n  {\n    class_name_copy = g_utf8_strdown (class_name, -1);\n    normalized_class_name = class_name_copy;\n  }\n  else\n  {\n    normalized_class_name = class_name;\n  }\n\n  if (!g_pattern_match_string (ctx->class_query, normalized_class_name))\n    goto skip_class;\n\n  group = NULL;\n  class_name_length = strlen (class_name);\n  seen_method_names = ctx->include_signature ? NULL : g_hash_table_new_full (g_str_hash, g_str_equal, g_free, NULL);\n\n  elements = read_art_array (klass, art_api.class_offset_methods, sizeof (gsize), NULL);\n  n = *(guint16 *) ((gpointer) klass + art_api.class_offset_copied_methods_offset);\n  for (i = 0; i != n; i++)\n  {\n    ArtMethod * method;\n    guint32 access_flags;\n    jboolean is_constructor;\n    StdString method_name = { 0, };\n    const gchar * bare_method_name;\n    gchar * bare_method_name_copy = NULL;\n    const gchar * normalized_method_name;\n    gchar * normalized_method_name_copy = NULL;\n\n    method = elements + (i * art_api.method_size);\n\n    access_flags = *(guint32 *) ((gpointer) method + art_api.method_offset_access_flags);\n    is_constructor = (access_flags & kAccConstructor) != 0;\n\n    art_api.pretty_method (&method_name, method, ctx->include_signature);\n    bare_method_name = std_string_c_str (&method_name);\n    if (ctx->include_signature)\n    {\n      const gchar * return_type_end, * name_begin;\n      GString * name;\n\n      return_type_end = strchr (bare_method_name, ' ');\n      name_begin = return_type_end + 1 + class_name_length + 1;\n      if (is_constructor && g_str_has_prefix (name_begin, \"<clinit>\"))\n        goto skip_method;\n\n      name = g_string_sized_new (64);\n\n      if (is_constructor)\n      {\n        g_string_append (name, \"$init\");\n        g_string_append (name, strchr (name_begin, '>') + 1);\n      }\n      else\n      {\n        g_string_append (name, name_begin);\n      }\n      g_string_append (name, \": \");\n      g_string_append_len (name, bare_method_name, return_type_end - bare_method_name);\n\n      bare_method_name_copy = g_string_free (name, FALSE);\n      bare_method_name = bare_method_name_copy;\n    }\n    else\n    {\n      const gchar * name_begin;\n\n      name_begin = bare_method_name + class_name_length + 1;\n      if (is_constructor && strcmp (name_begin, \"<clinit>\") == 0)\n        goto skip_method;\n\n      if (is_constructor)\n        bare_method_name = \"$init\";\n      else\n        bare_method_name += class_name_length + 1;\n    }\n\n    if (seen_method_names != NULL && g_hash_table_contains (seen_method_names, bare_method_name))\n      goto skip_method;\n\n    if (ctx->ignore_case)\n    {\n      normalized_method_name_copy = g_utf8_strdown (bare_method_name, -1);\n      normalized_method_name = normalized_method_name_copy;\n    }\n    else\n    {\n      normalized_method_name = bare_method_name;\n    }\n\n    if (!g_pattern_match_string (ctx->method_query, normalized_method_name))\n      goto skip_method;\n\n    if (group == NULL)\n    {\n      group = g_hash_table_lookup (ctx->groups, GUINT_TO_POINTER (klass->class_loader));\n      if (group == NULL)\n      {\n        group = json_builder_new_immutable ();\n        g_hash_table_insert (ctx->groups, GUINT_TO_POINTER (klass->class_loader), group);\n\n        json_builder_begin_object (group);\n\n        json_builder_set_member_name (group, \"loader\");\n        json_builder_add_int_value (group, klass->class_loader);\n\n        json_builder_set_member_name (group, \"classes\");\n        json_builder_begin_array (group);\n      }\n\n      json_builder_begin_object (group);\n\n      json_builder_set_member_name (group, \"name\");\n      json_builder_add_string_value (group, class_name);\n\n      json_builder_set_member_name (group, \"methods\");\n      json_builder_begin_array (group);\n    }\n\n    json_builder_add_string_value (group, bare_method_name);\n\n    if (seen_method_names != NULL)\n      g_hash_table_add (seen_method_names, g_strdup (bare_method_name));\n\nskip_method:\n    g_free (normalized_method_name_copy);\n    g_free (bare_method_name_copy);\n    std_string_destroy (&method_name);\n  }\n\n  if (seen_method_names != NULL)\n    g_hash_table_unref (seen_method_names);\n\n  if (group == NULL)\n    goto skip_class;\n\n  json_builder_end_array (group);\n  json_builder_end_object (group);\n\nskip_class:\n  g_free (class_name_copy);\n  g_free (class_name);\n  std_string_destroy (&descriptor_storage);\n\n  return TRUE;\n}\n\ngchar *\nenumerate_methods_jvm (const gchar * class_query,\n                       const gchar * method_query,\n                       jboolean include_signature,\n                       jboolean ignore_case,\n                       jboolean skip_system_classes,\n                       JNIEnv * env,\n                       jvmtiEnv * jvmti)\n{\n  gchar * result;\n  GPatternSpec * class_pattern, * method_pattern;\n  GHashTable * groups;\n  gpointer * ef = env->functions;\n  jobject (* new_global_ref) (JNIEnv *, jobject) = ef[21];\n  void (* delete_local_ref) (JNIEnv *, jobject) = ef[23];\n  jboolean (* is_same_object) (JNIEnv *, jobject, jobject) = ef[24];\n  gpointer * jf = jvmti->functions - 1;\n  jvmtiError (* deallocate) (jvmtiEnv *, void * mem) = jf[47];\n  jvmtiError (* get_class_signature) (jvmtiEnv *, jclass, char **, char **) = jf[48];\n  jvmtiError (* get_class_methods) (jvmtiEnv *, jclass, jint *, jmethodID **) = jf[52];\n  jvmtiError (* get_class_loader) (jvmtiEnv *, jclass, jobject *) = jf[57];\n  jvmtiError (* get_method_name) (jvmtiEnv *, jmethodID, char **, char **, char **) = jf[64];\n  jvmtiError (* get_loaded_classes) (jvmtiEnv *, jint *, jclass **) = jf[78];\n  jint class_count, class_index;\n  jclass * classes;\n\n  class_pattern = make_pattern_spec (class_query, ignore_case);\n  method_pattern = make_pattern_spec (method_query, ignore_case);\n  groups = g_hash_table_new_full (NULL, NULL, NULL, NULL);\n\n  if (get_loaded_classes (jvmti, &class_count, &classes) != JVMTI_ERROR_NONE)\n    goto emit_results;\n\n  for (class_index = 0; class_index != class_count; class_index++)\n  {\n    jclass klass = classes[class_index];\n    jobject loader = NULL;\n    gboolean have_loader = FALSE;\n    char * signature = NULL;\n    gchar * class_name = NULL;\n    gchar * class_name_copy = NULL;\n    const gchar * normalized_class_name;\n    jint method_count, method_index;\n    jmethodID * methods = NULL;\n    JsonBuilder * group = NULL;\n    GHashTable * seen_method_names = NULL;\n\n    if (skip_system_classes)\n    {\n      if (get_class_loader (jvmti, klass, &loader) != JVMTI_ERROR_NONE)\n        goto skip_class;\n      have_loader = TRUE;\n\n      if (loader == NULL)\n        goto skip_class;\n    }\n\n    if (get_class_signature (jvmti, klass, &signature, NULL) != JVMTI_ERROR_NONE)\n      goto skip_class;\n\n    class_name = class_name_from_signature (signature);\n\n    if (ignore_case)\n    {\n      class_name_copy = g_utf8_strdown (class_name, -1);\n      normalized_class_name = class_name_copy;\n    }\n    else\n    {\n      normalized_class_name = class_name;\n    }\n\n    if (!g_pattern_match_string (class_pattern, normalized_class_name))\n      goto skip_class;\n\n    if (get_class_methods (jvmti, klass, &method_count, &methods) != JVMTI_ERROR_NONE)\n      goto skip_class;\n\n    if (!include_signature)\n      seen_method_names = g_hash_table_new_full (g_str_hash, g_str_equal, g_free, NULL);\n\n    for (method_index = 0; method_index != method_count; method_index++)\n    {\n      jmethodID method = methods[method_index];\n      const gchar * method_name;\n      char * method_name_value = NULL;\n      char * method_signature_value = NULL;\n      gchar * method_name_copy = NULL;\n      const gchar * normalized_method_name;\n      gchar * normalized_method_name_copy = NULL;\n\n      if (get_method_name (jvmti, method, &method_name_value, include_signature ? &method_signature_value : NULL, NULL) != JVMTI_ERROR_NONE)\n        goto skip_method;\n      method_name = method_name_value;\n\n      if (method_name[0] == '<')\n      {\n        if (strcmp (method_name, \"<init>\") == 0)\n          method_name = \"$init\";\n        else if (strcmp (method_name, \"<clinit>\") == 0)\n          goto skip_method;\n      }\n\n      if (include_signature)\n      {\n        method_name_copy = format_method_signature (method_name, method_signature_value);\n        method_name = method_name_copy;\n      }\n\n      if (seen_method_names != NULL && g_hash_table_contains (seen_method_names, method_name))\n        goto skip_method;\n\n      if (ignore_case)\n      {\n        normalized_method_name_copy = g_utf8_strdown (method_name, -1);\n        normalized_method_name = normalized_method_name_copy;\n      }\n      else\n      {\n        normalized_method_name = method_name;\n      }\n\n      if (!g_pattern_match_string (method_pattern, normalized_method_name))\n        goto skip_method;\n\n      if (group == NULL)\n      {\n        if (!have_loader && get_class_loader (jvmti, klass, &loader) != JVMTI_ERROR_NONE)\n          goto skip_method;\n\n        if (loader == NULL)\n        {\n          group = g_hash_table_lookup (groups, NULL);\n        }\n        else\n        {\n          GHashTableIter iter;\n          jobject cur_loader;\n          JsonBuilder * cur_group;\n\n          g_hash_table_iter_init (&iter, groups);\n          while (g_hash_table_iter_next (&iter, (gpointer *) &cur_loader, (gpointer *) &cur_group))\n          {\n            if (cur_loader != NULL && is_same_object (env, cur_loader, loader))\n            {\n              group = cur_group;\n              break;\n            }\n          }\n        }\n\n        if (group == NULL)\n        {\n          jobject l;\n          gchar * str;\n\n          l = (loader != NULL) ? new_global_ref (env, loader) : NULL;\n\n          group = json_builder_new_immutable ();\n          g_hash_table_insert (groups, l, group);\n\n          json_builder_begin_object (group);\n\n          json_builder_set_member_name (group, \"loader\");\n          str = g_strdup_printf (\"0x%\" G_GSIZE_MODIFIER \"x\", GPOINTER_TO_SIZE (l));\n          json_builder_add_string_value (group, str);\n          g_free (str);\n\n          json_builder_set_member_name (group, \"classes\");\n          json_builder_begin_array (group);\n        }\n\n        json_builder_begin_object (group);\n\n        json_builder_set_member_name (group, \"name\");\n        json_builder_add_string_value (group, class_name);\n\n        json_builder_set_member_name (group, \"methods\");\n        json_builder_begin_array (group);\n      }\n\n      json_builder_add_string_value (group, method_name);\n\n      if (seen_method_names != NULL)\n        g_hash_table_add (seen_method_names, g_strdup (method_name));\n\nskip_method:\n      g_free (normalized_method_name_copy);\n      g_free (method_name_copy);\n      deallocate (jvmti, method_signature_value);\n      deallocate (jvmti, method_name_value);\n    }\n\nskip_class:\n    if (group != NULL)\n    {\n      json_builder_end_array (group);\n      json_builder_end_object (group);\n    }\n\n    if (seen_method_names != NULL)\n      g_hash_table_unref (seen_method_names);\n\n    deallocate (jvmti, methods);\n\n    g_free (class_name_copy);\n    g_free (class_name);\n    deallocate (jvmti, signature);\n\n    if (loader != NULL)\n      delete_local_ref (env, loader);\n\n    delete_local_ref (env, klass);\n  }\n\n  deallocate (jvmti, classes);\n\nemit_results:\n  result = finalize_method_groups_to_json (groups);\n\n  g_hash_table_unref (groups);\n  g_pattern_spec_free (method_pattern);\n  g_pattern_spec_free (class_pattern);\n\n  return result;\n}\n\nstatic gchar *\nfinalize_method_groups_to_json (GHashTable * groups)\n{\n  GString * result;\n  GHashTableIter iter;\n  guint i;\n  JsonBuilder * group;\n\n  result = g_string_sized_new (1024);\n\n  g_string_append_c (result, '[');\n\n  g_hash_table_iter_init (&iter, groups);\n  for (i = 0; g_hash_table_iter_next (&iter, NULL, (gpointer *) &group); i++)\n  {\n    JsonNode * root;\n    gchar * json;\n\n    if (i > 0)\n      g_string_append_c (result, ',');\n\n    json_builder_end_array (group);\n    json_builder_end_object (group);\n\n    root = json_builder_get_root (group);\n    json = json_to_string (root, FALSE);\n    g_string_append (result, json);\n    g_free (json);\n    json_node_unref (root);\n\n    g_object_unref (group);\n  }\n\n  g_string_append_c (result, ']');\n\n  return g_string_free (result, FALSE);\n}\n\nstatic GPatternSpec *\nmake_pattern_spec (const gchar * pattern,\n                   jboolean ignore_case)\n{\n  GPatternSpec * spec;\n\n  if (ignore_case)\n  {\n    gchar * str = g_utf8_strdown (pattern, -1);\n    spec = g_pattern_spec_new (str);\n    g_free (str);\n  }\n  else\n  {\n    spec = g_pattern_spec_new (pattern);\n  }\n\n  return spec;\n}\n\nstatic gchar *\nclass_name_from_signature (const gchar * descriptor)\n{\n  gchar * result, * c;\n\n  result = g_strdup (descriptor + 1);\n\n  for (c = result; *c != '\\0'; c++)\n  {\n    if (*c == '/')\n      *c = '.';\n  }\n\n  c[-1] = '\\0';\n\n  return result;\n}\n\nstatic gchar *\nformat_method_signature (const gchar * name,\n                         const gchar * signature)\n{\n  GString * sig;\n  const gchar * cursor;\n  gint arg_index;\n\n  sig = g_string_sized_new (128);\n\n  g_string_append (sig, name);\n\n  cursor = signature;\n  arg_index = -1;\n  while (TRUE)\n  {\n    const gchar c = *cursor;\n\n    if (c == '(')\n    {\n      g_string_append_c (sig, c);\n      cursor++;\n      arg_index = 0;\n    }\n    else if (c == ')')\n    {\n      g_string_append_c (sig, c);\n      cursor++;\n      break;\n    }\n    else\n    {\n      if (arg_index >= 1)\n        g_string_append (sig, \", \");\n\n      append_type (sig, &cursor);\n\n      if (arg_index != -1)\n        arg_index++;\n    }\n  }\n\n  g_string_append (sig, \": \");\n  append_type (sig, &cursor);\n\n  return g_string_free (sig, FALSE);\n}\n\nstatic void\nappend_type (GString * output,\n             const gchar ** type)\n{\n  const gchar * cursor = *type;\n\n  switch (*cursor)\n  {\n    case 'Z':\n      g_string_append (output, \"boolean\");\n      cursor++;\n      break;\n    case 'B':\n      g_string_append (output, \"byte\");\n      cursor++;\n      break;\n    case 'C':\n      g_string_append (output, \"char\");\n      cursor++;\n      break;\n    case 'S':\n      g_string_append (output, \"short\");\n      cursor++;\n      break;\n    case 'I':\n      g_string_append (output, \"int\");\n      cursor++;\n      break;\n    case 'J':\n      g_string_append (output, \"long\");\n      cursor++;\n      break;\n    case 'F':\n      g_string_append (output, \"float\");\n      cursor++;\n      break;\n    case 'D':\n      g_string_append (output, \"double\");\n      cursor++;\n      break;\n    case 'V':\n      g_string_append (output, \"void\");\n      cursor++;\n      break;\n    case 'L':\n    {\n      gchar ch;\n\n      cursor++;\n      for (; (ch = *cursor) != ';'; cursor++)\n      {\n        g_string_append_c (output, (ch != '/') ? ch : '.');\n      }\n      cursor++;\n\n      break;\n    }\n    case '[':\n      *type = cursor + 1;\n      append_type (output, type);\n      g_string_append (output, \"[]\");\n      return;\n    default:\n      g_string_append (output, \"BUG\");\n      cursor++;\n  }\n\n  *type = cursor;\n}\n\nvoid\ndealloc (gpointer mem)\n{\n  g_free (mem);\n}\n\nstatic gpointer\nread_art_array (gpointer object_base,\n                guint field_offset,\n                guint length_size,\n                guint * length)\n{\n  gpointer result, header;\n  guint n;\n\n  header = GSIZE_TO_POINTER (*(guint64 *) (object_base + field_offset));\n  if (header != NULL)\n  {\n    result = header + length_size;\n    if (length_size == sizeof (guint32))\n      n = *(guint32 *) header;\n    else\n      n = *(guint64 *) header;\n  }\n  else\n  {\n    result = NULL;\n    n = 0;\n  }\n\n  if (length != NULL)\n    *length = n;\n\n  return result;\n}\n\nstatic void\nstd_string_destroy (StdString * str)\n{\n  if ((str->l.capacity & 1) != 0)\n    art_api.free (str->l.data);\n}\n\nstatic gchar *\nstd_string_c_str (StdString * self)\n{\n  if ((self->l.capacity & 1) != 0)\n    return self->l.data;\n\n  return self->s.data;\n}\n",Pn=/(.+)!([^/]+)\/?([isu]+)?/;let xn=null,Tn=null;class Un{static build(e,t){return On(t),Tn(e,t,n=>new Un(xn.new(e,n,t)))}static enumerateMethods(e,t,n){On(n);const r=e.match(Pn);if(null===r)throw new Error("Invalid query; format is: class!method -- see documentation of Java.enumerateMethods(query) for details");const o=Memory.allocUtf8String(r[1]),i=Memory.allocUtf8String(r[2]);let a=!1,s=!1,l=!1;const c=r[3];let d;if(void 0!==c&&(a=-1!==c.indexOf("s"),s=-1!==c.indexOf("i"),l=-1!==c.indexOf("u")),"jvm"===t.flavor){const e=xn.enumerateMethodsJvm(o,i,Dn(a),Dn(s),Dn(l),n,t.jvmti);try{d=JSON.parse(e.readUtf8String()).map(e=>{const t=ptr(e.loader);return e.loader=t.isNull()?null:t,e})}finally{xn.dealloc(e)}}else Ye(n.vm,n,e=>{const n=xn.enumerateMethodsArt(o,i,Dn(a),Dn(s),Dn(l));try{const r=t["art::JavaVMExt::AddGlobalRef"],{vm:o}=t;d=JSON.parse(n.readUtf8String()).map(t=>{const n=t.loader;return t.loader=0!==n?r(o,e,ptr(n)):null,t})}finally{xn.dealloc(n)}});return d}constructor(e){this.handle=e}has(e){return 0!==xn.has(this.handle,Memory.allocUtf8String(e))}find(e){return xn.find(this.handle,Memory.allocUtf8String(e)).readUtf8String()}list(){const e=xn.list(this.handle);try{return JSON.parse(e.readUtf8String())}finally{xn.dealloc(e)}}}function On(e){null===xn&&(xn=function(e){const{pointerSize:t}=Process,n=8,r=t,o=6*t,i=40+5*t,a=n+r+o+i,s=Memory.alloc(a),l=s.add(n),c=l.add(r),{getDeclaredMethods:d,getDeclaredFields:u}=e.javaLangClass(),h=e.javaLangReflectMethod(),p=e.javaLangReflectField();let f=c;[d,u,h.getName,h.getModifiers,p.getName,p.getModifiers].forEach(e=>{f=f.writePointer(e).add(t)});const _=c.add(o),{vm:m}=e,g=Be(m);if(null!==g){const e=g.offset,n=he(m),r=Ge();let o=_;[1,e.ifields,e.methods,e.sfields,e.copiedMethodsOffset,n.size,n.offset.accessFlags,r.size,r.offset.accessFlags,4294967295].forEach(e=>{o=o.writeUInt(e).add(4)});const i=Oe();[i.artClassLinker.address,i["art::ClassLinker::VisitClasses"],i["art::mirror::Class::GetDescriptor"],i["art::ArtMethod::PrettyMethod"],Process.getModuleByName("libc.so").getExportByName("free")].forEach((e,n)=>{void 0===e&&(e=NULL),o=o.writePointer(e).add(t)})}const b=new CModule(An,{lock:s,models:l,java_api:c,art_api:_}),y={exceptions:"propagate"},v={exceptions:"propagate",scheduling:"exclusive"};return{handle:b,mode:null!==g?"full":"basic",new:new NativeFunction(b.model_new,"pointer",["pointer","pointer","pointer"],y),has:new NativeFunction(b.model_has,"bool",["pointer","pointer"],v),find:new NativeFunction(b.model_find,"pointer",["pointer","pointer"],v),list:new NativeFunction(b.model_list,"pointer",["pointer"],v),enumerateMethodsArt:new NativeFunction(b.enumerate_methods_art,"pointer",["pointer","pointer","bool","bool","bool"],y),enumerateMethodsJvm:new NativeFunction(b.enumerate_methods_jvm,"pointer",["pointer","pointer","bool","bool","bool","pointer","pointer"],y),dealloc:new NativeFunction(b.dealloc,"void",["pointer"],v)}}(e),Tn=function(e,t){if("basic"===e.mode)return Fn;const n=Oe()["art::JavaVMExt::DecodeGlobal"];return function(e,r,o){let i;return Ye(t,r,r=>{const a=n(t,r,e);i=o(a)}),i}}(xn,e.vm))}function Fn(e,t,n){return n(NULL)}function Dn(e){return e?1:0}class zn{constructor(e,t){this.items=new Map,this.capacity=e,this.destroy=t}dispose(e){const{items:t,destroy:n}=this;t.forEach(t=>{n(t,e)}),t.clear()}get(e){const{items:t}=this,n=t.get(e);return void 0!==n&&(t.delete(e),t.set(e,n)),n}set(e,t,n){const{items:r}=this,o=r.get(e);if(void 0!==o)r.delete(e),this.destroy(o,n);else if(r.size===this.capacity){const e=r.keys().next().value,t=r.get(e);r.delete(e),this.destroy(t,n)}r.set(e,t)}}const $n=[],Jn=[],Vn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";for(let e=0,t=64;e<t;++e)$n[e]=Vn[e],Jn[Vn.charCodeAt(e)]=e;function Bn(e){const t=function(e){const t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");let n=e.indexOf("=");return-1===n&&(n=t),[n,n===t?0:4-n%4]}(e),n=t[0],r=t[1],o=new Uint8Array(function(e,t,n){return 3*(t+n)/4-n}(0,n,r));let i=0;const a=r>0?n-4:n;let s;for(s=0;s<a;s+=4){const t=Jn[e.charCodeAt(s)]<<18|Jn[e.charCodeAt(s+1)]<<12|Jn[e.charCodeAt(s+2)]<<6|Jn[e.charCodeAt(s+3)];o[i++]=t>>16&255,o[i++]=t>>8&255,o[i++]=255&t}if(2===r){const t=Jn[e.charCodeAt(s)]<<2|Jn[e.charCodeAt(s+1)]>>4;o[i++]=255&t}if(1===r){const t=Jn[e.charCodeAt(s)]<<10|Jn[e.charCodeAt(s+1)]<<4|Jn[e.charCodeAt(s+2)]>>2;o[i++]=t>>8&255,o[i++]=255&t}return o}function Gn(e){return $n[e>>18&63]+$n[e>>12&63]+$n[e>>6&63]+$n[63&e]}function Zn(e,t,n){const r=[];for(let o=t;o<n;o+=3){const t=(e[o]<<16&16711680)+(e[o+1]<<8&65280)+(255&e[o+2]);r.push(Gn(t))}return r.join("")}function Hn(e){const t=e.length,n=t%3,r=[],o=16383;for(let i=0,a=t-n;i<a;i+=o)r.push(Zn(e,i,i+o>a?a:i+o));if(1===n){const n=e[t-1];r.push($n[n>>2]+$n[n<<4&63]+"==")}else if(2===n){const n=(e[t-2]<<8)+e[t-1];r.push($n[n>>10]+$n[n>>4&63]+$n[n<<2&63]+"=")}return r.join("")}
/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */function qn(e,t,n,r,o){let i,a;const s=8*o-r-1,l=(1<<s)-1,c=l>>1;let d=-7,u=n?o-1:0;const h=n?-1:1;let p=e[t+u];for(u+=h,i=p&(1<<-d)-1,p>>=-d,d+=s;d>0;)i=256*i+e[t+u],u+=h,d-=8;for(a=i&(1<<-d)-1,i>>=-d,d+=r;d>0;)a=256*a+e[t+u],u+=h,d-=8;if(0===i)i=1-c;else{if(i===l)return a?NaN:1/0*(p?-1:1);a+=Math.pow(2,r),i-=c}return(p?-1:1)*a*Math.pow(2,i-r)}function Wn(e,t,n,r,o,i){let a,s,l,c=8*i-o-1;const d=(1<<c)-1,u=d>>1,h=23===o?Math.pow(2,-24)-Math.pow(2,-77):0;let p=r?0:i-1;const f=r?1:-1,_=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(s=isNaN(t)?1:0,a=d):(a=Math.floor(Math.log(t)/Math.LN2),t*(l=Math.pow(2,-a))<1&&(a--,l*=2),(t+=a+u>=1?h/l:h*Math.pow(2,1-u))*l>=2&&(a++,l/=2),a+u>=d?(s=0,a=d):a+u>=1?(s=(t*l-1)*Math.pow(2,o),a+=u):(s=t*Math.pow(2,u-1)*Math.pow(2,o),a=0));o>=8;)e[n+p]=255&s,p+=f,s/=256,o-=8;for(a=a<<o|s,c+=o;c>0;)e[n+p]=255&a,p+=f,a/=256,c-=8;e[n+p-f]|=128*_}
/*!
   * The buffer module from node.js, for Frida.
   *
   * <AUTHOR> Aboukhadijeh <https://feross.org>
   * @license  MIT
   */Jn["-".charCodeAt(0)]=62,Jn["_".charCodeAt(0)]=63;const Kn=50,Qn=**********;function Xn(e){if(e>Qn)throw new RangeError('The value "'+e+'" is invalid for option "size"');const t=new Uint8Array(e);return Object.setPrototypeOf(t,Yn.prototype),t}function Yn(e,t,n){if("number"==typeof e){if("string"==typeof t)throw new TypeError('The "string" argument must be of type string. Received type number');return nr(e)}return er(e,t,n)}function er(e,t,n){if("string"==typeof e)return function(e,t){"string"==typeof t&&""!==t||(t="utf8");if(!Yn.isEncoding(t))throw new TypeError("Unknown encoding: "+t);const n=0|ar(e,t);let r=Xn(n);const o=r.write(e,t);o!==n&&(r=r.slice(0,o));return r}(e,t);if(ArrayBuffer.isView(e))return function(e){if(e instanceof Uint8Array){const t=new Uint8Array(e);return or(t.buffer,t.byteOffset,t.byteLength)}return rr(e)}(e);if(null==e)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e);if(e instanceof ArrayBuffer||e&&e.buffer instanceof ArrayBuffer)return or(e,t,n);if(e instanceof SharedArrayBuffer||e&&e.buffer instanceof SharedArrayBuffer)return or(e,t,n);if("number"==typeof e)throw new TypeError('The "value" argument must not be of type number. Received type number');const r=e.valueOf&&e.valueOf();if(null!=r&&r!==e)return Yn.from(r,t,n);const o=function(e){if(Yn.isBuffer(e)){const t=0|ir(e.length),n=Xn(t);return 0===n.length||e.copy(n,0,0,t),n}if(void 0!==e.length)return"number"!=typeof e.length||Number.isNaN(e.length)?Xn(0):rr(e);if("Buffer"===e.type&&Array.isArray(e.data))return rr(e.data)}(e);if(o)return o;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof e[Symbol.toPrimitive])return Yn.from(e[Symbol.toPrimitive]("string"),t,n);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e)}function tr(e){if("number"!=typeof e)throw new TypeError('"size" argument must be of type number');if(e<0)throw new RangeError('The value "'+e+'" is invalid for option "size"')}function nr(e){return tr(e),Xn(e<0?0:0|ir(e))}function rr(e){const t=e.length<0?0:0|ir(e.length),n=Xn(t);for(let r=0;r<t;r+=1)n[r]=255&e[r];return n}function or(e,t,n){if(t<0||e.byteLength<t)throw new RangeError('"offset" is outside of buffer bounds');if(e.byteLength<t+(n||0))throw new RangeError('"length" is outside of buffer bounds');let r;return r=void 0===t&&void 0===n?new Uint8Array(e):void 0===n?new Uint8Array(e,t):new Uint8Array(e,t,n),Object.setPrototypeOf(r,Yn.prototype),r}function ir(e){if(e>=Qn)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+Qn.toString(16)+" bytes");return 0|e}function ar(e,t){if(Yn.isBuffer(e))return e.length;if(ArrayBuffer.isView(e)||e instanceof ArrayBuffer)return e.byteLength;if("string"!=typeof e)throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof e);const n=e.length,r=arguments.length>2&&!0===arguments[2];if(!r&&0===n)return 0;let o=!1;for(;;)switch(t){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":return Or(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return Fr(e).length;default:if(o)return r?-1:Or(e).length;t=(""+t).toLowerCase(),o=!0}}function sr(e,t,n){let r=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===n||n>this.length)&&(n=this.length),n<=0)return"";if((n>>>=0)<=(t>>>=0))return"";for(e||(e="utf8");;)switch(e){case"hex":return wr(this,t,n);case"utf8":case"utf-8":return gr(this,t,n);case"ascii":return yr(this,t,n);case"latin1":case"binary":return vr(this,t,n);case"base64":return mr(this,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return Er(this,t,n);default:if(r)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),r=!0}}function lr(e,t,n){const r=e[t];e[t]=e[n],e[n]=r}function cr(e,t,n,r,o){if(0===e.length)return-1;if("string"==typeof n?(r=n,n=0):n>**********?n=**********:n<-2147483648&&(n=-2147483648),n=+n,Number.isNaN(n)&&(n=o?0:e.length-1),n<0&&(n=e.length+n),n>=e.length){if(o)return-1;n=e.length-1}else if(n<0){if(!o)return-1;n=0}if("string"==typeof t&&(t=Yn.from(t,r)),Yn.isBuffer(t))return 0===t.length?-1:dr(e,t,n,r,o);if("number"==typeof t)return t&=255,"function"==typeof Uint8Array.prototype.indexOf?o?Uint8Array.prototype.indexOf.call(e,t,n):Uint8Array.prototype.lastIndexOf.call(e,t,n):dr(e,[t],n,r,o);throw new TypeError("val must be string, number or Buffer")}function dr(e,t,n,r,o){let i,a=1,s=e.length,l=t.length;if(void 0!==r&&("ucs2"===(r=String(r).toLowerCase())||"ucs-2"===r||"utf16le"===r||"utf-16le"===r)){if(e.length<2||t.length<2)return-1;a=2,s/=2,l/=2,n/=2}function c(e,t){return 1===a?e[t]:e.readUInt16BE(t*a)}if(o){let r=-1;for(i=n;i<s;i++)if(c(e,i)===c(t,-1===r?0:i-r)){if(-1===r&&(r=i),i-r+1===l)return r*a}else-1!==r&&(i-=i-r),r=-1}else for(n+l>s&&(n=s-l),i=n;i>=0;i--){let n=!0;for(let r=0;r<l;r++)if(c(e,i+r)!==c(t,r)){n=!1;break}if(n)return i}return-1}function ur(e,t,n,r){n=Number(n)||0;const o=e.length-n;r?(r=Number(r))>o&&(r=o):r=o;const i=t.length;let a;for(r>i/2&&(r=i/2),a=0;a<r;++a){const r=parseInt(t.substr(2*a,2),16);if(Number.isNaN(r))return a;e[n+a]=r}return a}function hr(e,t,n,r){return Dr(Or(t,e.length-n),e,n,r)}function pr(e,t,n,r){return Dr(function(e){const t=[];for(let n=0;n<e.length;++n)t.push(255&e.charCodeAt(n));return t}(t),e,n,r)}function fr(e,t,n,r){return Dr(Fr(t),e,n,r)}function _r(e,t,n,r){return Dr(function(e,t){let n,r,o;const i=[];for(let a=0;a<e.length&&!((t-=2)<0);++a)n=e.charCodeAt(a),r=n>>8,o=n%256,i.push(o),i.push(r);return i}(t,e.length-n),e,n,r)}function mr(e,t,n){return 0===t&&n===e.length?Hn(e):Hn(e.slice(t,n))}function gr(e,t,n){n=Math.min(e.length,n);const r=[];let o=t;for(;o<n;){const t=e[o];let i=null,a=t>239?4:t>223?3:t>191?2:1;if(o+a<=n){let n,r,s,l;switch(a){case 1:t<128&&(i=t);break;case 2:n=e[o+1],128==(192&n)&&(l=(31&t)<<6|63&n,l>127&&(i=l));break;case 3:n=e[o+1],r=e[o+2],128==(192&n)&&128==(192&r)&&(l=(15&t)<<12|(63&n)<<6|63&r,l>2047&&(l<55296||l>57343)&&(i=l));break;case 4:n=e[o+1],r=e[o+2],s=e[o+3],128==(192&n)&&128==(192&r)&&128==(192&s)&&(l=(15&t)<<18|(63&n)<<12|(63&r)<<6|63&s,l>65535&&l<1114112&&(i=l))}}null===i?(i=65533,a=1):i>65535&&(i-=65536,r.push(i>>>10&1023|55296),i=56320|1023&i),r.push(i),o+=a}return function(e){const t=e.length;if(t<=br)return String.fromCharCode.apply(String,e);let n="",r=0;for(;r<t;)n+=String.fromCharCode.apply(String,e.slice(r,r+=br));return n}(r)}Yn.TYPED_ARRAY_SUPPORT=!0,Object.defineProperty(Yn.prototype,"parent",{enumerable:!0,get:function(){if(Yn.isBuffer(this))return this.buffer}}),Object.defineProperty(Yn.prototype,"offset",{enumerable:!0,get:function(){if(Yn.isBuffer(this))return this.byteOffset}}),Yn.poolSize=8192,Yn.from=function(e,t,n){return er(e,t,n)},Object.setPrototypeOf(Yn.prototype,Uint8Array.prototype),Object.setPrototypeOf(Yn,Uint8Array),Yn.alloc=function(e,t,n){return function(e,t,n){return tr(e),e<=0?Xn(e):void 0!==t?"string"==typeof n?Xn(e).fill(t,n):Xn(e).fill(t):Xn(e)}(e,t,n)},Yn.allocUnsafe=function(e){return nr(e)},Yn.allocUnsafeSlow=function(e){return nr(e)},Yn.isBuffer=function(e){return null!=e&&!0===e._isBuffer&&e!==Yn.prototype},Yn.compare=function(e,t){if(e instanceof Uint8Array&&(e=Yn.from(e,e.offset,e.byteLength)),t instanceof Uint8Array&&(t=Yn.from(t,t.offset,t.byteLength)),!Yn.isBuffer(e)||!Yn.isBuffer(t))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===t)return 0;let n=e.length,r=t.length;for(let o=0,i=Math.min(n,r);o<i;++o)if(e[o]!==t[o]){n=e[o],r=t[o];break}return n<r?-1:r<n?1:0},Yn.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},Yn.concat=function(e,t){if(!Array.isArray(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return Yn.alloc(0);let n;if(void 0===t)for(t=0,n=0;n<e.length;++n)t+=e[n].length;const r=Yn.allocUnsafe(t);let o=0;for(n=0;n<e.length;++n){let t=e[n];if(t instanceof Uint8Array)o+t.length>r.length?(Yn.isBuffer(t)||(t=Yn.from(t.buffer,t.byteOffset,t.byteLength)),t.copy(r,o)):Uint8Array.prototype.set.call(r,t,o);else{if(!Yn.isBuffer(t))throw new TypeError('"list" argument must be an Array of Buffers');t.copy(r,o)}o+=t.length}return r},Yn.byteLength=ar,Yn.prototype._isBuffer=!0,Yn.prototype.swap16=function(){const e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(let t=0;t<e;t+=2)lr(this,t,t+1);return this},Yn.prototype.swap32=function(){const e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(let t=0;t<e;t+=4)lr(this,t,t+3),lr(this,t+1,t+2);return this},Yn.prototype.swap64=function(){const e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(let t=0;t<e;t+=8)lr(this,t,t+7),lr(this,t+1,t+6),lr(this,t+2,t+5),lr(this,t+3,t+4);return this},Yn.prototype.toString=function(){const e=this.length;return 0===e?"":0===arguments.length?gr(this,0,e):sr.apply(this,arguments)},Yn.prototype.toLocaleString=Yn.prototype.toString,Yn.prototype.equals=function(e){if(!Yn.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===Yn.compare(this,e)},Yn.prototype.inspect=function(){let e="";const t=Kn;return e=this.toString("hex",0,t).replace(/(.{2})/g,"$1 ").trim(),this.length>t&&(e+=" ... "),"<Buffer "+e+">"},Yn.prototype[Symbol.for("nodejs.util.inspect.custom")]=Yn.prototype.inspect,Yn.prototype.compare=function(e,t,n,r,o){if(e instanceof Uint8Array&&(e=Yn.from(e,e.offset,e.byteLength)),!Yn.isBuffer(e))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(void 0===t&&(t=0),void 0===n&&(n=e?e.length:0),void 0===r&&(r=0),void 0===o&&(o=this.length),t<0||n>e.length||r<0||o>this.length)throw new RangeError("out of range index");if(r>=o&&t>=n)return 0;if(r>=o)return-1;if(t>=n)return 1;if(this===e)return 0;let i=(o>>>=0)-(r>>>=0),a=(n>>>=0)-(t>>>=0);const s=Math.min(i,a),l=this.slice(r,o),c=e.slice(t,n);for(let e=0;e<s;++e)if(l[e]!==c[e]){i=l[e],a=c[e];break}return i<a?-1:a<i?1:0},Yn.prototype.includes=function(e,t,n){return-1!==this.indexOf(e,t,n)},Yn.prototype.indexOf=function(e,t,n){return cr(this,e,t,n,!0)},Yn.prototype.lastIndexOf=function(e,t,n){return cr(this,e,t,n,!1)},Yn.prototype.write=function(e,t,n,r){if(void 0===t)r="utf8",n=this.length,t=0;else if(void 0===n&&"string"==typeof t)r=t,n=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t>>>=0,isFinite(n)?(n>>>=0,void 0===r&&(r="utf8")):(r=n,n=void 0)}const o=this.length-t;if((void 0===n||n>o)&&(n=o),e.length>0&&(n<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");r||(r="utf8");let i=!1;for(;;)switch(r){case"hex":return ur(this,e,t,n);case"utf8":case"utf-8":return hr(this,e,t,n);case"ascii":case"latin1":case"binary":return pr(this,e,t,n);case"base64":return fr(this,e,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return _r(this,e,t,n);default:if(i)throw new TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),i=!0}},Yn.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};const br=4096;function yr(e,t,n){let r="";n=Math.min(e.length,n);for(let o=t;o<n;++o)r+=String.fromCharCode(127&e[o]);return r}function vr(e,t,n){let r="";n=Math.min(e.length,n);for(let o=t;o<n;++o)r+=String.fromCharCode(e[o]);return r}function wr(e,t,n){const r=e.length;(!t||t<0)&&(t=0),(!n||n<0||n>r)&&(n=r);let o="";for(let r=t;r<n;++r)o+=zr[e[r]];return o}function Er(e,t,n){const r=e.slice(t,n);let o="";for(let e=0;e<r.length-1;e+=2)o+=String.fromCharCode(r[e]+256*r[e+1]);return o}function Sr(e,t,n){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+t>n)throw new RangeError("Trying to access beyond buffer length")}function Nr(e,t,n,r,o,i){if(!Yn.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>o||t<i)throw new RangeError('"value" argument is out of bounds');if(n+r>e.length)throw new RangeError("Index out of range")}function Lr(e,t,n,r,o){Pr(t,r,o,e,n,7);let i=Number(t&BigInt(4294967295));e[n++]=i,i>>=8,e[n++]=i,i>>=8,e[n++]=i,i>>=8,e[n++]=i;let a=Number(t>>BigInt(32)&BigInt(4294967295));return e[n++]=a,a>>=8,e[n++]=a,a>>=8,e[n++]=a,a>>=8,e[n++]=a,n}function Cr(e,t,n,r,o){Pr(t,r,o,e,n,7);let i=Number(t&BigInt(4294967295));e[n+7]=i,i>>=8,e[n+6]=i,i>>=8,e[n+5]=i,i>>=8,e[n+4]=i;let a=Number(t>>BigInt(32)&BigInt(4294967295));return e[n+3]=a,a>>=8,e[n+2]=a,a>>=8,e[n+1]=a,a>>=8,e[n]=a,n+8}function kr(e,t,n,r,o,i){if(n+r>e.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function Mr(e,t,n,r,o){return t=+t,n>>>=0,o||kr(e,0,n,4),Wn(e,t,n,r,23,4),n+4}function jr(e,t,n,r,o){return t=+t,n>>>=0,o||kr(e,0,n,8),Wn(e,t,n,r,52,8),n+8}Yn.prototype.slice=function(e,t){const n=this.length;(e=~~e)<0?(e+=n)<0&&(e=0):e>n&&(e=n),(t=void 0===t?n:~~t)<0?(t+=n)<0&&(t=0):t>n&&(t=n),t<e&&(t=e);const r=this.subarray(e,t);return Object.setPrototypeOf(r,Yn.prototype),r},Yn.prototype.readUintLE=Yn.prototype.readUIntLE=function(e,t,n){e>>>=0,t>>>=0,n||Sr(e,t,this.length);let r=this[e],o=1,i=0;for(;++i<t&&(o*=256);)r+=this[e+i]*o;return r},Yn.prototype.readUintBE=Yn.prototype.readUIntBE=function(e,t,n){e>>>=0,t>>>=0,n||Sr(e,t,this.length);let r=this[e+--t],o=1;for(;t>0&&(o*=256);)r+=this[e+--t]*o;return r},Yn.prototype.readUint8=Yn.prototype.readUInt8=function(e,t){return e>>>=0,t||Sr(e,1,this.length),this[e]},Yn.prototype.readUint16LE=Yn.prototype.readUInt16LE=function(e,t){return e>>>=0,t||Sr(e,2,this.length),this[e]|this[e+1]<<8},Yn.prototype.readUint16BE=Yn.prototype.readUInt16BE=function(e,t){return e>>>=0,t||Sr(e,2,this.length),this[e]<<8|this[e+1]},Yn.prototype.readUint32LE=Yn.prototype.readUInt32LE=function(e,t){return e>>>=0,t||Sr(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},Yn.prototype.readUint32BE=Yn.prototype.readUInt32BE=function(e,t){return e>>>=0,t||Sr(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},Yn.prototype.readBigUInt64LE=function(e){xr(e>>>=0,"offset");const t=this[e],n=this[e+7];void 0!==t&&void 0!==n||Tr(e,this.length-8);const r=t+256*this[++e]+65536*this[++e]+this[++e]*2**24,o=this[++e]+256*this[++e]+65536*this[++e]+n*2**24;return BigInt(r)+(BigInt(o)<<BigInt(32))},Yn.prototype.readBigUInt64BE=function(e){xr(e>>>=0,"offset");const t=this[e],n=this[e+7];void 0!==t&&void 0!==n||Tr(e,this.length-8);const r=t*2**24+65536*this[++e]+256*this[++e]+this[++e],o=this[++e]*2**24+65536*this[++e]+256*this[++e]+n;return(BigInt(r)<<BigInt(32))+BigInt(o)},Yn.prototype.readIntLE=function(e,t,n){e>>>=0,t>>>=0,n||Sr(e,t,this.length);let r=this[e],o=1,i=0;for(;++i<t&&(o*=256);)r+=this[e+i]*o;return o*=128,r>=o&&(r-=Math.pow(2,8*t)),r},Yn.prototype.readIntBE=function(e,t,n){e>>>=0,t>>>=0,n||Sr(e,t,this.length);let r=t,o=1,i=this[e+--r];for(;r>0&&(o*=256);)i+=this[e+--r]*o;return o*=128,i>=o&&(i-=Math.pow(2,8*t)),i},Yn.prototype.readInt8=function(e,t){return e>>>=0,t||Sr(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},Yn.prototype.readInt16LE=function(e,t){e>>>=0,t||Sr(e,2,this.length);const n=this[e]|this[e+1]<<8;return 32768&n?4294901760|n:n},Yn.prototype.readInt16BE=function(e,t){e>>>=0,t||Sr(e,2,this.length);const n=this[e+1]|this[e]<<8;return 32768&n?4294901760|n:n},Yn.prototype.readInt32LE=function(e,t){return e>>>=0,t||Sr(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},Yn.prototype.readInt32BE=function(e,t){return e>>>=0,t||Sr(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},Yn.prototype.readBigInt64LE=function(e){xr(e>>>=0,"offset");const t=this[e],n=this[e+7];void 0!==t&&void 0!==n||Tr(e,this.length-8);const r=this[e+4]+256*this[e+5]+65536*this[e+6]+(n<<24);return(BigInt(r)<<BigInt(32))+BigInt(t+256*this[++e]+65536*this[++e]+this[++e]*2**24)},Yn.prototype.readBigInt64BE=function(e){xr(e>>>=0,"offset");const t=this[e],n=this[e+7];void 0!==t&&void 0!==n||Tr(e,this.length-8);const r=(t<<24)+65536*this[++e]+256*this[++e]+this[++e];return(BigInt(r)<<BigInt(32))+BigInt(this[++e]*2**24+65536*this[++e]+256*this[++e]+n)},Yn.prototype.readFloatLE=function(e,t){return e>>>=0,t||Sr(e,4,this.length),qn(this,e,!0,23,4)},Yn.prototype.readFloatBE=function(e,t){return e>>>=0,t||Sr(e,4,this.length),qn(this,e,!1,23,4)},Yn.prototype.readDoubleLE=function(e,t){return e>>>=0,t||Sr(e,8,this.length),qn(this,e,!0,52,8)},Yn.prototype.readDoubleBE=function(e,t){return e>>>=0,t||Sr(e,8,this.length),qn(this,e,!1,52,8)},Yn.prototype.writeUintLE=Yn.prototype.writeUIntLE=function(e,t,n,r){if(e=+e,t>>>=0,n>>>=0,!r){Nr(this,e,t,n,Math.pow(2,8*n)-1,0)}let o=1,i=0;for(this[t]=255&e;++i<n&&(o*=256);)this[t+i]=e/o&255;return t+n},Yn.prototype.writeUintBE=Yn.prototype.writeUIntBE=function(e,t,n,r){if(e=+e,t>>>=0,n>>>=0,!r){Nr(this,e,t,n,Math.pow(2,8*n)-1,0)}let o=n-1,i=1;for(this[t+o]=255&e;--o>=0&&(i*=256);)this[t+o]=e/i&255;return t+n},Yn.prototype.writeUint8=Yn.prototype.writeUInt8=function(e,t,n){return e=+e,t>>>=0,n||Nr(this,e,t,1,255,0),this[t]=255&e,t+1},Yn.prototype.writeUint16LE=Yn.prototype.writeUInt16LE=function(e,t,n){return e=+e,t>>>=0,n||Nr(this,e,t,2,65535,0),this[t]=255&e,this[t+1]=e>>>8,t+2},Yn.prototype.writeUint16BE=Yn.prototype.writeUInt16BE=function(e,t,n){return e=+e,t>>>=0,n||Nr(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=255&e,t+2},Yn.prototype.writeUint32LE=Yn.prototype.writeUInt32LE=function(e,t,n){return e=+e,t>>>=0,n||Nr(this,e,t,4,4294967295,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e,t+4},Yn.prototype.writeUint32BE=Yn.prototype.writeUInt32BE=function(e,t,n){return e=+e,t>>>=0,n||Nr(this,e,t,4,4294967295,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},Yn.prototype.writeBigUInt64LE=function(e,t=0){return Lr(this,e,t,BigInt(0),BigInt("0xffffffffffffffff"))},Yn.prototype.writeBigUInt64BE=function(e,t=0){return Cr(this,e,t,BigInt(0),BigInt("0xffffffffffffffff"))},Yn.prototype.writeIntLE=function(e,t,n,r){if(e=+e,t>>>=0,!r){const r=Math.pow(2,8*n-1);Nr(this,e,t,n,r-1,-r)}let o=0,i=1,a=0;for(this[t]=255&e;++o<n&&(i*=256);)e<0&&0===a&&0!==this[t+o-1]&&(a=1),this[t+o]=(e/i|0)-a&255;return t+n},Yn.prototype.writeIntBE=function(e,t,n,r){if(e=+e,t>>>=0,!r){const r=Math.pow(2,8*n-1);Nr(this,e,t,n,r-1,-r)}let o=n-1,i=1,a=0;for(this[t+o]=255&e;--o>=0&&(i*=256);)e<0&&0===a&&0!==this[t+o+1]&&(a=1),this[t+o]=(e/i|0)-a&255;return t+n},Yn.prototype.writeInt8=function(e,t,n){return e=+e,t>>>=0,n||Nr(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=255&e,t+1},Yn.prototype.writeInt16LE=function(e,t,n){return e=+e,t>>>=0,n||Nr(this,e,t,2,32767,-32768),this[t]=255&e,this[t+1]=e>>>8,t+2},Yn.prototype.writeInt16BE=function(e,t,n){return e=+e,t>>>=0,n||Nr(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=255&e,t+2},Yn.prototype.writeInt32LE=function(e,t,n){return e=+e,t>>>=0,n||Nr(this,e,t,4,**********,-2147483648),this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},Yn.prototype.writeInt32BE=function(e,t,n){return e=+e,t>>>=0,n||Nr(this,e,t,4,**********,-2147483648),e<0&&(e=4294967295+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},Yn.prototype.writeBigInt64LE=function(e,t=0){return Lr(this,e,t,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))},Yn.prototype.writeBigInt64BE=function(e,t=0){return Cr(this,e,t,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))},Yn.prototype.writeFloatLE=function(e,t,n){return Mr(this,e,t,!0,n)},Yn.prototype.writeFloatBE=function(e,t,n){return Mr(this,e,t,!1,n)},Yn.prototype.writeDoubleLE=function(e,t,n){return jr(this,e,t,!0,n)},Yn.prototype.writeDoubleBE=function(e,t,n){return jr(this,e,t,!1,n)},Yn.prototype.copy=function(e,t,n,r){if(!Yn.isBuffer(e))throw new TypeError("argument should be a Buffer");if(n||(n=0),r||0===r||(r=this.length),t>=e.length&&(t=e.length),t||(t=0),r>0&&r<n&&(r=n),r===n)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),e.length-t<r-n&&(r=e.length-t+n);const o=r-n;return this===e?this.copyWithin(t,n,r):Uint8Array.prototype.set.call(e,this.subarray(n,r),t),o},Yn.prototype.fill=function(e,t,n,r){if("string"==typeof e){if("string"==typeof t?(r=t,t=0,n=this.length):"string"==typeof n&&(r=n,n=this.length),void 0!==r&&"string"!=typeof r)throw new TypeError("encoding must be a string");if("string"==typeof r&&!Yn.isEncoding(r))throw new TypeError("Unknown encoding: "+r);if(1===e.length){const t=e.charCodeAt(0);("utf8"===r&&t<128||"latin1"===r)&&(e=t)}}else"number"==typeof e?e&=255:"boolean"==typeof e&&(e=Number(e));if(t<0||this.length<t||this.length<n)throw new RangeError("Out of range index");if(n<=t)return this;let o;if(t>>>=0,n=void 0===n?this.length:n>>>0,e||(e=0),"number"==typeof e)for(o=t;o<n;++o)this[o]=e;else{const i=Yn.isBuffer(e)?e:Yn.from(e,r),a=i.length;if(0===a)throw new TypeError('The value "'+e+'" is invalid for argument "value"');for(o=0;o<n-t;++o)this[o+t]=i[o%a]}return this};const Ir={};function Rr(e,t,n){Ir[e]=class extends n{constructor(){super(),Object.defineProperty(this,"message",{value:t.apply(this,arguments),writable:!0,configurable:!0}),this.name=`${this.name} [${e}]`,this.stack,delete this.name}get code(){return e}set code(e){Object.defineProperty(this,"code",{configurable:!0,enumerable:!0,value:e,writable:!0})}toString(){return`${this.name} [${e}]: ${this.message}`}}}function Ar(e){let t="",n=e.length;const r="-"===e[0]?1:0;for(;n>=r+4;n-=3)t=`_${e.slice(n-3,n)}${t}`;return`${e.slice(0,n)}${t}`}function Pr(e,t,n,r,o,i){if(e>n||e<t){const n="bigint"==typeof t?"n":"";let r;throw r=0===t||t===BigInt(0)?`>= 0${n} and < 2${n} ** ${8*(i+1)}${n}`:`>= -(2${n} ** ${8*(i+1)-1}${n}) and < 2 ** ${8*(i+1)-1}${n}`,new Ir.ERR_OUT_OF_RANGE("value",r,e)}!function(e,t,n){xr(t,"offset"),void 0!==e[t]&&void 0!==e[t+n]||Tr(t,e.length-(n+1))}(r,o,i)}function xr(e,t){if("number"!=typeof e)throw new Ir.ERR_INVALID_ARG_TYPE(t,"number",e)}function Tr(e,t,n){if(Math.floor(e)!==e)throw xr(e,n),new Ir.ERR_OUT_OF_RANGE("offset","an integer",e);if(t<0)throw new Ir.ERR_BUFFER_OUT_OF_BOUNDS;throw new Ir.ERR_OUT_OF_RANGE("offset",`>= 0 and <= ${t}`,e)}Rr("ERR_BUFFER_OUT_OF_BOUNDS",function(e){return e?`${e} is outside of buffer bounds`:"Attempt to access memory outside buffer bounds"},RangeError),Rr("ERR_INVALID_ARG_TYPE",function(e,t){return`The "${e}" argument must be of type number. Received type ${typeof t}`},TypeError),Rr("ERR_OUT_OF_RANGE",function(e,t,n){let r=`The value of "${e}" is out of range.`,o=n;return Number.isInteger(n)&&Math.abs(n)>2**32?o=Ar(String(n)):"bigint"==typeof n&&(o=String(n),(n>BigInt(2)**BigInt(32)||n<-(BigInt(2)**BigInt(32)))&&(o=Ar(o)),o+="n"),r+=` It must be ${t}. Received ${o}`,r},RangeError);const Ur=/[^+/0-9A-Za-z-_]/g;function Or(e,t){let n;t=t||1/0;const r=e.length;let o=null;const i=[];for(let a=0;a<r;++a){if(n=e.charCodeAt(a),n>55295&&n<57344){if(!o){if(n>56319){(t-=3)>-1&&i.push(239,191,189);continue}if(a+1===r){(t-=3)>-1&&i.push(239,191,189);continue}o=n;continue}if(n<56320){(t-=3)>-1&&i.push(239,191,189),o=n;continue}n=65536+(o-55296<<10|n-56320)}else o&&(t-=3)>-1&&i.push(239,191,189);if(o=null,n<128){if((t-=1)<0)break;i.push(n)}else if(n<2048){if((t-=2)<0)break;i.push(n>>6|192,63&n|128)}else if(n<65536){if((t-=3)<0)break;i.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(n<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;i.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return i}function Fr(e){return Bn(function(e){if((e=(e=e.split("=")[0]).trim().replace(Ur,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function Dr(e,t,n,r){let o;for(o=0;o<r&&!(o+n>=t.length||o>=e.length);++o)t[o+n]=e[o];return o}const zr=function(){const e="0123456789abcdef",t=new Array(256);for(let n=0;n<16;++n){const r=16*n;for(let o=0;o<16;++o)t[r+o]=e[n]+e[o]}return t}(),$r=256,Jr=2,Vr=Yn.from([3,0,7,14,0]),Br="Ldalvik/annotation/Throws;",Gr=Yn.from([0]);class Zr{constructor(){this.classes=[]}addClass(e){this.classes.push(e)}build(){const e=function(e){const t=new Set,n=new Set,r={},o=[],i=[],a={},s=new Set,l=new Set;function c(e,o){const i=[e].concat(o),a=i.join("|");if(void 0!==r[a])return a;t.add(e),n.add(e),o.forEach(e=>{t.add(e),n.add(e)});const s=i.map(Qr).join("");return t.add(s),r[a]=[a,s,e,o],a}e.forEach(e=>{const{name:r,superClass:d,sourceFileName:u}=e;t.add("this"),t.add(r),n.add(r),t.add(d),n.add(d),t.add(u),e.interfaces.forEach(e=>{t.add(e),n.add(e)}),e.fields.forEach(r=>{const[i,a]=r;t.add(i),t.add(a),n.add(a),o.push([e.name,a,i])}),e.methods.some(([e])=>"<init>"===e)||(e.methods.unshift(["<init>","V",[]]),s.add(r)),e.methods.forEach(o=>{const[u,h,p,f=[],_]=o;t.add(u);const m=c(h,p);let g=null;if(f.length>0){const e=f.slice();e.sort(),g=e.join("|");let r=a[g];void 0===r&&(r={id:g,types:e},a[g]=r),t.add(Br),n.add(Br),f.forEach(e=>{t.add(e),n.add(e)}),t.add("value")}if(i.push([e.name,m,u,g,_]),"<init>"===u){l.add(r+"|"+m);const e=d+"|"+m;s.has(r)&&!l.has(e)&&(i.push([d,m,u,null,0]),l.add(e))}})});const d=Array.from(t);d.sort();const u=d.reduce((e,t,n)=>(e[t]=n,e),{}),h=Array.from(n).map(e=>u[e]);h.sort(Hr);const p=h.reduce((e,t,n)=>(e[d[t]]=n,e),{}),f=Object.keys(r).map(e=>r[e]);f.sort(qr);const _={},m=f.map(e=>{const[,t,n,r]=e;let o;if(r.length>0){const e=r.join("|");o=_[e],void 0===o&&(o={types:r.map(e=>p[e]),offset:-1},_[e]=o)}else o=null;return[u[t],p[n],o]}),g=f.reduce((e,t,n)=>{const[r]=t;return e[r]=n,e},{}),b=Object.keys(_).map(e=>_[e]),y=o.map(e=>{const[t,n,r]=e;return[p[t],p[n],u[r]]});y.sort(Wr);const v=i.map(e=>{const[t,n,r,o,i]=e;return[p[t],g[n],u[r],o,i]});v.sort(Kr);const w=Object.keys(a).map(e=>a[e]).map(e=>({id:e.id,type:p[Br],value:u.value,thrownTypes:e.types.map(e=>p[e]),offset:-1})),E=w.map(e=>({id:e.id,items:[e],offset:-1})),S=E.reduce((e,t,n)=>(e[t.id]=n,e),{}),N={},L=[],C=e.map(e=>{const t=p[e.name],n=1,r=p[e.superClass];let o;const i=e.interfaces.map(e=>p[e]);if(i.length>0){i.sort(Hr);const e=i.join("|");o=N[e],void 0===o&&(o={types:i,offset:-1},N[e]=o)}else o=null;const a=u[e.sourceFileName],l=v.reduce((e,n,r)=>{const[o,i,a,s,l]=n;return o===t&&e.push([r,a,s,i,l]),e},[]);let c=null;const d=l.filter(([,,e])=>null!==e).map(([e,,t])=>[e,E[S[t]]]);d.length>0&&(c={methods:d,offset:-1},L.push(c));const h=y.reduce((e,n,r)=>{const[o]=n;return o===t&&e.push([r>0?1:0,1]),e},[]),f=u["<init>"],_=l.filter(([,e])=>e===f).map(([t,,,n])=>{if(s.has(e.name)){let e=-1;const o=v.length;for(let t=0;t!==o;t++){const[o,i,a]=v[t];if(o===r&&a===f&&i===n){e=t;break}}return[t,65537,e]}return[t,65793,-1]}),m=function(e){let t=0;return e.map(([e,n],r)=>{let o;return o=0===r?[e,n]:[e-t,n],t=e,o})}(l.filter(([,e])=>e!==f).map(([e,,,,t])=>[e,1|t|$r]));return{index:t,accessFlags:n,superClassIndex:r,interfaces:o,sourceFileIndex:a,annotationsDirectory:c,classData:{instanceFields:h,constructorMethods:_,virtualMethods:m,offset:-1}}}),k=Object.keys(N).map(e=>N[e]);return{classes:C,interfaces:k,fields:y,methods:v,protos:m,parameters:b,annotationDirectories:L,annotationSets:E,throwsAnnotations:w,types:h,strings:d}}(this.classes),{classes:t,interfaces:n,fields:r,methods:o,protos:i,parameters:a,annotationDirectories:s,annotationSets:l,throwsAnnotations:c,types:d,strings:u}=e;let h=0;h+=112;const p=h,f=4*u.length;h+=f;const _=h,m=4*d.length;h+=m;const g=h,b=12*i.length;h+=b;const y=h,v=8*r.length;h+=v;const w=h,E=8*o.length;h+=E;const S=h,N=32*t.length;h+=N;const L=h,C=l.map(e=>{const t=h;return e.offset=t,h+=4+4*e.items.length,t}),k=t.reduce((e,t)=>(t.classData.constructorMethods.forEach(t=>{const[,n,r]=t;0===(n&$r)&&r>=0&&(t.push(h),e.push({offset:h,superConstructor:r}),h+=24)}),e),[]);s.forEach(e=>{e.offset=h,h+=16+8*e.methods.length});const M=n.map(e=>{h=Yr(h,4);const t=h;return e.offset=t,h+=4+2*e.types.length,t}),j=a.map(e=>{h=Yr(h,4);const t=h;return e.offset=t,h+=4+2*e.types.length,t}),I=[],R=u.map(e=>{const t=h,n=Yn.from(Xr(e.length)),r=Yn.from(e,"utf8"),o=Yn.concat([n,r,Gr]);return I.push(o),h+=o.length,t}),A=k.map(e=>{const t=h;return h+=Vr.length,t}),P=c.map(e=>{const t=function(e){const{thrownTypes:t}=e;return Yn.from([Jr].concat(Xr(e.type)).concat([1]).concat(Xr(e.value)).concat([28,t.length]).concat(t.reduce((e,t)=>(e.push(24,t),e),[])))}(e);return e.offset=h,h+=t.length,t}),x=t.map((e,t)=>{e.classData.offset=h;const n=function(e){const{instanceFields:t,constructorMethods:n,virtualMethods:r}=e.classData,o=0;return Yn.from([o].concat(Xr(t.length)).concat(Xr(n.length)).concat(Xr(r.length)).concat(t.reduce((e,[t,n])=>e.concat(Xr(t)).concat(Xr(n)),[])).concat(n.reduce((e,[t,n,,r])=>e.concat(Xr(t)).concat(Xr(n)).concat(Xr(r||0)),[])).concat(r.reduce((e,[t,n])=>{const r=0;return e.concat(Xr(t)).concat(Xr(n)).concat([r])},[])))}(e);return h+=n.length,n});h=Yr(h,4);const T=h,U=n.length+a.length,O=4+(r.length>0?1:0)+2+l.length+k.length+s.length+(U>0?1:0)+1+A.length+c.length+t.length+1;h+=4+12*O;const F=h-L,D=h,z=Yn.alloc(D);z.write("dex\n035"),z.writeUInt32LE(D,32),z.writeUInt32LE(112,36),z.writeUInt32LE(305419896,40),z.writeUInt32LE(0,44),z.writeUInt32LE(0,48),z.writeUInt32LE(T,52),z.writeUInt32LE(u.length,56),z.writeUInt32LE(p,60),z.writeUInt32LE(d.length,64),z.writeUInt32LE(_,68),z.writeUInt32LE(i.length,72),z.writeUInt32LE(g,76),z.writeUInt32LE(r.length,80),z.writeUInt32LE(r.length>0?y:0,84),z.writeUInt32LE(o.length,88),z.writeUInt32LE(w,92),z.writeUInt32LE(t.length,96),z.writeUInt32LE(S,100),z.writeUInt32LE(F,104),z.writeUInt32LE(L,108),R.forEach((e,t)=>{z.writeUInt32LE(e,p+4*t)}),d.forEach((e,t)=>{z.writeUInt32LE(e,_+4*t)}),i.forEach((e,t)=>{const[n,r,o]=e,i=g+12*t;z.writeUInt32LE(n,i),z.writeUInt32LE(r,i+4),z.writeUInt32LE(null!==o?o.offset:0,i+8)}),r.forEach((e,t)=>{const[n,r,o]=e,i=y+8*t;z.writeUInt16LE(n,i),z.writeUInt16LE(r,i+2),z.writeUInt32LE(o,i+4)}),o.forEach((e,t)=>{const[n,r,o]=e,i=w+8*t;z.writeUInt16LE(n,i),z.writeUInt16LE(r,i+2),z.writeUInt32LE(o,i+4)}),t.forEach((e,t)=>{const{interfaces:n,annotationsDirectory:r}=e,o=null!==n?n.offset:0,i=null!==r?r.offset:0,a=S+32*t;z.writeUInt32LE(e.index,a),z.writeUInt32LE(e.accessFlags,a+4),z.writeUInt32LE(e.superClassIndex,a+8),z.writeUInt32LE(o,a+12),z.writeUInt32LE(e.sourceFileIndex,a+16),z.writeUInt32LE(i,a+20),z.writeUInt32LE(e.classData.offset,a+24),z.writeUInt32LE(0,a+28)}),l.forEach((e,t)=>{const{items:n}=e,r=C[t];z.writeUInt32LE(n.length,r),n.forEach((e,t)=>{z.writeUInt32LE(e.offset,r+4+4*t)})}),k.forEach((e,t)=>{const{offset:n,superConstructor:r}=e;z.writeUInt16LE(1,n),z.writeUInt16LE(1,n+2),z.writeUInt16LE(1,n+4),z.writeUInt16LE(0,n+6),z.writeUInt32LE(A[t],n+8),z.writeUInt32LE(4,n+12),z.writeUInt16LE(4208,n+16),z.writeUInt16LE(r,n+18),z.writeUInt16LE(0,n+20),z.writeUInt16LE(14,n+22)}),s.forEach(e=>{const t=e.offset,n=e.methods.length;z.writeUInt32LE(0,t),z.writeUInt32LE(0,t+4),z.writeUInt32LE(n,t+8),z.writeUInt32LE(0,t+12),e.methods.forEach((e,n)=>{const r=t+16+8*n,[o,i]=e;z.writeUInt32LE(o,r),z.writeUInt32LE(i.offset,r+4)})}),n.forEach((e,t)=>{const n=M[t];z.writeUInt32LE(e.types.length,n),e.types.forEach((e,t)=>{z.writeUInt16LE(e,n+4+2*t)})}),a.forEach((e,t)=>{const n=j[t];z.writeUInt32LE(e.types.length,n),e.types.forEach((e,t)=>{z.writeUInt16LE(e,n+4+2*t)})}),I.forEach((e,t)=>{e.copy(z,R[t])}),A.forEach(e=>{Vr.copy(z,e)}),P.forEach((e,t)=>{e.copy(z,c[t].offset)}),x.forEach((e,n)=>{e.copy(z,t[n].classData.offset)}),z.writeUInt32LE(O,T);const $=[[0,1,0],[1,u.length,p],[2,d.length,_],[3,i.length,g]];r.length>0&&$.push([4,r.length,y]),$.push([5,o.length,w]),$.push([6,t.length,S]),l.forEach((e,t)=>{$.push([4099,e.items.length,C[t]])}),k.forEach(e=>{$.push([8193,1,e.offset])}),s.forEach(e=>{$.push([8198,1,e.offset])}),U>0&&$.push([4097,U,M.concat(j)[0]]),$.push([8194,u.length,R[0]]),A.forEach(e=>{$.push([8195,1,e])}),c.forEach(e=>{$.push([8196,1,e.offset])}),t.forEach(e=>{$.push([8192,1,e.classData.offset])}),$.push([4096,1,T]),$.forEach((e,t)=>{const[n,r,o]=e,i=T+4+12*t;z.writeUInt16LE(n,i),z.writeUInt32LE(r,i+4),z.writeUInt32LE(o,i+8)});const J=new Checksum("sha1");return J.update(z.slice(32)),Yn.from(J.getDigest()).copy(z,12),z.writeUInt32LE(function(e,t){let n=1,r=0;const o=e.length;for(let i=t;i<o;i++)n=(n+e[i])%65521,r=(r+n)%65521;return(r<<16|n)>>>0}(z,12),8),z}}function Hr(e,t){return e-t}function qr(e,t){const[,,n,r]=e,[,,o,i]=t;if(n<o)return-1;if(n>o)return 1;const a=r.join("|"),s=i.join("|");return a<s?-1:a>s?1:0}function Wr(e,t){const[n,r,o]=e,[i,a,s]=t;return n!==i?n-i:o!==s?o-s:r-a}function Kr(e,t){const[n,r,o]=e,[i,a,s]=t;return n!==i?n-i:o!==s?o-s:r-a}function Qr(e){const t=e[0];return"L"===t||"["===t?"L":e}function Xr(e){if(e<=127)return[e];const t=[];let n=!1;do{let r=127&e;n=0!==(e>>=7),n&&(r|=128),t.push(r)}while(n);return t}function Yr(e,t){const n=e%t;return 0===n?e:e+t-n}let eo=null,to=null;function no(e,t,n){let r=io(e);return null===r&&(0===e.indexOf("[")?r=so(e,t,n):("L"===e[0]&&";"===e[e.length-1]&&(e=e.substring(1,e.length-1)),r=function(e,t,n){const r=n._types[t?1:0];let o=r[e];if(void 0!==o)return o;o="java.lang.Object"===e?function(e){return{name:"Ljava/lang/Object;",type:"pointer",size:1,defaultValue:NULL,isCompatible(e){if(null===e)return!0;if(void 0===e)return!1;return!!(e.$h instanceof NativePointer)||"string"==typeof e},fromJni:(t,n,r)=>t.isNull()?null:e.cast(t,e.use("java.lang.Object"),r),toJni:(e,t)=>null===e?NULL:"string"==typeof e?t.newStringUtf(e):e.$h}}(n):function(e,t,n){let r=null,o=null,i=null;function a(){return null===r&&(r=n.use(e).class),r}function s(e){const t=a();return null===o&&(o=t.isInstance.overload("java.lang.Object")),o.call(t,e)}function l(){if(null===i){const e=a();i=n.use("java.lang.String").class.isAssignableFrom(e)}return i}return{name:uo(e),type:"pointer",size:1,defaultValue:NULL,isCompatible(e){if(null===e)return!0;if(void 0===e)return!1;return e.$h instanceof NativePointer?s(e):"string"==typeof e&&l()},fromJni:(r,o,i)=>r.isNull()?null:l()&&t?o.stringFromJni(r):n.cast(r,n.use(e),i),toJni:(e,t)=>null===e?NULL:"string"==typeof e?t.newStringUtf(e):e.$h,toString(){return this.name}}}(e,t,n);return r[e]=o,o}(e,t,n))),Object.assign({className:e},r)}const ro={boolean:{name:"Z",type:"uint8",size:1,byteSize:1,defaultValue:!1,isCompatible:e=>"boolean"==typeof e,fromJni:e=>!!e,toJni:e=>e?1:0,read:e=>e.readU8(),write(e,t){e.writeU8(t)},toString(){return this.name}},byte:{name:"B",type:"int8",size:1,byteSize:1,defaultValue:0,isCompatible:e=>Number.isInteger(e)&&e>=-128&&e<=127,fromJni:ho,toJni:ho,read:e=>e.readS8(),write(e,t){e.writeS8(t)},toString(){return this.name}},char:{name:"C",type:"uint16",size:1,byteSize:2,defaultValue:0,isCompatible(e){if("string"!=typeof e||1!==e.length)return!1;const t=e.charCodeAt(0);return t>=0&&t<=65535},fromJni:e=>String.fromCharCode(e),toJni:e=>e.charCodeAt(0),read:e=>e.readU16(),write(e,t){e.writeU16(t)},toString(){return this.name}},short:{name:"S",type:"int16",size:1,byteSize:2,defaultValue:0,isCompatible:e=>Number.isInteger(e)&&e>=-32768&&e<=32767,fromJni:ho,toJni:ho,read:e=>e.readS16(),write(e,t){e.writeS16(t)},toString(){return this.name}},int:{name:"I",type:"int32",size:1,byteSize:4,defaultValue:0,isCompatible:e=>Number.isInteger(e)&&e>=-2147483648&&e<=**********,fromJni:ho,toJni:ho,read:e=>e.readS32(),write(e,t){e.writeS32(t)},toString(){return this.name}},long:{name:"J",type:"int64",size:2,byteSize:8,defaultValue:0,isCompatible:e=>"number"==typeof e||e instanceof Int64,fromJni:ho,toJni:ho,read:e=>e.readS64(),write(e,t){e.writeS64(t)},toString(){return this.name}},float:{name:"F",type:"float",size:1,byteSize:4,defaultValue:0,isCompatible:e=>"number"==typeof e,fromJni:ho,toJni:ho,read:e=>e.readFloat(),write(e,t){e.writeFloat(t)},toString(){return this.name}},double:{name:"D",type:"double",size:2,byteSize:8,defaultValue:0,isCompatible:e=>"number"==typeof e,fromJni:ho,toJni:ho,read:e=>e.readDouble(),write(e,t){e.writeDouble(t)},toString(){return this.name}},void:{name:"V",type:"void",size:0,byteSize:0,defaultValue:void 0,isCompatible:e=>void 0===e,fromJni(){},toJni:()=>NULL,toString(){return this.name}}},oo=new Set(Object.values(ro).map(e=>e.name));function io(e){const t=ro[e];return void 0!==t?t:null}const ao=[["Z","boolean"],["B","byte"],["C","char"],["D","double"],["F","float"],["I","int"],["J","long"],["S","short"]].reduce((e,[t,n])=>(e["["+t]=function(e,t){const n=f.prototype,r=(i=t,i.charAt(0).toUpperCase()+i.slice(1)),o={typeName:t,newArray:n["new"+r+"Array"],setRegion:n["set"+r+"ArrayRegion"],getElements:n["get"+r+"ArrayElements"],releaseElements:n["release"+r+"ArrayElements"]};var i;return{name:e,type:"pointer",size:1,defaultValue:NULL,isCompatible:e=>function(e,t){if(null===e)return!0;if(e instanceof co)return e.$s.typeName===t;if("object"!=typeof e||void 0===e.length)return!1;const n=io(t);return Array.prototype.every.call(e,e=>n.isCompatible(e))}(e,t),fromJni:(e,t,n)=>function(e,t,n,r){if(e.isNull())return null;const o=io(t.typeName),i=n.getArrayLength(e);return new co(e,t,o,i,n,r)}(e,o,t,n),toJni:(e,t)=>function(e,t,n){if(null===e)return NULL;const r=e.$h;if(void 0!==r)return r;const o=e.length,i=io(t.typeName),a=t.newArray.call(n,o);if(a.isNull())throw new Error("Unable to construct array");if(o>0){const r=i.byteSize,s=i.write,l=i.toJni,c=Memory.alloc(o*i.byteSize);for(let t=0;t!==o;t++)s(c.add(t*r),l(e[t]));t.setRegion.call(n,a,0,o,c),n.throwIfExceptionPending()}return a}(e,o,t)}}("["+t,n),e),{});function so(e,t,n){const r=ao[e];if(void 0!==r)return r;if(0!==e.indexOf("["))throw new Error("Unsupported type: "+e);let o=e.substring(1);const i=no(o,t,n);let a=0;const s=o.length;for(;a!==s&&"["===o[a];)a++;o=o.substring(a),"L"===o[0]&&";"===o[o.length-1]&&(o=o.substring(1,o.length-1));let l=o.replace(/\./g,"/");l=oo.has(l)?"[".repeat(a)+l:"[".repeat(a)+"L"+l+";";const c="["+l;return o="[".repeat(a)+o,{name:e.replace(/\./g,"/"),type:"pointer",size:1,defaultValue:NULL,isCompatible:e=>null===e||"object"==typeof e&&void 0!==e.length&&e.every(function(e){return i.isCompatible(e)}),fromJni(e,t,r){if(e.isNull())return null;const a=[],s=t.getArrayLength(e);for(let n=0;n!==s;n++){const r=t.getObjectArrayElement(e,n);try{a.push(i.fromJni(r,t))}finally{t.deleteLocalRef(r)}}try{a.$w=n.cast(e,n.use(c),r)}catch(t){n.use("java.lang.reflect.Array").newInstance(n.use(o).class,0),a.$w=n.cast(e,n.use(c),r)}return a.$dispose=lo,a},toJni(e,t){if(null===e)return NULL;if(!(e instanceof Array))throw new Error("Expected an array");const r=e.$w;if(void 0!==r)return r.$h;const a=e.length,s=n.use(o).$borrowClassHandle(t);try{const n=t.newObjectArray(a,s.value,NULL);t.throwIfExceptionPending();for(let r=0;r!==a;r++){const o=i.toJni(e[r],t);try{t.setObjectArrayElement(n,r,o)}finally{"pointer"===i.type&&1===t.getObjectRefType(o)&&t.deleteLocalRef(o)}t.throwIfExceptionPending()}return n}finally{s.unref(t)}}}}function lo(){const e=this.length;for(let t=0;t!==e;t++){const e=this[t];if(null===e)continue;const n=e.$dispose;if(void 0===n)break;n.call(e)}this.$w.$dispose()}function co(e,t,n,r,o,i=!0){if(i){const t=o.newGlobalRef(e);this.$h=t,this.$r=Script.bindWeak(this,o.vm.makeHandleDestructor(t))}else this.$h=e,this.$r=null;return this.$s=t,this.$t=n,this.length=r,new Proxy(this,to)}function uo(e){return"L"+e.replace(/\./g,"/")+";"}function ho(e){return e}to={has:(e,t)=>t in e||null!==e.tryParseIndex(t),get(e,t,n){const r=e.tryParseIndex(t);return null===r?e[t]:e.readElement(r)},set(e,t,n,r){const o=e.tryParseIndex(t);return null===o?(e[t]=n,!0):(e.writeElement(o,n),!0)},ownKeys(e){const t=[],{length:n}=e;for(let e=0;e!==n;e++){const n=e.toString();t.push(n)}return t.push("length"),t},getOwnPropertyDescriptor:(e,t)=>null!==e.tryParseIndex(t)?{writable:!0,configurable:!0,enumerable:!0}:Object.getOwnPropertyDescriptor(e,t)},Object.defineProperties(co.prototype,{$dispose:{enumerable:!0,value(){const e=this.$r;null!==e&&(this.$r=null,Script.unbindWeak(e))}},$clone:{value(e){return new co(this.$h,this.$s,this.$t,this.length,e)}},tryParseIndex:{value(e){if("symbol"==typeof e)return null;const t=parseInt(e);return isNaN(t)||t<0||t>=this.length?null:t}},readElement:{value(e){return this.withElements(t=>{const n=this.$t;return n.fromJni(n.read(t.add(e*n.byteSize)))})}},writeElement:{value(e,t){const{$h:n,$s:r,$t:o}=this,i=eo.getEnv(),a=Memory.alloc(o.byteSize);o.write(a,o.toJni(t)),r.setRegion.call(i,n,e,1,a)}},withElements:{value(e){const{$h:t,$s:n}=this,r=eo.getEnv(),o=n.getElements.call(r,t);if(o.isNull())throw new Error("Unable to get array elements");try{return e(o)}finally{n.releaseElements.call(r,t,o)}}},toJSON:{value(){const{length:e,$t:t}=this,{byteSize:n,fromJni:r,read:o}=t;return this.withElements(t=>{const i=[];for(let a=0;a!==e;a++){const e=r(o(t.add(a*n)));i.push(e)}return i})}},toString:{value(){return this.toJSON().toString()}}});let{ensureClassInitialized:po,makeMethodMangler:fo}=on;const _o=Symbol("PENDING_USE"),mo="/data/local/tmp",{getCurrentThreadId:go,pointerSize:bo}=Process,yo={state:"empty",factories:[],loaders:null,Integer:null};let vo=null,wo=null,Eo=null,So=null,No=null,Lo=null,Co=null,ko=null,Mo=null;const jo=new Map;class Io{static _initialize(e,t){vo=e,wo=t,Eo="art"===t.flavor,"jvm"===t.flavor&&(po=vn,fo=Ln)}static _disposeAll(e){yo.factories.forEach(t=>{t._dispose(e)})}static get(e){const t=function(){switch(yo.state){case"empty":{yo.state="pending";const e=yo.factories[0],t=e.use("java.util.HashMap"),n=e.use("java.lang.Integer");yo.loaders=t.$new(),yo.Integer=n;const r=e.loader;return null!==r&&Vo(e,r),yo.state="ready",yo}case"pending":do{Thread.sleep(.05)}while("pending"===yo.state);return yo;case"ready":return yo}}(),n=t.factories[0];if(null===e)return n;const r=t.loaders.get(e);if(null!==r){const e=n.cast(r,t.Integer);return t.factories[e.intValue()]}const o=new Io;return o.loader=e,o.cacheDir=n.cacheDir,Vo(o,e),o}constructor(){this.cacheDir=mo,this.codeCacheDir=mo+"/dalvik-cache",this.tempFileNaming={prefix:"frida",suffix:""},this._classes={},this._classHandles=new zn(10,Po),this._patchedMethods=new Set,this._loader=null,this._types=[{},{}],yo.factories.push(this)}_dispose(e){Array.from(this._patchedMethods).forEach(e=>{e.implementation=null}),this._patchedMethods.clear(),yt(),this._classHandles.dispose(e),this._classes={}}get loader(){return this._loader}set loader(e){const t=null===this._loader&&null!==e;this._loader=e,t&&"ready"===yo.state&&this===yo.factories[0]&&Vo(this,e)}use(e,t={}){const n="skip"!==t.cache;let r=n?this._getUsedClass(e):void 0;if(void 0===r)try{const t=vo.getEnv(),{_loader:n}=this,o=null!==n?function(e,t,n){null===Mo&&(ko=n.vaMethod("pointer",["pointer"]),Mo=t.loadClass.overload("java.lang.String").handle);return n=null,function(n){const r=n.newStringUtf(e),o=go();Bo(o);try{const e=ko(n.handle,t.$h,Mo,r);return n.throwIfExceptionPending(),e}finally{Go(o),n.deleteLocalRef(r)}}}(e,n,t):function(e){const t=e.replace(/\./g,"/");return function(e){const n=go();Bo(n);try{return e.findClass(t)}finally{Go(n)}}}(e);r=this._make(e,o,t)}finally{n&&this._setUsedClass(e,r)}return r}_getUsedClass(e){let t;for(;(t=this._classes[e])===_o;)Thread.sleep(.05);return void 0===t&&(this._classes[e]=_o),t}_setUsedClass(e,t){void 0!==t?this._classes[e]=t:delete this._classes[e]}_make(e,t,n){const r=function(e,t,n,r){return Ro.call(this,e,t,n,r)},o=Object.create(Ro.prototype,{[Symbol.for("n")]:{value:e},$n:{get(){return this[Symbol.for("n")]}},[Symbol.for("C")]:{value:r},$C:{get(){return this[Symbol.for("C")]}},[Symbol.for("w")]:{value:null,writable:!0},$w:{get(){return this[Symbol.for("w")]},set(e){this[Symbol.for("w")]=e}},[Symbol.for("_s")]:{writable:!0},$_s:{get(){return this[Symbol.for("_s")]},set(e){this[Symbol.for("_s")]=e}},[Symbol.for("c")]:{value:[null]},$c:{get(){return this[Symbol.for("c")]}},[Symbol.for("m")]:{value:new Map},$m:{get(){return this[Symbol.for("m")]}},[Symbol.for("l")]:{value:null,writable:!0},$l:{get(){return this[Symbol.for("l")]},set(e){this[Symbol.for("l")]=e}},[Symbol.for("gch")]:{value:t},$gch:{get(){return this[Symbol.for("gch")]}},[Symbol.for("f")]:{value:this},$f:{get(){return this[Symbol.for("f")]}}});r.prototype=o;const i=new r(null);o[Symbol.for("w")]=i,o.$w=i;const a=i.$borrowClassHandle(n);try{const e=a.value;po(n,e),o.$l=Un.build(e,n)}finally{a.unref(n)}return i}retain(e){const t=vo.getEnv();return e.$clone(t)}cast(e,t,n){const r=vo.getEnv();let o=e.$h;void 0===o&&(o=e);const i=t.$borrowClassHandle(r);try{if(!r.isInstanceOf(o,i.value))throw new Error(`Cast from '${r.getObjectClassName(o)}' to '${t.$n}' isn't possible`)}finally{i.unref(r)}return new(0,t.$C)(o,1,r,n)}wrap(e,t,n){const r=new(0,t.$C)(e,1,n,!1);return r.$r=Script.bindWeak(r,vo.makeHandleDestructor(e)),r}array(e,t){const n=vo.getEnv(),r=io(e);null!==r&&(e=r.name);const o=so("["+e,!1,this),i=o.toJni(t,n);return o.fromJni(i,n,!0)}registerClass(e){const t=vo.getEnv(),n=[];try{const r=this.use("java.lang.Class"),o=t.javaLangReflectMethod(),i=t.vaMethod("pointer",[]),a=e.name,s=e.implements||[],l=e.superClass||this.use("java.lang.Object"),c=[],d=[],u={name:uo(a),sourceFileName:Ho(a),superClass:uo(l.$n),interfaces:s.map(e=>uo(e.$n)),fields:c,methods:d},h=s.slice();s.forEach(e=>{Array.prototype.slice.call(e.class.getInterfaces()).forEach(e=>{const t=this.cast(e,r).getCanonicalName();h.push(this.use(t))})});const p=e.fields||{};Object.getOwnPropertyNames(p).forEach(e=>{const t=this._getType(p[e]);c.push([e,t.name])});const f={},_={};h.forEach(e=>{const r=e.$borrowClassHandle(t);n.push(r);const o=r.value;e.$ownMembers.filter(t=>void 0!==e[t].overloads).forEach(t=>{const n=e[t],r=n.overloads,i=r.map(e=>To(t,e.returnType,e.argumentTypes));f[t]=[n,i,o],r.forEach((e,t)=>{const n=i[t];_[n]=[e,o]})})});const m=e.methods||{},g=Object.keys(m).reduce((e,t)=>{const n=m[t],r="$init"===t?"<init>":t;return n instanceof Array?e.push(...n.map(e=>[r,e])):e.push([r,n]),e},[]),b=[];g.forEach(([e,n])=>{let r,a,s,l=3,c=[];if("function"==typeof n){const d=f[e];if(void 0!==d&&Array.isArray(d)){const[u,h,p]=d;if(h.length>1)throw new Error(`More than one overload matching '${e}': signature must be specified`);delete _[h[0]];const f=u.overloads[0];l=f.type,r=f.returnType,a=f.argumentTypes,s=n;const m=t.toReflectedMethod(p,f.handle,0),g=i(t.handle,m,o.getGenericExceptionTypes);c=Zo(t,g).map(uo),t.deleteLocalRef(g),t.deleteLocalRef(m)}else r=this._getType("void"),a=[],s=n}else{if(n.isStatic&&(l=2),r=this._getType(n.returnType||"void"),a=(n.argumentTypes||[]).map(e=>this._getType(e)),s=n.implementation,"function"!=typeof s)throw new Error("Expected a function implementation for method: "+e);const d=To(e,r,a),u=_[d];if(void 0!==u){const[e,n]=u;delete _[d],l=e.type,r=e.returnType,a=e.argumentTypes;const s=t.toReflectedMethod(n,e.handle,0),h=i(t.handle,s,o.getGenericExceptionTypes);c=Zo(t,h).map(uo),t.deleteLocalRef(h),t.deleteLocalRef(s)}}const u=r.name,h=a.map(e=>e.name),p="("+h.join("")+")"+u;d.push([e,u,h,c,2===l?8:0]),b.push([e,p,l,r,a,s])});const y=Object.keys(_);if(y.length>0)throw new Error("Missing implementation for: "+y.join(", "));const v=$o.fromBuffer(function(e){const t=new Zr,n=Object.assign({},e);return t.addClass(n),t.build()}(u),this);try{v.load()}finally{v.file.delete()}const w=this.use(e.name),E=g.length;if(E>0){const e=3*bo,r=Memory.alloc(E*e),o=[],i=[];b.forEach(([t,n,a,s,l,c],d)=>{const u=Memory.allocUtf8String(t),h=Memory.allocUtf8String(n),p=Do(t,w,a,s,l,c);r.add(d*e).writePointer(u),r.add(d*e+bo).writePointer(h),r.add(d*e+2*bo).writePointer(p),i.push(u,h),o.push(p)});const a=w.$borrowClassHandle(t);n.push(a);const s=a.value;t.registerNatives(s,r,E),t.throwIfExceptionPending(),w.$nativeMethods=o}return w}finally{n.forEach(e=>{e.unref(t)})}}choose(e,t){const n=vo.getEnv(),{flavor:r}=wo;if("jvm"===r)this._chooseObjectsJvm(e,n,t);else if("art"===r){const r=void 0===wo["art::gc::Heap::VisitObjects"];if(r){if(void 0===wo["art::gc::Heap::GetInstances"])return this._chooseObjectsJvm(e,n,t)}Ye(vo,n,o=>{r?this._chooseObjectsArtPreA12(e,n,o,t):this._chooseObjectsArtLegacy(e,n,o,t)})}else this._chooseObjectsDalvik(e,n,t)}_chooseObjectsJvm(e,t,n){const r=this.use(e),{jvmti:o}=wo,i=r.$borrowClassHandle(t),a=int64(i.value.toString());try{const e=new NativeCallback((e,t,n,r)=>(n.writeS64(a),1),"int",["int64","int64","pointer","pointer"]);o.iterateOverInstancesOfClass(i.value,3,e,i.value);const s=Memory.alloc(8);s.writeS64(a);const l=Memory.alloc(4),c=Memory.alloc(bo);o.getObjectsWithTags(1,s,l,c,NULL);const d=l.readS32(),u=c.readPointer(),h=[];for(let e=0;e!==d;e++)h.push(u.add(e*bo).readPointer());o.deallocate(u);try{for(const e of h){const t=this.cast(e,r);if("stop"===n.onMatch(t))break}n.onComplete()}finally{h.forEach(e=>{t.deleteLocalRef(e)})}}finally{i.unref(t)}}_chooseObjectsArtPreA12(e,t,n,r){const o=this.use(e),i=Yt.$new(n,vo);let a;const s=o.$borrowClassHandle(t);try{const e=wo["art::JavaVMExt::DecodeGlobal"](wo.vm,n,s.value);a=i.newHandle(e)}finally{s.unref(t)}const l=Zt.$new();wo["art::gc::Heap::GetInstances"](wo.artHeap,i,a,0,l);const c=l.handles.map(e=>t.newGlobalRef(e));l.$delete(),i.$delete();try{for(const e of c){const t=this.cast(e,o);if("stop"===r.onMatch(t))break}r.onComplete()}finally{c.forEach(e=>{t.deleteGlobalRef(e)})}}_chooseObjectsArtLegacy(e,t,n,r){const o=this.use(e),i=[],a=wo["art::JavaVMExt::AddGlobalRef"],s=wo.vm;let l;const c=o.$borrowClassHandle(t);try{l=wo["art::JavaVMExt::DecodeGlobal"](s,n,c.value).toInt32()}finally{c.unref(t)}const d=nn(l,e=>{i.push(a(s,n,e))});wo["art::gc::Heap::VisitObjects"](wo.artHeap,d,NULL);try{for(const e of i){const t=this.cast(e,o);if("stop"===r.onMatch(t))break}}finally{i.forEach(e=>{t.deleteGlobalRef(e)})}r.onComplete()}_chooseObjectsDalvik(e,t,n){const r=this.use(e);if(null===wo.addLocalReference){const e=Process.getModuleByName("libdvm.so");let t;switch(Process.arch){case"arm":t="2d e9 f0 41 05 46 15 4e 0c 46 7e 44 11 b3 43 68";break;case"ia32":t="8d 64 24 d4 89 5c 24 1c 89 74 24 20 e8 ?? ?? ?? ?? ?? ?? ?? ?? ?? ?? 85 d2"}Memory.scan(e.base,e.size,t,{onMatch:(e,t)=>{let n;if("arm"===Process.arch)e=e.or(1),n=new NativeFunction(e,"pointer",["pointer","pointer"]);else{const t=Memory.alloc(Process.pageSize);Memory.patchCode(t,16,n=>{const r=new X86Writer(n,{pc:t});r.putMovRegRegOffsetPtr("eax","esp",4),r.putMovRegRegOffsetPtr("edx","esp",8),r.putJmpAddress(e),r.flush()}),n=new NativeFunction(t,"pointer",["pointer","pointer"]),n._thunk=t}return wo.addLocalReference=n,vo.perform(e=>{o(this,e)}),"stop"},onError(e){},onComplete(){null===wo.addLocalReference&&n.onComplete()}})}else o(this,t);function o(e,t){const{DVM_JNI_ENV_OFFSET_SELF:o}=on,i=t.handle.add(o).readPointer();let a;const s=r.$borrowClassHandle(t);try{a=wo.dvmDecodeIndirectRef(i,s.value)}finally{s.unref(t)}const l=a.toMatchPattern(),c=wo.dvmHeapSourceGetBase(),d=wo.dvmHeapSourceGetLimit().sub(c).toInt32();Memory.scan(c,d,l,{onMatch:(t,i)=>{wo.dvmIsValidObject(t)&&vo.perform(i=>{const a=i.handle.add(o).readPointer();let s;const l=wo.addLocalReference(a,t);try{s=e.cast(l,r)}finally{i.deleteLocalRef(l)}if("stop"===n.onMatch(s))return"stop"})},onError(e){},onComplete(){n.onComplete()}})}}openClassFile(e){return new $o(e,null,this)}_getType(e,t=!0){return no(e,t,this)}}function Ro(e,t,n,r=!0){if(null!==e)if(r){const t=n.newGlobalRef(e);this.$h=t,this.$r=Script.bindWeak(this,vo.makeHandleDestructor(t))}else this.$h=e,this.$r=null;else this.$h=null,this.$r=null;return this.$t=t,new Proxy(this,So)}function Ao(e,t){this.value=t.newGlobalRef(e),t.deleteLocalRef(e),this.refs=1}function Po(e,t){e.unref(t)}function xo(e){const t=function(){const e=function(){return e.invoke(this,arguments)};return e}();return Object.setPrototypeOf(t,No),t._o=e,t}function To(e,t,n){return`${t.className} ${e}(${n.map(e=>e.className).join(", ")})`}function Uo(e){const t=e._o;t.length>1&&Oo(t[0].methodName,t,"has more than one overload, use .overload(<signature>) to choose from:")}function Oo(e,t,n){const r=t.slice().sort((e,t)=>e.argumentTypes.length-t.argumentTypes.length).map(e=>e.argumentTypes.length>0?".overload('"+e.argumentTypes.map(e=>e.className).join("', '")+"')":".overload()");throw new Error(`${e}(): ${n}\n\t${r.join("\n\t")}`)}function Fo(e,t,n,r,o,i,a,s){const l=o.type,c=i.map(e=>e.type);let d,u;return null===a&&(a=vo.getEnv()),3===n?(d=a.vaMethod(l,c,s),u=a.nonvirtualVaMethod(l,c,s)):2===n?(d=a.staticVaMethod(l,c,s),u=d):(d=a.constructor(c,s),u=d),function(e){const t=function(){const e=function(){return e.invoke(this,arguments)};return e}();return Object.setPrototypeOf(t,Lo),t._p=e,t}([e,t,n,r,o,i,d,u])}function Do(e,t,n,r,o,i,a=null){const s=new Set,l=(c=[e,t,n,r,o,i,a,s],function(){return function(e,t){const n=new f(e[0],vo),[r,o,i,a,s,l,c,d]=t,u=[];let h;h=3===i?new(0,o.$C)(e[1],1,n,!1):o;const p=go();n.pushLocalFrame(3);let _=!0;vo.link(p,n);try{let t;d.add(p),t=null!==c&&jo.has(p)?c:l;const o=[],i=e.length-2;for(let t=0;t!==i;t++){const r=s[t].fromJni(e[2+t],n,!1);o.push(r),u.push(r)}const f=t.apply(h,o);if(!a.isCompatible(f))throw new Error(`Implementation for ${r} expected return value compatible with ${a.className}`);let m=a.toJni(f,n);return"pointer"===a.type&&(m=n.popLocalFrame(m),_=!1,u.push(f)),m}catch(e){const t=e.$h;return void 0!==t?n.throw(t):Script.nextTick(()=>{throw e}),a.defaultValue}finally{vo.unlink(p),_&&n.popLocalFrame(NULL),d.delete(p),u.forEach(e=>{if(null===e)return;const t=e.$dispose;void 0!==t&&t.call(e)})}}(arguments,c)});var c;const d=new NativeCallback(l,r.type,["pointer","pointer"].concat(o.map(e=>e.type)));return d._c=s,d}function zo(e){this._p=e}So={has:(e,t)=>t in e||e.$has(t),get(e,t,n){if("string"!=typeof t||t.startsWith("$")||"class"===t)return e[t];const r=e.$find(t);return null!==r?r(n):e[t]},set:(e,t,n,r)=>(e[t]=n,!0),ownKeys:e=>e.$list(),getOwnPropertyDescriptor:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)?Object.getOwnPropertyDescriptor(e,t):{writable:!1,configurable:!0,enumerable:!0}},Object.defineProperties(Ro.prototype,{[Symbol.for("new")]:{enumerable:!1,get(){return this.$getCtor("allocAndInit")}},$new:{enumerable:!0,get(){return this[Symbol.for("new")]}},[Symbol.for("alloc")]:{enumerable:!1,value(){const e=vo.getEnv(),t=this.$borrowClassHandle(e);try{const n=e.allocObject(t.value);return this.$f.cast(n,this)}finally{t.unref(e)}}},$alloc:{enumerable:!0,get(){return this[Symbol.for("alloc")]}},[Symbol.for("init")]:{enumerable:!1,get(){return this.$getCtor("initOnly")}},$init:{enumerable:!0,get(){return this[Symbol.for("init")]}},[Symbol.for("dispose")]:{enumerable:!1,value(){const e=this.$r;null!==e&&(this.$r=null,Script.unbindWeak(e)),null!==this.$h&&(this.$h=void 0)}},$dispose:{enumerable:!0,get(){return this[Symbol.for("dispose")]}},[Symbol.for("clone")]:{enumerable:!1,value(e){return new(0,this.$C)(this.$h,this.$t,e)}},$clone:{value(e){return this[Symbol.for("clone")](e)}},[Symbol.for("class")]:{enumerable:!1,get(){const e=vo.getEnv(),t=this.$borrowClassHandle(e);try{const e=this.$f;return e.cast(t.value,e.use("java.lang.Class"))}finally{t.unref(e)}}},class:{enumerable:!0,get(){return this[Symbol.for("class")]}},[Symbol.for("className")]:{enumerable:!1,get(){const e=this.$h;return null===e?this.$n:vo.getEnv().getObjectClassName(e)}},$className:{enumerable:!0,get(){return this[Symbol.for("className")]}},[Symbol.for("ownMembers")]:{enumerable:!1,get(){return this.$l.list()}},$ownMembers:{enumerable:!0,get(){return this[Symbol.for("ownMembers")]}},[Symbol.for("super")]:{enumerable:!1,get(){const e=vo.getEnv();return new(0,this.$s.$C)(this.$h,2,e)}},$super:{enumerable:!0,get(){return this[Symbol.for("super")]}},[Symbol.for("s")]:{enumerable:!1,get(){const e=Object.getPrototypeOf(this);let t=e.$_s;if(void 0===t){const r=vo.getEnv(),o=this.$borrowClassHandle(r);try{const i=r.getSuperclass(o.value);if(i.isNull())t=null;else try{const o=r.getClassName(i),a=e.$f;if(t=a._getUsedClass(o),void 0===t)try{const e=(n=this,function(e){const t=n.$borrowClassHandle(e);try{return e.getSuperclass(t.value)}finally{t.unref(e)}});t=a._make(o,e,r)}finally{a._setUsedClass(o,t)}}finally{r.deleteLocalRef(i)}}finally{o.unref(r)}e.$_s=t}var n;return t}},$s:{get(){return this[Symbol.for("s")]}},[Symbol.for("isSameObject")]:{enumerable:!1,value(e){return vo.getEnv().isSameObject(e.$h,this.$h)}},$isSameObject:{value(e){return this[Symbol.for("isSameObject")](e)}},[Symbol.for("getCtor")]:{enumerable:!1,value(e){const t=this.$c;let n=t[0];if(null===n){const e=vo.getEnv(),r=this.$borrowClassHandle(e);try{n=function(e,t,n){const{$n:r,$f:o}=t,i=function(e){return e.slice(e.lastIndexOf(".")+1)}(r),a=n.javaLangClass(),s=n.javaLangReflectConstructor(),l=n.vaMethod("pointer",[]),c=n.vaMethod("uint8",[]),d=[],u=[],h=o._getType(r,!1),p=o._getType("void",!1),f=l(n.handle,e,a.getDeclaredConstructors);try{const r=n.getArrayLength(f);if(0!==r)for(let e=0;e!==r;e++){let r,a;const c=n.getObjectArrayElement(f,e);try{r=n.fromReflectedMethod(c),a=l(n.handle,c,s.getGenericParameterTypes)}finally{n.deleteLocalRef(c)}let _;try{_=Zo(n,a).map(e=>o._getType(e))}finally{n.deleteLocalRef(a)}d.push(Fo(i,t,1,r,h,_,n)),u.push(Fo(i,t,3,r,p,_,n))}else{if(c(n.handle,e,a.isInterface))throw new Error("cannot instantiate an interface");const r=n.javaLangObject(),o=n.getMethodId(r,"<init>","()V");d.push(Fo(i,t,1,o,h,[],n)),u.push(Fo(i,t,3,o,p,[],n))}}finally{n.deleteLocalRef(f)}if(0===u.length)throw new Error("no supported overloads");return{allocAndInit:xo(d),initOnly:xo(u)}}(r.value,this.$w,e),t[0]=n}finally{r.unref(e)}}return n[e]}},$getCtor:{value(e){return this[Symbol.for("getCtor")](e)}},[Symbol.for("borrowClassHandle")]:{enumerable:!1,value(e){const t=this.$n,n=this.$f._classHandles;let r=n.get(t);return void 0===r&&(r=new Ao(this.$gch(e),e),n.set(t,r,e)),r.ref()}},$borrowClassHandle:{value(e){return this[Symbol.for("borrowClassHandle")](e)}},[Symbol.for("copyClassHandle")]:{enumerable:!1,value(e){const t=this.$borrowClassHandle(e);try{return e.newLocalRef(t.value)}finally{t.unref(e)}}},$copyClassHandle:{value(e){return this[Symbol.for("copyClassHandle")](e)}},[Symbol.for("getHandle")]:{enumerable:!1,value(e){const t=this.$h;if(void 0===t)throw new Error("Wrapper is disposed; perhaps it was borrowed from a hook instead of calling Java.retain() to make a long-lived wrapper?");return t}},$getHandle:{value(e){return this[Symbol.for("getHandle")](e)}},[Symbol.for("list")]:{enumerable:!1,value(){const e=this.$s,t=null!==e?e.$list():[],n=this.$l;return Array.from(new Set(t.concat(n.list())))}},$list:{get(){return this[Symbol.for("list")]}},[Symbol.for("has")]:{enumerable:!1,value(e){if(this.$m.has(e))return!0;if(this.$l.has(e))return!0;const t=this.$s;return!(null===t||!t.$has(e))}},$has:{value(e){return this[Symbol.for("has")](e)}},[Symbol.for("find")]:{enumerable:!1,value(e){const t=this.$m;let n=t.get(e);if(void 0!==n)return n;const r=this.$l.find(e);if(null!==r){const o=vo.getEnv(),i=this.$borrowClassHandle(o);try{n=function(e,t,n,r,o){if(t.startsWith("m"))return function(e,t,n,r,o){const{$f:i}=r,a=t.split(":").slice(1),s=o.javaLangReflectMethod(),l=o.vaMethod("pointer",[]),c=o.vaMethod("uint8",[]),d=a.map(t=>{const a="s"===t[0]?2:3,d=ptr(t.substr(1));let u;const h=[],p=o.toReflectedMethod(n,d,2===a?1:0);try{const e=!!c(o.handle,p,s.isVarArgs),t=l(o.handle,p,s.getGenericReturnType);o.throwIfExceptionPending();try{u=i._getType(o.getTypeName(t))}finally{o.deleteLocalRef(t)}const n=l(o.handle,p,s.getParameterTypes);try{const t=o.getArrayLength(n);for(let r=0;r!==t;r++){const a=o.getObjectArrayElement(n,r);let s;try{s=e&&r===t-1?o.getArrayTypeName(a):o.getTypeName(a)}finally{o.deleteLocalRef(a)}const l=i._getType(s);h.push(l)}}finally{o.deleteLocalRef(n)}}catch(e){return null}finally{o.deleteLocalRef(p)}return Fo(e,r,a,d,u,h,o)}).filter(e=>null!==e);if(0===d.length)throw new Error("No supported overloads");"valueOf"===e&&function(e){const{holder:t,type:n}=e[0],r=e.some(e=>e.type===n&&0===e.argumentTypes.length);if(r)return;e.push(function(e){const t=function(){const e=function(){return this};return e}();return Object.setPrototypeOf(t,Co),t._p=e,t}([t,n]))}(d);const u=xo(d);return function(e){return u}}(e,t,n,r,o);return function(e,t,n,r,o){const i="s"===t[2]?1:2,a=ptr(t.substr(3)),{$f:s}=r;let l;const c=o.toReflectedField(n,a,1===i?1:0);try{l=o.vaMethod("pointer",[])(o.handle,c,o.javaLangReflectField().getGenericType),o.throwIfExceptionPending()}finally{o.deleteLocalRef(c)}let d,u,h;try{d=s._getType(o.getTypeName(l))}finally{o.deleteLocalRef(l)}const p=d.type;1===i?(u=o.getStaticField(p),h=o.setStaticField(p)):(u=o.getField(p),h=o.setField(p));return f=[i,d,a,u,h],function(e){return new zo([e].concat(f))};var f}(0,t,n,r,o)}(e,r,i.value,this.$w,o)}finally{i.unref(o)}return t.set(e,n),n}const o=this.$s;return null!==o?o.$find(e):null}},$find:{value(e){return this[Symbol.for("find")](e)}},[Symbol.for("toJSON")]:{enumerable:!1,value(){const e=this.$n;if(null===this.$h)return`<class: ${e}>`;const t=this.$className;return e===t?`<instance: ${e}>`:`<instance: ${e}, $className: ${t}>`}},toJSON:{get(){return this[Symbol.for("toJSON")]}}}),Ao.prototype.ref=function(){return this.refs++,this},Ao.prototype.unref=function(e){0===--this.refs&&e.deleteGlobalRef(this.value)},No=Object.create(Function.prototype,{overloads:{enumerable:!0,get(){return this._o}},overload:{value(...e){const t=this._o,n=e.length,r=e.join(":");for(let e=0;e!==t.length;e++){const o=t[e],{argumentTypes:i}=o;if(i.length!==n)continue;if(i.map(e=>e.className).join(":")===r)return o}Oo(this.methodName,this.overloads,"specified argument types do not match any of:")}},methodName:{enumerable:!0,get(){return this._o[0].methodName}},holder:{enumerable:!0,get(){return this._o[0].holder}},type:{enumerable:!0,get(){return this._o[0].type}},handle:{enumerable:!0,get(){return Uo(this),this._o[0].handle}},implementation:{enumerable:!0,get(){return Uo(this),this._o[0].implementation},set(e){Uo(this),this._o[0].implementation=e}},returnType:{enumerable:!0,get(){return Uo(this),this._o[0].returnType}},argumentTypes:{enumerable:!0,get(){return Uo(this),this._o[0].argumentTypes}},canInvokeWith:{enumerable:!0,get(e){return Uo(this),this._o[0].canInvokeWith}},clone:{enumerable:!0,value(e){return Uo(this),this._o[0].clone(e)}},invoke:{value(e,t){const n=this._o,r=null!==e.$h;for(let o=0;o!==n.length;o++){const i=n[o];if(i.canInvokeWith(t)){if(3===i.type&&!r){const t=this.methodName;if("toString"===t)return`<class: ${e.$n}>`;throw new Error(t+": cannot call instance method without an instance")}return i.apply(e,t)}}if("toString"===this.methodName)return`<class: ${e.$n}>`;Oo(this.methodName,this.overloads,"argument types do not match any of:")}}}),Lo=Object.create(Function.prototype,{methodName:{enumerable:!0,get(){return this._p[0]}},holder:{enumerable:!0,get(){return this._p[1]}},type:{enumerable:!0,get(){return this._p[2]}},handle:{enumerable:!0,get(){return this._p[3]}},implementation:{enumerable:!0,get(){const e=this._r;return void 0!==e?e:null},set(e){const t=this._p,n=t[1];if(1===t[2])throw new Error("Reimplementing $new is not possible; replace implementation of $init instead");const r=this._r;if(void 0!==r){n.$f._patchedMethods.delete(this);r._m.revert(vo),this._r=void 0}if(null!==e){const[r,o,i,a,s,l]=t,c=Do(r,o,i,s,l,e,this),d=fo(a);c._m=d,this._r=c,d.replace(c,3===i,l,vo,wo),n.$f._patchedMethods.add(this)}}},returnType:{enumerable:!0,get(){return this._p[4]}},argumentTypes:{enumerable:!0,get(){return this._p[5]}},canInvokeWith:{enumerable:!0,value(e){const t=this._p[5];return e.length===t.length&&t.every((t,n)=>t.isCompatible(e[n]))}},clone:{enumerable:!0,value(e){return Fo(...this._p.slice(0,6),null,e)}},invoke:{value(e,t){const n=vo.getEnv(),r=this._p,o=r[2],i=r[4],a=r[5],s=this._r,l=3===o,c=t.length,d=2+c;n.pushLocalFrame(d);let u=null;try{let o,d;l?o=e.$getHandle():(u=e.$borrowClassHandle(n),o=u.value);let h=e.$t;if(void 0===s)d=r[3];else{if(d=s._m.resolveTarget(e,l,n,wo),Eo){s._c.has(go())&&(h=2)}}const p=[n.handle,o,d];for(let e=0;e!==c;e++)p.push(a[e].toJni(t[e],n));let f;1===h?f=r[6]:(f=r[7],l&&p.splice(2,0,e.$copyClassHandle(n)));const _=f.apply(null,p);return n.throwIfExceptionPending(),i.fromJni(_,n,!0)}finally{null!==u&&u.unref(n),n.popLocalFrame(NULL)}}},toString:{enumerable:!0,value(){return`function ${this.methodName}(${this.argumentTypes.map(e=>e.className).join(", ")}): ${this.returnType.className}`}}}),Co=Object.create(Function.prototype,{methodName:{enumerable:!0,get:()=>"valueOf"},holder:{enumerable:!0,get(){return this._p[0]}},type:{enumerable:!0,get(){return this._p[1]}},handle:{enumerable:!0,get:()=>NULL},implementation:{enumerable:!0,get:()=>null,set(e){}},returnType:{enumerable:!0,get(){const e=this.holder;return e.$f.use(e.$n)}},argumentTypes:{enumerable:!0,get:()=>[]},canInvokeWith:{enumerable:!0,value:e=>0===e.length},clone:{enumerable:!0,value(e){throw new Error("Invalid operation")}}}),Object.defineProperties(zo.prototype,{value:{enumerable:!0,get(){const[e,t,n,r,o]=this._p,i=vo.getEnv();i.pushLocalFrame(4);let a=null;try{let s;if(2===t){if(s=e.$getHandle(),null===s)throw new Error("Cannot access an instance field without an instance")}else a=e.$borrowClassHandle(i),s=a.value;const l=o(i.handle,s,r);return i.throwIfExceptionPending(),n.fromJni(l,i,!0)}finally{null!==a&&a.unref(i),i.popLocalFrame(NULL)}},set(e){const[t,n,r,o,,i]=this._p,a=vo.getEnv();a.pushLocalFrame(4);let s=null;try{let l;if(2===n){if(l=t.$getHandle(),null===l)throw new Error("Cannot access an instance field without an instance")}else s=t.$borrowClassHandle(a),l=s.value;if(!r.isCompatible(e))throw new Error(`Expected value compatible with ${r.className}`);const c=r.toJni(e,a);i(a.handle,l,o,c),a.throwIfExceptionPending()}finally{null!==s&&s.unref(a),a.popLocalFrame(NULL)}}},holder:{enumerable:!0,get(){return this._p[0]}},fieldType:{enumerable:!0,get(){return this._p[1]}},fieldReturnType:{enumerable:!0,get(){return this._p[2]}},toString:{enumerable:!0,value(){const e=`Java.Field{holder: ${this.holder}, fieldType: ${this.fieldType}, fieldReturnType: ${this.fieldReturnType}, value: ${this.value}}`;if(e.length<200)return e;return`Java.Field{\n\tholder: ${this.holder},\n\tfieldType: ${this.fieldType},\n\tfieldReturnType: ${this.fieldReturnType},\n\tvalue: ${this.value},\n}`.split("\n").map(e=>e.length>200?e.slice(0,e.indexOf(" ")+1)+"...,":e).join("\n")}}});class $o{static fromBuffer(e,t){const n=Jo(t),r=n.getCanonicalPath().toString(),o=new File(r,"w");return o.write(e.buffer),o.close(),function(e,t){const n=t.use("java.io.File"),r=n.$new(e);r.setWritable(!1,!1)}(r,t),new $o(r,n,t)}constructor(e,t,n){this.path=e,this.file=t,this._factory=n}load(){const{_factory:e}=this,{codeCacheDir:t}=e,n=e.use("dalvik.system.DexClassLoader"),r=e.use("java.io.File");let o=this.file;if(null===o&&(o=e.use("java.io.File").$new(this.path)),!o.exists())throw new Error("File not found");r.$new(t).mkdirs(),e.loader=n.$new(o.getCanonicalPath(),t,null,e.loader),vo.preventDetachDueToClassLoader()}getClassNames(){const{_factory:e}=this,t=e.use("dalvik.system.DexFile"),n=Jo(e),r=[],o=t.loadDex(this.path,n.getCanonicalPath(),0).entries();for(;o.hasMoreElements();)r.push(o.nextElement().toString());return r}}function Jo(e){const{cacheDir:t,tempFileNaming:n}=e,r=e.use("java.io.File"),o=r.$new(t);return o.mkdirs(),r.createTempFile(n.prefix,n.suffix+".dex",o)}function Vo(e,t){const{factories:n,loaders:r,Integer:o}=yo,i=o.$new(n.indexOf(e));r.put(t,i);for(let e=t.getParent();null!==e&&!r.containsKey(e);e=e.getParent())r.put(e,i)}function Bo(e){let t=jo.get(e);void 0===t&&(t=0),t++,jo.set(e,t)}function Go(e){let t=jo.get(e);if(void 0===t)throw new Error(`Thread ${e} is not ignored`);t--,0===t?jo.delete(e):jo.set(e,t)}function Zo(e,t){const n=[],r=e.getArrayLength(t);for(let o=0;o!==r;o++){const r=e.getObjectArrayElement(t,o);try{n.push(e.getTypeName(r))}finally{e.deleteLocalRef(r)}}return n}function Ho(e){const t=e.split(".");return t[t.length-1]+".java"}const qo=Process.pointerSize;function Wo(e,t){const n=e.use("android.os.Process");e.loader=t.getClassLoader(),n.myUid()===n.SYSTEM_UID.value?(e.cacheDir="/data/system",e.codeCacheDir="/data/dalvik-cache"):"getCodeCacheDir"in t?(e.cacheDir=t.getCacheDir().getCanonicalPath(),e.codeCacheDir=t.getCodeCacheDir().getCanonicalPath()):(e.cacheDir=t.getFilesDir().getCanonicalPath(),e.codeCacheDir=t.getCacheDir().getCanonicalPath())}function Ko(e,t){const n=e.use("java.io.File");e.loader=t.getClassLoader();const r=n.$new(t.getDataDir()).getCanonicalPath();e.cacheDir=r,e.codeCacheDir=r+"/cache"}const Qo=new class{ACC_PUBLIC=1;ACC_PRIVATE=2;ACC_PROTECTED=4;ACC_STATIC=8;ACC_FINAL=16;ACC_SYNCHRONIZED=32;ACC_BRIDGE=64;ACC_VARARGS=128;ACC_NATIVE=256;ACC_ABSTRACT=1024;ACC_STRICT=2048;ACC_SYNTHETIC=4096;constructor(){this.classFactory=null,this.ClassFactory=Io,this.vm=null,this.api=null,this._initialized=!1,this._apiError=null,this._wakeupHandler=null,this._pollListener=null,this._pendingMainOps=[],this._pendingVmOps=[],this._cachedIsAppProcess=null;try{this._tryInitialize()}catch(e){}}_tryInitialize(){if(this._initialized)return!0;if(null!==this._apiError)throw this._apiError;let e;try{e=Rn(),this.api=e}catch(e){throw this._apiError=e,e}if(null===e)return!1;const t=new Q(e);return this.vm=t,eo=t,Io._initialize(t,e),this.classFactory=new Io,this._initialized=!0,!0}_dispose(){if(null===this.api)return;const{vm:e}=this;e.perform(e=>{Io._disposeAll(e),f.dispose(e)}),Script.nextTick(()=>{Q.dispose(e)})}get available(){return this._tryInitialize()}get androidVersion(){return me()}synchronized(e,t){const{$h:n=e}=e;if(!(n instanceof NativePointer))throw new Error("Java.synchronized: the first argument `obj` must be either a pointer or a Java instance");const r=this.vm.getEnv();o("VM::MonitorEnter",r.monitorEnter(n));try{t()}finally{r.monitorExit(n)}}enumerateLoadedClasses(e){this._checkAvailable();const{flavor:t}=this.api;"jvm"===t?this._enumerateLoadedClassesJvm(e):"art"===t?this._enumerateLoadedClassesArt(e):this._enumerateLoadedClassesDalvik(e)}enumerateLoadedClassesSync(){const e=[];return this.enumerateLoadedClasses({onMatch(t){e.push(t)},onComplete(){}}),e}enumerateClassLoaders(e){this._checkAvailable();const{flavor:t}=this.api;if("jvm"===t)this._enumerateClassLoadersJvm(e);else{if("art"!==t)throw new Error("Enumerating class loaders is not supported on Dalvik");this._enumerateClassLoadersArt(e)}}enumerateClassLoadersSync(){const e=[];return this.enumerateClassLoaders({onMatch(t){e.push(t)},onComplete(){}}),e}_enumerateLoadedClassesJvm(e){const{api:t,vm:n}=this,{jvmti:r}=t,o=n.getEnv(),i=Memory.alloc(4),a=Memory.alloc(qo);r.getLoadedClasses(i,a);const s=i.readS32(),l=a.readPointer(),c=[];for(let e=0;e!==s;e++)c.push(l.add(e*qo).readPointer());r.deallocate(l);try{for(const t of c){const n=o.getClassName(t);e.onMatch(n,t)}e.onComplete()}finally{c.forEach(e=>{o.deleteLocalRef(e)})}}_enumerateClassLoadersJvm(e){this.choose("java.lang.ClassLoader",e)}_enumerateLoadedClassesArt(e){const{vm:t,api:n}=this,r=t.getEnv(),o=n["art::JavaVMExt::AddGlobalRef"],{vm:i}=n;Ye(t,r,t=>{const a=rt(n=>{const a=o(i,t,n);try{const t=r.getClassName(a);e.onMatch(t,a)}finally{r.deleteGlobalRef(a)}return!0});n["art::ClassLinker::VisitClasses"](n.artClassLinker.address,a)}),e.onComplete()}_enumerateClassLoadersArt(e){const{classFactory:t,vm:n,api:r}=this,o=n.getEnv(),i=r["art::ClassLinker::VisitClassLoaders"];if(void 0===i)throw new Error("This API is only available on Android >= 7.0");const a=t.use("java.lang.ClassLoader"),s=[],l=r["art::JavaVMExt::AddGlobalRef"],{vm:c}=r;Ye(n,o,e=>{const t=it(t=>(s.push(l(c,e,t)),!0));tt(()=>{i(r.artClassLinker.address,t)})});try{s.forEach(n=>{const r=t.cast(n,a);e.onMatch(r)})}finally{s.forEach(e=>{o.deleteGlobalRef(e)})}e.onComplete()}_enumerateLoadedClassesDalvik(e){const{api:t}=this,n=ptr("0xcbcacccd"),r=t.gDvm.add(172).readPointer(),o=r.readS32(),i=r.add(12).readPointer(),a=8*o;for(let t=0;t<a;t+=8){const r=i.add(t).add(4).readPointer();if(r.isNull()||r.equals(n))continue;const o=r.add(24).readPointer().readUtf8String();if(o.startsWith("L")){const t=o.substring(1,o.length-1).replace(/\//g,".");e.onMatch(t)}}e.onComplete()}enumerateMethods(e){const{classFactory:t}=this,n=this.vm.getEnv(),r=t.use("java.lang.ClassLoader");return Un.enumerateMethods(e,this.api,n).map(e=>{const o=e.loader;return e.loader=null!==o?t.wrap(o,r,n):null,e})}scheduleOnMainThread(e){this.performNow(()=>{this._pendingMainOps.push(e);let{_wakeupHandler:t}=this;if(null===t){const{classFactory:e}=this,n=e.use("android.os.Handler"),r=e.use("android.os.Looper");t=n.$new(r.getMainLooper()),this._wakeupHandler=t}null===this._pollListener&&(this._pollListener=Interceptor.attach(Process.getModuleByName("libc.so").getExportByName("epoll_wait"),this._makePollHook()),Interceptor.flush()),t.sendEmptyMessage(1)})}_makePollHook(){const e=Process.id,{_pendingMainOps:t}=this;return function(){if(this.threadId!==e)return;let n;for(;void 0!==(n=t.shift());)try{n()}catch(e){Script.nextTick(()=>{throw e})}}}perform(e){if(this._checkAvailable(),this._isAppProcess()&&null===this.classFactory.loader)this._pendingVmOps.push(e),1===this._pendingVmOps.length&&this._performPendingVmOpsWhenReady();else try{this.vm.perform(e)}catch(e){Script.nextTick(()=>{throw e})}}performNow(e){return this._checkAvailable(),this.vm.perform(()=>{const{classFactory:t}=this;if(this._isAppProcess()&&null===t.loader){const e=t.use("android.app.ActivityThread").currentApplication();null!==e&&Wo(t,e)}return e()})}_performPendingVmOpsWhenReady(){this.vm.perform(()=>{const{classFactory:e}=this,t=e.use("android.app.ActivityThread"),n=t.currentApplication();if(null!==n)return Wo(e,n),void this._performPendingVmOps();const r=this;let o=!1,i="early";const a=t.handleBindApplication;a.implementation=function(t){if(null!==t.instrumentationName.value){i="late";const t=e.use("android.app.LoadedApk").makeApplication;t.implementation=function(n,i){return o||(o=!0,Ko(e,this),r._performPendingVmOps()),t.apply(this,arguments)}}a.apply(this,arguments)};const s=t.getPackageInfo.overloads.map(e=>[e.argumentTypes.length,e]).sort(([e],[t])=>t-e).map(([e,t])=>t)[0];s.implementation=function(...t){const n=s.call(this,...t);return o||"early"!==i||(o=!0,Ko(e,n),r._performPendingVmOps()),n}})}_performPendingVmOps(){const{vm:e,_pendingVmOps:t}=this;let n;for(;void 0!==(n=t.shift());)try{e.perform(n)}catch(e){Script.nextTick(()=>{throw e})}}use(e,t){return this.classFactory.use(e,t)}openClassFile(e){return this.classFactory.openClassFile(e)}choose(e,t){this.classFactory.choose(e,t)}retain(e){return this.classFactory.retain(e)}cast(e,t){return this.classFactory.cast(e,t)}array(e,t){return this.classFactory.array(e,t)}backtrace(e){return gt(this.vm,e)}isMainThread(){const e=this.classFactory.use("android.os.Looper"),t=e.getMainLooper(),n=e.myLooper();return null!==n&&t.$isSameObject(n)}registerClass(e){return this.classFactory.registerClass(e)}deoptimizeEverything(){const{vm:e}=this;return Pt(e,e.getEnv())}deoptimizeBootImage(){const{vm:e}=this;return xt(e,e.getEnv())}deoptimizeMethod(e){const{vm:t}=this;return At(t,t.getEnv(),e)}_checkAvailable(){if(!this.available)throw new Error("Java API not available")}_isAppProcess(){let e=this._cachedIsAppProcess;if(null===e){if("jvm"===this.api.flavor)return e=!1,this._cachedIsAppProcess=e,e;const t=new NativeFunction(Module.getGlobalExportByName("readlink"),"pointer",["pointer","pointer","pointer"],{exceptions:"propagate"}),n=Memory.allocUtf8String("/proc/self/exe"),r=1024,o=Memory.alloc(r),i=t(n,o,ptr(r)).toInt32();if(-1!==i){const t=o.readUtf8String(i);e=/^\/system\/bin\/app_process/.test(t)}else e=!0;this._cachedIsAppProcess=e}return e}};return Script.bindWeak(Qo,()=>{Qo._dispose()}),Qo}();
