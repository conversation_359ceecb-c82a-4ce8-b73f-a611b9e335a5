"""
    pygments.lexers._scheme_builtins
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

    Scheme builtins.

    :copyright: Copyright 2006-2025 by the Pygments team, see AUTHORS.
    :license: BSD, see LICENSE for details.
"""

# Autogenerated by external/scheme-builtins-generator.scm
# using Guile 3.0.5.130-5a1e7.

scheme_keywords = {
    "*unspecified*",
    "...",
    "=>",
    "@",
    "@@",
    "_",
    "add-to-load-path",
    "and",
    "begin",
    "begin-deprecated",
    "case",
    "case-lambda",
    "case-lambda*",
    "cond",
    "cond-expand",
    "current-filename",
    "current-source-location",
    "debug-set!",
    "define",
    "define*",
    "define-inlinable",
    "define-library",
    "define-macro",
    "define-module",
    "define-once",
    "define-option-interface",
    "define-private",
    "define-public",
    "define-record-type",
    "define-syntax",
    "define-syntax-parameter",
    "define-syntax-rule",
    "define-values",
    "defmacro",
    "defmacro-public",
    "delay",
    "do",
    "else",
    "eval-when",
    "export",
    "export!",
    "export-syntax",
    "false-if-exception",
    "identifier-syntax",
    "if",
    "import",
    "include",
    "include-ci",
    "include-from-path",
    "include-library-declarations",
    "lambda",
    "lambda*",
    "let",
    "let*",
    "let*-values",
    "let-syntax",
    "let-values",
    "letrec",
    "letrec*",
    "letrec-syntax",
    "library",
    "load",
    "match",
    "match-lambda",
    "match-lambda*",
    "match-let",
    "match-let*",
    "match-letrec",
    "or",
    "parameterize",
    "print-set!",
    "quasiquote",
    "quasisyntax",
    "quote",
    "quote-syntax",
    "re-export",
    "re-export-syntax",
    "read-set!",
    "require-extension",
    "set!",
    "start-stack",
    "syntax",
    "syntax-case",
    "syntax-error",
    "syntax-parameterize",
    "syntax-rules",
    "unless",
    "unquote",
    "unquote-splicing",
    "unsyntax",
    "unsyntax-splicing",
    "use-modules",
    "when",
    "while",
    "with-ellipsis",
    "with-fluids",
    "with-syntax",
    "λ",
}

scheme_builtins = {
    "$sc-dispatch",
    "%char-set-dump",
    "%get-pre-modules-obarray",
    "%get-stack-size",
    "%global-site-dir",
    "%init-rdelim-builtins",
    "%init-rw-builtins",
    "%library-dir",
    "%load-announce",
    "%load-hook",
    "%make-void-port",
    "%package-data-dir",
    "%port-property",
    "%print-module",
    "%resolve-variable",
    "%search-load-path",
    "%set-port-property!",
    "%site-ccache-dir",
    "%site-dir",
    "%start-stack",
    "%string-dump",
    "%symbol-dump",
    "%warn-auto-compilation-enabled",
    "*",
    "+",
    "-",
    "->bool",
    "->char-set",
    "/",
    "1+",
    "1-",
    "<",
    "<=",
    "=",
    ">",
    ">=",
    "abort-to-prompt",
    "abort-to-prompt*",
    "abs",
    "absolute-file-name?",
    "accept",
    "access?",
    "acons",
    "acos",
    "acosh",
    "add-hook!",
    "addrinfo:addr",
    "addrinfo:canonname",
    "addrinfo:fam",
    "addrinfo:flags",
    "addrinfo:protocol",
    "addrinfo:socktype",
    "adjust-port-revealed!",
    "alarm",
    "alist-cons",
    "alist-copy",
    "alist-delete",
    "alist-delete!",
    "allocate-struct",
    "and-map",
    "and=>",
    "angle",
    "any",
    "append",
    "append!",
    "append-map",
    "append-map!",
    "append-reverse",
    "append-reverse!",
    "apply",
    "array->list",
    "array-cell-ref",
    "array-cell-set!",
    "array-contents",
    "array-copy!",
    "array-copy-in-order!",
    "array-dimensions",
    "array-equal?",
    "array-fill!",
    "array-for-each",
    "array-in-bounds?",
    "array-index-map!",
    "array-length",
    "array-map!",
    "array-map-in-order!",
    "array-rank",
    "array-ref",
    "array-set!",
    "array-shape",
    "array-slice",
    "array-slice-for-each",
    "array-slice-for-each-in-order",
    "array-type",
    "array-type-code",
    "array?",
    "ash",
    "asin",
    "asinh",
    "assert-load-verbosity",
    "assoc",
    "assoc-ref",
    "assoc-remove!",
    "assoc-set!",
    "assq",
    "assq-ref",
    "assq-remove!",
    "assq-set!",
    "assv",
    "assv-ref",
    "assv-remove!",
    "assv-set!",
    "atan",
    "atanh",
    "autoload-done!",
    "autoload-done-or-in-progress?",
    "autoload-in-progress!",
    "backtrace",
    "basename",
    "batch-mode?",
    "beautify-user-module!",
    "bind",
    "bind-textdomain-codeset",
    "bindtextdomain",
    "bit-count",
    "bit-count*",
    "bit-extract",
    "bit-invert!",
    "bit-position",
    "bit-set*!",
    "bitvector",
    "bitvector->list",
    "bitvector-bit-clear?",
    "bitvector-bit-set?",
    "bitvector-clear-all-bits!",
    "bitvector-clear-bit!",
    "bitvector-clear-bits!",
    "bitvector-count",
    "bitvector-count-bits",
    "bitvector-fill!",
    "bitvector-flip-all-bits!",
    "bitvector-length",
    "bitvector-position",
    "bitvector-ref",
    "bitvector-set!",
    "bitvector-set-all-bits!",
    "bitvector-set-bit!",
    "bitvector-set-bits!",
    "bitvector?",
    "boolean?",
    "bound-identifier=?",
    "break",
    "break!",
    "caaaar",
    "caaadr",
    "caaar",
    "caadar",
    "caaddr",
    "caadr",
    "caar",
    "cadaar",
    "cadadr",
    "cadar",
    "caddar",
    "cadddr",
    "caddr",
    "cadr",
    "call-with-blocked-asyncs",
    "call-with-current-continuation",
    "call-with-deferred-observers",
    "call-with-include-port",
    "call-with-input-file",
    "call-with-input-string",
    "call-with-module-autoload-lock",
    "call-with-output-file",
    "call-with-output-string",
    "call-with-port",
    "call-with-prompt",
    "call-with-unblocked-asyncs",
    "call-with-values",
    "call/cc",
    "canonicalize-path",
    "car",
    "car+cdr",
    "catch",
    "cdaaar",
    "cdaadr",
    "cdaar",
    "cdadar",
    "cdaddr",
    "cdadr",
    "cdar",
    "cddaar",
    "cddadr",
    "cddar",
    "cdddar",
    "cddddr",
    "cdddr",
    "cddr",
    "cdr",
    "ceiling",
    "ceiling-quotient",
    "ceiling-remainder",
    "ceiling/",
    "centered-quotient",
    "centered-remainder",
    "centered/",
    "char->integer",
    "char-alphabetic?",
    "char-ci<=?",
    "char-ci<?",
    "char-ci=?",
    "char-ci>=?",
    "char-ci>?",
    "char-downcase",
    "char-general-category",
    "char-is-both?",
    "char-lower-case?",
    "char-numeric?",
    "char-ready?",
    "char-set",
    "char-set->list",
    "char-set->string",
    "char-set-adjoin",
    "char-set-adjoin!",
    "char-set-any",
    "char-set-complement",
    "char-set-complement!",
    "char-set-contains?",
    "char-set-copy",
    "char-set-count",
    "char-set-cursor",
    "char-set-cursor-next",
    "char-set-delete",
    "char-set-delete!",
    "char-set-diff+intersection",
    "char-set-diff+intersection!",
    "char-set-difference",
    "char-set-difference!",
    "char-set-every",
    "char-set-filter",
    "char-set-filter!",
    "char-set-fold",
    "char-set-for-each",
    "char-set-hash",
    "char-set-intersection",
    "char-set-intersection!",
    "char-set-map",
    "char-set-ref",
    "char-set-size",
    "char-set-unfold",
    "char-set-unfold!",
    "char-set-union",
    "char-set-union!",
    "char-set-xor",
    "char-set-xor!",
    "char-set<=",
    "char-set=",
    "char-set?",
    "char-titlecase",
    "char-upcase",
    "char-upper-case?",
    "char-whitespace?",
    "char<=?",
    "char<?",
    "char=?",
    "char>=?",
    "char>?",
    "char?",
    "chdir",
    "chmod",
    "chown",
    "chroot",
    "circular-list",
    "circular-list?",
    "close",
    "close-fdes",
    "close-input-port",
    "close-output-port",
    "close-port",
    "closedir",
    "command-line",
    "complex?",
    "compose",
    "concatenate",
    "concatenate!",
    "cond-expand-provide",
    "connect",
    "cons",
    "cons*",
    "cons-source",
    "const",
    "convert-assignment",
    "copy-file",
    "copy-random-state",
    "copy-tree",
    "cos",
    "cosh",
    "count",
    "crypt",
    "ctermid",
    "current-dynamic-state",
    "current-error-port",
    "current-input-port",
    "current-language",
    "current-load-port",
    "current-module",
    "current-output-port",
    "current-time",
    "current-warning-port",
    "datum->random-state",
    "datum->syntax",
    "debug-disable",
    "debug-enable",
    "debug-options",
    "debug-options-interface",
    "default-duplicate-binding-handler",
    "default-duplicate-binding-procedures",
    "default-prompt-tag",
    "define!",
    "define-module*",
    "defined?",
    "delete",
    "delete!",
    "delete-duplicates",
    "delete-duplicates!",
    "delete-file",
    "delete1!",
    "delq",
    "delq!",
    "delq1!",
    "delv",
    "delv!",
    "delv1!",
    "denominator",
    "directory-stream?",
    "dirname",
    "display",
    "display-application",
    "display-backtrace",
    "display-error",
    "dotted-list?",
    "doubly-weak-hash-table?",
    "drain-input",
    "drop",
    "drop-right",
    "drop-right!",
    "drop-while",
    "dup",
    "dup->fdes",
    "dup->inport",
    "dup->outport",
    "dup->port",
    "dup2",
    "duplicate-port",
    "dynamic-call",
    "dynamic-func",
    "dynamic-link",
    "dynamic-object?",
    "dynamic-pointer",
    "dynamic-state?",
    "dynamic-unlink",
    "dynamic-wind",
    "effective-version",
    "eighth",
    "end-of-char-set?",
    "endgrent",
    "endhostent",
    "endnetent",
    "endprotoent",
    "endpwent",
    "endservent",
    "ensure-batch-mode!",
    "environ",
    "eof-object?",
    "eq?",
    "equal?",
    "eqv?",
    "error",
    "euclidean-quotient",
    "euclidean-remainder",
    "euclidean/",
    "eval",
    "eval-string",
    "even?",
    "every",
    "exact->inexact",
    "exact-integer-sqrt",
    "exact-integer?",
    "exact?",
    "exception-accessor",
    "exception-args",
    "exception-kind",
    "exception-predicate",
    "exception-type?",
    "exception?",
    "execl",
    "execle",
    "execlp",
    "exit",
    "exp",
    "expt",
    "f32vector",
    "f32vector->list",
    "f32vector-length",
    "f32vector-ref",
    "f32vector-set!",
    "f32vector?",
    "f64vector",
    "f64vector->list",
    "f64vector-length",
    "f64vector-ref",
    "f64vector-set!",
    "f64vector?",
    "fcntl",
    "fdes->inport",
    "fdes->outport",
    "fdes->ports",
    "fdopen",
    "fifth",
    "file-encoding",
    "file-exists?",
    "file-is-directory?",
    "file-name-separator?",
    "file-port?",
    "file-position",
    "file-set-position",
    "fileno",
    "filter",
    "filter!",
    "filter-map",
    "find",
    "find-tail",
    "finite?",
    "first",
    "flock",
    "floor",
    "floor-quotient",
    "floor-remainder",
    "floor/",
    "fluid->parameter",
    "fluid-bound?",
    "fluid-ref",
    "fluid-ref*",
    "fluid-set!",
    "fluid-thread-local?",
    "fluid-unset!",
    "fluid?",
    "flush-all-ports",
    "fold",
    "fold-right",
    "for-each",
    "force",
    "force-output",
    "format",
    "fourth",
    "frame-address",
    "frame-arguments",
    "frame-dynamic-link",
    "frame-instruction-pointer",
    "frame-previous",
    "frame-procedure-name",
    "frame-return-address",
    "frame-source",
    "frame-stack-pointer",
    "frame?",
    "free-identifier=?",
    "fsync",
    "ftell",
    "gai-strerror",
    "gc",
    "gc-disable",
    "gc-dump",
    "gc-enable",
    "gc-run-time",
    "gc-stats",
    "gcd",
    "generate-temporaries",
    "gensym",
    "get-internal-real-time",
    "get-internal-run-time",
    "get-output-string",
    "get-print-state",
    "getaddrinfo",
    "getaffinity",
    "getcwd",
    "getegid",
    "getenv",
    "geteuid",
    "getgid",
    "getgr",
    "getgrent",
    "getgrgid",
    "getgrnam",
    "getgroups",
    "gethost",
    "gethostbyaddr",
    "gethostbyname",
    "gethostent",
    "gethostname",
    "getitimer",
    "getlogin",
    "getnet",
    "getnetbyaddr",
    "getnetbyname",
    "getnetent",
    "getpass",
    "getpeername",
    "getpgrp",
    "getpid",
    "getppid",
    "getpriority",
    "getproto",
    "getprotobyname",
    "getprotobynumber",
    "getprotoent",
    "getpw",
    "getpwent",
    "getpwnam",
    "getpwuid",
    "getrlimit",
    "getserv",
    "getservbyname",
    "getservbyport",
    "getservent",
    "getsid",
    "getsockname",
    "getsockopt",
    "gettext",
    "gettimeofday",
    "getuid",
    "gmtime",
    "group:gid",
    "group:mem",
    "group:name",
    "group:passwd",
    "hash",
    "hash-clear!",
    "hash-count",
    "hash-create-handle!",
    "hash-fold",
    "hash-for-each",
    "hash-for-each-handle",
    "hash-get-handle",
    "hash-map->list",
    "hash-ref",
    "hash-remove!",
    "hash-set!",
    "hash-table?",
    "hashq",
    "hashq-create-handle!",
    "hashq-get-handle",
    "hashq-ref",
    "hashq-remove!",
    "hashq-set!",
    "hashv",
    "hashv-create-handle!",
    "hashv-get-handle",
    "hashv-ref",
    "hashv-remove!",
    "hashv-set!",
    "hashx-create-handle!",
    "hashx-get-handle",
    "hashx-ref",
    "hashx-remove!",
    "hashx-set!",
    "hook->list",
    "hook-empty?",
    "hook?",
    "hostent:addr-list",
    "hostent:addrtype",
    "hostent:aliases",
    "hostent:length",
    "hostent:name",
    "identifier?",
    "identity",
    "imag-part",
    "in-vicinity",
    "include-deprecated-features",
    "inet-lnaof",
    "inet-makeaddr",
    "inet-netof",
    "inet-ntop",
    "inet-pton",
    "inexact->exact",
    "inexact?",
    "inf",
    "inf?",
    "inherit-print-state",
    "input-port?",
    "install-r6rs!",
    "install-r7rs!",
    "integer->char",
    "integer-expt",
    "integer-length",
    "integer?",
    "interaction-environment",
    "iota",
    "isatty?",
    "issue-deprecation-warning",
    "keyword->symbol",
    "keyword-like-symbol->keyword",
    "keyword?",
    "kill",
    "kw-arg-ref",
    "last",
    "last-pair",
    "lcm",
    "length",
    "length+",
    "link",
    "list",
    "list->array",
    "list->bitvector",
    "list->char-set",
    "list->char-set!",
    "list->f32vector",
    "list->f64vector",
    "list->s16vector",
    "list->s32vector",
    "list->s64vector",
    "list->s8vector",
    "list->string",
    "list->symbol",
    "list->typed-array",
    "list->u16vector",
    "list->u32vector",
    "list->u64vector",
    "list->u8vector",
    "list->vector",
    "list-cdr-ref",
    "list-cdr-set!",
    "list-copy",
    "list-head",
    "list-index",
    "list-ref",
    "list-set!",
    "list-tabulate",
    "list-tail",
    "list=",
    "list?",
    "listen",
    "load-compiled",
    "load-extension",
    "load-from-path",
    "load-in-vicinity",
    "load-user-init",
    "local-define",
    "local-define-module",
    "local-ref",
    "local-ref-module",
    "local-remove",
    "local-set!",
    "localtime",
    "log",
    "log10",
    "logand",
    "logbit?",
    "logcount",
    "logior",
    "lognot",
    "logtest",
    "logxor",
    "lookup-duplicates-handlers",
    "lset-adjoin",
    "lset-diff+intersection",
    "lset-diff+intersection!",
    "lset-difference",
    "lset-difference!",
    "lset-intersection",
    "lset-intersection!",
    "lset-union",
    "lset-union!",
    "lset-xor",
    "lset-xor!",
    "lset<=",
    "lset=",
    "lstat",
    "macro-binding",
    "macro-name",
    "macro-transformer",
    "macro-type",
    "macro?",
    "macroexpand",
    "macroexpanded?",
    "magnitude",
    "major-version",
    "make-array",
    "make-autoload-interface",
    "make-bitvector",
    "make-doubly-weak-hash-table",
    "make-exception",
    "make-exception-from-throw",
    "make-exception-type",
    "make-f32vector",
    "make-f64vector",
    "make-fluid",
    "make-fresh-user-module",
    "make-generalized-vector",
    "make-guardian",
    "make-hash-table",
    "make-hook",
    "make-list",
    "make-module",
    "make-modules-in",
    "make-mutable-parameter",
    "make-object-property",
    "make-parameter",
    "make-polar",
    "make-procedure-with-setter",
    "make-promise",
    "make-prompt-tag",
    "make-record-type",
    "make-rectangular",
    "make-regexp",
    "make-s16vector",
    "make-s32vector",
    "make-s64vector",
    "make-s8vector",
    "make-shared-array",
    "make-socket-address",
    "make-soft-port",
    "make-srfi-4-vector",
    "make-stack",
    "make-string",
    "make-struct-layout",
    "make-struct/no-tail",
    "make-struct/simple",
    "make-symbol",
    "make-syntax-transformer",
    "make-thread-local-fluid",
    "make-typed-array",
    "make-u16vector",
    "make-u32vector",
    "make-u64vector",
    "make-u8vector",
    "make-unbound-fluid",
    "make-undefined-variable",
    "make-variable",
    "make-variable-transformer",
    "make-vector",
    "make-vtable",
    "make-weak-key-hash-table",
    "make-weak-value-hash-table",
    "map",
    "map!",
    "map-in-order",
    "max",
    "member",
    "memoize-expression",
    "memoized-typecode",
    "memq",
    "memv",
    "merge",
    "merge!",
    "micro-version",
    "min",
    "minor-version",
    "mkdir",
    "mkdtemp",
    "mknod",
    "mkstemp",
    "mkstemp!",
    "mktime",
    "module-add!",
    "module-autoload!",
    "module-binder",
    "module-bound?",
    "module-call-observers",
    "module-clear!",
    "module-constructor",
    "module-declarative?",
    "module-defer-observers",
    "module-define!",
    "module-define-submodule!",
    "module-defined?",
    "module-duplicates-handlers",
    "module-ensure-local-variable!",
    "module-export!",
    "module-export-all!",
    "module-filename",
    "module-for-each",
    "module-generate-unique-id!",
    "module-gensym",
    "module-import-interface",
    "module-import-obarray",
    "module-kind",
    "module-local-variable",
    "module-locally-bound?",
    "module-make-local-var!",
    "module-map",
    "module-modified",
    "module-name",
    "module-next-unique-id",
    "module-obarray",
    "module-obarray-get-handle",
    "module-obarray-ref",
    "module-obarray-remove!",
    "module-obarray-set!",
    "module-observe",
    "module-observe-weak",
    "module-observers",
    "module-public-interface",
    "module-re-export!",
    "module-ref",
    "module-ref-submodule",
    "module-remove!",
    "module-replace!",
    "module-replacements",
    "module-reverse-lookup",
    "module-search",
    "module-set!",
    "module-submodule-binder",
    "module-submodules",
    "module-symbol-binding",
    "module-symbol-interned?",
    "module-symbol-local-binding",
    "module-symbol-locally-interned?",
    "module-transformer",
    "module-unobserve",
    "module-use!",
    "module-use-interfaces!",
    "module-uses",
    "module-variable",
    "module-version",
    "module-weak-observers",
    "module?",
    "modulo",
    "modulo-expt",
    "move->fdes",
    "nan",
    "nan?",
    "negate",
    "negative?",
    "nested-define!",
    "nested-define-module!",
    "nested-ref",
    "nested-ref-module",
    "nested-remove!",
    "nested-set!",
    "netent:addrtype",
    "netent:aliases",
    "netent:name",
    "netent:net",
    "newline",
    "ngettext",
    "nice",
    "nil?",
    "ninth",
    "noop",
    "not",
    "not-pair?",
    "null-environment",
    "null-list?",
    "null?",
    "number->string",
    "number?",
    "numerator",
    "object->string",
    "object-address",
    "object-properties",
    "object-property",
    "odd?",
    "open",
    "open-fdes",
    "open-file",
    "open-input-file",
    "open-input-string",
    "open-io-file",
    "open-output-file",
    "open-output-string",
    "opendir",
    "or-map",
    "output-port?",
    "pair-fold",
    "pair-fold-right",
    "pair-for-each",
    "pair?",
    "parameter-converter",
    "parameter-fluid",
    "parameter?",
    "parse-path",
    "parse-path-with-ellipsis",
    "partition",
    "partition!",
    "passwd:dir",
    "passwd:gecos",
    "passwd:gid",
    "passwd:name",
    "passwd:passwd",
    "passwd:shell",
    "passwd:uid",
    "pause",
    "peek",
    "peek-char",
    "pipe",
    "pk",
    "port->fdes",
    "port-closed?",
    "port-column",
    "port-conversion-strategy",
    "port-encoding",
    "port-filename",
    "port-for-each",
    "port-line",
    "port-mode",
    "port-revealed",
    "port-with-print-state",
    "port?",
    "positive?",
    "primitive-_exit",
    "primitive-eval",
    "primitive-exit",
    "primitive-fork",
    "primitive-load",
    "primitive-load-path",
    "primitive-move->fdes",
    "primitive-read",
    "print-disable",
    "print-enable",
    "print-exception",
    "print-options",
    "print-options-interface",
    "procedure",
    "procedure-documentation",
    "procedure-minimum-arity",
    "procedure-name",
    "procedure-properties",
    "procedure-property",
    "procedure-source",
    "procedure-with-setter?",
    "procedure?",
    "process-use-modules",
    "program-arguments",
    "promise?",
    "proper-list?",
    "protoent:aliases",
    "protoent:name",
    "protoent:proto",
    "provide",
    "provided?",
    "purify-module!",
    "putenv",
    "quit",
    "quotient",
    "raise",
    "raise-exception",
    "random",
    "random-state->datum",
    "random-state-from-platform",
    "random:exp",
    "random:hollow-sphere!",
    "random:normal",
    "random:normal-vector!",
    "random:solid-sphere!",
    "random:uniform",
    "rational?",
    "rationalize",
    "read",
    "read-char",
    "read-disable",
    "read-enable",
    "read-hash-extend",
    "read-hash-procedure",
    "read-hash-procedures",
    "read-options",
    "read-options-interface",
    "read-syntax",
    "readdir",
    "readlink",
    "real-part",
    "real?",
    "record-accessor",
    "record-constructor",
    "record-modifier",
    "record-predicate",
    "record-type-constructor",
    "record-type-descriptor",
    "record-type-extensible?",
    "record-type-fields",
    "record-type-has-parent?",
    "record-type-mutable-fields",
    "record-type-name",
    "record-type-opaque?",
    "record-type-parent",
    "record-type-parents",
    "record-type-properties",
    "record-type-uid",
    "record-type?",
    "record?",
    "recv!",
    "recvfrom!",
    "redirect-port",
    "reduce",
    "reduce-right",
    "regexp-exec",
    "regexp?",
    "release-port-handle",
    "reload-module",
    "remainder",
    "remove",
    "remove!",
    "remove-hook!",
    "rename-file",
    "repl-reader",
    "reset-hook!",
    "resolve-interface",
    "resolve-module",
    "resolve-r6rs-interface",
    "restore-signals",
    "restricted-vector-sort!",
    "reverse",
    "reverse!",
    "reverse-list->string",
    "rewinddir",
    "rmdir",
    "round",
    "round-ash",
    "round-quotient",
    "round-remainder",
    "round/",
    "run-hook",
    "s16vector",
    "s16vector->list",
    "s16vector-length",
    "s16vector-ref",
    "s16vector-set!",
    "s16vector?",
    "s32vector",
    "s32vector->list",
    "s32vector-length",
    "s32vector-ref",
    "s32vector-set!",
    "s32vector?",
    "s64vector",
    "s64vector->list",
    "s64vector-length",
    "s64vector-ref",
    "s64vector-set!",
    "s64vector?",
    "s8vector",
    "s8vector->list",
    "s8vector-length",
    "s8vector-ref",
    "s8vector-set!",
    "s8vector?",
    "save-module-excursion",
    "scheme-report-environment",
    "scm-error",
    "search-path",
    "second",
    "seed->random-state",
    "seek",
    "select",
    "self-evaluating?",
    "send",
    "sendfile",
    "sendto",
    "servent:aliases",
    "servent:name",
    "servent:port",
    "servent:proto",
    "set-autoloaded!",
    "set-car!",
    "set-cdr!",
    "set-current-dynamic-state",
    "set-current-error-port",
    "set-current-input-port",
    "set-current-module",
    "set-current-output-port",
    "set-exception-printer!",
    "set-module-binder!",
    "set-module-declarative?!",
    "set-module-duplicates-handlers!",
    "set-module-filename!",
    "set-module-kind!",
    "set-module-name!",
    "set-module-next-unique-id!",
    "set-module-obarray!",
    "set-module-observers!",
    "set-module-public-interface!",
    "set-module-submodule-binder!",
    "set-module-submodules!",
    "set-module-transformer!",
    "set-module-uses!",
    "set-module-version!",
    "set-object-properties!",
    "set-object-property!",
    "set-port-column!",
    "set-port-conversion-strategy!",
    "set-port-encoding!",
    "set-port-filename!",
    "set-port-line!",
    "set-port-revealed!",
    "set-procedure-minimum-arity!",
    "set-procedure-properties!",
    "set-procedure-property!",
    "set-program-arguments",
    "set-source-properties!",
    "set-source-property!",
    "set-struct-vtable-name!",
    "set-symbol-property!",
    "set-tm:gmtoff",
    "set-tm:hour",
    "set-tm:isdst",
    "set-tm:mday",
    "set-tm:min",
    "set-tm:mon",
    "set-tm:sec",
    "set-tm:wday",
    "set-tm:yday",
    "set-tm:year",
    "set-tm:zone",
    "setaffinity",
    "setegid",
    "setenv",
    "seteuid",
    "setgid",
    "setgr",
    "setgrent",
    "setgroups",
    "sethost",
    "sethostent",
    "sethostname",
    "setitimer",
    "setlocale",
    "setnet",
    "setnetent",
    "setpgid",
    "setpriority",
    "setproto",
    "setprotoent",
    "setpw",
    "setpwent",
    "setrlimit",
    "setserv",
    "setservent",
    "setsid",
    "setsockopt",
    "setter",
    "setuid",
    "setvbuf",
    "seventh",
    "shared-array-increments",
    "shared-array-offset",
    "shared-array-root",
    "shutdown",
    "sigaction",
    "simple-exceptions",
    "simple-format",
    "sin",
    "sinh",
    "sixth",
    "sleep",
    "sloppy-assoc",
    "sloppy-assq",
    "sloppy-assv",
    "sockaddr:addr",
    "sockaddr:fam",
    "sockaddr:flowinfo",
    "sockaddr:path",
    "sockaddr:port",
    "sockaddr:scopeid",
    "socket",
    "socketpair",
    "sort",
    "sort!",
    "sort-list",
    "sort-list!",
    "sorted?",
    "source-properties",
    "source-property",
    "span",
    "span!",
    "split-at",
    "split-at!",
    "sqrt",
    "stable-sort",
    "stable-sort!",
    "stack-id",
    "stack-length",
    "stack-ref",
    "stack?",
    "stat",
    "stat:atime",
    "stat:atimensec",
    "stat:blksize",
    "stat:blocks",
    "stat:ctime",
    "stat:ctimensec",
    "stat:dev",
    "stat:gid",
    "stat:ino",
    "stat:mode",
    "stat:mtime",
    "stat:mtimensec",
    "stat:nlink",
    "stat:perms",
    "stat:rdev",
    "stat:size",
    "stat:type",
    "stat:uid",
    "status:exit-val",
    "status:stop-sig",
    "status:term-sig",
    "strerror",
    "strftime",
    "string",
    "string->char-set",
    "string->char-set!",
    "string->list",
    "string->number",
    "string->symbol",
    "string-any",
    "string-any-c-code",
    "string-append",
    "string-append/shared",
    "string-bytes-per-char",
    "string-capitalize",
    "string-capitalize!",
    "string-ci->symbol",
    "string-ci<",
    "string-ci<=",
    "string-ci<=?",
    "string-ci<>",
    "string-ci<?",
    "string-ci=",
    "string-ci=?",
    "string-ci>",
    "string-ci>=",
    "string-ci>=?",
    "string-ci>?",
    "string-compare",
    "string-compare-ci",
    "string-concatenate",
    "string-concatenate-reverse",
    "string-concatenate-reverse/shared",
    "string-concatenate/shared",
    "string-contains",
    "string-contains-ci",
    "string-copy",
    "string-copy!",
    "string-count",
    "string-delete",
    "string-downcase",
    "string-downcase!",
    "string-drop",
    "string-drop-right",
    "string-every",
    "string-every-c-code",
    "string-fill!",
    "string-filter",
    "string-fold",
    "string-fold-right",
    "string-for-each",
    "string-for-each-index",
    "string-hash",
    "string-hash-ci",
    "string-index",
    "string-index-right",
    "string-join",
    "string-length",
    "string-map",
    "string-map!",
    "string-normalize-nfc",
    "string-normalize-nfd",
    "string-normalize-nfkc",
    "string-normalize-nfkd",
    "string-null?",
    "string-pad",
    "string-pad-right",
    "string-prefix-ci?",
    "string-prefix-length",
    "string-prefix-length-ci",
    "string-prefix?",
    "string-ref",
    "string-replace",
    "string-reverse",
    "string-reverse!",
    "string-rindex",
    "string-set!",
    "string-skip",
    "string-skip-right",
    "string-split",
    "string-suffix-ci?",
    "string-suffix-length",
    "string-suffix-length-ci",
    "string-suffix?",
    "string-tabulate",
    "string-take",
    "string-take-right",
    "string-titlecase",
    "string-titlecase!",
    "string-tokenize",
    "string-trim",
    "string-trim-both",
    "string-trim-right",
    "string-unfold",
    "string-unfold-right",
    "string-upcase",
    "string-upcase!",
    "string-utf8-length",
    "string-xcopy!",
    "string<",
    "string<=",
    "string<=?",
    "string<>",
    "string<?",
    "string=",
    "string=?",
    "string>",
    "string>=",
    "string>=?",
    "string>?",
    "string?",
    "strptime",
    "struct-layout",
    "struct-ref",
    "struct-ref/unboxed",
    "struct-set!",
    "struct-set!/unboxed",
    "struct-vtable",
    "struct-vtable-name",
    "struct-vtable?",
    "struct?",
    "substring",
    "substring-fill!",
    "substring-move!",
    "substring/copy",
    "substring/read-only",
    "substring/shared",
    "supports-source-properties?",
    "symbol",
    "symbol->keyword",
    "symbol->string",
    "symbol-append",
    "symbol-fref",
    "symbol-fset!",
    "symbol-hash",
    "symbol-interned?",
    "symbol-pref",
    "symbol-prefix-proc",
    "symbol-property",
    "symbol-property-remove!",
    "symbol-pset!",
    "symbol?",
    "symlink",
    "sync",
    "syntax->datum",
    "syntax-source",
    "syntax-violation",
    "system",
    "system*",
    "system-async-mark",
    "system-error-errno",
    "system-file-name-convention",
    "take",
    "take!",
    "take-right",
    "take-while",
    "take-while!",
    "tan",
    "tanh",
    "tcgetpgrp",
    "tcsetpgrp",
    "tenth",
    "textdomain",
    "third",
    "throw",
    "thunk?",
    "times",
    "tm:gmtoff",
    "tm:hour",
    "tm:isdst",
    "tm:mday",
    "tm:min",
    "tm:mon",
    "tm:sec",
    "tm:wday",
    "tm:yday",
    "tm:year",
    "tm:zone",
    "tmpfile",
    "tmpnam",
    "tms:clock",
    "tms:cstime",
    "tms:cutime",
    "tms:stime",
    "tms:utime",
    "transpose-array",
    "truncate",
    "truncate-file",
    "truncate-quotient",
    "truncate-remainder",
    "truncate/",
    "try-load-module",
    "try-module-autoload",
    "ttyname",
    "typed-array?",
    "tzset",
    "u16vector",
    "u16vector->list",
    "u16vector-length",
    "u16vector-ref",
    "u16vector-set!",
    "u16vector?",
    "u32vector",
    "u32vector->list",
    "u32vector-length",
    "u32vector-ref",
    "u32vector-set!",
    "u32vector?",
    "u64vector",
    "u64vector->list",
    "u64vector-length",
    "u64vector-ref",
    "u64vector-set!",
    "u64vector?",
    "u8vector",
    "u8vector->list",
    "u8vector-length",
    "u8vector-ref",
    "u8vector-set!",
    "u8vector?",
    "ucs-range->char-set",
    "ucs-range->char-set!",
    "umask",
    "uname",
    "unfold",
    "unfold-right",
    "unmemoize-expression",
    "unread-char",
    "unread-string",
    "unsetenv",
    "unspecified?",
    "unzip1",
    "unzip2",
    "unzip3",
    "unzip4",
    "unzip5",
    "use-srfis",
    "user-modules-declarative?",
    "using-readline?",
    "usleep",
    "utime",
    "utsname:machine",
    "utsname:nodename",
    "utsname:release",
    "utsname:sysname",
    "utsname:version",
    "values",
    "variable-bound?",
    "variable-ref",
    "variable-set!",
    "variable-unset!",
    "variable?",
    "vector",
    "vector->list",
    "vector-copy",
    "vector-fill!",
    "vector-length",
    "vector-move-left!",
    "vector-move-right!",
    "vector-ref",
    "vector-set!",
    "vector?",
    "version",
    "version-matches?",
    "waitpid",
    "warn",
    "weak-key-hash-table?",
    "weak-value-hash-table?",
    "with-continuation-barrier",
    "with-dynamic-state",
    "with-error-to-file",
    "with-error-to-port",
    "with-error-to-string",
    "with-exception-handler",
    "with-fluid*",
    "with-fluids*",
    "with-input-from-file",
    "with-input-from-port",
    "with-input-from-string",
    "with-output-to-file",
    "with-output-to-port",
    "with-output-to-string",
    "with-throw-handler",
    "write",
    "write-char",
    "xcons",
    "xsubstring",
    "zero?",
    "zip",
}

