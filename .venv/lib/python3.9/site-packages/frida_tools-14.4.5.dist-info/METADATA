Metadata-Version: 2.4
Name: frida-tools
Version: 14.4.5
Summary: Frida CLI tools
Home-page: https://frida.re
Author: Frida Developers
Author-email: <EMAIL>
License: wxWindows Library Licence, Version 3.1
Keywords: frida debugger dynamic instrumentation inject javascript windows macos linux ios iphone ipad android qnx
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Environment :: MacOS X
Classifier: Environment :: Win32 (MS Windows)
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved
Classifier: Natural Language :: English
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Operating System :: Microsoft :: Windows
Classifier: Operating System :: POSIX :: Linux
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: JavaScript
Classifier: Topic :: Software Development :: Debuggers
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Description-Content-Type: text/markdown
License-File: COPYING
Requires-Dist: colorama<1.0.0,>=0.2.7
Requires-Dist: frida<18.0.0,>=17.2.8
Requires-Dist: prompt-toolkit<4.0.0,>=2.0.0
Requires-Dist: pygments<3.0.0,>=2.0.2
Requires-Dist: websockets<14.0.0,>=13.0.0
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: license-file
Dynamic: requires-dist
Dynamic: summary

CLI tools for [Frida](https://frida.re).
