     ____
    / _  |   Frida 17.2.17 - A world-class dynamic instrumentation toolkit
   | (_| |
    > _  |   Commands:
   /_/ |_|       help      -> Displays the help system
   . . . .       object?   -> Display information about 'object'
   . . . .       exit/quit -> Exit
   . . . .
   . . . .   More info at https://frida.re/docs/home/
   . . . .
   . . . .   Connected to Android Emulator 5554 (id=emulator-5554)
Spawning `com.iloda.beacon`...
[+] ====== Ultimate SSL Bypass Starting ======
[+] ====== Ultimate SSL Bypass Ready ======
[+] All SSL/TLS verification should now be bypassed
[+] Monitor the console for intercepted traffic
Spawned `com.iloda.beacon`. Resuming main thread!
[Android Emulator 5554::com.iloda.beacon ]-> 
[Android Emulator 5554::com.iloda.beacon ]-> [+] Java层Hook开始...
[+] SSLContext hooked
[+] Native层Hook开始...
[+] Found libssl.so at: 0xd3d91000
[-] libssl.so hook failed: TypeError: not a function
[+] Found libcrypto.so at: 0xecbc4000
[-] libcrypto.so hook failed: TypeError: not a function
[+] Native层Hook完成
[+] Java层Hook完成
Process terminated

Thank you for using Frida!
