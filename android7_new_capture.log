[15:57:02.234] Loading script mitm_url_capture.py
[15:57:02.236] HTTP(S) proxy listening at *:8888.
[15:57:43.339][127.0.0.1:60112] client connect
[15:57:43.351][127.0.0.1:60112] server connect android.apis.google.com:443 (***********:443)
🔒 HTTPS: POST https://android.apis.google.com/c2dm/register3
   ❌ 400 Bad Request
127.0.0.1:60112: POST https://android.apis.google.com/c2dm/register3
              << 400 Bad Request 39b
[15:57:43.912][127.0.0.1:60118] client connect
[15:57:43.917][127.0.0.1:60118] server connect android.googleapis.com:443 (************:443)
🔒 HTTPS: POST https://android.googleapis.com/auth/devicekey
   ❌ 400 Bad Request
127.0.0.1:60118: POST https://android.googleapis.com/auth/devicekey
              << 400 Bad Request 801b
[16:01:43.900][127.0.0.1:60112] server disconnect android.apis.google.com:443 (***********:443)
[16:01:44.343][127.0.0.1:60118] server disconnect android.googleapis.com:443 (************:443)
[16:02:43.906][127.0.0.1:60112] client disconnect
[16:02:44.366][127.0.0.1:60118] client disconnect
[16:15:03.684][127.0.0.1:61243] client connect
[16:15:03.697][127.0.0.1:61243] server connect android.apis.google.com:443 (***********:443)
🔒 HTTPS: POST https://android.apis.google.com/c2dm/register3
   ❌ 400 Bad Request
127.0.0.1:61243: POST https://android.apis.google.com/c2dm/register3
              << 400 Bad Request 39b
[16:15:04.209][127.0.0.1:61246] client connect
[16:15:04.215][127.0.0.1:61246] server connect android.googleapis.com:443 (************:443)
🔒 HTTPS: POST https://android.googleapis.com/auth/devicekey
   ❌ 400 Bad Request
127.0.0.1:61246: POST https://android.googleapis.com/auth/devicekey
              << 400 Bad Request 801b
[16:19:04.188][127.0.0.1:61243] server disconnect android.apis.google.com:443 (***********:443)
[16:19:04.638][127.0.0.1:61246] server disconnect android.googleapis.com:443 (************:443)
[16:20:04.214][127.0.0.1:61243] client disconnect
[16:20:04.548][127.0.0.1:61246] client disconnect
[16:31:40.168][127.0.0.1:63130] client connect
[16:31:40.176][127.0.0.1:63131] client connect
[16:31:40.196][127.0.0.1:63132] client connect
[16:31:40.206][127.0.0.1:63131] server connect config.jpush.cn:443 (************:443)
[16:31:40.209][127.0.0.1:63130] server connect api.sobot.com:443 (198.18.0.135:443)
[16:31:40.212][127.0.0.1:63136] client connect
[16:31:40.213][127.0.0.1:63132] server connect config.jpush.cn:443 (************:443)
[16:31:40.238][127.0.0.1:63136] server connect ce3e75d5.jpush.cn:443 (************:443)
[16:31:40.432][127.0.0.1:63142] client connect
[16:31:40.442][127.0.0.1:63142] server connect android.bugly.qq.com:443 (198.18.0.149:443)
🔒 HTTPS: POST https://config.jpush.cn/v1/status
🔒 HTTPS: POST https://config.jpush.cn/v1/status
🔒 HTTPS: POST https://ce3e75d5.jpush.cn/wi/op8jdu
[16:31:40.494][127.0.0.1:63145] client connect
🔒 HTTPS: POST https://api.sobot.com/chat-sdk/sdk/user/v2/config.action
[16:31:40.509][127.0.0.1:63145] server connect sdk.verification.jiguang.cn:443 (************:443)
   ✅ 200 OK
127.0.0.1:63131: POST https://config.jpush.cn/v1/status
              << 200 OK 109b
   ✅ 200 OK
127.0.0.1:63132: POST https://config.jpush.cn/v1/status
              << 200 OK 109b
🔒 HTTPS: POST https://android.bugly.qq.com/rqd/async?aid=3a86a0c5-ee28-4b9d-9c11-342f5883c441
🔒 HTTPS: POST https://sdk.verification.jiguang.cn/ip/android
   ✅ 200 OK
127.0.0.1:63136: POST https://ce3e75d5.jpush.cn/wi/op8jdu
              << 200 OK 5.8k
   ✅ 200 OK
127.0.0.1:63145: POST https://sdk.verification.jiguang.cn/ip/android
              << 200 OK 37b
[16:31:40.673][127.0.0.1:63148] client connect
[16:31:40.680][127.0.0.1:63148] server connect sdk.verification.jiguang.cn:443 (************:443)
   ✅ 200 OK
127.0.0.1:63142: POST https://android.bugly.qq.com/rqd/async?aid=3a86a0c5-ee2…
              << 200 OK 309b
🔒 HTTPS: POST https://sdk.verification.jiguang.cn/config/ver/v5/android
   ✅ 200 
127.0.0.1:63130: POST https://api.sobot.com/chat-sdk/sdk/user/v2/config.actio…
              << 200  137b
   ✅ 200 OK
127.0.0.1:63148: POST https://sdk.verification.jiguang.cn/config/ver/v5/andro…
              << 200 OK 1.9k
[16:31:40.827][127.0.0.1:63151] client connect
[16:31:40.837][127.0.0.1:63151] server connect h.trace.qq.com:443 (************:443)
🔒 HTTPS: POST https://android.bugly.qq.com/rqd/async?aid=5f725e92-efd8-4d06-a803-81dad34f145c
🔒 HTTPS: POST https://h.trace.qq.com/kv
   ✅ 200 OK
127.0.0.1:63151: POST https://h.trace.qq.com/kv
              << 200 OK 2b
   ✅ 200 OK
127.0.0.1:63142: POST https://android.bugly.qq.com/rqd/async?aid=5f725e92-efd…
              << 200 OK 95b
🔒 HTTPS: POST https://h.trace.qq.com/kv
   ✅ 200 OK
127.0.0.1:63151: POST https://h.trace.qq.com/kv
              << 200 OK 2b
[16:31:41.663][127.0.0.1:63161] client connect
[16:31:41.673][127.0.0.1:63161] server connect ce3e75d5.jpush.cn:443 (************:443)
🔒 HTTPS: POST https://ce3e75d5.jpush.cn/wi/d8n3hj
   ✅ 200 OK
127.0.0.1:63161: POST https://ce3e75d5.jpush.cn/wi/d8n3hj
              << 200 OK 108b
[16:31:42.053][127.0.0.1:63166] client connect
[16:31:42.065][127.0.0.1:63166] server connect ce3e75d5.jpush.cn:443 (************:443)
[16:31:42.102][127.0.0.1:63169] client connect
[16:31:42.164][127.0.0.1:63169] server connect tsis.jpush.cn:443 (198.18.1.113:443)
[16:31:42.216][127.0.0.1:63172] client connect
[16:31:42.227][127.0.0.1:63172] server connect bjuser.jpush.cn:443 (198.18.0.144:443)
🔒 HTTPS: POST https://ce3e75d5.jpush.cn/wi/bd0r1q
🔒 HTTPS: POST https://bjuser.jpush.cn/v2/appawake/status
🔒 HTTPS: POST https://tsis.jpush.cn/
   ✅ 200 OK
127.0.0.1:63172: POST https://bjuser.jpush.cn/v2/appawake/status
              << 200 OK 328b
   ✅ 200 OK
127.0.0.1:63166: POST https://ce3e75d5.jpush.cn/wi/bd0r1q
              << 200 OK 128b
[16:31:42.421][127.0.0.1:63175] client connect
   ✅ 200 OK
127.0.0.1:63169: POST https://tsis.jpush.cn/
              << 200 OK 192b
[16:31:42.433][127.0.0.1:63131] client disconnect
[16:31:42.436][127.0.0.1:63131] server disconnect config.jpush.cn:443 (************:443)
[16:31:42.437][127.0.0.1:63175] server connect status-ipv6.jpush.cn:443 (************:443)
[16:31:42.443][127.0.0.1:63177] client connect
[16:31:42.456][127.0.0.1:63177] server connect ali-stats.jpush.cn:443 (************:443)
[16:31:42.469][127.0.0.1:63175] Server TLS handshake failed. connection closed
[16:31:42.470][127.0.0.1:63175] Unable to establish TLS connection with server (connection closed). Trying to establish TLS with client anyway. If you plan to redirect requests away from this server, consider setting `connection_strategy` to `lazy` to suppress early connections.
[16:31:42.485][127.0.0.1:63175] server disconnect status-ipv6.jpush.cn:443 (************:443)
🔒 HTTPS: POST https://status-ipv6.jpush.cn/wi/jx/6ae71c
127.0.0.1:63175: POST https://status-ipv6.jpush.cn/wi/jx/6ae71c
 << connection closed
[16:31:42.498][127.0.0.1:63175] client disconnect
🔒 HTTPS: POST https://stats.jpush.cn/v3/report
   ✅ 200 OK
127.0.0.1:63177: POST https://ali-stats.jpush.cn/v3/report
              << 200 OK 29b
[16:31:42.759][127.0.0.1:63136] client disconnect
[16:31:42.762][127.0.0.1:63136] server disconnect ce3e75d5.jpush.cn:443 (************:443)
[16:31:45.244][127.0.0.1:63184] client connect
[16:31:45.248][127.0.0.1:63184] server connect ce3e75d5.jpush.cn:443 (************:443)
🔒 HTTPS: POST https://ce3e75d5.jpush.cn/wi/yb36xs
   ✅ 200 OK
127.0.0.1:63184: POST https://ce3e75d5.jpush.cn/wi/yb36xs
              << 200 OK 180b
[16:31:46.028][127.0.0.1:63161] client disconnect
[16:31:46.030][127.0.0.1:63161] server disconnect ce3e75d5.jpush.cn:443 (************:443)
[16:32:11.127][127.0.0.1:63151] server disconnect h.trace.qq.com:443 (************:443)
[16:32:11.513][127.0.0.1:63209] client connect
[16:32:11.518][127.0.0.1:63209] server connect fcapi.jpush.cn:443 (198.18.1.116:443)
🔒 HTTPS: POST https://fcapi.jpush.cn/lbs/v1/appgwc/status
   ✅ 200 OK
127.0.0.1:63209: POST https://fcapi.jpush.cn/lbs/v1/appgwc/status
              << 200 OK 243b
[16:32:11.694][127.0.0.1:63172] client disconnect
[16:32:11.695][127.0.0.1:63172] server disconnect bjuser.jpush.cn:443 (198.18.0.144:443)
[16:32:12.161][127.0.0.1:63214] client connect
[16:32:12.165][127.0.0.1:63214] server connect fcapi.jpush.cn:443 (198.18.1.116:443)
🔒 HTTPS: POST https://fcapi.jpush.cn/lbs/v1/appgwc/status
   ✅ 200 OK
127.0.0.1:63214: POST https://fcapi.jpush.cn/lbs/v1/appgwc/status
              << 200 OK 243b
[16:32:12.295][127.0.0.1:63166] client disconnect
[16:32:12.296][127.0.0.1:63166] server disconnect ce3e75d5.jpush.cn:443 (************:443)
[16:32:40.540][127.0.0.1:63132] server disconnect config.jpush.cn:443 (************:443)
[16:32:40.644][127.0.0.1:63145] server disconnect sdk.verification.jiguang.cn:443 (************:443)
[16:32:40.807][127.0.0.1:63148] server disconnect sdk.verification.jiguang.cn:443 (************:443)
[16:32:42.421][127.0.0.1:63169] server disconnect tsis.jpush.cn:443 (198.18.1.113:443)
[16:32:42.755][127.0.0.1:63177] server disconnect ali-stats.jpush.cn:443 (************:443)
[16:32:46.026][127.0.0.1:63184] server disconnect ce3e75d5.jpush.cn:443 (************:443)
[16:32:56.019][127.0.0.1:63142] server disconnect android.bugly.qq.com:443 (198.18.0.149:443)
[16:33:11.690][127.0.0.1:63209] server disconnect fcapi.jpush.cn:443 (198.18.1.116:443)
[16:33:12.297][127.0.0.1:63214] server disconnect fcapi.jpush.cn:443 (198.18.1.116:443)
[16:33:40.905][127.0.0.1:63130] server disconnect api.sobot.com:443 (198.18.0.135:443)
[16:36:23.475][127.0.0.1:63590] client connect
[16:36:23.481][127.0.0.1:63590] server connect android.apis.google.com:443 (***********:443)
🔒 HTTPS: POST https://android.apis.google.com/c2dm/register3
   ✅ 200 OK
127.0.0.1:63590: POST https://android.apis.google.com/c2dm/register3
              << 200 OK 152b
🔒 HTTPS: POST https://android.apis.google.com/c2dm/register3
   ❌ 400 Bad Request
127.0.0.1:63590: POST https://android.apis.google.com/c2dm/register3
              << 400 Bad Request 39b
[16:36:25.750][127.0.0.1:63598] client connect
[16:36:25.755][127.0.0.1:63598] server connect android.googleapis.com:443 (************:443)
🔒 HTTPS: POST https://android.googleapis.com/auth/devicekey
   ❌ 400 Bad Request
127.0.0.1:63598: POST https://android.googleapis.com/auth/devicekey
              << 400 Bad Request 801b
[16:36:26.092][127.0.0.1:63603] client connect
[16:36:26.099][127.0.0.1:63603] server connect www.google.com:443 (***********:443)
🔒 HTTPS: POST https://www.google.com/google.internal.communications.instantmessaging.v1.IM/RegisterSilent
   ✅ 200 
127.0.0.1:63603: POST https://www.google.com/google.internal.communications.i… HTTP/2.0
     << HTTP/2.0 200 OK 0b
[16:36:27.181][127.0.0.1:63609] client connect
[16:36:27.186][127.0.0.1:63609] server connect status-ipv6.jpush.cn:443 (************:443)
[16:36:27.211][127.0.0.1:63609] Server TLS handshake failed. connection closed
[16:36:27.213][127.0.0.1:63609] Unable to establish TLS connection with server (connection closed). Trying to establish TLS with client anyway. If you plan to redirect requests away from this server, consider setting `connection_strategy` to `lazy` to suppress early connections.
[16:36:27.219][127.0.0.1:63609] server disconnect status-ipv6.jpush.cn:443 (************:443)
🔒 HTTPS: POST https://status-ipv6.jpush.cn/wi/jx/6ae71c
127.0.0.1:63609: POST https://status-ipv6.jpush.cn/wi/jx/6ae71c
 << connection closed
[16:36:27.226][127.0.0.1:63609] client disconnect
[16:36:40.543][127.0.0.1:63132] client disconnect
[16:36:40.702][127.0.0.1:63145] client disconnect
[16:36:40.767][127.0.0.1:63130] client disconnect
[16:36:40.857][127.0.0.1:63148] client disconnect
[16:36:41.074][127.0.0.1:63142] client disconnect
[16:36:41.180][127.0.0.1:63151] client disconnect
[16:36:42.467][127.0.0.1:63169] client disconnect
[16:36:42.800][127.0.0.1:63177] client disconnect
[16:36:46.074][127.0.0.1:63184] client disconnect
[16:37:11.703][127.0.0.1:63209] client disconnect
[16:37:12.301][127.0.0.1:63214] client disconnect
[16:38:03.541][127.0.0.1:63773] client connect
[16:38:03.544][127.0.0.1:63774] client connect
[16:38:03.548][127.0.0.1:63773] server connect www.gstatic.com:443 (************:443)
[16:38:03.551][127.0.0.1:63774] server connect www.gstatic.com:443 (************:443)
🔒 HTTPS: GET https://www.gstatic.com/android/config_update/08202014-metadata.txt
🔒 HTTPS: GET https://www.gstatic.com/android/config_update/07282025-sms-denylist-metadata.txt
   ✅ 200 OK
127.0.0.1:63773: GET https://www.gstatic.com/android/config_update/08202014-…
              << 200 OK 384b
[16:38:03.868][127.0.0.1:63773] server disconnect www.gstatic.com:443 (************:443)
[16:38:03.869][127.0.0.1:63773] client disconnect
   ✅ 200 OK
127.0.0.1:63774: GET https://www.gstatic.com/android/config_update/07282025-…
              << 200 OK 384b
[16:38:03.923][127.0.0.1:63774] server disconnect www.gstatic.com:443 (************:443)
[16:38:03.923][127.0.0.1:63774] client disconnect
🔒 HTTPS: POST https://android.apis.google.com/c2dm/register3
   ❌ 301 Moved Permanently
127.0.0.1:63590: POST https://android.apis.google.com/c2dm/register3
              << 301 Moved Permanently 45b
[16:40:26.086][127.0.0.1:63598] server disconnect android.googleapis.com:443 (************:443)
[16:40:26.435][127.0.0.1:63603] ***********:443: HTTP/2 protocol error: Invalid input ConnectionInputs.RECV_PING in state ConnectionState.CLOSED
[16:40:26.437][127.0.0.1:63603] server disconnect www.google.com:443 (***********:443)
[16:41:26.090][127.0.0.1:63598] client disconnect
[16:41:36.997][127.0.0.1:64164] client connect
[16:41:37.004][127.0.0.1:64164] server connect android.bugly.qq.com:443 (198.18.0.149:443)
[16:41:37.029][127.0.0.1:64167] client connect
[16:41:37.036][127.0.0.1:64167] server connect api.sobot.com:443 (198.18.0.135:443)
🔒 HTTPS: POST https://android.bugly.qq.com/rqd/async?aid=d01db3c7-e6eb-4cb6-9d16-8ffbd1dad8d9
🔒 HTTPS: POST https://api.sobot.com/chat-sdk/sdk/user/v2/config.action
   ✅ 200 OK
127.0.0.1:64164: POST https://android.bugly.qq.com/rqd/async?aid=d01db3c7-e6e…
              << 200 OK 95b
[16:41:37.396][127.0.0.1:64171] client connect
[16:41:37.404][127.0.0.1:64171] server connect h.trace.qq.com:443 (************:443)
🔒 HTTPS: POST https://android.bugly.qq.com/rqd/async?aid=85b7d2c5-900b-4a77-ad2d-d03c6473f854
   ✅ 200 
127.0.0.1:64167: POST https://api.sobot.com/chat-sdk/sdk/user/v2/config.actio…
              << 200  137b
🔒 HTTPS: POST https://h.trace.qq.com/kv
   ✅ 200 OK
127.0.0.1:64164: POST https://android.bugly.qq.com/rqd/async?aid=85b7d2c5-900…
              << 200 OK 95b
[16:41:37.741][127.0.0.1:64174] client connect
[16:41:37.746][127.0.0.1:64174] server connect h.trace.qq.com:443 (************:443)
🔒 HTTPS: POST https://h.trace.qq.com/kv
   ✅ 200 OK
127.0.0.1:64171: POST https://h.trace.qq.com/kv
              << 200 OK 2b
   ✅ 200 OK
127.0.0.1:64174: POST https://h.trace.qq.com/kv
              << 200 OK 2b
[16:41:38.004][127.0.0.1:64177] client connect
[16:41:38.011][127.0.0.1:64177] server connect ali-stats.jpush.cn:443 (************:443)
🔒 HTTPS: POST https://stats.jpush.cn/v3/report
   ✅ 200 OK
127.0.0.1:64177: POST https://ali-stats.jpush.cn/v3/report
              << 200 OK 29b
[16:42:07.698][127.0.0.1:64171] server disconnect h.trace.qq.com:443 (************:443)
[16:42:08.006][127.0.0.1:64174] server disconnect h.trace.qq.com:443 (************:443)
[16:42:38.370][127.0.0.1:64177] server disconnect ali-stats.jpush.cn:443 (************:443)
[16:42:48.895][127.0.0.1:64164] client disconnect
[16:42:48.897][127.0.0.1:64167] client disconnect
[16:42:48.898][127.0.0.1:64171] client disconnect
[16:42:48.900][127.0.0.1:64174] client disconnect
[16:42:48.901][127.0.0.1:64177] client disconnect
[16:42:48.902][127.0.0.1:64164] server disconnect android.bugly.qq.com:443 (198.18.0.149:443)
[16:42:48.905][127.0.0.1:64167] server disconnect api.sobot.com:443 (198.18.0.135:443)
[16:43:03.881][127.0.0.1:63590] server disconnect android.apis.google.com:443 (***********:443)
[16:43:40.309][127.0.0.1:64328] client connect
[16:43:40.313][127.0.0.1:64328] server connect play.googleapis.com:443 (198.18.0.111:443)
🔒 HTTPS: POST https://play.googleapis.com/log/batch
   ✅ 200 OK
127.0.0.1:64328: POST https://play.googleapis.com/log/batch
              << 200 OK 102b
🔒 HTTPS: POST https://play.googleapis.com/log/batch
   ✅ 200 OK
127.0.0.1:64328: POST https://play.googleapis.com/log/batch
              << 200 OK 102b
🔒 HTTPS: POST https://play.googleapis.com/log/batch
   ✅ 200 OK
127.0.0.1:64328: POST https://play.googleapis.com/log/batch
              << 200 OK 102b
🔒 HTTPS: POST https://play.googleapis.com/log/batch
   ✅ 200 OK
127.0.0.1:64328: POST https://play.googleapis.com/log/batch
              << 200 OK 102b
[16:44:03.871][127.0.0.1:63590] client disconnect
[16:47:41.450][127.0.0.1:64770] client connect
[16:47:41.467][127.0.0.1:64770] server connect android.apis.google.com:443 (***********:443)
🔒 HTTPS: POST https://android.apis.google.com/c2dm/register3
   ❌ 400 Bad Request
127.0.0.1:64770: POST https://android.apis.google.com/c2dm/register3
              << 400 Bad Request 39b
[16:47:41.987][127.0.0.1:64774] client connect
[16:47:41.997][127.0.0.1:64774] server connect android.googleapis.com:443 (************:443)
[16:47:42.009][127.0.0.1:64328] server disconnect play.googleapis.com:443 (198.18.0.111:443)
🔒 HTTPS: POST https://android.googleapis.com/auth/devicekey
   ❌ 400 Bad Request
127.0.0.1:64774: POST https://android.googleapis.com/auth/devicekey
              << 400 Bad Request 801b
[16:48:42.017][127.0.0.1:64328] client disconnect
[16:51:13.544][127.0.0.1:65111] client connect
[16:51:13.555][127.0.0.1:65111] server connect api.sobot.com:443 (198.18.0.135:443)
[16:51:13.568][127.0.0.1:65113] client connect
[16:51:13.583][127.0.0.1:65113] server connect android.bugly.qq.com:443 (198.18.0.149:443)
🔒 HTTPS: POST https://api.sobot.com/chat-sdk/sdk/user/v2/config.action
🔒 HTTPS: POST https://android.bugly.qq.com/rqd/async?aid=31131cd7-9a03-41c8-8931-f9de4a77a104
   ✅ 200 OK
127.0.0.1:65113: POST https://android.bugly.qq.com/rqd/async?aid=31131cd7-9a0…
              << 200 OK 95b
   ✅ 200 
127.0.0.1:65111: POST https://api.sobot.com/chat-sdk/sdk/user/v2/config.actio…
              << 200  137b
[16:51:14.095][127.0.0.1:65119] client connect
[16:51:14.105][127.0.0.1:65119] server connect ali-stats.jpush.cn:443 (************:443)
[16:51:14.213][127.0.0.1:65124] client connect
[16:51:14.225][127.0.0.1:65124] server connect h.trace.qq.com:443 (************:443)
🔒 HTTPS: POST https://android.bugly.qq.com/rqd/async?aid=61025ce5-2220-45ca-8759-7007eb40934f
🔒 HTTPS: POST https://stats.jpush.cn/v3/report
🔒 HTTPS: POST https://h.trace.qq.com/kv
   ✅ 200 OK
127.0.0.1:65113: POST https://android.bugly.qq.com/rqd/async?aid=61025ce5-222…
              << 200 OK 95b
   ✅ 200 OK
127.0.0.1:65124: POST https://h.trace.qq.com/kv
              << 200 OK 2b
   ✅ 200 OK
127.0.0.1:65119: POST https://ali-stats.jpush.cn/v3/report
              << 200 OK 29b
🔒 HTTPS: POST https://h.trace.qq.com/kv
   ✅ 200 OK
127.0.0.1:65124: POST https://h.trace.qq.com/kv
              << 200 OK 2b
[16:51:14.650][127.0.0.1:65127] client connect
[16:51:14.656][127.0.0.1:65127] server connect ali-stats.jpush.cn:443 (************:443)
🔒 HTTPS: POST https://stats.jpush.cn/v3/report
   ✅ 200 OK
127.0.0.1:65127: POST https://ali-stats.jpush.cn/v3/report
              << 200 OK 29b
[16:51:15.915][127.0.0.1:65133] client connect
[16:51:15.922][127.0.0.1:65133] server connect status-ipv6.jpush.cn:443 (************:443)
[16:51:15.940][127.0.0.1:65133] Server TLS handshake failed. connection closed
[16:51:15.941][127.0.0.1:65133] Unable to establish TLS connection with server (connection closed). Trying to establish TLS with client anyway. If you plan to redirect requests away from this server, consider setting `connection_strategy` to `lazy` to suppress early connections.
[16:51:15.950][127.0.0.1:65133] server disconnect status-ipv6.jpush.cn:443 (************:443)
🔒 HTTPS: POST https://status-ipv6.jpush.cn/wi/jx/6ae71c
127.0.0.1:65133: POST https://status-ipv6.jpush.cn/wi/jx/6ae71c
 << connection closed
