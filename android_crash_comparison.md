# Android 11 vs Android 7 崩溃处理对比

## 🔍 关键差异

### Android 11 (API 30+)
- **静默处理崩溃**：应用崩溃后自动重启或返回主屏幕
- **无持久弹窗**：不显示"has stopped"对话框
- **后台崩溃日志**：崩溃信息记录在logcat，不干扰用户
- **更好的用户体验**：崩溃不会阻塞其他操作

### Android 7 (API 24)
- **显示崩溃对话框**："App has stopped"弹窗
- **阻塞式UI**：弹窗覆盖所有内容，必须手动处理
- **用户必须选择**："Open app again"或关闭
- **传统崩溃处理**：更明显的错误提示

## 📊 测试影响

| 方面 | Android 11 | Android 7 |
|------|------------|-----------|
| Activity启动 | ✅ 崩溃后可继续 | ⚠️ 需先处理弹窗 |
| UI自动化 | ✅ 不受影响 | ❌ 被弹窗阻塞 |
| 流量捕获 | ✅ 正常 | ✅ 正常 |
| 用户体验 | ✅ 流畅 | ❌ 中断 |

## 💡 解决方案

### Android 7上禁用崩溃对话框：
```bash
# 方法1：通过开发者选项（需要root）
adb shell settings put global show_first_crash_dialog 0
adb shell settings put global show_first_crash_dialog_bug_report 0

# 方法2：自动关闭弹窗
adb shell input keyevent 4  # 返回键
adb shell input keyevent 66 # 确认键
```

### 最佳实践：
1. 在Android 11上进行UI测试（无弹窗干扰）
2. 在Android 7上进行HTTPS捕获（系统证书可写）
3. 或在Android 7上添加自动弹窗处理逻辑
