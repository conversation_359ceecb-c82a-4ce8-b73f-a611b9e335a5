#!/usr/bin/env python3
"""
Frida Hook测试脚本 - 验证exported绕过是否工作
"""

import subprocess
import time
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_adb(cmd, timeout=5):
    """执行ADB命令"""
    adb_path = "/Users/<USER>/Desktop/project/apk_detect/android-sdk/platform-tools/adb"
    try:
        result = subprocess.run(
            [adb_path, '-s', 'emulator-5554'] + cmd,
            capture_output=True, text=True, timeout=timeout
        )
        return result.stdout.strip()
    except Exception as e:
        logger.error(f"ADB命令失败: {e}")
        return None

def test_frida_hook():
    """测试Frida Hook功能"""
    logger.info("🧪 开始Frida Hook测试...")
    
    package_name = "com.iloda.beacon"
    base_dir = Path("/Users/<USER>/Desktop/project/apk_detect")
    hook_script = base_dir / "exported_bypass_hook.js"
    
    if not hook_script.exists():
        logger.error("❌ Hook脚本不存在")
        return False
    
    # 1. 检查应用是否已安装和运行
    logger.info("🔍 检查应用状态...")
    
    # 先启动应用
    logger.info("📱 启动应用...")
    launch_result = run_adb([
        'shell', 'am', 'start', 
        '-n', f'{package_name}/.activity.LoginActivity'
    ])
    
    if launch_result and "Error" not in launch_result:
        logger.info("✅ 应用启动成功")
        time.sleep(3)  # 等待应用完全启动
    else:
        logger.error("❌ 应用启动失败")
        return False
    
    # 2. 启动Frida Hook
    logger.info("🔗 启动Frida Hook...")
    
    try:
        # 使用attach模式连接到运行中的应用
        frida_cmd = [
            'frida', '-U', '-l', str(hook_script), 
            package_name
        ]
        
        logger.info(f"执行命令: {' '.join(frida_cmd)}")
        
        frida_process = subprocess.Popen(
            frida_cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待Hook部署
        time.sleep(8)
        
        # 检查Frida进程状态
        if frida_process.poll() is None:
            logger.info("✅ Frida Hook进程正在运行")
        else:
            stdout, stderr = frida_process.communicate()
            logger.error(f"❌ Frida进程已退出")
            logger.error(f"stdout: {stdout}")
            logger.error(f"stderr: {stderr}")
            return False
        
        # 3. 测试Hook效果 - 尝试启动一个exported=false的Activity
        logger.info("🎯 测试Hook效果...")
        
        test_activities = [
            f'{package_name}/.activity.MainActivity',
            f'{package_name}/.activity.GuideActivity', 
            f'{package_name}/.activity.SplashActivity'
        ]
        
        success_count = 0
        
        for activity in test_activities:
            logger.info(f"📱 测试启动Activity: {activity}")
            
            result = run_adb([
                'shell', 'am', 'start', '-n', activity
            ])
            
            if result and "Error" not in result:
                logger.info(f"✅ 成功启动: {activity}")
                success_count += 1
                time.sleep(2)
            else:
                logger.warning(f"❌ 启动失败: {activity}")
                logger.debug(f"失败详情: {result}")
        
        # 4. 使用Frida直接调用强制启动函数
        logger.info("🔧 测试Frida直接调用...")
        
        for activity in test_activities:
            activity_short = activity.split('/')[-1]
            
            frida_eval_cmd = [
                'frida', '-U', package_name, '--eval',
                f'if(typeof forceStartActivity !== "undefined") {{ console.log("调用forceStartActivity"); forceStartActivity("{package_name}", "{package_name}{activity_short}"); }} else {{ console.log("forceStartActivity函数不可用"); }}'
            ]
            
            try:
                eval_result = subprocess.run(
                    frida_eval_cmd,
                    capture_output=True, text=True, timeout=10
                )
                
                logger.info(f"Frida eval结果: {eval_result.stdout}")
                if eval_result.stderr:
                    logger.warning(f"Frida eval错误: {eval_result.stderr}")
                    
                if "Activity启动成功" in eval_result.stdout:
                    success_count += 1
                    logger.info(f"✅ Frida强制启动成功: {activity}")
                    
            except Exception as e:
                logger.error(f"Frida eval异常: {e}")
        
        # 5. 生成测试报告
        logger.info("=" * 50)
        logger.info("🧪 Frida Hook测试完成")
        logger.info(f"📊 测试结果:")
        logger.info(f"   成功启动Activity: {success_count}个")
        logger.info(f"   测试Activity总数: {len(test_activities)}个")
        logger.info(f"   成功率: {success_count/len(test_activities)*100:.1f}%")
        
        if success_count > 0:
            logger.info("🎉 Hook部分生效！")
            return True
        else:
            logger.warning("⚠️ Hook未生效，需要调试")
            return False
        
    except Exception as e:
        logger.error(f"Hook测试异常: {e}")
        return False
    
    finally:
        # 清理进程
        try:
            if 'frida_process' in locals():
                frida_process.terminate()
                logger.info("🧹 Frida进程已清理")
        except:
            pass

if __name__ == "__main__":
    success = test_frida_hook()
    exit(0 if success else 1)
