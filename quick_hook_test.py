#!/usr/bin/env python3
"""
快速Hook测试 - 用正确的进程名
"""

import subprocess
import time

def quick_test():
    print("🔧 快速Hook测试...")
    
    # 使用正确的进程名"Liuwa"而不是包名
    cmd = [
        'frida', '-U', '-p', '11122',  # 直接使用进程ID
        '--eval', '''
console.log("[+] Hook连接成功!");
Java.perform(function() {
    try {
        // 简单测试：Hook startActivity
        var ContextImpl = Java.use("android.app.ContextImpl");
        var originalStartActivity = ContextImpl.startActivity.overload('android.content.Intent');
        
        originalStartActivity.implementation = function(intent) {
            console.log("[*] Hook成功! startActivity被调用");
            console.log("    Intent: " + intent.toString());
            
            try {
                return originalStartActivity.call(this, intent);
            } catch (e) {
                console.log("[+] 绕过异常: " + e.toString());
                return null; // 强制返回成功
            }
        };
        
        console.log("[+] Hook设置完成");
        
        // 测试强制启动Activity
        setTimeout(function() {
            console.log("[*] 开始测试强制启动Activity...");
            
            var Intent = Java.use("android.content.Intent");
            var ComponentName = Java.use("android.content.ComponentName");
            var ActivityThread = Java.use("android.app.ActivityThread");
            
            var currentApp = ActivityThread.currentApplication();
            var context = currentApp.getApplicationContext();
            
            // 测试启动MainActivity
            try {
                var component = ComponentName.$new("com.iloda.beacon", "com.iloda.beacon.activity.MainActivity");
                var intent = Intent.$new();
                intent.setComponent(component);
                intent.addFlags(0x10000000); // FLAG_ACTIVITY_NEW_TASK
                
                context.startActivity(intent);
                console.log("[+] MainActivity启动成功!");
            } catch (e) {
                console.log("[-] MainActivity启动失败: " + e.toString());
            }
            
            // 测试启动GuideActivity
            setTimeout(function() {
                try {
                    var component2 = ComponentName.$new("com.iloda.beacon", "com.iloda.beacon.activity.GuideActivity");
                    var intent2 = Intent.$new();
                    intent2.setComponent(component2);
                    intent2.addFlags(0x10000000);
                    
                    context.startActivity(intent2);
                    console.log("[+] GuideActivity启动成功!");
                } catch (e) {
                    console.log("[-] GuideActivity启动失败: " + e.toString());
                }
            }, 1000);
            
        }, 2000);
        
    } catch (e) {
        console.log("[-] Hook失败: " + e.toString());
    }
});

setTimeout(function() {
    console.log("[+] 测试完成，5秒后退出");
    exit();
}, 8000);
'''
    ]
    
    print(f"执行命令: frida -U -p 11122 --eval ...")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=12)
        print("✅ Hook测试输出:")
        print(result.stdout)
        if result.stderr:
            print("⚠️ 错误输出:")
            print(result.stderr)
            
        if "Hook连接成功" in result.stdout and ("启动成功" in result.stdout):
            print("🎉 Hook测试成功！exported绕过工作正常！")
            return True
        elif "Hook连接成功" in result.stdout:
            print("✅ Hook连接正常，但Activity启动需要进一步调试")
            return True
        else:
            print("❌ Hook连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

if __name__ == "__main__":
    success = quick_test()
    if success:
        print("\n💡 结论：Frida Hook环境正常，现在可以集成到Activity启动器中！")
    else:
        print("\n❌ 结论：需要进一步调试Frida环境")
    
    exit(0 if success else 1)




