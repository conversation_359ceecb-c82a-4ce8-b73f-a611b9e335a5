#!/usr/bin/env python3
"""
简单的Frida SSL测试
使用Python API而不是命令行
"""

import subprocess
import sys
import time
import json
from pathlib import Path

def run_adb(cmd, timeout=30):
    """执行adb命令"""
    full_cmd = f"source android_env.sh && adb {cmd}"
    try:
        result = subprocess.run(full_cmd, shell=True, 
                              capture_output=True, text=True, 
                              timeout=timeout, check=True)
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        return f"ERROR: {e.stderr}"
    except subprocess.TimeoutExpired:
        return "ERROR: Timeout"

def start_frida_server():
    """启动frida-server"""
    print("🚀 启动frida-server...")
    
    # 杀死现有进程
    run_adb("shell pkill frida-server")
    time.sleep(2)
    
    # 启动frida-server
    subprocess.Popen("source android_env.sh && adb shell '/data/local/tmp/frida-server &'", shell=True)
    time.sleep(8)
    
    # 验证启动
    result = run_adb("shell ps | grep frida-server")
    if "frida-server" in result:
        print("✅ frida-server启动成功")
        return True
    else:
        print("❌ frida-server启动失败")
        return False

def test_frida_python_api():
    """使用Python API测试Frida"""
    print("🔧 使用Python API测试Frida...")
    
    try:
        import frida
        print("✅ Frida Python模块导入成功")
        
        # 连接到设备
        device = frida.get_usb_device()
        print("✅ 连接到USB设备成功")
        
        # 列出进程
        processes = device.enumerate_processes()
        print(f"✅ 获取进程列表成功，共{len(processes)}个进程")
        
        # 查找目标应用
        target_process = None
        for process in processes:
            if "yjzx" in process.name.lower() or "易金在线" in process.name:
                target_process = process
                print(f"✅ 找到目标进程: {process.name} (PID: {process.pid})")
                break
        
        if not target_process:
            print("⚠️  未找到目标进程，尝试spawn模式...")
            
            # 使用spawn模式
            package = "com.yjzx.yjzx2017"
            
            # 读取SSL绕过脚本
            with open("ssl_bypass.js", 'r') as f:
                script_code = f.read()
            
            print(f"🚀 Spawn模式启动应用: {package}")
            pid = device.spawn([package])
            print(f"✅ 应用已spawn，PID: {pid}")
            
            # 附加到进程
            session = device.attach(pid)
            print("✅ 附加到进程成功")
            
            # 创建脚本
            script = session.create_script(script_code)
            print("✅ 创建SSL绕过脚本成功")
            
            # 加载脚本
            script.load()
            print("✅ SSL绕过脚本加载成功")
            
            # 恢复进程
            device.resume(pid)
            print("✅ 进程已恢复运行")
            
            return script, session
        else:
            # 附加到现有进程
            print(f"🔗 附加到现有进程: {target_process.name}")
            
            session = device.attach(target_process.pid)
            print("✅ 附加成功")
            
            # 读取SSL绕过脚本
            with open("ssl_bypass.js", 'r') as f:
                script_code = f.read()
            
            # 创建并加载脚本
            script = session.create_script(script_code)
            script.load()
            print("✅ SSL绕过脚本加载成功")
            
            return script, session
            
    except ImportError:
        print("❌ Frida Python模块未安装")
        print("🔧 安装命令: pip3 install frida")
        return None, None
    except Exception as e:
        print(f"❌ Frida Python API异常: {e}")
        return None, None

def test_https_capture():
    """测试HTTPS捕获"""
    print("🔍 测试HTTPS捕获...")
    
    # 清空捕获文件
    realtime_file = Path("mitm-logs/realtime_capture.json")
    if realtime_file.exists():
        with open(realtime_file, 'w') as f:
            json.dump([], f)
    
    time.sleep(2)
    
    # 触发HTTPS请求
    print("🌐 触发HTTPS请求...")
    
    test_urls = [
        "https://httpbin.org/get",
        "https://www.baidu.com",
        "https://www.google.com"
    ]
    
    for url in test_urls:
        print(f"   🔄 访问: {url}")
        run_adb(f"shell am start -a android.intent.action.VIEW -d {url}")
        time.sleep(8)
    
    # 应用内操作
    print("   🔄 应用内操作...")
    run_adb("shell input tap 540 960")
    time.sleep(5)
    
    # 等待网络请求
    print("⏳ 等待网络请求处理...")
    time.sleep(15)
    
    # 检查结果
    try:
        with open(realtime_file, 'r') as f:
            data = json.load(f)
            
            if isinstance(data, list) and len(data) > 0:
                https_requests = [req for req in data if req.get('scheme') == 'https']
                
                print(f"📊 捕获结果:")
                print(f"   总请求: {len(data)}")
                print(f"   HTTPS请求: {len(https_requests)}")
                
                if https_requests:
                    print("🎉 SSL绕过成功！捕获到HTTPS请求！")
                    for i, req in enumerate(https_requests[:3], 1):
                        print(f"   {i}. {req.get('method', 'N/A')} {req.get('url', 'N/A')}")
                    return True
                else:
                    print("⚠️  只捕获到HTTP请求")
                    return False
            else:
                print("❌ 没有捕获到请求")
                return False
                
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    print("🚀 简单Frida SSL测试")
    print("🔧 使用Python API进行SSL绕过")
    print("=" * 50)
    
    try:
        # 检查设备
        result = run_adb("devices")
        if "device" not in result:
            print("❌ 设备未连接")
            return False
        print("✅ 设备已连接")
        
        # 启动frida-server
        if not start_frida_server():
            return False
        
        # 使用Python API测试
        script, session = test_frida_python_api()
        
        if script and session:
            print("\n🎉 Frida SSL绕过脚本运行成功！")
            
            # 测试HTTPS捕获
            print("\n" + "="*40)
            print("🔍 测试HTTPS捕获")
            print("="*40)
            
            success = test_https_capture()
            
            if success:
                print("\n🎉 SSL绕过完全成功！")
                print("✅ HTTPS流量现在可以被捕获！")
                
                print("\n⚠️  脚本正在运行...")
                print("💡 按Ctrl+C停止")
                
                try:
                    # 保持运行
                    while True:
                        time.sleep(30)
                        print("💡 SSL绕过仍在运行...")
                except KeyboardInterrupt:
                    print("\n⚠️  停止脚本...")
                    
            else:
                print("\n⚠️  HTTPS捕获测试未完全成功")
                print("🔧 但SSL绕过脚本可能仍在工作")
            
            # 清理
            try:
                script.unload()
                session.detach()
            except:
                pass
            
            return success
        else:
            print("❌ Frida脚本启动失败")
            return False
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断")
        return False
    except Exception as e:
        print(f"\n❌ 异常: {e}")
        return False
    finally:
        # 清理frida-server
        print("🧹 清理frida-server...")
        subprocess.run("source android_env.sh && adb shell pkill frida-server", shell=True)

if __name__ == "__main__":
    main()
