#!/usr/bin/env python3
"""
测试全自动化动态分析器
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.local_dynamic_analyzer import LocalDynamicAnalyzer

async def test_automated_dynamic():
    print('🚀 测试全自动化动态分析器...')
    analyzer = LocalDynamicAnalyzer()
    apk_path = 'apk/5577.com.androidfuture.chrismas.framesfree.apk'
    
    try:
        print(f'📱 开始全自动化分析: {apk_path}')
        result = await analyzer.analyze(apk_path, timeout=45)
        
        print('✅ 全自动化动态分析完成!')
        print(f'📊 结果统计:')
        print(f'  - 动态URLs: {len(result.dynamic_urls)}')
        print(f'  - 分析时长: {result.duration:.2f}秒')
        print(f'  - Android版本: {result.android_version}')
        print(f'  - 网络请求: {len(result.network_requests) if result.network_requests else 0}')
        print(f'  - 运行时信息: {"有" if result.runtime_info else "无"}')
        
        if result.error:
            print(f'⚠️  错误信息: {result.error}')
        else:
            print('🎉 全自动化分析成功，无错误!')
            
        # 显示前几个URL
        if result.dynamic_urls:
            print(f'🔗 捕获的动态URLs:')
            for i, url in enumerate(result.dynamic_urls[:3]):
                print(f'  {i+1}. {url.url}')
        else:
            print('📝 未捕获到动态URLs（可能应用无网络活动或需要更多交互）')
                
    except Exception as e:
        print(f'❌ 全自动化动态分析失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_automated_dynamic())
