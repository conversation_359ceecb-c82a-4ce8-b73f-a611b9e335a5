#!/bin/bash

echo "===== Android 11 HTTPS流量捕获启动器 ====="
echo ""

# 配置参数
DEVICE_ID="${1:-emulator-5554}"
MITM_PORT="${2:-8080}"
AVD_NAME="${3:-Android11_API30}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    print_status "检查依赖工具..."
    
    local missing_deps=()
    
    # 检查Android SDK工具
    if ! command -v adb &> /dev/null; then
        missing_deps+=("adb")
    fi
    
    if ! command -v emulator &> /dev/null; then
        missing_deps+=("emulator")
    fi
    
    # 检查虚拟环境
    if [ ! -f .venv/bin/activate ]; then
        print_error "虚拟环境不存在"
        echo "请先创建虚拟环境:"
        echo "  python3 -m venv .venv"
        echo "  source .venv/bin/activate" 
        echo "  pip install -r requirements.txt"
        exit 1
    fi
    
    # 激活虚拟环境并检查Python工具
    source .venv/bin/activate
    
    if [ ! -f .venv/bin/mitmdump ]; then
        missing_deps+=("mitmproxy (在虚拟环境中)")
    fi
    
    if [ ! -f .venv/bin/python ]; then
        missing_deps+=("python3 (在虚拟环境中)")
    fi
    
    # 检查系统工具
    if ! command -v openssl &> /dev/null; then
        missing_deps+=("openssl")
    fi
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        print_error "缺少必要依赖: ${missing_deps[*]}"
        echo ""
        echo "请安装缺少的工具："
        echo "  # Android SDK工具"
        echo "  brew install android-platform-tools"
        echo ""
        echo "  # 系统工具"
        echo "  brew install openssl"
        echo ""
        echo "  # Python依赖 (在虚拟环境中)"
        echo "  source .venv/bin/activate"
        echo "  pip install -r requirements.txt"
        exit 1
    fi
    
    print_status "✅ 依赖检查通过"
}

# 启动模拟器
start_emulator() {
    print_status "检查模拟器状态..."
    
    # 检查模拟器是否已运行
    if adb -s "$DEVICE_ID" get-state >/dev/null 2>&1; then
        print_status "模拟器已运行: $DEVICE_ID"
        return 0
    fi
    
    print_status "启动Android 11模拟器..."
    print_warning "注意: 模拟器必须使用-writable-system参数启动"
    
    # 在后台启动模拟器
    emulator -avd "$AVD_NAME" -writable-system -no-snapshot-load -no-window &
    EMULATOR_PID=$!
    
    print_status "等待模拟器启动... (PID: $EMULATOR_PID)"
    
    # 等待模拟器启动完成
    local timeout=120
    local count=0
    
    while [ $count -lt $timeout ]; do
        if adb -s "$DEVICE_ID" get-state >/dev/null 2>&1; then
            print_status "✅ 模拟器启动成功"
            
            # 等待系统完全启动
            sleep 10
            return 0
        fi
        
        sleep 2
        count=$((count + 2))
        
        if [ $((count % 20)) -eq 0 ]; then
            print_status "等待中... ($count/${timeout}s)"
        fi
    done
    
    print_error "模拟器启动超时"
    kill $EMULATOR_PID 2>/dev/null
    exit 1
}

# 安装系统证书
install_certificate() {
    print_status "安装mitmproxy系统证书..."
    
    # 使用虚拟环境中的Python
    source .venv/bin/activate
    .venv/bin/python android11_advanced_cert_installer.py "$DEVICE_ID"
    local install_result=$?
    
    if [ $install_result -eq 0 ]; then
        print_status "✅ 证书安装成功"
        
        # 询问是否重启设备
        echo ""
        read -p "是否立即重启设备以确保证书生效? (y/N): " -n 1 -r
        echo ""
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            print_status "重启设备..."
            adb -s "$DEVICE_ID" reboot
            
            print_status "等待设备重启..."
            sleep 15
            
            # 等待设备重新连接
            local timeout=60
            local count=0
            
            while [ $count -lt $timeout ]; do
                if adb -s "$DEVICE_ID" get-state >/dev/null 2>&1; then
                    print_status "✅ 设备重启完成"
                    return 0
                fi
                
                sleep 2
                count=$((count + 2))
            done
            
            print_warning "设备重启可能未完成，请手动检查"
        fi
        
        return 0
    else
        print_error "证书安装失败"
        return 1
    fi
}

# 启动mitmproxy
start_mitmproxy() {
    print_status "启动mitmproxy代理服务器..."
    
    # 创建日志目录
    mkdir -p mitm-logs
    
    local log_file="mitm-logs/capture_$(date +%Y%m%d_%H%M%S).log"
    
    # 启动mitmproxy（使用虚拟环境）
    if [ ! -f .venv/bin/activate ]; then
        print_error "虚拟环境不存在，请先创建: python3 -m venv .venv && source .venv/bin/activate && pip install -r requirements.txt"
        return 1
    fi
    
    source .venv/bin/activate
    .venv/bin/mitmdump \
        --listen-host 0.0.0.0 \
        --listen-port "$MITM_PORT" \
        --set confdir=./mitm-config \
        --set flow_detail=2 \
        --showhost \
        --save-stream-file "$log_file" \
        --scripts capture_result_organizer.py \
        > "mitm-logs/mitm_output_$(date +%Y%m%d_%H%M%S).log" 2>&1 &
    
    MITM_PID=$!
    echo $MITM_PID > .mitm_pid
    
    print_status "✅ mitmproxy已启动 (PID: $MITM_PID, 端口: $MITM_PORT)"
    print_status "📁 捕获日志: $log_file"
    
    # 等待mitmproxy完全启动
    sleep 3
    
    # 验证mitmproxy是否正常运行
    if kill -0 $MITM_PID 2>/dev/null; then
        print_status "✅ mitmproxy运行正常"
        return 0
    else
        print_error "mitmproxy启动失败"
        return 1
    fi
}

# 启动Frida SSL绕过
start_frida_bypass() {
    print_status "启动Frida SSL绕过..."
    
    # 检查是否有Frida脚本
    local frida_script=""
    for script in ultimate_ssl_bypass.js advanced_ssl_bypass.js ssl_bypass.js; do
        if [ -f "$script" ]; then
            frida_script="$script"
            break
        fi
    done
    
    if [ -z "$frida_script" ]; then
        print_warning "未找到Frida SSL绕过脚本，跳过Frida启动"
        return 0
    fi
    
    print_status "使用Frida脚本: $frida_script"
    
    # 启动Frida (后台，使用虚拟环境)
    source .venv/bin/activate
    .venv/bin/frida -D "$DEVICE_ID" -l "$frida_script" --runtime=duk > "mitm-logs/frida_$(date +%Y%m%d_%H%M%S).log" 2>&1 &
    FRIDA_PID=$!
    echo $FRIDA_PID > .frida_pid
    
    print_status "✅ Frida SSL绕过已启动 (PID: $FRIDA_PID)"
    
    return 0
}

# 显示状态信息
show_status() {
    echo ""
    echo "===== HTTPS流量捕获环境状态 ====="
    echo ""
    
    # 设备状态
    if adb -s "$DEVICE_ID" get-state >/dev/null 2>&1; then
        local android_version=$(adb -s "$DEVICE_ID" shell getprop ro.build.version.release)
        print_status "📱 设备: $DEVICE_ID (Android $android_version)"
    else
        print_error "📱 设备: $DEVICE_ID (未连接)"
    fi
    
    # mitmproxy状态
    if [ -f .mitm_pid ] && kill -0 $(cat .mitm_pid) 2>/dev/null; then
        print_status "🌐 mitmproxy: 运行中 (端口: $MITM_PORT)"
    else
        print_warning "🌐 mitmproxy: 未运行"
    fi
    
    # Frida状态
    if [ -f .frida_pid ] && kill -0 $(cat .frida_pid) 2>/dev/null; then
        print_status "🛡️  Frida: SSL绕过已启动"
    else
        print_warning "🛡️  Frida: 未运行"
    fi
    
    echo ""
    echo "📊 使用方法:"
    echo "  - 查看捕获日志: tail -f mitm-logs/*.log"
    echo "  - Web界面: http://127.0.0.1:8081"
    echo "  - 停止服务: ./stop_https_capture.sh"
    echo ""
}

# 创建停止脚本
create_stop_script() {
    cat > stop_https_capture.sh << 'EOF'
#!/bin/bash

echo "停止HTTPS流量捕获服务..."

# 停止mitmproxy
if [ -f .mitm_pid ]; then
    MITM_PID=$(cat .mitm_pid)
    if kill -0 $MITM_PID 2>/dev/null; then
        kill $MITM_PID
        echo "✅ mitmproxy已停止 (PID: $MITM_PID)"
    fi
    rm .mitm_pid
fi

# 停止Frida
if [ -f .frida_pid ]; then
    FRIDA_PID=$(cat .frida_pid)
    if kill -0 $FRIDA_PID 2>/dev/null; then
        kill $FRIDA_PID
        echo "✅ Frida已停止 (PID: $FRIDA_PID)"
    fi
    rm .frida_pid
fi

echo "🛑 所有服务已停止"
EOF
    
    chmod +x stop_https_capture.sh
}

# 清理函数
cleanup() {
    echo ""
    print_status "正在清理..."
    
    if [ -f .mitm_pid ]; then
        kill $(cat .mitm_pid) 2>/dev/null
        rm .mitm_pid
    fi
    
    if [ -f .frida_pid ]; then
        kill $(cat .frida_pid) 2>/dev/null
        rm .frida_pid
    fi
    
    exit 0
}

# 注册清理函数
trap cleanup SIGINT SIGTERM

# 主流程
main() {
    print_status "启动Android 11 HTTPS流量捕获环境"
    print_status "设备: $DEVICE_ID, mitmproxy端口: $MITM_PORT"
    echo ""
    
    # 检查依赖
    check_dependencies
    
    # 启动模拟器
    start_emulator
    
    # 安装证书
    install_certificate
    
    # 启动mitmproxy
    start_mitmproxy
    
    # 启动Frida绕过
    start_frida_bypass
    
    # 创建停止脚本
    create_stop_script
    
    # 显示状态
    show_status
    
    print_status "🎯 HTTPS流量捕获环境就绪!"
    print_status "💡 现在可以在模拟器中打开应用进行流量捕获"
    
    # 保持脚本运行
    echo ""
    echo "按 Ctrl+C 停止所有服务"
    
    while true; do
        sleep 1
    done
}

# 执行主流程
main "$@"