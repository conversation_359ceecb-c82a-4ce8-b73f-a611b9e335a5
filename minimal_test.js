console.log("[*] Minimal test script loaded");

setTimeout(function() {
    if (Java.available) {
        console.log("[*] Java is available");
        
        Java.perform(function() {
            console.log("[*] Inside Java.perform");
            
            // 只 Hook 最基本的 URL 类
            try {
                var URL = Java.use('java.net.URL');
                URL.$init.overload('java.lang.String').implementation = function(url) {
                    console.log("[捕获 URL] " + url);
                    return this.$init(url);
                };
                console.log("[*] URL hook 安装成功");
            } catch(e) {
                console.log("[!] Hook URL 失败: " + e);
            }
            
            // Hook HttpURLConnection
            try {
                var HttpURLConnection = Java.use('java.net.HttpURLConnection');
                HttpURLConnection.connect.implementation = function() {
                    console.log("[捕获 HTTP] " + this.getURL().toString());
                    return this.connect();
                };
                console.log("[*] HttpURLConnection hook 安装成功");
            } catch(e) {
                console.log("[!] Hook HttpURLConnection 失败: " + e);
            }
            
            console.log("[*] Hooks 安装完成，等待网络活动...");
        });
    } else {
        console.log("[!] Java 不可用，1秒后重试...");
        setTimeout(arguments.callee, 1000);
    }
}, 1000);




