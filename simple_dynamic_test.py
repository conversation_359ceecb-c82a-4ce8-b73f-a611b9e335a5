#!/usr/bin/env python3
"""
简化的动态分析核心功能测试
避免复杂依赖，直接测试Android模拟器、ADB、Frida等核心功能
"""

import subprocess
import time
import json
import logging
import asyncio
import os
import tempfile
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SimpleDynamicTester:
    """简化的动态分析测试器"""
    
    def __init__(self):
        self.device_id = "emulator-5554"
        self.test_results = {
            "adb_connection": False,
            "device_info": {},
            "frida_server": False,
            "ui_dump": False,
            "network_test": False,
            "app_install": False,
            "errors": [],
            "ui_elements_count": 0,
            "test_duration": 0
        }
    
    def run_adb_command(self, command, timeout=30):
        """执行ADB命令"""
        try:
            full_command = ['adb', '-s', self.device_id] + command
            result = subprocess.run(
                full_command, 
                capture_output=True, 
                text=True, 
                timeout=timeout
            )
            return result
        except subprocess.TimeoutExpired:
            logger.error(f"ADB命令超时: {' '.join(command)}")
            return None
        except Exception as e:
            logger.error(f"ADB命令执行异常: {e}")
            return None
    
    def test_adb_connection(self):
        """测试ADB连接"""
        logger.info("🔧 测试ADB连接...")
        
        try:
            # 检查设备列表
            result = subprocess.run(['adb', 'devices'], capture_output=True, text=True)
            if self.device_id in result.stdout:
                logger.info(f"✅ 设备 {self.device_id} 连接成功")
                self.test_results["adb_connection"] = True
                return True
            else:
                logger.error(f"❌ 设备 {self.device_id} 未连接")
                self.test_results["errors"].append("ADB设备未连接")
                return False
        except Exception as e:
            logger.error(f"❌ ADB连接测试失败: {e}")
            self.test_results["errors"].append(f"ADB连接异常: {str(e)}")
            return False
    
    def get_device_info(self):
        """获取设备信息"""
        logger.info("📱 获取设备信息...")
        
        try:
            # 获取Android版本
            android_version = self.run_adb_command(['shell', 'getprop', 'ro.build.version.release'])
            if android_version and android_version.returncode == 0:
                self.test_results["device_info"]["android_version"] = android_version.stdout.strip()
            
            # 获取API级别
            api_level = self.run_adb_command(['shell', 'getprop', 'ro.build.version.sdk'])
            if api_level and api_level.returncode == 0:
                self.test_results["device_info"]["api_level"] = api_level.stdout.strip()
            
            # 获取设备型号
            model = self.run_adb_command(['shell', 'getprop', 'ro.product.model'])
            if model and model.returncode == 0:
                self.test_results["device_info"]["model"] = model.stdout.strip()
            
            logger.info(f"✅ 设备信息: {self.test_results['device_info']}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 获取设备信息失败: {e}")
            self.test_results["errors"].append(f"设备信息获取异常: {str(e)}")
            return False
    
    def test_frida_server(self):
        """测试Frida服务器"""
        logger.info("🔒 测试Frida服务器...")
        
        try:
            # 检查frida-server进程
            result = self.run_adb_command(['shell', 'ps | grep frida-server'])
            
            if result and 'frida-server' in result.stdout:
                logger.info("✅ Frida服务器正在运行")
                self.test_results["frida_server"] = True
                return True
            
            # 尝试启动frida-server
            logger.info("🚀 尝试启动Frida服务器...")
            
            # 检查frida-server文件是否存在
            check_result = self.run_adb_command(['shell', 'ls /data/local/tmp/frida-server'])
            if check_result and check_result.returncode == 0:
                # 启动frida-server
                self.run_adb_command(['shell', 'chmod 755 /data/local/tmp/frida-server'])
                subprocess.Popen([
                    'adb', '-s', self.device_id, 'shell', 
                    '/data/local/tmp/frida-server &'
                ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                
                # 等待启动
                time.sleep(3)
                
                # 再次检查
                result = self.run_adb_command(['shell', 'ps | grep frida-server'])
                if result and 'frida-server' in result.stdout:
                    logger.info("✅ Frida服务器启动成功")
                    self.test_results["frida_server"] = True
                    return True
                else:
                    logger.warning("⚠️  Frida服务器启动可能失败，但继续测试")
                    return True
            else:
                logger.warning("⚠️  未找到frida-server文件，跳过Frida测试")
                return True
                
        except Exception as e:
            logger.error(f"❌ Frida服务器测试失败: {e}")
            self.test_results["errors"].append(f"Frida服务器异常: {str(e)}")
            return False
    
    def test_ui_dump(self):
        """测试UI dump功能"""
        logger.info("🖱️  测试UI dump功能...")
        
        try:
            # 获取当前Activity
            current_activity = self.run_adb_command([
                'shell', 'dumpsys', 'window', 'windows', '|', 'grep', 'mCurrentFocus'
            ])
            
            if current_activity and current_activity.returncode == 0:
                logger.info(f"当前Activity: {current_activity.stdout.strip()}")
            
            # 进行UI dump
            with tempfile.NamedTemporaryFile(mode='w+', suffix='.xml', delete=False) as tmp_file:
                ui_dump_result = self.run_adb_command(['shell', 'uiautomator', 'dump', '/sdcard/ui_dump.xml'])
                
                if ui_dump_result and ui_dump_result.returncode == 0:
                    # 拉取dump文件
                    pull_result = subprocess.run([
                        'adb', '-s', self.device_id, 'pull', '/sdcard/ui_dump.xml', tmp_file.name
                    ], capture_output=True, text=True)
                    
                    if pull_result.returncode == 0:
                        # 分析UI dump文件
                        try:
                            with open(tmp_file.name, 'r', encoding='utf-8') as f:
                                ui_content = f.read()
                                
                            # 简单统计UI元素
                            clickable_count = ui_content.count('clickable="true"')
                            node_count = ui_content.count('<node')
                            
                            self.test_results["ui_elements_count"] = node_count
                            
                            logger.info(f"✅ UI dump成功: {node_count} 个节点, {clickable_count} 个可点击元素")
                            self.test_results["ui_dump"] = True
                            
                            # 保存UI dump到项目目录
                            ui_save_path = f"/Users/<USER>/Desktop/project/apk_detect/ui_dump_test_{int(time.time())}.xml"
                            with open(ui_save_path, 'w', encoding='utf-8') as f:
                                f.write(ui_content)
                            logger.info(f"UI dump已保存: {ui_save_path}")
                            
                            return True
                            
                        except Exception as e:
                            logger.error(f"解析UI dump失败: {e}")
                            return False
                        finally:
                            # 清理临时文件
                            os.unlink(tmp_file.name)
                    else:
                        logger.error("拉取UI dump文件失败")
                        return False
                else:
                    logger.error("UI dump命令执行失败")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ UI dump测试失败: {e}")
            self.test_results["errors"].append(f"UI dump异常: {str(e)}")
            return False
    
    def test_network_connectivity(self):
        """测试网络连接"""
        logger.info("🌐 测试网络连接...")
        
        try:
            # 测试ping
            ping_result = self.run_adb_command(['shell', 'ping', '-c', '3', '8.8.8.8'])
            
            if ping_result and ping_result.returncode == 0:
                logger.info("✅ 网络连接正常")
                self.test_results["network_test"] = True
                return True
            else:
                logger.warning("⚠️  网络连接可能有问题")
                return True  # 不阻塞其他测试
                
        except Exception as e:
            logger.error(f"❌ 网络连接测试失败: {e}")
            self.test_results["errors"].append(f"网络连接异常: {str(e)}")
            return False
    
    def test_app_installation(self):
        """测试应用安装功能"""
        logger.info("📦 测试应用安装功能...")
        
        try:
            # 查看已安装的应用包
            packages_result = self.run_adb_command(['shell', 'pm', 'list', 'packages'])
            
            if packages_result and packages_result.returncode == 0:
                packages = packages_result.stdout.split('\n')
                package_count = len([p for p in packages if p.startswith('package:')])
                
                logger.info(f"✅ 系统已安装 {package_count} 个应用包")
                self.test_results["app_install"] = True
                return True
            else:
                logger.error("获取应用包列表失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ 应用安装测试失败: {e}")
            self.test_results["errors"].append(f"应用安装异常: {str(e)}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        logger.info("="*60)
        logger.info("🧪 开始简化动态分析核心功能测试")
        logger.info("="*60)
        
        start_time = time.time()
        
        tests = [
            ("ADB连接", self.test_adb_connection),
            ("设备信息", self.get_device_info),
            ("Frida服务器", self.test_frida_server),
            ("UI Dump", self.test_ui_dump),
            ("网络连接", self.test_network_connectivity),
            ("应用安装", self.test_app_installation),
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            logger.info(f"\n🔍 开始测试: {test_name}")
            try:
                success = test_func()
                if success:
                    logger.info(f"✅ {test_name} 测试通过")
                    passed_tests += 1
                else:
                    logger.error(f"❌ {test_name} 测试失败")
            except Exception as e:
                logger.error(f"💥 {test_name} 测试异常: {e}")
                self.test_results["errors"].append(f"{test_name}异常: {str(e)}")
        
        self.test_results["test_duration"] = time.time() - start_time
        
        # 生成测试报告
        self.generate_report(passed_tests, total_tests)
        
        return passed_tests >= total_tests * 0.8  # 80%通过率算作成功
    
    def generate_report(self, passed_tests, total_tests):
        """生成测试报告"""
        logger.info("\n" + "="*60)
        logger.info("📊 简化动态分析核心功能测试报告")
        logger.info("="*60)
        
        logger.info(f"📈 测试通过率: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
        logger.info(f"⏱️  测试耗时: {self.test_results['test_duration']:.2f} 秒")
        logger.info(f"📱 设备信息: {self.test_results['device_info']}")
        logger.info(f"🖱️  UI元素数量: {self.test_results['ui_elements_count']}")
        
        logger.info(f"\n🔧 核心功能状态:")
        logger.info(f"  - ADB连接: {'✅' if self.test_results['adb_connection'] else '❌'}")
        logger.info(f"  - Frida服务器: {'✅' if self.test_results['frida_server'] else '❌'}")
        logger.info(f"  - UI自动化: {'✅' if self.test_results['ui_dump'] else '❌'}")
        logger.info(f"  - 网络功能: {'✅' if self.test_results['network_test'] else '❌'}")
        logger.info(f"  - 应用安装: {'✅' if self.test_results['app_install'] else '❌'}")
        
        if self.test_results['errors']:
            logger.info(f"\n❌ 错误列表 ({len(self.test_results['errors'])}):")
            for error in self.test_results['errors']:
                logger.info(f"  - {error}")
        
        # 保存详细报告
        report_file = f'/Users/<USER>/Desktop/project/apk_detect/simple_dynamic_test_report_{int(time.time())}.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"\n📄 详细报告已保存: {report_file}")
        
        # 结论
        if passed_tests >= total_tests * 0.9:
            logger.info("\n🎉 动态分析核心功能完全正常！")
        elif passed_tests >= total_tests * 0.7:
            logger.info("\n⚠️  动态分析核心功能基本可用，个别问题需要关注")
        else:
            logger.info("\n❌ 动态分析核心功能存在问题，需要修复")
        
        logger.info("="*60)

def main():
    """主函数"""
    tester = SimpleDynamicTester()
    success = tester.run_all_tests()
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())




