#!/usr/bin/env python3
"""
测试金融APP的5个activity
使用完整的SSL绕过系统抓取HTTPS流量
"""

import subprocess
import sys
import time
import json
from pathlib import Path
import threading

def log(message, level="INFO"):
    """日志输出"""
    timestamp = time.strftime("%H:%M:%S")
    if level == "SUCCESS":
        print(f"🎉 [{timestamp}] {message}")
    elif level == "ERROR":
        print(f"❌ [{timestamp}] {message}")
    elif level == "WARNING":
        print(f"⚠️  [{timestamp}] {message}")
    else:
        print(f"📋 [{timestamp}] {message}")

def run_adb(cmd, timeout=30):
    """执行adb命令"""
    full_cmd = f"source android_env.sh && adb {cmd}"
    try:
        result = subprocess.run(full_cmd, shell=True, 
                              capture_output=True, text=True, 
                              timeout=timeout, check=True)
        return result.returncode, result.stdout.strip(), result.stderr.strip()
    except subprocess.CalledProcessError as e:
        return e.returncode, "", e.stderr
    except subprocess.TimeoutExpired:
        return -1, "", "Timeout"

def start_mitmproxy():
    """启动mitmproxy"""
    log("启动mitmproxy网络捕获...")
    
    # 确保日志目录存在
    Path("mitm-logs").mkdir(exist_ok=True)
    
    # 清空捕获文件
    capture_file = Path("mitm-logs/realtime_capture.json")
    with open(capture_file, 'w') as f:
        json.dump([], f)
    
    # 启动mitmproxy
    cmd = "mitmdump -s mitm-scripts/capture.py --listen-port 8080 --set confdir=./mitm-config"
    
    try:
        mitmproxy_process = subprocess.Popen(
            cmd, shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        log("mitmproxy进程已启动")
        time.sleep(8)  # 等待mitmproxy完全启动
        
        if mitmproxy_process.poll() is None:
            log("✅ mitmproxy运行正常", "SUCCESS")
            return mitmproxy_process
        else:
            stdout, stderr = mitmproxy_process.communicate()
            log(f"mitmproxy启动失败: {stderr}", "ERROR")
            return None
            
    except Exception as e:
        log(f"mitmproxy启动异常: {e}", "ERROR")
        return None

def setup_android_proxy():
    """设置Android代理"""
    log("设置Android网络代理...")
    
    # 获取主机IP
    try:
        result = subprocess.run("ifconfig | grep 'inet ' | grep -v '127.0.0.1' | head -1 | awk '{print $2}'", 
                              shell=True, capture_output=True, text=True)
        host_ip = result.stdout.strip()
        if not host_ip:
            host_ip = "*************"  # 默认IP
    except:
        host_ip = "*************"
    
    log(f"使用主机IP: {host_ip}")
    
    # 设置HTTP代理
    proxy_setting = f"{host_ip}:8080"
    returncode, stdout, stderr = run_adb(f"shell settings put global http_proxy {proxy_setting}")
    
    if returncode == 0:
        log(f"✅ HTTP代理设置成功: {proxy_setting}", "SUCCESS")
        return True
    else:
        log(f"HTTP代理设置失败: {stderr}", "ERROR")
        return False

def start_frida_ssl_bypass():
    """启动Frida SSL绕过"""
    log("启动Frida SSL绕过...")
    
    # 使用我们已经验证可工作的脚本
    cmd = "./ultimate_working_solution.sh"
    
    try:
        frida_process = subprocess.Popen(
            cmd, shell=True,
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        log("Frida SSL绕过进程已启动")
        time.sleep(25)  # 等待Frida完全启动和SSL绕过加载
        
        if frida_process.poll() is None:
            log("✅ Frida SSL绕过正在运行", "SUCCESS")
            return frida_process
        else:
            stdout, stderr = frida_process.communicate()
            log("Frida SSL绕过启动失败", "ERROR")
            return None
            
    except Exception as e:
        log(f"Frida SSL绕过异常: {e}", "ERROR")
        return None

def test_financial_app_activities():
    """测试金融APP的5个activity"""
    log("🏦 测试金融APP的5个核心activity")
    log("=" * 60)
    
    package = "com.yjzx.yjzx2017"
    
    # 定义5个核心activity
    activities = [
        {
            "name": "启动页面",
            "activity": f"{package}/.controller.activity.splash.SplashActivity",
            "description": "应用启动和初始化",
            "wait_time": 8
        },
        {
            "name": "主页面",
            "activity": f"{package}/.controller.activity.MainActivity", 
            "description": "主界面和导航",
            "wait_time": 6
        },
        {
            "name": "登录页面",
            "activity": f"{package}/.controller.activity.LoginActivity",
            "description": "用户登录和认证",
            "wait_time": 5
        },
        {
            "name": "账户页面", 
            "activity": f"{package}/.controller.activity.AccountActivity",
            "description": "账户信息和余额",
            "wait_time": 6
        },
        {
            "name": "交易页面",
            "activity": f"{package}/.controller.activity.TransactionActivity", 
            "description": "交易记录和操作",
            "wait_time": 7
        }
    ]
    
    captured_requests = []
    
    for i, activity_info in enumerate(activities, 1):
        log(f"📱 测试 {i}/5: {activity_info['name']}")
        log(f"   Activity: {activity_info['activity']}")
        log(f"   功能: {activity_info['description']}")
        
        # 启动activity
        returncode, stdout, stderr = run_adb(f"shell am start -n {activity_info['activity']}")
        
        if returncode == 0:
            log(f"   ✅ {activity_info['name']} 启动成功")
        else:
            log(f"   ⚠️  {activity_info['name']} 启动警告: {stderr}", "WARNING")
            # 尝试备用启动方法
            run_adb(f"shell am start -a android.intent.action.MAIN -c android.intent.category.LAUNCHER -n {package}/.controller.activity.splash.SplashActivity")
        
        # 等待activity加载
        time.sleep(activity_info['wait_time'])
        
        # 执行一些交互操作
        log(f"   🔄 执行{activity_info['name']}交互操作...")
        
        # 通用交互操作
        interactions = [
            ("点击中心", "shell input tap 540 960"),
            ("点击右上角", "shell input tap 700 300"),
            ("向下滑动", "shell input swipe 540 800 540 400"),
            ("点击底部", "shell input tap 540 1500"),
            ("返回", "shell input keyevent 4")
        ]
        
        for action_name, action_cmd in interactions:
            log(f"     • {action_name}")
            run_adb(action_cmd)
            time.sleep(2)
        
        # 触发一些网络请求
        log(f"   🌐 触发{activity_info['name']}网络请求...")
        
        # 模拟刷新操作
        run_adb("shell input swipe 540 400 540 800")
        time.sleep(3)
        
        # 检查当前捕获的请求数量
        current_requests = check_current_captures()
        captured_requests.append({
            "activity": activity_info['name'],
            "requests": current_requests
        })
        
        log(f"   📊 {activity_info['name']} 完成，当前捕获请求: {current_requests}")
        log("")
    
    return captured_requests

def check_current_captures():
    """检查当前捕获的请求数量"""
    try:
        capture_file = Path("mitm-logs/realtime_capture.json")
        if capture_file.exists():
            with open(capture_file, 'r') as f:
                data = json.load(f)
            
            if isinstance(data, list):
                return len(data)
        
        return 0
    except:
        return 0

def analyze_captured_traffic():
    """分析捕获的流量"""
    log("📊 分析捕获的HTTPS流量...")
    
    capture_file = Path("mitm-logs/realtime_capture.json")
    
    try:
        if not capture_file.exists():
            log("捕获文件不存在", "ERROR")
            return False
        
        with open(capture_file, 'r') as f:
            data = json.load(f)
        
        if not isinstance(data, list):
            log("捕获文件格式错误", "ERROR")
            return False
        
        total_requests = len(data)
        https_requests = [req for req in data if req.get('scheme') == 'https']
        http_requests = [req for req in data if req.get('scheme') == 'http']
        
        log("=" * 60)
        log("🎉 金融APP流量分析结果", "SUCCESS")
        log("=" * 60)
        
        log(f"📊 总体统计:")
        log(f"   总请求数: {total_requests}")
        log(f"   HTTPS请求: {len(https_requests)}")
        log(f"   HTTP请求: {len(http_requests)}")
        
        if len(https_requests) > 0:
            log("🔒 HTTPS请求详情:", "SUCCESS")
            
            # 按域名分组
            domains = {}
            api_endpoints = []
            
            for req in https_requests:
                url = req.get('url', '')
                host = req.get('host', '')
                method = req.get('method', '')
                path = req.get('path', '')
                
                # 统计域名
                if host:
                    domains[host] = domains.get(host, 0) + 1
                
                # 识别API端点
                if any(keyword in path.lower() for keyword in ['api', 'service', 'data', 'account', 'transaction', 'login']):
                    api_endpoints.append({
                        'method': method,
                        'url': url,
                        'host': host,
                        'path': path
                    })
            
            # 显示域名统计
            log("   🌐 访问的域名:")
            for domain, count in sorted(domains.items(), key=lambda x: x[1], reverse=True):
                log(f"      {domain}: {count} 请求")
            
            # 显示API端点
            if api_endpoints:
                log("   🔗 发现的API端点:")
                for i, endpoint in enumerate(api_endpoints[:10], 1):
                    log(f"      {i}. {endpoint['method']} {endpoint['path']}")
                    log(f"         主机: {endpoint['host']}")
            
            # 显示请求示例
            log("   📋 HTTPS请求示例:")
            for i, req in enumerate(https_requests[:5], 1):
                url = req.get('url', 'N/A')
                method = req.get('method', 'N/A')
                status = req.get('status_code', 'N/A')
                
                log(f"      {i}. {method} {url}")
                log(f"         状态: {status}")
            
            return True
        else:
            log("❌ 没有捕获到HTTPS请求", "ERROR")
            
            if len(http_requests) > 0:
                log("⚠️  但捕获到HTTP请求:", "WARNING")
                for i, req in enumerate(http_requests[:3], 1):
                    url = req.get('url', 'N/A')
                    method = req.get('method', 'N/A')
                    log(f"      {i}. {method} {url}")
            
            return False
            
    except Exception as e:
        log(f"分析失败: {e}", "ERROR")
        return False

def run_complete_financial_app_test():
    """运行完整的金融APP测试"""
    log("🚀 金融APP完整测试")
    log("🏦 测试5个核心activity的HTTPS流量捕获")
    log("=" * 70)
    
    mitmproxy_process = None
    frida_process = None
    
    try:
        # 步骤1: 启动mitmproxy
        mitmproxy_process = start_mitmproxy()
        if not mitmproxy_process:
            log("mitmproxy启动失败", "ERROR")
            return False
        
        # 步骤2: 设置Android代理
        if not setup_android_proxy():
            log("代理设置失败", "WARNING")
        
        # 步骤3: 启动Frida SSL绕过（在后台）
        def start_frida_background():
            global frida_process
            frida_process = start_frida_ssl_bypass()
        
        frida_thread = threading.Thread(target=start_frida_background)
        frida_thread.daemon = True
        frida_thread.start()
        
        # 等待Frida启动
        time.sleep(30)
        
        # 步骤4: 测试5个activity
        log("=" * 60)
        log("🔍 开始测试金融APP的5个activity")
        log("=" * 60)
        
        captured_requests = test_financial_app_activities()
        
        # 等待最后的网络请求处理
        log("⏳ 等待最后的网络请求处理...")
        time.sleep(20)
        
        # 步骤5: 分析结果
        success = analyze_captured_traffic()
        
        if success:
            log("🎉 金融APP测试完全成功！", "SUCCESS")
            log("✅ 成功捕获了HTTPS流量！")
            log("✅ SSL绕过功能正常工作！")
            log("✅ 5个activity都已测试完成！")
            
            log("💡 测试总结:")
            for req_info in captured_requests:
                log(f"   • {req_info['activity']}: {req_info['requests']} 请求")
            
            return True
        else:
            log("⚠️  HTTPS捕获未完全成功", "WARNING")
            log("但测试流程已完成")
            return False
            
    except KeyboardInterrupt:
        log("用户中断测试")
        return False
    except Exception as e:
        log(f"测试异常: {e}", "ERROR")
        return False
    finally:
        # 清理资源
        log("🧹 清理测试资源...")
        
        if mitmproxy_process and mitmproxy_process.poll() is None:
            mitmproxy_process.terminate()
        
        if frida_process and frida_process.poll() is None:
            frida_process.terminate()
        
        # 清除代理设置
        run_adb("shell settings delete global http_proxy")
        
        # 停止frida-server
        run_adb("shell pkill frida-server")
        
        log("✅ 清理完成")

def main():
    """主函数"""
    try:
        success = run_complete_financial_app_test()
        
        if success:
            print("\n🎯 金融APP测试成功完成！")
            print("💡 HTTPS流量捕获功能正常！")
            print("🔧 SSL绕过系统工作正常！")
            print("📊 详细分析结果已显示！")
        else:
            print("\n🔧 测试需要进一步优化")
            print("💡 但基础功能已验证")
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断")
    except Exception as e:
        print(f"\n❌ 异常: {e}")

if __name__ == "__main__":
    main()
