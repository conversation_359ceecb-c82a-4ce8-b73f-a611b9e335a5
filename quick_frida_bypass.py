#!/usr/bin/env python3
"""
快速Frida SSL绕过脚本
简化版本，快速启动和测试
"""

import subprocess
import sys
import time
import json
from pathlib import Path

class QuickFridaBypass:
    def __init__(self):
        self.package = "com.yjzx.yjzx2017"
        
    def run_adb(self, cmd, timeout=30):
        """执行adb命令"""
        full_cmd = f"source android_env.sh && adb {cmd}"
        try:
            result = subprocess.run(full_cmd, shell=True, 
                                  capture_output=True, text=True, 
                                  timeout=timeout, check=True)
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            return f"ERROR: {e.stderr}"
        except subprocess.TimeoutExpired:
            return "ERROR: Timeout"
    
    def check_device(self):
        """检查设备连接"""
        print("🔍 检查Android设备...")
        result = self.run_adb("devices")
        if "device" in result and "ERROR" not in result:
            print("✅ Android设备已连接")
            return True
        else:
            print("❌ Android设备未连接")
            return False
    
    def download_frida_server(self):
        """下载frida-server"""
        print("📥 准备frida-server...")
        
        # 检查是否已存在
        if Path("frida-server").exists():
            print("✅ frida-server已存在")
            return True
        
        # 获取设备架构
        arch = self.run_adb("shell getprop ro.product.cpu.abi")
        print(f"📋 设备架构: {arch}")
        
        if "x86_64" in arch:
            server_arch = "x86_64"
        elif "x86" in arch:
            server_arch = "x86"
        else:
            server_arch = "x86"  # 模拟器通常是x86
        
        # 下载URL
        download_url = f"https://github.com/frida/frida/releases/download/17.2.17/frida-server-17.2.17-android-{server_arch}.xz"
        
        print(f"🔄 下载frida-server...")
        print(f"📋 URL: {download_url}")
        
        try:
            # 下载
            cmd = f"curl -L -o frida-server.xz '{download_url}'"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                print("✅ 下载成功，解压中...")
                
                # 解压
                subprocess.run("xz -d frida-server.xz", shell=True)
                
                if Path("frida-server").exists():
                    print("✅ frida-server准备完成")
                    return True
            
            print("⚠️  自动下载失败")
            return False
            
        except Exception as e:
            print(f"⚠️  下载异常: {e}")
            return False
    
    def setup_and_start_frida_server(self):
        """设置并启动frida-server"""
        print("🚀 设置frida-server...")
        
        # 推送到设备
        result = self.run_adb("push frida-server /data/local/tmp/frida-server")
        if "ERROR" in result:
            print(f"❌ 推送失败: {result}")
            return False
        
        # 设置权限
        self.run_adb("shell chmod 755 /data/local/tmp/frida-server")
        
        # 启动frida-server
        print("🔄 启动frida-server...")
        
        # 先杀死可能存在的进程
        self.run_adb("shell pkill frida-server")
        time.sleep(2)
        
        # 启动新进程
        subprocess.Popen("source android_env.sh && adb shell '/data/local/tmp/frida-server &'", shell=True)
        
        # 等待启动
        time.sleep(5)
        
        # 验证启动
        result = self.run_adb("shell ps | grep frida-server")
        if "frida-server" in result:
            print("✅ frida-server启动成功")
            return True
        else:
            print("❌ frida-server启动失败")
            return False
    
    def run_ssl_bypass(self):
        """运行SSL绕过"""
        print("🔧 运行SSL绕过...")
        
        # 启动应用
        print("🚀 启动目标应用...")
        self.run_adb(f"shell am start -n {self.package}/.controller.activity.MainActivity")
        time.sleep(3)
        
        # 运行Frida脚本
        print("🔧 注入SSL绕过脚本...")
        
        try:
            # 使用spawn模式启动
            cmd = f"frida -U -f {self.package} -l ssl_bypass.js --no-pause"
            print(f"📋 执行命令: {cmd}")
            
            # 启动Frida进程
            process = subprocess.Popen(
                cmd, shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            print("✅ SSL绕过脚本已启动")
            print("⏳ 等待脚本加载...")
            time.sleep(8)
            
            # 检查进程状态
            if process.poll() is None:
                print("✅ SSL绕过脚本运行中...")
                return process
            else:
                stdout, stderr = process.communicate()
                print(f"❌ 脚本启动失败")
                print(f"输出: {stdout}")
                print(f"错误: {stderr}")
                return None
                
        except Exception as e:
            print(f"❌ SSL绕过异常: {e}")
            return None
    
    def test_https_capture(self):
        """测试HTTPS抓包"""
        print("🔍 测试HTTPS抓包...")
        
        # 清空捕获文件
        realtime_file = Path("mitm-logs/realtime_capture.json")
        if realtime_file.exists():
            with open(realtime_file, 'w') as f:
                json.dump([], f)
        
        time.sleep(2)
        
        # 触发网络活动
        print("🌐 触发网络请求...")
        
        test_actions = [
            ("启动应用", f"shell am start -n {self.package}/.controller.activity.MainActivity"),
            ("访问HTTPS", "shell am start -a android.intent.action.VIEW -d https://httpbin.org/get"),
            ("用户交互", "shell input tap 540 960"),
        ]
        
        for action_name, cmd in test_actions:
            print(f"   🔄 {action_name}...")
            self.run_adb(cmd)
            time.sleep(3)
        
        # 等待网络请求
        print("⏳ 等待网络请求...")
        time.sleep(10)
        
        # 检查结果
        try:
            with open(realtime_file, 'r') as f:
                data = json.load(f)
                
                if isinstance(data, list) and len(data) > 0:
                    https_requests = [req for req in data if req.get('scheme') == 'https']
                    
                    print(f"📊 捕获统计:")
                    print(f"   总请求: {len(data)}")
                    print(f"   HTTPS请求: {len(https_requests)}")
                    
                    if https_requests:
                        print("🎉 SSL绕过成功！捕获到HTTPS请求！")
                        for i, req in enumerate(https_requests[:3], 1):
                            print(f"   {i}. {req.get('method', 'N/A')} {req.get('url', 'N/A')}")
                        return True
                    else:
                        print("⚠️  只捕获到HTTP请求")
                        return False
                else:
                    print("❌ 没有捕获到网络请求")
                    return False
                    
        except Exception as e:
            print(f"❌ 检查失败: {e}")
            return False
    
    def run_quick_bypass(self):
        """运行快速绕过"""
        print("🚀 Frida SSL绕过 - 快速版本")
        print("🔧 自动化HTTPS证书绕过")
        print("=" * 50)
        
        # 步骤1: 检查设备
        if not self.check_device():
            return False
        
        # 步骤2: 准备frida-server
        if not self.download_frida_server():
            print("⚠️  frida-server下载失败，请手动下载")
            print("📋 手动下载步骤:")
            print("   1. 访问: https://github.com/frida/frida/releases/tag/17.2.17")
            print("   2. 下载对应架构的frida-server")
            print("   3. 解压并重命名为 frida-server")
            print("   4. 放在当前目录")
            
            if not Path("frida-server").exists():
                return False
        
        # 步骤3: 设置并启动frida-server
        if not self.setup_and_start_frida_server():
            return False
        
        # 步骤4: 运行SSL绕过
        frida_process = self.run_ssl_bypass()
        if not frida_process:
            return False
        
        # 步骤5: 测试HTTPS抓包
        print("\n" + "="*40)
        print("🔍 测试HTTPS抓包功能")
        print("="*40)
        
        success = self.test_https_capture()
        
        if success:
            print("\n🎉 Frida SSL绕过成功！")
            print("✅ HTTPS流量现在可以被mitmproxy捕获！")
            print("🔧 SSL证书验证已被绕过！")
            
            print("\n📋 Frida进程正在后台运行...")
            print("💡 你可以继续进行网络分析测试")
            print("⚠️  按Ctrl+C停止Frida进程")
            
            try:
                # 保持Frida进程运行
                frida_process.wait()
            except KeyboardInterrupt:
                print("\n⚠️  用户中断，停止Frida进程...")
                frida_process.terminate()
                
            return True
        else:
            print("\n⚠️  SSL绕过测试失败")
            print("🔧 可能需要进一步调试")
            
            if frida_process:
                frida_process.terminate()
            
            return False

def main():
    bypass = QuickFridaBypass()
    
    try:
        success = bypass.run_quick_bypass()
        
        if success:
            print("\n🎯 Frida SSL绕过完成！")
        else:
            print("\n🔧 需要进一步调试")
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断")
    except Exception as e:
        print(f"\n❌ 异常: {e}")
    finally:
        # 清理
        subprocess.run("source android_env.sh && adb shell pkill frida-server", shell=True)

if __name__ == "__main__":
    main()
