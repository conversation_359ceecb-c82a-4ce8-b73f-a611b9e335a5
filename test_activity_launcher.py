#!/usr/bin/env python3
"""
简化的Activity启动测试脚本
"""

import subprocess
import sys
import time
from pathlib import Path

def run_adb(cmd, timeout=30):
    """执行adb命令"""
    full_cmd = f"adb {cmd}"
    try:
        result = subprocess.run(full_cmd, shell=True, 
                              capture_output=True, text=True, 
                              timeout=timeout, check=True)
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        print(f"ADB命令失败: {full_cmd}")
        print(f"错误: {e.stderr}")
        return None
    except subprocess.TimeoutExpired:
        print(f"ADB命令超时: {full_cmd}")
        return None

def get_activities_from_apk(package):
    """从APK文件获取Activity列表"""
    apk_files = list(Path("apk").glob(f"*{package.split('.')[-1]}*.apk"))
    if not apk_files:
        print(f"❌ 未找到APK文件")
        return []
    
    apk_file = apk_files[0]
    print(f"🔍 分析APK文件: {apk_file}")
    
    # 使用aapt获取Activity列表
    cmd = f"source android_env.sh && $ANDROID_HOME/build-tools/*/aapt dump xmltree {apk_file} AndroidManifest.xml | grep -A 2 -B 2 'activity' | grep 'android:name'"
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    
    activities = []
    if result.returncode == 0:
        lines = result.stdout.split('\n')
        for line in lines:
            if 'android:name' in line and 'Raw:' in line:
                try:
                    name_part = line.split('Raw: "')[1].split('"')[0]
                    # 只保留包含Activity的类名
                    if 'Activity' in name_part and 'Provider' not in name_part:
                        activities.append(name_part)
                except IndexError:
                    continue
    
    return activities

def test_activity_launch(package, activity):
    """测试启动单个Activity"""
    print(f"\n🚀 测试启动: {activity}")
    
    # 方法1: 直接启动
    cmd = f"shell am start -n {package}/{activity}"
    result = run_adb(cmd)
    
    if result and "Error" not in result and "Exception" not in result:
        print(f"✅ 成功启动: {activity}")
        return True
    else:
        print(f"❌ 启动失败: {activity}")
        if result:
            print(f"   错误信息: {result}")
        return False

def main():
    package = "com.yjzx.yjzx2017"
    
    print(f"📱 测试应用: {package}")
    print("=" * 60)
    
    # 获取Activity列表
    activities = get_activities_from_apk(package)
    
    if not activities:
        print("❌ 未找到任何Activity")
        return
    
    print(f"📋 找到 {len(activities)} 个Activity:")
    for i, activity in enumerate(activities, 1):
        print(f"  {i}. {activity}")
    
    print("\n🧪 开始启动测试:")
    print("=" * 60)
    
    successful = []
    failed = []
    
    # 测试前几个Activity
    test_activities = activities[:5]  # 只测试前5个
    
    for activity in test_activities:
        if test_activity_launch(package, activity):
            successful.append(activity)
            time.sleep(2)  # 等待Activity启动
            
            # 检查Activity是否真的启动了
            ui_result = run_adb("shell uiautomator dump /sdcard/ui_test.xml")
            if ui_result:
                ui_content = run_adb("shell cat /sdcard/ui_test.xml")
                if ui_content and package in ui_content:
                    print(f"✅ 确认Activity已启动并显示")
                else:
                    print(f"⚠️  Activity启动但可能立即退出")
        else:
            failed.append(activity)
        
        time.sleep(1)  # 测试间隔
    
    print(f"\n📊 测试结果:")
    print(f"✅ 成功启动: {len(successful)}/{len(test_activities)}")
    print(f"❌ 启动失败: {len(failed)}/{len(test_activities)}")
    
    if successful:
        print(f"\n🎉 成功启动的Activity:")
        for activity in successful:
            print(f"  ✅ {activity}")
    
    if failed:
        print(f"\n💔 启动失败的Activity:")
        for activity in failed:
            print(f"  ❌ {activity}")

if __name__ == "__main__":
    main()
