#!/usr/bin/env python3
"""
测试10个Activity的详细网络抓包
使用广播方法启动Activity并详细分析网络请求
"""

import subprocess
import sys
import time
import json
from pathlib import Path
from datetime import datetime

class DetailedActivity10Tester:
    def __init__(self):
        self.package = "com.yjzx.yjzx2017"
        self.test_results = {
            "start_time": datetime.now().isoformat(),
            "package": self.package,
            "method": "broadcast_trigger",
            "activities_tested": [],
            "network_captures": [],
            "summary": {
                "total_tested": 0,
                "successful_launches": 0,
                "failed_launches": 0,
                "network_requests_captured": 0,
                "unique_hosts": set(),
                "request_methods": {},
                "status_codes": {}
            }
        }
        
    def run_adb(self, cmd, timeout=30):
        """执行adb命令"""
        full_cmd = f"source android_env.sh && adb {cmd}"
        try:
            result = subprocess.run(full_cmd, shell=True, 
                                  capture_output=True, text=True, 
                                  timeout=timeout, check=True)
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            return f"ERROR: {e.stderr}"
        except subprocess.TimeoutExpired:
            return "ERROR: Timeout"
    
    def get_network_requests_count(self):
        """获取网络请求数量"""
        try:
            realtime_file = Path("mitm-logs/realtime_capture.json")
            if realtime_file.exists():
                with open(realtime_file, 'r') as f:
                    data = json.load(f)
                    if isinstance(data, list):
                        return len(data)
            return 0
        except Exception as e:
            print(f"⚠️  读取网络捕获文件失败: {e}")
            return 0
    
    def get_network_requests_details(self):
        """获取网络请求详情"""
        try:
            realtime_file = Path("mitm-logs/realtime_capture.json")
            if realtime_file.exists():
                with open(realtime_file, 'r') as f:
                    data = json.load(f)
                    if isinstance(data, list):
                        return data
            return []
        except Exception as e:
            print(f"⚠️  读取网络请求详情失败: {e}")
            return []
    
    def analyze_network_request(self, req):
        """分析单个网络请求"""
        host = req.get('host', 'unknown')
        method = req.get('method', 'unknown')
        status_code = req.get('status_code', 'unknown')
        
        self.test_results["summary"]["unique_hosts"].add(host)
        
        if method in self.test_results["summary"]["request_methods"]:
            self.test_results["summary"]["request_methods"][method] += 1
        else:
            self.test_results["summary"]["request_methods"][method] = 1
            
        if status_code in self.test_results["summary"]["status_codes"]:
            self.test_results["summary"]["status_codes"][status_code] += 1
        else:
            self.test_results["summary"]["status_codes"][status_code] = 1
    
    def simulate_user_interaction(self):
        """模拟用户交互"""
        print("🖱️  模拟用户交互...")
        
        # 模拟点击屏幕中央
        self.run_adb("shell input tap 540 960")
        time.sleep(1)
        
        # 模拟滑动
        self.run_adb("shell input swipe 540 1500 540 500 1000")
        time.sleep(1)
        
        # 模拟返回键
        self.run_adb("shell input keyevent 4")
        time.sleep(1)
    
    def launch_activity_broadcast(self, activity, index):
        """使用广播方法启动单个Activity"""
        print(f"\n🚀 [{index}/10] 广播启动Activity: {activity}")
        print("-" * 80)
        
        activity_result = {
            "name": activity,
            "index": index,
            "method": "broadcast_trigger",
            "start_time": datetime.now().isoformat(),
            "launch_success": False,
            "network_before": 0,
            "network_after": 0,
            "network_increase": 0,
            "error_message": None,
            "captured_requests": [],
            "new_hosts": []
        }
        
        # 记录启动前的网络活动
        activity_result["network_before"] = self.get_network_requests_count()
        before_requests = self.get_network_requests_details()
        before_hosts = set(req.get('host', 'unknown') for req in before_requests)
        
        print(f"📡 启动前网络请求数: {activity_result['network_before']}")
        
        # 使用广播方法启动Activity
        cmd = f"shell am broadcast -a android.intent.action.MAIN -n {self.package}/{activity}"
        result = self.run_adb(cmd)
        
        if result and "ERROR" not in result and "Exception" not in result:
            print(f"✅ 广播发送成功")
            activity_result["launch_success"] = True
            self.test_results["summary"]["successful_launches"] += 1
            
            # 等待Activity加载
            print("⏳ 等待Activity加载...")
            time.sleep(3)
            
            # 模拟用户交互
            self.simulate_user_interaction()
            
            # 再等待网络请求
            print("⏳ 等待网络请求...")
            time.sleep(5)
            
            # 记录启动后的网络活动
            activity_result["network_after"] = self.get_network_requests_count()
            activity_result["network_increase"] = activity_result["network_after"] - activity_result["network_before"]
            
            print(f"📡 启动后网络请求数: {activity_result['network_after']}")
            
            if activity_result["network_increase"] > 0:
                print(f"🎉 检测到网络活动: +{activity_result['network_increase']} 个请求")
                self.test_results["summary"]["network_requests_captured"] += activity_result["network_increase"]
                
                # 获取新增的网络请求详情
                after_requests = self.get_network_requests_details()
                if len(after_requests) >= activity_result["network_after"]:
                    new_requests = after_requests[-activity_result["network_increase"]:]
                    activity_result["captured_requests"] = new_requests
                    
                    # 分析新请求
                    after_hosts = set(req.get('host', 'unknown') for req in after_requests)
                    activity_result["new_hosts"] = list(after_hosts - before_hosts)
                    
                    print("📋 新捕获的网络请求:")
                    for i, req in enumerate(new_requests, 1):
                        self.analyze_network_request(req)
                        url = req.get('url', 'N/A')
                        method = req.get('method', 'N/A')
                        status = req.get('status_code', 'N/A')
                        host = req.get('host', 'N/A')
                        
                        print(f"   {i}. {method} {url}")
                        print(f"      主机: {host}")
                        print(f"      状态: {status} {req.get('status_message', '')}")
                        
                        if 'response_time' in req:
                            print(f"      响应时间: {req['response_time']:.3f}s")
                        
                        if 'user_agent' in req:
                            ua = req['user_agent']
                            if len(ua) > 50:
                                ua = ua[:50] + "..."
                            print(f"      User-Agent: {ua}")
                        
                        # 显示请求体大小
                        if 'request_body_size' in req and req['request_body_size'] > 0:
                            print(f"      请求体大小: {req['request_body_size']} bytes")
                        
                        # 显示响应体大小
                        if 'response_body_size' in req and req['response_body_size'] > 0:
                            print(f"      响应体大小: {req['response_body_size']} bytes")
                        
                        print()
                    
                    if activity_result["new_hosts"]:
                        print(f"🌐 新发现的主机: {', '.join(activity_result['new_hosts'])}")
                
            else:
                print("📡 未检测到新的网络活动")
            
        else:
            print(f"❌ 广播发送失败")
            activity_result["error_message"] = result
            self.test_results["summary"]["failed_launches"] += 1
        
        activity_result["end_time"] = datetime.now().isoformat()
        self.test_results["activities_tested"].append(activity_result)
        
        # 短暂休息
        time.sleep(2)
        
        return activity_result["launch_success"]
    
    def run_test(self):
        """运行10个Activity测试"""
        print("🎯 开始10个Activity详细网络抓包测试")
        print("📡 使用广播触发方法 + 用户交互模拟")
        print("=" * 80)
        
        # 测试的Activity（选择不同类型的Activity）
        test_activities = [
            "com.yjzx.yjzx2017.controller.activity.MainActivity",
            "com.yjzx.yjzx2017.controller.login.activity.LoginActivity", 
            "com.yjzx.yjzx2017.controller.login.activity.RegistActivity",
            "com.yjzx.yjzx2017.controller.activity.setting.SettingActivity",
            "com.yjzx.yjzx2017.controller.camera.CameraNewActivity",
            "com.yjzx.yjzx2017.controller.activity.auction.AuctionSelfBuyActivity",
            "com.yjzx.yjzx2017.controller.selfAuction.SelfAuctionAddActivity",
            "com.yjzx.yjzx2017.controller.login.activity.ForgotPasswordActivity",
            "com.yjzx.yjzx2017.controller.activity.setting.AddressCompanyActivity",
            "com.yjzx.yjzx2017.controller.selfAuction.SelfAuctionManageActivity"
        ]
        
        self.test_results["summary"]["total_tested"] = len(test_activities)
        
        print(f"📊 将测试 {len(test_activities)} 个Activity")
        print(f"🌐 网络抓包已启动")
        print("\n开始测试...")
        
        # 记录初始网络状态
        initial_network_count = self.get_network_requests_count()
        initial_requests = self.get_network_requests_details()
        print(f"📡 初始网络请求数: {initial_network_count}")
        
        # 显示已有的网络请求
        if initial_network_count > 0:
            print(f"📋 已有的网络请求:")
            for i, req in enumerate(initial_requests, 1):
                print(f"   {i}. {req.get('method', 'N/A')} {req.get('url', 'N/A')}")
        
        # 测试每个Activity
        for i, activity in enumerate(test_activities, 1):
            try:
                self.launch_activity_broadcast(activity, i)
                
                # 每3个Activity显示一次进度
                if i % 3 == 0:
                    current_count = self.get_network_requests_count()
                    total_increase = current_count - initial_network_count
                    success_rate = (self.test_results["summary"]["successful_launches"] / i) * 100
                    print(f"\n📈 进度报告 [{i}/10]:")
                    print(f"   启动成功率: {success_rate:.1f}%")
                    print(f"   累计新增请求: +{total_increase} 个")
                    print(f"   发现主机数: {len(self.test_results['summary']['unique_hosts'])}")
                    print()
                
            except KeyboardInterrupt:
                print(f"\n⚠️  用户中断测试，已完成 {i-1}/10 个Activity")
                break
            except Exception as e:
                print(f"❌ 测试异常: {e}")
                continue
        
        # 记录最终网络状态
        final_network_count = self.get_network_requests_count()
        total_network_increase = final_network_count - initial_network_count
        
        print(f"\n📡 最终网络请求数: {final_network_count}")
        print(f"📡 总网络增长: +{total_network_increase} 个请求")
        
        # 完成测试
        self.test_results["end_time"] = datetime.now().isoformat()
        self.test_results["total_network_increase"] = total_network_increase
        
        # 转换set为list以便JSON序列化
        self.test_results["summary"]["unique_hosts"] = list(self.test_results["summary"]["unique_hosts"])
        
        self.generate_report()
    
    def generate_report(self):
        """生成详细测试报告"""
        print("\n" + "=" * 80)
        print("📊 10个Activity详细网络抓包测试报告")
        print("=" * 80)
        
        summary = self.test_results["summary"]
        
        print(f"📱 测试应用: {self.package}")
        print(f"🔧 启动方法: 广播触发 + 用户交互模拟")
        print(f"🕐 测试时间: {self.test_results['start_time']} - {self.test_results.get('end_time', '进行中')}")
        print(f"📋 测试Activity数: {summary['total_tested']}")
        print(f"✅ 启动成功: {summary['successful_launches']}")
        print(f"❌ 启动失败: {summary['failed_launches']}")
        print(f"📡 捕获网络请求: {summary['network_requests_captured']}")
        print(f"📈 总网络增长: +{self.test_results.get('total_network_increase', 0)} 个请求")
        print(f"🌐 发现主机数: {len(summary['unique_hosts'])}")
        
        if summary['total_tested'] > 0:
            success_rate = (summary['successful_launches'] / summary['total_tested']) * 100
            print(f"📊 启动成功率: {success_rate:.1f}%")
        
        # 显示发现的主机
        if summary['unique_hosts']:
            print(f"\n🌐 发现的主机列表:")
            for i, host in enumerate(sorted(summary['unique_hosts']), 1):
                print(f"   {i}. {host}")
        
        # 显示请求方法统计
        if summary['request_methods']:
            print(f"\n📊 HTTP方法统计:")
            for method, count in sorted(summary['request_methods'].items()):
                print(f"   {method}: {count} 次")
        
        # 显示状态码统计
        if summary['status_codes']:
            print(f"\n📊 状态码统计:")
            for status, count in sorted(summary['status_codes'].items()):
                print(f"   {status}: {count} 次")
        
        # 保存详细报告
        report_file = f"activity_detailed_10_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 详细报告已保存: {report_file}")
        
        # 显示有网络活动的Activity
        active_activities = [a for a in self.test_results["activities_tested"] 
                           if a.get("network_increase", 0) > 0]
        
        if active_activities:
            print(f"\n🌐 有网络活动的Activity:")
            for i, activity in enumerate(active_activities, 1):
                requests = activity.get("network_increase", 0)
                hosts = activity.get("new_hosts", [])
                print(f"   {i}. {activity['name']}: +{requests} 请求")
                if hosts:
                    print(f"      新主机: {', '.join(hosts)}")

def main():
    print("🚀 易金在线APK - 10个Activity详细网络抓包测试")
    print("🔧 使用广播触发方法 + 用户交互模拟 + 详细分析")
    print("=" * 80)
    
    tester = DetailedActivity10Tester()
    
    try:
        tester.run_test()
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
        tester.generate_report()
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        tester.generate_report()

if __name__ == "__main__":
    main()
