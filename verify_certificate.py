#!/usr/bin/env python3
"""
验证mitmproxy证书安装状态
检查证书是否正确安装并被Android信任
"""

import subprocess
import sys
import time
import json
from pathlib import Path
from datetime import datetime

class CertificateVerifier:
    def __init__(self):
        self.cert_file = "mitmproxy-ca-cert.pem"
        
    def run_adb(self, cmd, timeout=30):
        """执行adb命令"""
        full_cmd = f"source android_env.sh && adb {cmd}"
        try:
            result = subprocess.run(full_cmd, shell=True, 
                                  capture_output=True, text=True, 
                                  timeout=timeout, check=True)
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            return f"ERROR: {e.stderr}"
        except subprocess.TimeoutExpired:
            return "ERROR: Timeout"
    
    def check_device_connection(self):
        """检查设备连接"""
        print("🔍 检查设备连接...")
        result = self.run_adb("devices")
        if "device" in result and "ERROR" not in result:
            print("✅ Android设备已连接")
            return True
        else:
            print("❌ Android设备未连接")
            return False
    
    def check_proxy_settings(self):
        """检查代理设置"""
        print("🔍 检查代理设置...")
        result = self.run_adb("shell settings get global http_proxy")
        
        if "*************:8080" in result:
            print("✅ 代理设置正确: *************:8080")
            return True
        else:
            print(f"⚠️  代理设置: {result}")
            print("🔧 重新设置代理...")
            self.run_adb("shell settings put global http_proxy *************:8080")
            return True
    
    def check_user_certificates(self):
        """检查用户安装的证书"""
        print("🔍 检查用户证书...")
        
        # 尝试列出用户证书
        result = self.run_adb("shell ls -la /data/misc/user/0/cacerts-added/")
        if "ERROR" not in result and result:
            print("✅ 发现用户证书目录")
            print(f"📋 用户证书: {result}")
            return True
        else:
            print("⚠️  无法访问用户证书目录（可能需要权限）")
            return False
    
    def check_system_certificates(self):
        """检查系统证书"""
        print("🔍 检查系统证书...")
        
        # 检查是否有mitmproxy相关的系统证书
        result = self.run_adb("shell ls -la /system/etc/security/cacerts/ | wc -l")
        if "ERROR" not in result:
            cert_count = result.strip()
            print(f"📋 系统证书总数: {cert_count}")
            
            # 检查特定的证书hash
            cert_hashes = ["c8750f0d.0", "269953fb.0", "5ed36f99.0"]  # 常见的mitmproxy证书hash
            for cert_hash in cert_hashes:
                result = self.run_adb(f"shell ls -la /system/etc/security/cacerts/{cert_hash}")
                if "ERROR" not in result and cert_hash in result:
                    print(f"✅ 找到可能的mitmproxy系统证书: {cert_hash}")
                    return True
            
            print("⚠️  未找到mitmproxy系统证书")
            return False
        else:
            print("❌ 无法访问系统证书目录")
            return False
    
    def test_certificate_trust(self):
        """测试证书信任"""
        print("🔍 测试证书信任...")
        
        # 清空之前的捕获文件
        realtime_file = Path("mitm-logs/realtime_capture.json")
        if realtime_file.exists():
            with open(realtime_file, 'w') as f:
                json.dump([], f)
        
        # 等待清空生效
        time.sleep(2)
        
        print("🌐 尝试访问HTTPS网站...")
        
        # 测试访问不同的HTTPS网站
        test_urls = [
            "https://httpbin.org/get",
            "https://www.google.com",
            "https://api.github.com"
        ]
        
        for url in test_urls:
            print(f"   访问: {url}")
            self.run_adb(f"shell am start -a android.intent.action.VIEW -d '{url}'")
            time.sleep(3)
        
        # 等待网络请求
        print("⏳ 等待网络请求...")
        time.sleep(5)
        
        # 检查是否捕获到HTTPS请求
        try:
            if realtime_file.exists():
                with open(realtime_file, 'r') as f:
                    data = json.load(f)
                    if isinstance(data, list) and len(data) > 0:
                        https_requests = [req for req in data if req.get('scheme') == 'https']
                        http_requests = [req for req in data if req.get('scheme') == 'http']
                        
                        print(f"📊 捕获结果:")
                        print(f"   HTTPS请求: {len(https_requests)}")
                        print(f"   HTTP请求: {len(http_requests)}")
                        
                        if https_requests:
                            print("🎉 证书信任测试成功！")
                            for i, req in enumerate(https_requests[:3], 1):  # 显示前3个
                                print(f"   {i}. {req.get('method', 'N/A')} {req.get('url', 'N/A')}")
                            return True
                        else:
                            print("❌ 没有捕获到HTTPS请求")
                            return False
                    else:
                        print("❌ 没有捕获到任何网络请求")
                        return False
        except Exception as e:
            print(f"❌ 检查捕获文件失败: {e}")
            return False
    
    def test_app_https_requests(self):
        """测试应用的HTTPS请求"""
        print("🔍 测试应用HTTPS请求...")
        
        # 清空捕获文件
        realtime_file = Path("mitm-logs/realtime_capture.json")
        if realtime_file.exists():
            with open(realtime_file, 'w') as f:
                json.dump([], f)
        
        time.sleep(2)
        
        print("🚀 启动易金在线应用...")
        
        # 启动应用的主要Activity
        activities = [
            "com.yjzx.yjzx2017.controller.activity.splash.SplashActivity",
            "com.yjzx.yjzx2017.controller.activity.MainActivity",
            "com.yjzx.yjzx2017.controller.login.activity.LoginActivity"
        ]
        
        for activity in activities:
            print(f"   启动: {activity.split('.')[-1]}")
            self.run_adb(f"shell am broadcast -a android.intent.action.MAIN -n com.yjzx.yjzx2017/{activity}")
            time.sleep(3)
            
            # 模拟用户交互
            self.run_adb("shell input tap 540 960")
            time.sleep(2)
        
        print("⏳ 等待应用网络请求...")
        time.sleep(8)
        
        # 检查捕获结果
        try:
            if realtime_file.exists():
                with open(realtime_file, 'r') as f:
                    data = json.load(f)
                    if isinstance(data, list) and len(data) > 0:
                        app_requests = []
                        for req in data:
                            host = req.get('host', '').lower()
                            if any(domain in host for domain in ['yjzx.com', 'jpush.cn', 'qq.com', 'sobot.com']):
                                app_requests.append(req)
                        
                        print(f"📊 应用网络请求:")
                        print(f"   总请求数: {len(data)}")
                        print(f"   应用相关: {len(app_requests)}")
                        
                        if app_requests:
                            print("🎉 成功捕获应用HTTPS请求！")
                            for i, req in enumerate(app_requests[:5], 1):  # 显示前5个
                                print(f"   {i}. {req.get('method', 'N/A')} {req.get('url', 'N/A')}")
                                print(f"      状态: {req.get('status_code', 'N/A')}")
                            return True
                        else:
                            print("⚠️  捕获到网络请求，但不是应用相关的")
                            # 显示所有请求用于调试
                            for i, req in enumerate(data[:5], 1):
                                print(f"   {i}. {req.get('method', 'N/A')} {req.get('url', 'N/A')}")
                            return False
                    else:
                        print("❌ 没有捕获到任何网络请求")
                        return False
        except Exception as e:
            print(f"❌ 检查应用请求失败: {e}")
            return False
    
    def check_mitmproxy_status(self):
        """检查mitmproxy状态"""
        print("🔍 检查mitmproxy状态...")
        
        try:
            # 检查mitmproxy是否在运行
            import requests
            response = requests.get("http://127.0.0.1:8080", timeout=5)
            print("✅ mitmproxy正在运行")
            return True
        except Exception as e:
            print(f"❌ mitmproxy连接失败: {e}")
            return False
    
    def run_comprehensive_verification(self):
        """运行全面的证书验证"""
        print("🚀 mitmproxy证书安装验证")
        print("🔧 全面检查证书安装和信任状态")
        print("=" * 60)
        
        verification_results = []
        
        # 检查项目列表
        checks = [
            ("设备连接", self.check_device_connection),
            ("mitmproxy状态", self.check_mitmproxy_status),
            ("代理设置", self.check_proxy_settings),
            ("用户证书", self.check_user_certificates),
            ("系统证书", self.check_system_certificates),
            ("证书信任测试", self.test_certificate_trust),
            ("应用HTTPS测试", self.test_app_https_requests)
        ]
        
        print("开始验证...\n")
        
        for check_name, check_func in checks:
            print(f"{'='*40}")
            print(f"检查项目: {check_name}")
            print(f"{'='*40}")
            
            try:
                result = check_func()
                verification_results.append((check_name, result))
                
                if result:
                    print(f"✅ {check_name}: 通过")
                else:
                    print(f"❌ {check_name}: 失败")
                    
            except Exception as e:
                print(f"❌ {check_name}: 异常 - {e}")
                verification_results.append((check_name, False))
            
            print()
        
        # 生成总结报告
        print("=" * 60)
        print("📊 证书验证总结报告")
        print("=" * 60)
        
        passed = sum(1 for _, result in verification_results if result)
        total = len(verification_results)
        
        print(f"📈 验证通过率: {passed}/{total} ({passed/total*100:.1f}%)")
        print()
        
        for check_name, result in verification_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {check_name}: {status}")
        
        print()
        
        if passed == total:
            print("🎉 所有验证通过！证书安装完美！")
            print("🚀 HTTPS抓包功能完全正常，可以进行完整的网络分析！")
            return True
        elif passed >= total * 0.7:  # 70%以上通过
            print("⚠️  大部分验证通过，证书基本工作正常")
            print("🔧 可能需要微调某些设置")
            return True
        else:
            print("❌ 多项验证失败，证书安装可能有问题")
            print()
            print("🔧 建议的解决方案:")
            print("   1. 确认在Android设备中手动安装了证书")
            print("   2. 检查证书是否被标记为'受信任'")
            print("   3. 尝试重启Android设备")
            print("   4. 考虑使用root权限安装系统证书")
            print("   5. 检查应用是否有网络安全配置限制")
            return False

def main():
    verifier = CertificateVerifier()
    
    try:
        success = verifier.run_comprehensive_verification()
        
        if success:
            print("\n🎯 证书验证成功！现在可以运行完整的HTTPS网络抓包测试了！")
        else:
            print("\n🔧 需要进一步解决证书问题")
            
    except KeyboardInterrupt:
        print("\n⚠️  验证被用户中断")
    except Exception as e:
        print(f"\n❌ 验证异常: {e}")

if __name__ == "__main__":
    main()
