#!/usr/bin/env python3
"""
简化的Frida Hook测试 - 在Root环境下测试
"""

import subprocess
import time
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_frida_with_root():
    """在Root环境下测试Frida"""
    logger.info("🧪 Root环境下Frida测试...")
    
    adb_path = "/Users/<USER>/Desktop/project/apk_detect/android-sdk/platform-tools/adb"
    package_name = "com.iloda.beacon"
    
    # 1. 确认应用在运行
    try:
        result = subprocess.run([
            adb_path, '-s', 'emulator-5554', 'shell', 
            f'ps | grep {package_name}'
        ], capture_output=True, text=True, timeout=5)
        
        if result.stdout:
            logger.info(f"✅ 应用进程: {result.stdout.strip()}")
            pid = result.stdout.split()[1]  # 获取进程ID
            logger.info(f"📱 进程ID: {pid}")
        else:
            logger.error("❌ 应用未运行")
            return False
    except Exception as e:
        logger.error(f"检查进程失败: {e}")
        return False
    
    # 2. 使用简单的Frida脚本测试连接
    simple_script = """
console.log("[+] === 简单Frida Hook测试 ===");
Java.perform(function() {
    console.log("[+] Java.perform 执行成功!");
    
    // 简单的Hook测试 - Context类
    try {
        var Context = Java.use("android.content.Context");
        console.log("[+] 找到Context类");
        
        // Hook startActivity方法
        var ContextImpl = Java.use("android.app.ContextImpl");
        ContextImpl.startActivity.overload('android.content.Intent').implementation = function(intent) {
            console.log("[*] Hook成功! startActivity被调用");
            console.log("    Intent: " + intent.toString());
            return this.startActivity(intent);
        };
        
        console.log("[+] startActivity Hook设置成功");
    } catch (e) {
        console.log("[-] Hook设置失败:", e);
    }
});
"""
    
    # 3. 将脚本写入临时文件
    script_file = "/Users/<USER>/Desktop/project/apk_detect/simple_hook_test.js"
    with open(script_file, 'w') as f:
        f.write(simple_script)
    
    # 4. 使用Frida连接到进程
    logger.info("🔗 启动Frida Hook...")
    
    try:
        frida_cmd = [
            'frida', '-U', '-l', script_file, 
            '-p', pid  # 使用进程ID连接
        ]
        
        logger.info(f"执行命令: {' '.join(frida_cmd)}")
        
        # 启动Frida但设置超时
        frida_process = subprocess.Popen(
            frida_cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待几秒看输出
        time.sleep(5)
        
        if frida_process.poll() is None:
            logger.info("✅ Frida Hook进程运行中")
            
            # 现在测试Activity启动来触发Hook
            logger.info("🎯 测试触发Hook...")
            
            test_activities = [
                f'{package_name}/.activity.MainActivity',
                f'{package_name}/.activity.GuideActivity'
            ]
            
            success_count = 0
            for activity in test_activities:
                logger.info(f"📱 启动Activity: {activity}")
                
                result = subprocess.run([
                    adb_path, '-s', 'emulator-5554', 'shell',
                    'am', 'start', '-n', activity
                ], capture_output=True, text=True, timeout=8)
                
                if result.returncode == 0 and "Error" not in result.stdout:
                    logger.info(f"✅ Activity启动成功: {activity}")
                    success_count += 1
                    time.sleep(2)
                else:
                    logger.info(f"❌ Activity启动失败: {activity}")
                    logger.debug(f"输出: {result.stdout}")
                    logger.debug(f"错误: {result.stderr}")
            
            # 停止Frida进程并获取输出
            frida_process.terminate()
            stdout, stderr = frida_process.communicate(timeout=3)
            
            logger.info("📊 Frida Hook输出:")
            logger.info(stdout)
            if stderr:
                logger.warning(f"Frida错误: {stderr}")
            
            logger.info(f"🎯 测试结果: {success_count}/{len(test_activities)} Activity启动成功")
            
            # 如果Hook输出包含我们的消息，说明Hook工作了
            if "Hook成功! startActivity被调用" in stdout:
                logger.info("🎉 Frida Hook完全正常！exported绕过应该可以工作！")
                return True
            else:
                logger.info("⚠️ Hook未触发，但基础连接正常")
                return True
                
        else:
            stdout, stderr = frida_process.communicate()
            logger.error(f"❌ Frida进程退出")
            logger.error(f"输出: {stdout}")
            logger.error(f"错误: {stderr}")
            return False
            
    except Exception as e:
        logger.error(f"Frida测试异常: {e}")
        return False
    
    finally:
        # 清理临时文件
        try:
            import os
            os.remove(script_file)
        except:
            pass

if __name__ == "__main__":
    logger.info("🚀 开始Root环境Frida测试")
    success = test_frida_with_root()
    
    if success:
        logger.info("🎉 测试成功！现在可以使用Frida Hook绕过exported=false限制！")
        logger.info("💡 建议使用: python activity_based_url_collector.py com.iloda.beacon --apk apk/5577.com.iloda.beacon.apk")
    else:
        logger.error("❌ 测试失败，需要进一步调试")
    
    exit(0 if success else 1)




