console.log("[*] <PERSON>rip<PERSON> loaded");

if (Java.available) {
    console.log("[*] Java is available");
    
    Java.perform(function() {
        console.log("[*] Inside Java.perform");
        
        // 测试基本的 Hook
        try {
            var URL = Java.use('java.net.URL');
            console.log("[*] java.net.URL class found");
            
            URL.$init.overload('java.lang.String').implementation = function(url) {
                console.log("[URL] " + url);
                return this.$init(url);
            };
            console.log("[*] URL hook installed");
        } catch(e) {
            console.log("[!] Error: " + e);
        }
        
        // 列出一些类
        Java.enumerateLoadedClasses({
            onMatch: function(className) {
                if (className.indexOf("com.iloda.beacon") !== -1) {
                    console.log("[Class] " + className);
                }
            },
            onComplete: function() {
                console.log("[*] Class enumeration complete");
            }
        });
    });
} else {
    console.log("[!] Java is NOT available");
}




