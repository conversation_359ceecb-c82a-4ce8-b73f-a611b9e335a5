#!/usr/bin/env python3
"""
使用广播方法测试启动20个Activity并进行网络抓包
基于之前成功的广播触发方法
"""

import subprocess
import sys
import time
import json
from pathlib import Path
from datetime import datetime

class BroadcastActivity20Tester:
    def __init__(self):
        self.package = "com.yjzx.yjzx2017"
        self.test_results = {
            "start_time": datetime.now().isoformat(),
            "package": self.package,
            "method": "broadcast_trigger",
            "activities_tested": [],
            "network_captures": [],
            "summary": {
                "total_tested": 0,
                "successful_launches": 0,
                "failed_launches": 0,
                "network_activity_detected": 0
            }
        }
        
    def run_adb(self, cmd, timeout=30):
        """执行adb命令"""
        full_cmd = f"source android_env.sh && adb {cmd}"
        try:
            result = subprocess.run(full_cmd, shell=True, 
                                  capture_output=True, text=True, 
                                  timeout=timeout, check=True)
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            return f"ERROR: {e.stderr}"
        except subprocess.TimeoutExpired:
            return "ERROR: Timeout"
    
    def get_activity_list(self):
        """获取Activity列表"""
        print("🔍 获取Activity列表...")
        
        apk_files = list(Path("apk").glob(f"*{self.package.split('.')[-1]}*.apk"))
        if not apk_files:
            print("❌ 未找到APK文件")
            return []
        
        apk_file = apk_files[0]
        print(f"📱 分析APK: {apk_file}")
        
        cmd = f"source android_env.sh && $ANDROID_HOME/build-tools/*/aapt dump xmltree {apk_file} AndroidManifest.xml | grep -A 2 -B 2 'activity' | grep 'android:name'"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        activities = []
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            for line in lines:
                if 'android:name' in line and 'Raw:' in line:
                    try:
                        name_part = line.split('Raw: "')[1].split('"')[0]
                        if 'Activity' in name_part and 'Provider' not in name_part:
                            activities.append(name_part)
                    except IndexError:
                        continue
        
        print(f"📋 找到 {len(activities)} 个Activity")
        return activities
    
    def check_network_activity_count(self):
        """检查当前网络活动数量"""
        try:
            # 检查实时捕获文件
            realtime_file = Path("mitm-logs/realtime_capture.json")
            if realtime_file.exists():
                with open(realtime_file, 'r') as f:
                    data = json.load(f)
                    return len(data) if isinstance(data, list) else 0
            return 0
        except Exception:
            return 0
    
    def launch_activity_broadcast(self, activity, index):
        """使用广播方法启动单个Activity"""
        print(f"\n🚀 [{index}/20] 广播启动Activity: {activity}")
        print("-" * 80)
        
        activity_result = {
            "name": activity,
            "index": index,
            "method": "broadcast_trigger",
            "start_time": datetime.now().isoformat(),
            "launch_success": False,
            "network_before": 0,
            "network_after": 0,
            "network_increase": 0,
            "error_message": None
        }
        
        # 记录启动前的网络活动
        activity_result["network_before"] = self.check_network_activity_count()
        
        # 使用广播方法启动Activity
        cmd = f"shell am broadcast -a android.intent.action.MAIN -n {self.package}/{activity}"
        result = self.run_adb(cmd)
        
        if result and "ERROR" not in result and "Exception" not in result:
            print(f"✅ 广播发送成功")
            activity_result["launch_success"] = True
            self.test_results["summary"]["successful_launches"] += 1
            
            # 等待Activity加载和可能的网络请求
            print("⏳ 等待网络活动...")
            time.sleep(5)
            
            # 记录启动后的网络活动
            activity_result["network_after"] = self.check_network_activity_count()
            activity_result["network_increase"] = activity_result["network_after"] - activity_result["network_before"]
            
            if activity_result["network_increase"] > 0:
                print(f"📡 检测到网络活动: +{activity_result['network_increase']} 个请求")
                self.test_results["summary"]["network_activity_detected"] += 1
            else:
                print("📡 未检测到新的网络活动")
            
        else:
            print(f"❌ 广播发送失败")
            activity_result["error_message"] = result
            self.test_results["summary"]["failed_launches"] += 1
        
        activity_result["end_time"] = datetime.now().isoformat()
        self.test_results["activities_tested"].append(activity_result)
        
        # 短暂休息
        time.sleep(2)
        
        return activity_result["launch_success"]
    
    def run_test(self):
        """运行20个Activity广播测试"""
        print("🎯 开始20个Activity广播网络抓包测试")
        print("📡 使用广播触发方法 (android.intent.action.MAIN)")
        print("=" * 80)
        
        # 获取Activity列表
        activities = self.get_activity_list()
        if not activities:
            print("❌ 没有找到Activity，测试终止")
            return
        
        # 选择前20个Activity进行测试
        test_activities = activities[:20]
        self.test_results["summary"]["total_tested"] = len(test_activities)
        
        print(f"📊 将测试 {len(test_activities)} 个Activity")
        print(f"🌐 网络抓包已启动")
        print("\n开始测试...")
        
        # 记录初始网络状态
        initial_network_count = self.check_network_activity_count()
        print(f"📡 初始网络请求数: {initial_network_count}")
        
        # 测试每个Activity
        for i, activity in enumerate(test_activities, 1):
            try:
                self.launch_activity_broadcast(activity, i)
                
                # 每5个Activity显示一次进度
                if i % 5 == 0:
                    success_rate = (self.test_results["summary"]["successful_launches"] / i) * 100
                    network_rate = (self.test_results["summary"]["network_activity_detected"] / i) * 100
                    print(f"\n📈 进度报告 [{i}/20]:")
                    print(f"   启动成功率: {success_rate:.1f}%")
                    print(f"   网络活动率: {network_rate:.1f}%")
                    print()
                
            except KeyboardInterrupt:
                print(f"\n⚠️  用户中断测试，已完成 {i-1}/20 个Activity")
                break
            except Exception as e:
                print(f"❌ 测试异常: {e}")
                continue
        
        # 记录最终网络状态
        final_network_count = self.check_network_activity_count()
        total_network_increase = final_network_count - initial_network_count
        
        print(f"\n📡 最终网络请求数: {final_network_count}")
        print(f"📡 总网络增长: +{total_network_increase} 个请求")
        
        # 完成测试
        self.test_results["end_time"] = datetime.now().isoformat()
        self.test_results["total_network_increase"] = total_network_increase
        self.generate_report()
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "=" * 80)
        print("📊 20个Activity广播网络抓包测试报告")
        print("=" * 80)
        
        summary = self.test_results["summary"]
        
        print(f"📱 测试应用: {self.package}")
        print(f"🔧 启动方法: 广播触发 (android.intent.action.MAIN)")
        print(f"🕐 测试时间: {self.test_results['start_time']} - {self.test_results.get('end_time', '进行中')}")
        print(f"📋 测试Activity数: {summary['total_tested']}")
        print(f"✅ 启动成功: {summary['successful_launches']}")
        print(f"❌ 启动失败: {summary['failed_launches']}")
        print(f"📡 有网络活动: {summary['network_activity_detected']}")
        print(f"📈 总网络增长: +{self.test_results.get('total_network_increase', 0)} 个请求")
        
        if summary['total_tested'] > 0:
            success_rate = (summary['successful_launches'] / summary['total_tested']) * 100
            network_rate = (summary['network_activity_detected'] / summary['total_tested']) * 100
            print(f"📊 启动成功率: {success_rate:.1f}%")
            print(f"🌐 网络活动率: {network_rate:.1f}%")
        
        # 保存详细报告
        report_file = f"activity_broadcast_20_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, indent=2, ensure_ascii=False)
        
        print(f"📄 详细报告已保存: {report_file}")
        
        # 显示网络活动最多的Activity
        network_activities = [a for a in self.test_results["activities_tested"] 
                            if a.get("network_increase", 0) > 0]
        
        if network_activities:
            print(f"\n🌐 有网络活动的Activity:")
            network_activities.sort(key=lambda x: x.get("network_increase", 0), reverse=True)
            for i, activity in enumerate(network_activities, 1):
                requests = activity.get("network_increase", 0)
                print(f"   {i}. {activity['name']}: +{requests} 请求")
        
        # 显示启动成功的Activity
        successful_activities = [a for a in self.test_results["activities_tested"] 
                               if a.get("launch_success", False)]
        
        if successful_activities:
            print(f"\n✅ 启动成功的Activity:")
            for i, activity in enumerate(successful_activities, 1):
                print(f"   {i}. {activity['name']}")

def main():
    print("🚀 易金在线APK - 20个Activity广播网络抓包测试")
    print("🔧 使用广播触发方法绕过导出限制")
    print("=" * 80)
    
    tester = BroadcastActivity20Tester()
    
    try:
        tester.run_test()
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
        tester.generate_report()
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        tester.generate_report()

if __name__ == "__main__":
    main()
