#!/usr/bin/env python3
"""
简单的HTTPS测试
直接验证是否能捕获HTTPS请求
"""

import subprocess
import sys
import time
import json
from pathlib import Path

def log(message):
    timestamp = time.strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def run_adb(cmd):
    full_cmd = f"source android_env.sh && adb {cmd}"
    result = subprocess.run(full_cmd, shell=True, capture_output=True, text=True)
    return result.returncode, result.stdout.strip(), result.stderr.strip()

def main():
    log("🔍 简单HTTPS测试")
    
    # 1. 清理环境
    log("清理环境...")
    subprocess.run("pkill -f mitmdump", shell=True, capture_output=True)
    run_adb("shell pkill frida-server")
    run_adb("shell settings delete global http_proxy")
    time.sleep(3)
    
    # 2. 启动mitmproxy
    log("启动mitmproxy...")
    Path("mitm-logs").mkdir(exist_ok=True)
    
    capture_file = Path("mitm-logs/realtime_capture.json")
    with open(capture_file, 'w') as f:
        json.dump([], f)
    
    mitm_cmd = "/Users/<USER>/Library/Python/3.9/bin/mitmdump -s mitm-scripts/capture.py --listen-port 8080"
    mitmproxy_process = subprocess.Popen(mitm_cmd, shell=True)
    time.sleep(10)
    
    # 3. 设置代理
    log("设置代理...")
    run_adb("shell settings put global http_proxy ********:8080")
    
    # 4. 测试HTTP请求
    log("测试HTTP请求...")
    run_adb("shell am start -a android.intent.action.VIEW -d http://httpbin.org/get")
    time.sleep(8)
    
    # 5. 测试HTTPS请求
    log("测试HTTPS请求...")
    run_adb("shell am start -a android.intent.action.VIEW -d https://httpbin.org/get")
    time.sleep(8)
    
    # 6. 检查结果
    log("检查捕获结果...")
    time.sleep(5)
    
    try:
        with open(capture_file, 'r') as f:
            data = json.load(f)
        
        total = len(data)
        https_count = len([req for req in data if req.get('url', '').startswith('https://')])
        http_count = len([req for req in data if req.get('url', '').startswith('http://')])
        
        log(f"总请求: {total}")
        log(f"HTTP请求: {http_count}")
        log(f"HTTPS请求: {https_count}")
        
        if https_count > 0:
            log("✅ HTTPS捕获成功！")
            for req in data:
                if req.get('url', '').startswith('https://'):
                    log(f"  HTTPS: {req.get('method')} {req.get('url')}")
        else:
            log("❌ HTTPS捕获失败")
            if http_count > 0:
                log("但HTTP捕获正常")
            else:
                log("连HTTP都没捕获到")
        
    except Exception as e:
        log(f"检查失败: {e}")
    
    # 清理
    mitmproxy_process.terminate()
    run_adb("shell settings delete global http_proxy")

if __name__ == "__main__":
    main()
