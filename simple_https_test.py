#!/usr/bin/env python3
"""
简化的HTTPS流量测试
直接监控Android应用的网络活动
"""

import subprocess
import time
import json
import os
from datetime import datetime

class SimpleHTTPSTest:
    def __init__(self):
        self.device_id = "emulator-5554"
        self.package_name = "com.iloda.beacon"
        
    def run_adb_command(self, command, timeout=30):
        """执行ADB命令"""
        try:
            full_command = ['adb', '-s', self.device_id] + command
            result = subprocess.run(
                full_command, 
                capture_output=True, 
                text=True, 
                timeout=timeout
            )
            return result
        except Exception as e:
            print(f"ADB命令执行异常: {e}")
            return None
    
    def test_network_permissions(self):
        """测试应用网络权限"""
        print("🔍 检查应用网络权限...")
        
        result = self.run_adb_command([
            'shell', 'dumpsys', 'package', self.package_name
        ])
        
        if result and result.returncode == 0:
            output = result.stdout
            has_internet = 'android.permission.INTERNET' in output
            has_network_state = 'android.permission.ACCESS_NETWORK_STATE' in output
            
            print(f"   - INTERNET权限: {'✅' if has_internet else '❌'}")
            print(f"   - NETWORK_STATE权限: {'✅' if has_network_state else '❌'}")
            
            return has_internet
        
        return False
    
    def monitor_app_network_activity(self):
        """监控应用网络活动"""
        print("📊 监控应用网络活动...")
        
        try:
            # 获取应用UID
            result = self.run_adb_command([
                'shell', 'dumpsys', 'package', self.package_name, '|', 'grep', 'userId'
            ])
            
            uid = None
            if result and result.returncode == 0:
                for line in result.stdout.split('\n'):
                    if 'userId=' in line:
                        uid = line.split('userId=')[1].split()[0]
                        break
            
            print(f"   - 应用UID: {uid}")
            
            if uid:
                # 监控网络连接
                print("   - 监控网络连接...")
                result = self.run_adb_command([
                    'shell', 'netstat', '-an'
                ])
                
                if result and result.returncode == 0:
                    connections = result.stdout
                    # 简单统计连接数
                    tcp_count = connections.count('tcp')
                    udp_count = connections.count('udp')
                    
                    print(f"   - TCP连接: {tcp_count} 个")
                    print(f"   - UDP连接: {udp_count} 个")
                    
                    return True
            
            return False
            
        except Exception as e:
            print(f"网络活动监控异常: {e}")
            return False
    
    def test_with_real_app_interaction(self):
        """通过真实应用交互测试网络活动"""
        print("\n🚀 启动真实应用网络活动测试...")
        
        # 1. 检查网络权限
        has_network = self.test_network_permissions()
        if not has_network:
            print("❌ 应用没有网络权限")
            return False
        
        # 2. 停止并重启应用
        print("重启应用...")
        self.run_adb_command(['shell', 'am', 'force-stop', self.package_name])
        time.sleep(2)
        
        # 清除应用数据以触发初始化网络请求
        print("清除应用数据...")
        self.run_adb_command(['shell', 'pm', 'clear', self.package_name])
        time.sleep(2)
        
        # 启动应用
        print("启动应用...")
        launch_result = self.run_adb_command([
            'shell', 'am', 'start', '-n', 
            f'{self.package_name}/com.iloda.beacon.activity.LoginActivity'
        ])
        
        if not (launch_result and launch_result.returncode == 0):
            print("❌ 应用启动失败")
            return False
        
        print("✅ 应用启动成功")
        
        # 3. 等待应用加载并监控网络
        print("等待应用加载...")
        time.sleep(5)
        
        # 4. 监控网络活动
        network_active = self.monitor_app_network_activity()
        
        # 5. 模拟一些用户交互
        print("\n🎯 模拟用户交互...")
        interactions = [
            (969, 2014, "点击Continue按钮"),
            (540, 600, "点击输入框"),
            (540, 880, "点击验证码按钮")
        ]
        
        for x, y, desc in interactions:
            print(f"   - {desc}")
            self.run_adb_command(['shell', 'input', 'tap', str(x), str(y)])
            time.sleep(3)
            
            # 每次交互后检查网络
            self.monitor_app_network_activity()
        
        # 6. 最终网络状态检查
        print("\n📊 最终网络状态检查...")
        final_network = self.monitor_app_network_activity()
        
        return network_active or final_network
    
    def check_system_network_traffic(self):
        """检查系统级网络流量"""
        print("\n🌐 检查系统级网络流量...")
        
        try:
            # 检查系统网络统计
            result = self.run_adb_command(['shell', 'cat', '/proc/net/dev'])
            
            if result and result.returncode == 0:
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'wlan0' in line or 'eth0' in line:
                        parts = line.split()
                        if len(parts) >= 10:
                            rx_bytes = parts[1]
                            tx_bytes = parts[9]
                            print(f"   - 接收字节: {rx_bytes}")
                            print(f"   - 发送字节: {tx_bytes}")
                            
                            if int(rx_bytes) > 0 or int(tx_bytes) > 0:
                                print("   ✅ 检测到网络流量")
                                return True
        except Exception as e:
            print(f"系统网络检查异常: {e}")
        
        return False
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        print("="*60)
        print("🔒 HTTPS流量测试 - 真实网络活动验证")
        print("="*60)
        
        success_count = 0
        total_tests = 3
        
        # 测试1: 应用网络交互
        print(f"\n📱 测试1: 应用网络交互")
        if self.test_with_real_app_interaction():
            print("✅ 测试1通过")
            success_count += 1
        else:
            print("❌ 测试1失败")
        
        # 测试2: 系统网络流量
        print(f"\n🌐 测试2: 系统网络流量")
        if self.check_system_network_traffic():
            print("✅ 测试2通过")
            success_count += 1
        else:
            print("❌ 测试2失败")
        
        # 测试3: 代理连通性测试
        print(f"\n🔗 测试3: 代理连通性")
        try:
            # 测试我们是否能通过代理连接
            result = subprocess.run([
                'curl', '-x', 'localhost:8080', 'http://httpbin.org/ip', '--max-time', '10'
            ], capture_output=True, text=True)
            
            if result.returncode == 0 and 'origin' in result.stdout:
                print("✅ 测试3通过 - 代理连接正常")
                print(f"   通过代理获取的IP: {result.stdout.strip()}")
                success_count += 1
            else:
                print("❌ 测试3失败 - 代理连接有问题")
        except Exception as e:
            print(f"❌ 测试3失败: {e}")
        
        # 总结
        print(f"\n📊 测试结果总结:")
        print(f"   - 通过率: {success_count}/{total_tests} ({success_count/total_tests*100:.1f}%)")
        
        if success_count >= 2:
            print("\n🎉 网络基础设施正常！")
            print("💡 建议:")
            print("   - 代理服务器已就绪")
            print("   - 应用具备网络访问能力")
            print("   - 系统可以进行网络流量捕获")
            
            if success_count < total_tests:
                print("\n⚠️  需要改进的地方:")
                print("   - 确保mitmproxy正确配置")
                print("   - 验证SSL证书绕过脚本")
                print("   - 检查Android代理设置")
        else:
            print("\n❌ 网络基础设施存在问题")
            print("💡 需要检查:")
            print("   - mitmproxy服务状态")
            print("   - Android模拟器网络配置")  
            print("   - 代理设置和证书安装")
        
        print("="*60)
        
        return success_count >= 2

def main():
    test = SimpleHTTPSTest()
    success = test.run_comprehensive_test()
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())


