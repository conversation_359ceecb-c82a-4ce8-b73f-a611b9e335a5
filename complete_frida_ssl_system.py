#!/usr/bin/env python3
"""
完整的Frida SSL绕过系统
集成所有功能，实现完整的HTTPS流量分析
"""

import subprocess
import sys
import time
import json
import signal
from pathlib import Path
import frida

class CompleteFridaSSLSystem:
    def __init__(self):
        self.package = "com.yjzx.yjzx2017"
        self.frida_session = None
        self.frida_script = None
        self.device = None
        self.target_pid = None
        self.running = False
        
    def log(self, message, level="INFO"):
        """日志输出"""
        timestamp = time.strftime("%H:%M:%S")
        if level == "SUCCESS":
            print(f"🎉 [{timestamp}] {message}")
        elif level == "ERROR":
            print(f"❌ [{timestamp}] {message}")
        elif level == "WARNING":
            print(f"⚠️  [{timestamp}] {message}")
        else:
            print(f"📋 [{timestamp}] {message}")
    
    def run_adb(self, cmd, timeout=30):
        """执行adb命令"""
        full_cmd = f"source android_env.sh && adb {cmd}"
        try:
            result = subprocess.run(full_cmd, shell=True, 
                                  capture_output=True, text=True, 
                                  timeout=timeout, check=True)
            return result.returncode, result.stdout.strip(), result.stderr.strip()
        except subprocess.CalledProcessError as e:
            return e.returncode, "", e.stderr
        except subprocess.TimeoutExpired:
            return -1, "", "Timeout"
    
    def setup_frida_server(self):
        """设置并启动frida-server"""
        self.log("设置frida-server环境...")
        
        # 检查设备连接
        returncode, stdout, stderr = self.run_adb("devices")
        if "device" not in stdout:
            self.log("Android设备未连接", "ERROR")
            return False
        
        self.log("Android设备已连接")
        
        # 推送frida-server（如果需要）
        if not self.check_frida_server_exists():
            self.log("推送frida-server到设备...")
            returncode, stdout, stderr = self.run_adb("push frida-server /data/local/tmp/frida-server")
            if returncode != 0:
                self.log(f"推送失败: {stderr}", "ERROR")
                return False
            
            # 设置权限
            self.run_adb("shell chmod 755 /data/local/tmp/frida-server")
            self.log("frida-server推送完成")
        
        # 启动frida-server
        return self.start_frida_server()
    
    def check_frida_server_exists(self):
        """检查frida-server是否存在"""
        returncode, stdout, stderr = self.run_adb("shell ls -la /data/local/tmp/frida-server")
        return returncode == 0
    
    def start_frida_server(self):
        """启动frida-server"""
        self.log("启动frida-server...")
        
        # 杀死现有进程
        self.run_adb("shell pkill frida-server")
        time.sleep(2)
        
        # 后台启动（忽略SELinux警告）
        subprocess.Popen("source android_env.sh && adb shell '/data/local/tmp/frida-server' 2>/dev/null &", shell=True)
        time.sleep(8)
        
        # 验证启动
        returncode, stdout, stderr = self.run_adb("shell ps | grep frida-server")
        if "frida-server" in stdout:
            self.log("frida-server启动成功")
            return True
        else:
            self.log("frida-server启动失败", "ERROR")
            return False
    
    def prepare_target_app(self):
        """准备目标应用"""
        self.log("准备目标应用...")
        
        # 检查应用安装
        returncode, stdout, stderr = self.run_adb(f"shell pm list packages | grep {self.package}")
        if self.package not in stdout:
            self.log("目标应用未安装", "ERROR")
            return False
        
        self.log("目标应用已安装")
        
        # 重启应用
        self.run_adb(f"shell am force-stop {self.package}")
        time.sleep(2)
        
        # 启动应用
        returncode, stdout, stderr = self.run_adb(f"shell am start -n {self.package}/.controller.activity.splash.SplashActivity")
        if returncode == 0:
            self.log("应用启动成功")
            time.sleep(8)
            return self.get_target_pid()
        else:
            self.log(f"应用启动失败: {stderr}", "ERROR")
            return False
    
    def get_target_pid(self):
        """获取目标PID"""
        returncode, stdout, stderr = self.run_adb(f"shell ps | grep {self.package}")
        
        if self.package in stdout:
            lines = stdout.split('\n')
            for line in lines:
                if self.package in line:
                    parts = line.split()
                    if len(parts) >= 2:
                        pid = parts[1]
                        # 优先选择主进程
                        if 'pushcore' not in line:
                            self.target_pid = int(pid)
                            self.log(f"找到主进程 PID: {pid}")
                            return True
                        elif not self.target_pid:
                            self.target_pid = int(pid)
                            self.log(f"使用pushcore进程 PID: {pid}")
            
            return self.target_pid is not None
        
        self.log("未找到应用进程", "ERROR")
        return False
    
    def connect_frida(self):
        """连接Frida"""
        self.log("连接Frida到目标应用...")
        
        try:
            # 连接设备
            self.device = frida.get_usb_device()
            self.log("连接到USB设备成功")
            
            # 尝试多种连接方式
            connection_methods = [
                ("PID附加", self.attach_by_pid),
                ("包名附加", self.attach_by_name),
                ("Spawn模式", self.spawn_and_attach)
            ]
            
            for method_name, method_func in connection_methods:
                try:
                    self.log(f"尝试{method_name}...")
                    self.frida_session = method_func()
                    if self.frida_session:
                        self.log(f"{method_name}成功")
                        return True
                except Exception as e:
                    self.log(f"{method_name}失败: {e}", "WARNING")
                    continue
            
            self.log("所有连接方法失败", "ERROR")
            return False
            
        except Exception as e:
            self.log(f"Frida连接异常: {e}", "ERROR")
            return False
    
    def attach_by_pid(self):
        """通过PID附加"""
        if self.target_pid:
            return self.device.attach(self.target_pid)
        return None
    
    def attach_by_name(self):
        """通过包名附加"""
        return self.device.attach(self.package)
    
    def spawn_and_attach(self):
        """Spawn模式"""
        # 停止应用
        self.run_adb(f"shell am force-stop {self.package}")
        time.sleep(2)
        
        # Spawn应用
        pid = self.device.spawn([self.package])
        self.log(f"Spawn成功，PID: {pid}")
        
        # 附加
        session = self.device.attach(pid)
        
        # 恢复
        self.device.resume(pid)
        self.log("进程已恢复")
        
        return session
    
    def load_ssl_bypass_script(self):
        """加载SSL绕过脚本"""
        self.log("加载SSL绕过脚本...")
        
        script_file = Path("ssl_bypass.js")
        if not script_file.exists():
            self.log("SSL绕过脚本不存在", "ERROR")
            return False
        
        with open(script_file, 'r') as f:
            script_code = f.read()
        
        try:
            # 创建脚本
            self.frida_script = self.frida_session.create_script(script_code)
            
            # 消息处理
            def on_message(message, data):
                if message['type'] == 'send':
                    self.log(f"[SSL] {message['payload']}")
                elif message['type'] == 'error':
                    self.log(f"[SSL Error] {message['stack']}", "ERROR")
            
            self.frida_script.on('message', on_message)
            
            # 加载脚本
            self.frida_script.load()
            self.log("SSL绕过脚本加载成功")
            
            time.sleep(5)  # 等待初始化
            return True
            
        except Exception as e:
            self.log(f"脚本加载失败: {e}", "ERROR")
            return False
    
    def test_ssl_bypass(self):
        """测试SSL绕过"""
        self.log("测试SSL绕过效果...")
        
        # 清空捕获文件
        capture_file = Path("mitm-logs/realtime_capture.json")
        capture_file.parent.mkdir(exist_ok=True)
        
        with open(capture_file, 'w') as f:
            json.dump([], f)
        
        self.log("清空网络捕获文件")
        time.sleep(3)
        
        # 测试HTTPS请求
        test_urls = [
            "https://httpbin.org/get",
            "https://www.baidu.com",
            "https://api.github.com"
        ]
        
        self.log("触发HTTPS测试请求...")
        for i, url in enumerate(test_urls, 1):
            self.log(f"测试 {i}/{len(test_urls)}: {url}")
            self.run_adb(f"shell am start -a android.intent.action.VIEW -d {url}")
            time.sleep(8)
        
        # 应用内操作
        self.log("应用内操作...")
        self.run_adb("shell input tap 540 960")
        time.sleep(5)
        
        # 等待处理
        self.log("等待网络请求处理...")
        time.sleep(20)
        
        return self.check_results()
    
    def check_results(self):
        """检查结果"""
        capture_file = Path("mitm-logs/realtime_capture.json")
        
        try:
            with open(capture_file, 'r') as f:
                data = json.load(f)
            
            if not isinstance(data, list):
                self.log("捕获文件格式错误", "ERROR")
                return False
            
            total = len(data)
            https_reqs = [req for req in data if req.get('scheme') == 'https']
            http_reqs = [req for req in data if req.get('scheme') == 'http']
            
            self.log(f"捕获统计: 总={total}, HTTPS={len(https_reqs)}, HTTP={len(http_reqs)}")
            
            if len(https_reqs) > 0:
                self.log("SSL绕过成功！捕获到HTTPS请求！", "SUCCESS")
                
                # 显示示例
                for i, req in enumerate(https_reqs[:3], 1):
                    url = req.get('url', 'N/A')
                    method = req.get('method', 'N/A')
                    status = req.get('status_code', 'N/A')
                    self.log(f"  {i}. {method} {url} (状态: {status})")
                
                return True
            else:
                self.log("未捕获到HTTPS请求", "WARNING")
                return False
                
        except Exception as e:
            self.log(f"检查结果失败: {e}", "ERROR")
            return False
    
    def start_monitoring(self):
        """开始监控"""
        self.log("开始SSL绕过监控...")
        self.running = True
        
        try:
            while self.running:
                time.sleep(30)
                self.log("SSL绕过监控中...")
                
                # 检查会话状态
                if self.frida_session and not self.frida_session.is_detached:
                    continue
                else:
                    self.log("会话断开，尝试重连...", "WARNING")
                    if not self.reconnect():
                        break
                        
        except KeyboardInterrupt:
            self.log("用户中断监控")
    
    def reconnect(self):
        """重连"""
        try:
            if self.frida_script:
                self.frida_script.unload()
            if self.frida_session:
                self.frida_session.detach()
            
            if self.connect_frida() and self.load_ssl_bypass_script():
                self.log("重连成功")
                return True
            else:
                self.log("重连失败", "ERROR")
                return False
        except:
            return False
    
    def cleanup(self):
        """清理"""
        self.log("清理资源...")
        self.running = False
        
        try:
            if self.frida_script:
                self.frida_script.unload()
            if self.frida_session:
                self.frida_session.detach()
        except:
            pass
        
        self.run_adb("shell pkill frida-server")
        self.log("清理完成")
    
    def run_complete_system(self):
        """运行完整系统"""
        self.log("🚀 启动完整的Frida SSL绕过系统")
        self.log("=" * 60)
        
        try:
            # 步骤1: 设置frida-server
            if not self.setup_frida_server():
                return False
            
            # 步骤2: 准备应用
            if not self.prepare_target_app():
                return False
            
            # 步骤3: 连接Frida
            if not self.connect_frida():
                return False
            
            # 步骤4: 加载脚本
            if not self.load_ssl_bypass_script():
                return False
            
            # 步骤5: 测试
            self.log("=" * 50)
            self.log("🔍 测试SSL绕过效果")
            self.log("=" * 50)
            
            success = self.test_ssl_bypass()
            
            if success:
                self.log("🎉 Frida SSL绕过系统完全成功！", "SUCCESS")
                self.log("✅ HTTPS流量现在可以被完全捕获！")
                self.log("🔒 SSL证书验证已被绕过！")
                
                self.log("📋 系统功能:")
                self.log("   ✅ 完整APK动态分析")
                self.log("   ✅ HTTPS流量透明捕获")
                self.log("   ✅ SSL证书绕过")
                self.log("   ✅ 实时网络监控")
                self.log("   ✅ API端点分析")
                
                # 进入监控模式
                self.start_monitoring()
                return True
            else:
                self.log("SSL绕过测试未完全成功", "WARNING")
                self.log("但基础功能已建立，可手动测试")
                
                try:
                    time.sleep(120)  # 运行2分钟
                except KeyboardInterrupt:
                    pass
                
                return False
                
        except KeyboardInterrupt:
            self.log("用户中断")
            return False
        except Exception as e:
            self.log(f"系统异常: {e}", "ERROR")
            return False
        finally:
            self.cleanup()

def main():
    system = CompleteFridaSSLSystem()
    
    def signal_handler(sig, frame):
        print("\n⚠️  接收到中断信号...")
        system.cleanup()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    
    success = system.run_complete_system()
    
    if success:
        print("\n🎯 Frida SSL绕过系统实现成功！")
        print("💡 APK动态分析系统现在完全可用！")
    else:
        print("\n🔧 系统需要进一步调试")
        print("💡 但基础框架已建立")

if __name__ == "__main__":
    main()
