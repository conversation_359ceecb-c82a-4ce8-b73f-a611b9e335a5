#!/usr/bin/env python3
"""
手动安装mitmproxy证书的详细指南
提供逐步指导和自动化辅助
"""

import subprocess
import sys
import time
from pathlib import Path

class ManualCertInstaller:
    def __init__(self):
        self.cert_file = "mitmproxy-ca-cert.pem"
        
    def run_adb(self, cmd, timeout=30):
        """执行adb命令"""
        full_cmd = f"source android_env.sh && adb {cmd}"
        try:
            result = subprocess.run(full_cmd, shell=True, 
                                  capture_output=True, text=True, 
                                  timeout=timeout, check=True)
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            return f"ERROR: {e.stderr}"
        except subprocess.TimeoutExpired:
            return "ERROR: Timeout"
    
    def prepare_certificate(self):
        """准备证书文件"""
        print("📋 准备证书文件...")
        
        # 检查证书文件
        if not Path(self.cert_file).exists():
            print(f"❌ 证书文件不存在: {self.cert_file}")
            print("🔧 重新下载证书...")
            
            # 重新下载证书
            result = subprocess.run(
                "curl -x 127.0.0.1:8080 http://mitm.it/cert/pem -o mitmproxy-ca-cert.pem",
                shell=True, capture_output=True, text=True
            )
            
            if result.returncode != 0:
                print("❌ 证书下载失败")
                return False
        
        print(f"✅ 证书文件存在: {self.cert_file}")
        
        # 推送到设备
        print("📱 推送证书到设备...")
        result = self.run_adb(f"push {self.cert_file} /sdcard/Download/{self.cert_file}")
        if "ERROR" in result:
            print(f"❌ 推送失败: {result}")
            return False
        
        print("✅ 证书已推送到 /sdcard/Download/")
        return True
    
    def open_settings(self):
        """打开Android设置"""
        print("📱 打开Android设置...")
        
        # 打开设置应用
        self.run_adb("shell am start -a android.settings.SETTINGS")
        time.sleep(2)
        
        print("✅ 设置应用已打开")
        return True
    
    def guide_manual_installation(self):
        """指导手动安装"""
        print("\n" + "="*60)
        print("📋 手动安装证书详细步骤")
        print("="*60)
        
        steps = [
            "1. 在Android设备上，找到并点击 '设置' 应用",
            "2. 滚动找到 '安全' 或 'Security' 选项",
            "3. 点击 '安全' 进入安全设置",
            "4. 找到 '加密与凭据' 或 'Encryption & credentials'",
            "5. 点击 '从存储设备安装' 或 'Install from storage'",
            "6. 浏览到 'Download' 文件夹",
            "7. 选择 'mitmproxy-ca-cert.pem' 文件",
            "8. 输入证书名称（建议：mitmproxy）",
            "9. 选择证书用途：'VPN和应用' 或 'VPN and apps'",
            "10. 点击 '确定' 完成安装"
        ]
        
        for step in steps:
            print(f"   {step}")
        
        print("\n⚠️  重要提示:")
        print("   - 某些应用可能不信任用户安装的证书")
        print("   - 如果应用仍然无法抓包，可能需要root权限")
        print("   - Android 7.0+默认不信任用户证书")
        
        return True
    
    def test_installation(self):
        """测试证书安装"""
        print("\n🔍 测试证书安装...")
        
        # 尝试访问一个HTTPS网站
        print("🌐 测试HTTPS连接...")
        self.run_adb("shell am start -a android.intent.action.VIEW -d https://httpbin.org/get")
        time.sleep(5)
        
        # 检查是否有网络请求被捕获
        try:
            import json
            realtime_file = Path("mitm-logs/realtime_capture.json")
            if realtime_file.exists():
                with open(realtime_file, 'r') as f:
                    data = json.load(f)
                    if isinstance(data, list) and len(data) > 0:
                        https_requests = [req for req in data if req.get('scheme') == 'https']
                        if https_requests:
                            print(f"✅ 检测到 {len(https_requests)} 个HTTPS请求！")
                            return True
                        else:
                            print("⚠️  只检测到HTTP请求，HTTPS可能仍有问题")
                            return False
        except Exception as e:
            print(f"⚠️  检测异常: {e}")
        
        print("❌ 没有检测到HTTPS请求")
        return False
    
    def alternative_solutions(self):
        """提供替代解决方案"""
        print("\n" + "="*60)
        print("🔧 替代解决方案")
        print("="*60)
        
        print("如果用户证书安装后仍然无法抓包，可以尝试以下方案：")
        print()
        
        print("方案1: 使用root权限安装系统证书")
        print("   - 需要root设备")
        print("   - 将证书安装到 /system/etc/security/cacerts/")
        print("   - 应用会自动信任系统证书")
        print()
        
        print("方案2: 修改应用网络安全配置")
        print("   - 反编译APK")
        print("   - 修改 network_security_config.xml")
        print("   - 允许用户证书")
        print("   - 重新打包APK")
        print()
        
        print("方案3: 使用Xposed框架")
        print("   - 安装Xposed框架")
        print("   - 使用TrustMeAlready模块")
        print("   - 绕过证书验证")
        print()
        
        print("方案4: 使用Frida动态分析")
        print("   - 安装Frida")
        print("   - 使用SSL Kill Switch脚本")
        print("   - 运行时禁用证书验证")
        print()
        
        print("方案5: 分析HTTP流量")
        print("   - 专注于HTTP请求（已经可以抓取）")
        print("   - 分析应用的网络行为模式")
        print("   - 识别API端点和数据传输")
        
    def run_manual_installation(self):
        """运行手动安装流程"""
        print("🚀 mitmproxy证书手动安装指南")
        print("🔧 解决HTTPS抓包证书问题")
        print("=" * 60)
        
        # 准备证书
        if not self.prepare_certificate():
            return False
        
        # 打开设置
        self.open_settings()
        
        # 显示安装指南
        self.guide_manual_installation()
        
        # 等待用户完成安装
        print("\n⏳ 请按照上述步骤在Android设备上手动安装证书...")
        input("📱 完成证书安装后，按回车键继续测试...")
        
        # 测试安装
        if self.test_installation():
            print("🎉 证书安装成功！HTTPS抓包正常工作！")
            return True
        else:
            print("❌ 证书可能没有正确安装或应用不信任用户证书")
            self.alternative_solutions()
            return False

def main():
    installer = ManualCertInstaller()
    
    try:
        success = installer.run_manual_installation()
        
        if success:
            print("\n🎯 现在可以运行完整的网络抓包测试了！")
            print("🔧 建议运行: python3 test_10_activities_detailed.py")
        else:
            print("\n🔧 如果仍然无法抓取HTTPS，可以考虑:")
            print("   1. 使用root权限的设备")
            print("   2. 分析HTTP流量（已经可以工作）")
            print("   3. 使用其他绕过方法")
            
    except KeyboardInterrupt:
        print("\n⚠️  安装被用户中断")
    except Exception as e:
        print(f"\n❌ 安装异常: {e}")

if __name__ == "__main__":
    main()
