#!/usr/bin/env python3
"""
测试不同Activity启动方式和自动处理弹窗
"""
import asyncio
import time

async def test_activity_launches():
    print('🚀 测试不同Activity启动方式...')
    
    # 等待设备连接
    print('📱 等待设备连接...')
    process = await asyncio.create_subprocess_exec(
        'adb', 'wait-for-device',
        stdout=asyncio.subprocess.PIPE,
        stderr=asyncio.subprocess.PIPE
    )
    await process.communicate()
    print('✅ 设备已连接')
    
    # 安装应用
    print('📥 安装应用...')
    process = await asyncio.create_subprocess_exec(
        'adb', 'install', '-r', '-g', 'apk/com.yjzx.yjzx2017_v5.5.8_2265.com.apk',
        stdout=asyncio.subprocess.PIPE,
        stderr=asyncio.subprocess.PIPE
    )
    stdout, stderr = await process.communicate()
    if process.returncode == 0:
        print('✅ 应用安装成功')
    else:
        print(f'❌ 应用安装失败: {stderr.decode()}')
        return
    
    # 测试不同的Activity启动方式
    activities_to_test = [
        ('主Activity', 'com.yjzx.yjzx2017/.controller.activity.MainActivity'),
        ('登录Activity', 'com.yjzx.yjzx2017/.controller.login.activity.LoginActivity'),
        ('WebView Activity', 'com.yjzx.yjzx2017/.controller.activity.webview.WebViewActivity'),
        ('搜索Activity', 'com.yjzx.yjzx2017/.controller.search.activity.SearchMainActivity'),
        ('启动页Activity', 'com.yjzx.yjzx2017/.controller.activity.splash.SplashActivity'),
    ]
    
    for name, activity in activities_to_test:
        print(f'\n🎯 测试启动 {name}...')
        
        # 启动Activity
        process = await asyncio.create_subprocess_exec(
            'adb', 'shell', 'am', 'start', '-n', activity,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        stdout, stderr = await process.communicate()
        
        if process.returncode == 0 and 'Error' not in stdout.decode():
            print(f'✅ {name} 启动成功')
            
            # 等待Activity加载
            await asyncio.sleep(3)
            
            # 自动处理可能的弹窗
            await handle_dialogs()
            
            # 检查应用是否在运行
            is_running = await check_app_running()
            print(f'📱 应用运行状态: {"运行中" if is_running else "未运行"}')
            
            if is_running:
                print(f'🎉 {name} 成功启动并运行！')
                # 进行一些基础交互测试网络
                await perform_basic_interactions()
                break
            else:
                print(f'⚠️ {name} 启动后未正常运行')
        else:
            print(f'❌ {name} 启动失败: {stdout.decode()}')
        
        # 强制停止应用，准备下一次测试
        await asyncio.create_subprocess_exec(
            'adb', 'shell', 'am', 'force-stop', 'com.yjzx.yjzx2017',
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        await asyncio.sleep(2)

async def handle_dialogs():
    """自动处理各种弹窗"""
    print('🔧 自动处理弹窗...')
    
    # 常见的同意/确定按钮位置
    agree_positions = [
        (800, 1400, "右下角-同意"),
        (540, 1400, "中下-确定"),
        (600, 1350, "中间偏右-同意"),
        (800, 1300, "右中-确定"),
        (270, 1400, "左下角-取消"), # 最后尝试，避免误点
    ]
    
    # 尝试点击同意按钮
    for x, y, desc in agree_positions[:-1]:  # 排除取消按钮
        print(f'   点击 {desc} ({x}, {y})')
        await asyncio.create_subprocess_exec(
            'adb', 'shell', 'input', 'tap', str(x), str(y),
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        await asyncio.sleep(0.5)
    
    # 尝试按键事件
    key_events = ['KEYCODE_ENTER', 'KEYCODE_DPAD_CENTER']
    for key in key_events:
        print(f'   按键 {key}')
        await asyncio.create_subprocess_exec(
            'adb', 'shell', 'input', 'keyevent', key,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        await asyncio.sleep(0.3)

async def check_app_running():
    """检查应用是否在运行"""
    process = await asyncio.create_subprocess_exec(
        'adb', 'shell', 'ps | grep yjzx',
        stdout=asyncio.subprocess.PIPE,
        stderr=asyncio.subprocess.PIPE
    )
    stdout, _ = await process.communicate()
    return 'yjzx' in stdout.decode()

async def perform_basic_interactions():
    """执行基础交互以触发网络请求"""
    print('🎮 执行基础交互...')
    
    interactions = [
        ('点击屏幕中心', 'tap', '540', '1000'),
        ('向上滑动', 'swipe', '540', '1200', '540', '800', '300'),
        ('点击右上角', 'tap', '900', '200'),
        ('点击左下角', 'tap', '200', '1400'),
    ]
    
    for desc, *args in interactions:
        print(f'   {desc}')
        await asyncio.create_subprocess_exec(
            'adb', 'shell', 'input', *args,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        await asyncio.sleep(1)

if __name__ == "__main__":
    asyncio.run(test_activity_launches())
