#!/usr/bin/env python3
"""
测试基于Burp Suite文档的代理配置
"""
import asyncio
import subprocess
import time
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
import sys
sys.path.insert(0, str(project_root))

async def test_proxy_configuration():
    print('🔧 测试基于Burp Suite文档的代理配置...')
    
    # 1. 获取电脑IP地址
    try:
        result = subprocess.run(['ifconfig'], capture_output=True, text=True)
        lines = result.stdout.split('\n')
        host_ip = None
        for line in lines:
            if 'inet ' in line and '127.0.0.1' not in line and 'inet 169.254' not in line:
                host_ip = line.split()[1]
                break
        
        if not host_ip:
            host_ip = '*************'
            
    except Exception as e:
        host_ip = '*************'
        print(f"⚠️ 无法自动获取IP，使用默认: {host_ip}")
    
    print(f'🌐 电脑IP地址: {host_ip}')
    
    # 2. 启动mitmproxy
    print('🚀 启动mitmproxy...')
    mitm_env = os.environ.copy()
    mitm_env['MITM_SESSION_ID'] = 'proxy_test'
    mitm_env['MITM_OUTPUT_DIR'] = 'mitm-logs'
    
    mitm_process = await asyncio.create_subprocess_exec(
        '/Users/<USER>/Library/Python/3.9/bin/mitmdump',
        '-s', 'mitm-scripts/local_capture.py',
        '--listen-host', '0.0.0.0',  # 监听所有接口
        '--listen-port', '8080',
        '--set', 'ssl_insecure=true',
        env=mitm_env,
        stdout=asyncio.subprocess.PIPE,
        stderr=asyncio.subprocess.PIPE
    )
    
    # 等待mitmproxy启动
    await asyncio.sleep(3)
    
    # 3. 配置Android代理
    print('📱 配置Android代理...')
    proxy_host = f'{host_ip}:8080'
    
    # 清除旧的代理设置
    await asyncio.create_subprocess_exec(
        'adb', 'shell', 'settings', 'delete', 'global', 'http_proxy',
        stdout=asyncio.subprocess.PIPE,
        stderr=asyncio.subprocess.PIPE
    )
    
    # 设置新的代理
    process = await asyncio.create_subprocess_exec(
        'adb', 'shell', 'settings', 'put', 'global', 'http_proxy', proxy_host,
        stdout=asyncio.subprocess.PIPE,
        stderr=asyncio.subprocess.PIPE
    )
    await process.communicate()
    
    print(f'✅ 代理设置完成: {proxy_host}')
    
    # 4. 验证代理设置
    process = await asyncio.create_subprocess_exec(
        'adb', 'shell', 'settings', 'get', 'global', 'http_proxy',
        stdout=asyncio.subprocess.PIPE,
        stderr=asyncio.subprocess.PIPE
    )
    stdout, _ = await process.communicate()
    current_proxy = stdout.decode().strip()
    print(f'📊 当前代理设置: {current_proxy}')
    
    # 5. 测试网络连接
    print('🌐 测试网络连接...')
    
    # 启动Chrome访问测试页面
    process = await asyncio.create_subprocess_exec(
        'adb', 'shell', 'am', 'start', '-a', 'android.intent.action.VIEW',
        '-d', 'http://httpbin.org/get',
        stdout=asyncio.subprocess.PIPE,
        stderr=asyncio.subprocess.PIPE
    )
    await process.communicate()
    
    print('⏳ 等待10秒让Chrome加载页面...')
    await asyncio.sleep(10)
    
    # 6. 检查mitmproxy是否捕获到流量
    print('📊 检查mitmproxy捕获结果...')
    
    # 停止mitmproxy
    mitm_process.terminate()
    try:
        await asyncio.wait_for(mitm_process.wait(), timeout=5)
    except asyncio.TimeoutError:
        mitm_process.kill()
        await mitm_process.wait()
    
    # 检查会话文件
    session_files = list(Path('mitm-logs').glob('session_proxy_test*.json'))
    if session_files:
        session_file = session_files[0]
        if session_file.exists() and session_file.stat().st_size > 100:
            print(f'✅ 成功捕获网络流量！会话文件: {session_file}')
            
            # 显示前几行内容
            with open(session_file, 'r') as f:
                content = f.read()[:500]
                print(f'📄 会话内容预览:\n{content}...')
        else:
            print(f'⚠️ 会话文件存在但为空: {session_file}')
    else:
        print('❌ 未找到会话文件，网络捕获可能失败')
    
    # 7. 清理代理设置
    print('🧹 清理代理设置...')
    await asyncio.create_subprocess_exec(
        'adb', 'shell', 'settings', 'delete', 'global', 'http_proxy',
        stdout=asyncio.subprocess.PIPE,
        stderr=asyncio.subprocess.PIPE
    )
    
    print('✅ 代理配置测试完成！')

if __name__ == "__main__":
    asyncio.run(test_proxy_configuration())
