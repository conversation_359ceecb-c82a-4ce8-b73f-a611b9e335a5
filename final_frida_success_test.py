#!/usr/bin/env python3
"""
最终Frida成功测试
基于调试结果，实现可工作的SSL绕过
"""

import subprocess
import sys
import time
import json
from pathlib import Path

def run_cmd(cmd, timeout=30):
    """执行命令"""
    try:
        result = subprocess.run(cmd, shell=True, 
                              capture_output=True, text=True, 
                              timeout=timeout)
        return result.returncode, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return -1, "", "Timeout"

def setup_and_test():
    """设置并测试Frida SSL绕过"""
    print("🚀 最终Frida SSL绕过成功测试")
    print("🔧 基于调试结果的可工作方案")
    print("=" * 70)
    
    # 步骤1: 重新启动frida-server
    print("🚀 重新启动frida-server...")
    run_cmd("source android_env.sh && adb shell pkill frida-server")
    time.sleep(2)
    
    # 后台启动frida-server
    subprocess.Popen("source android_env.sh && adb shell '/data/local/tmp/frida-server' 2>/dev/null &", shell=True)
    time.sleep(8)
    
    # 验证frida-server
    returncode, stdout, stderr = run_cmd("source android_env.sh && adb shell ps | grep frida-server")
    if "frida-server" not in stdout:
        print("❌ frida-server启动失败")
        return False
    
    print("✅ frida-server重新启动成功")
    
    # 步骤2: 重新启动应用（使用正确的Activity）
    print("📱 重新启动应用...")
    package = "com.yjzx.yjzx2017"
    
    run_cmd(f"source android_env.sh && adb shell am force-stop {package}")
    time.sleep(2)
    
    # 使用可以工作的Activity
    returncode, stdout, stderr = run_cmd(f"source android_env.sh && adb shell am start -n {package}/.controller.activity.splash.SplashActivity")
    
    if returncode == 0:
        print("✅ 应用启动成功")
        time.sleep(8)
    else:
        print(f"⚠️  应用启动警告: {stderr}")
    
    # 检查应用进程
    returncode, stdout, stderr = run_cmd(f"source android_env.sh && adb shell ps | grep {package}")
    if package in stdout:
        print("✅ 应用进程正在运行")
        
        # 提取主进程PID（非pushcore）
        lines = stdout.split('\n')
        main_pid = None
        pushcore_pid = None
        
        for line in lines:
            if package in line:
                parts = line.split()
                if len(parts) >= 2:
                    pid = parts[1]
                    if 'pushcore' in line:
                        pushcore_pid = pid
                    else:
                        main_pid = pid
        
        print(f"   主进程PID: {main_pid}")
        print(f"   Pushcore PID: {pushcore_pid}")
        
        # 步骤3: 使用Python API进行SSL绕过
        return test_python_api_bypass(main_pid or pushcore_pid)
    else:
        print("❌ 应用进程未找到")
        return False

def test_python_api_bypass(target_pid):
    """使用Python API进行SSL绕过"""
    print(f"\n🔧 使用Python API进行SSL绕过 (PID: {target_pid})...")
    
    try:
        import frida
        
        # 连接到设备
        device = frida.get_usb_device()
        print("✅ 连接到USB设备成功")
        
        # 读取SSL绕过脚本
        with open("ssl_bypass.js", 'r') as f:
            script_code = f.read()
        
        # 附加到进程
        if target_pid:
            session = device.attach(int(target_pid))
            print(f"✅ 附加到进程 {target_pid} 成功")
        else:
            print("❌ 没有有效的目标PID")
            return False
        
        # 创建脚本
        script = session.create_script(script_code)
        
        # 设置消息处理器
        def on_message(message, data):
            if message['type'] == 'send':
                print(f"[SSL Bypass] {message['payload']}")
            elif message['type'] == 'error':
                print(f"[SSL Error] {message['stack']}")
        
        script.on('message', on_message)
        
        # 加载脚本
        script.load()
        print("✅ SSL绕过脚本加载成功")
        
        # 等待脚本初始化
        time.sleep(10)
        
        # 测试HTTPS捕获
        return test_https_with_bypass(script, session)
        
    except ImportError:
        print("❌ Frida Python模块未安装")
        return False
    except Exception as e:
        print(f"❌ Python API绕过失败: {e}")
        return False

def test_https_with_bypass(script, session):
    """测试HTTPS捕获"""
    print("\n🔍 测试HTTPS捕获...")
    
    # 清空捕获文件
    realtime_file = Path("mitm-logs/realtime_capture.json")
    with open(realtime_file, 'w') as f:
        json.dump([], f)
    
    time.sleep(3)
    
    # 触发HTTPS请求
    print("🌐 触发HTTPS请求...")
    
    test_urls = [
        "https://httpbin.org/get",
        "https://www.baidu.com"
    ]
    
    for url in test_urls:
        print(f"   🔄 访问: {url}")
        run_cmd(f"source android_env.sh && adb shell am start -a android.intent.action.VIEW -d {url}")
        time.sleep(10)
    
    # 等待网络请求
    print("⏳ 等待网络请求处理...")
    time.sleep(20)
    
    # 检查结果
    try:
        with open(realtime_file, 'r') as f:
            data = json.load(f)
        
        if isinstance(data, list) and len(data) > 0:
            https_requests = [req for req in data if req.get('scheme') == 'https']
            
            print(f"📊 捕获结果:")
            print(f"   总请求: {len(data)}")
            print(f"   HTTPS请求: {len(https_requests)}")
            
            if len(https_requests) > 0:
                print("🎉 SSL绕过完全成功！")
                print("📋 HTTPS请求示例:")
                
                for i, req in enumerate(https_requests[:3], 1):
                    url = req.get('url', 'N/A')
                    method = req.get('method', 'N/A')
                    print(f"   {i}. {method} {url}")
                
                # 保持脚本运行
                print("\n💡 SSL绕过脚本正在运行...")
                print("⚠️  按Ctrl+C停止")
                
                try:
                    while True:
                        time.sleep(30)
                        print("💡 SSL绕过仍在运行...")
                except KeyboardInterrupt:
                    print("\n⚠️  停止SSL绕过脚本...")
                
                return True
            else:
                print("⚠️  未捕获到HTTPS请求")
                return False
        else:
            print("❌ 没有捕获到网络请求")
            return False
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False
    finally:
        # 清理
        try:
            script.unload()
            session.detach()
        except:
            pass

def main():
    try:
        success = setup_and_test()
        
        if success:
            print("\n🎉 Frida SSL绕过测试完全成功！")
            print("✅ HTTPS流量现在可以被完全捕获和分析！")
            print("🔒 SSL证书验证已被成功绕过！")
            
            print("\n📋 系统现在具备:")
            print("   ✅ 完整的APK动态分析平台")
            print("   ✅ HTTPS网络流量透明捕获")
            print("   ✅ SSL证书验证绕过")
            print("   ✅ 实时网络监控")
            print("   ✅ API端点发现和分析")
            
            print("\n💡 可以用于:")
            print("   • 移动应用安全测试")
            print("   • HTTPS API逆向工程")
            print("   • 加密通信分析")
            print("   • 隐私数据泄露检测")
            print("   • 恶意软件行为分析")
            
        else:
            print("\n⚠️  SSL绕过测试未完全成功")
            print("🔧 但基础Frida环境已建立")
            print("💡 可以进行手动调试和优化")
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
    finally:
        # 清理
        print("🧹 清理frida-server...")
        subprocess.run("source android_env.sh && adb shell pkill frida-server", shell=True)

if __name__ == "__main__":
    main()
