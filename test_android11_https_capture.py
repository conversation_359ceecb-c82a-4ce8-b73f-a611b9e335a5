#!/usr/bin/env python3
"""
使用方法：
1. 激活虚拟环境: source .venv/bin/activate
2. 运行测试: python test_android11_https_capture.py
"""
"""
Android 11 HTTPS流量捕获测试脚本
验证证书安装和HTTPS抓包功能
"""

import os
import sys
import subprocess
import time
import json
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Android11HttpsCaptureTest:
    """Android 11 HTTPS捕获测试器"""
    
    def __init__(self, device_id: str = "emulator-5554"):
        self.device_id = device_id
        self.test_results = {
            'timestamp': time.strftime('%Y%m%d_%H%M%S'),
            'device_id': device_id,
            'tests': [],
            'summary': {}
        }
        
        # 设置ADB路径（优先使用本地路径）
        local_adb = Path(__file__).parent / "platform-tools" / "adb"
        if local_adb.exists():
            self.adb_path = str(local_adb)
        else:
            self.adb_path = "adb"  # 回退到系统路径
        
    def run_adb_command(self, command: List[str], timeout: int = 30) -> Optional[subprocess.CompletedProcess]:
        """执行ADB命令"""
        try:
            full_command = [self.adb_path, '-s', self.device_id] + command
            result = subprocess.run(
                full_command,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            return result
        except Exception as e:
            logger.error(f"ADB命令执行异常: {e}")
            return None
    
    def add_test_result(self, test_name: str, success: bool, details: str = "", data: Dict[str, Any] = None):
        """添加测试结果"""
        self.test_results['tests'].append({
            'name': test_name,
            'success': success,
            'details': details,
            'data': data or {},
            'timestamp': time.strftime('%H:%M:%S')
        })
        
        status = "✅" if success else "❌"
        logger.info(f"{status} {test_name}: {details}")
    
    def test_device_connection(self) -> bool:
        """测试设备连接"""
        result = self.run_adb_command(['get-state'])
        
        if result and result.returncode == 0 and 'device' in result.stdout:
            # 获取设备信息
            version_result = self.run_adb_command(['shell', 'getprop', 'ro.build.version.release'])
            sdk_result = self.run_adb_command(['shell', 'getprop', 'ro.build.version.sdk'])
            
            version = version_result.stdout.strip() if version_result else "unknown"
            sdk = sdk_result.stdout.strip() if sdk_result else "unknown"
            
            self.add_test_result(
                "设备连接测试",
                True,
                f"设备已连接 (Android {version}, SDK {sdk})",
                {"android_version": version, "sdk_version": sdk}
            )
            return True
        else:
            self.add_test_result("设备连接测试", False, "设备未连接或连接异常")
            return False
    
    def test_root_access(self) -> bool:
        """测试Root权限"""
        result = self.run_adb_command(['shell', 'id'])
        
        if result and 'uid=0(root)' in result.stdout:
            self.add_test_result("Root权限测试", True, "Root权限正常")
            return True
        else:
            # 尝试获取root权限
            self.run_adb_command(['root'])
            time.sleep(2)
            
            result = self.run_adb_command(['shell', 'id'])
            if result and 'uid=0(root)' in result.stdout:
                self.add_test_result("Root权限测试", True, "Root权限已获取")
                return True
            else:
                self.add_test_result("Root权限测试", False, "无法获取Root权限")
                return False
    
    def test_system_certificate_installation(self) -> bool:
        """测试系统证书安装"""
        # 检查系统证书目录
        result = self.run_adb_command([
            'shell', 'ls', '/system/etc/security/cacerts/', '|', 'grep', '-E', '(mitm|mitmproxy)'
        ])
        
        if result and result.returncode == 0 and result.stdout.strip():
            cert_files = result.stdout.strip().split('\n')
            self.add_test_result(
                "系统证书安装测试",
                True,
                f"发现 {len(cert_files)} 个相关证书文件",
                {"certificate_files": cert_files}
            )
            return True
        else:
            # 尝试查找mitmproxy证书的哈希
            possible_hashes = ['c8750f0d.0', '9a5ba575.0', '269953fb.0']  # 常见的mitmproxy证书哈希
            
            for cert_hash in possible_hashes:
                result = self.run_adb_command([
                    'shell', 'test', '-f', f'/system/etc/security/cacerts/{cert_hash}', '&&', 'echo', 'found'
                ])
                
                if result and 'found' in result.stdout:
                    self.add_test_result(
                        "系统证书安装测试",
                        True,
                        f"找到证书文件: {cert_hash}",
                        {"certificate_hash": cert_hash}
                    )
                    return True
            
            self.add_test_result("系统证书安装测试", False, "未找到系统证书")
            return False
    
    def test_proxy_configuration(self) -> bool:
        """测试代理配置"""
        result = self.run_adb_command(['shell', 'settings', 'get', 'global', 'http_proxy'])
        
        if result and result.returncode == 0 and result.stdout.strip():
            proxy_setting = result.stdout.strip()
            if '********:8080' in proxy_setting or '127.0.0.1:8080' in proxy_setting:
                self.add_test_result(
                    "代理配置测试",
                    True,
                    f"代理已配置: {proxy_setting}",
                    {"proxy_setting": proxy_setting}
                )
                return True
            else:
                self.add_test_result(
                    "代理配置测试",
                    False,
                    f"代理配置异常: {proxy_setting}"
                )
                return False
        else:
            self.add_test_result("代理配置测试", False, "未配置系统代理")
            return False
    
    def test_network_connectivity(self) -> bool:
        """测试网络连接"""
        # 测试基本网络连接
        result = self.run_adb_command(['shell', 'ping', '-c', '3', '*******'], timeout=15)
        
        if result and result.returncode == 0:
            self.add_test_result("网络连接测试", True, "网络连接正常")
            return True
        else:
            self.add_test_result("网络连接测试", False, "网络连接异常")
            return False
    
    def test_https_interception_capability(self) -> bool:
        """测试HTTPS拦截能力"""
        logger.info("测试HTTPS拦截能力...")
        
        # 检查mitmproxy是否运行
        try:
            result = subprocess.run(['pgrep', '-f', 'mitmdump'], capture_output=True, text=True)
            if result.returncode == 0:
                mitm_running = True
                mitm_pids = result.stdout.strip().split('\n')
            else:
                mitm_running = False
                mitm_pids = []
        except:
            mitm_running = False
            mitm_pids = []
        
        if mitm_running:
            # 尝试通过设备访问HTTPS网站
            test_urls = [
                "https://www.google.com",
                "https://httpbin.org/get",
                "https://www.baidu.com"
            ]
            
            successful_requests = 0
            
            for url in test_urls:
                result = self.run_adb_command([
                    'shell', 'curl', '-s', '-I', url
                ], timeout=10)
                
                if result and result.returncode == 0 and 'HTTP/' in result.stdout:
                    successful_requests += 1
            
            if successful_requests > 0:
                self.add_test_result(
                    "HTTPS拦截测试",
                    True,
                    f"成功拦截 {successful_requests}/{len(test_urls)} 个HTTPS请求",
                    {
                        "mitmproxy_running": True,
                        "mitmproxy_pids": mitm_pids,
                        "successful_requests": successful_requests,
                        "total_requests": len(test_urls)
                    }
                )
                return True
            else:
                self.add_test_result(
                    "HTTPS拦截测试",
                    False,
                    "HTTPS请求无法成功执行",
                    {"mitmproxy_running": True, "successful_requests": 0}
                )
                return False
        else:
            self.add_test_result(
                "HTTPS拦截测试",
                False,
                "mitmproxy未运行",
                {"mitmproxy_running": False}
            )
            return False
    
    def test_app_https_traffic(self, package_name: str = "com.android.chrome") -> bool:
        """测试应用HTTPS流量捕获"""
        logger.info(f"测试应用HTTPS流量捕获: {package_name}")
        
        # 启动应用
        result = self.run_adb_command([
            'shell', 'am', 'start', '-n', f'{package_name}/.MainActivity'
        ])
        
        if not (result and result.returncode == 0):
            # 尝试启动浏览器
            result = self.run_adb_command([
                'shell', 'am', 'start', '-a', 'android.intent.action.VIEW',
                '-d', 'https://www.google.com'
            ])
        
        if result and result.returncode == 0:
            # 等待应用启动
            time.sleep(5)
            
            # 检查进程
            result = self.run_adb_command([
                'shell', 'ps', '|', 'grep', package_name
            ])
            
            if result and package_name in result.stdout:
                self.add_test_result(
                    "应用HTTPS流量测试",
                    True,
                    f"成功启动应用: {package_name}",
                    {"package_name": package_name}
                )
                return True
        
        self.add_test_result(
            "应用HTTPS流量测试",
            False,
            f"无法启动应用: {package_name}"
        )
        return False
    
    def test_frida_ssl_bypass(self) -> bool:
        """测试Frida SSL绕过"""
        try:
            # 检查Frida是否运行
            result = subprocess.run(['pgrep', '-f', 'frida'], capture_output=True, text=True)
            frida_running = result.returncode == 0
            
            if frida_running:
                # 检查Frida脚本文件
                frida_scripts = []
                for script_name in ['ultimate_ssl_bypass.js', 'advanced_ssl_bypass.js', 'ssl_bypass.js']:
                    if Path(script_name).exists():
                        frida_scripts.append(script_name)
                
                self.add_test_result(
                    "Frida SSL绕过测试",
                    True,
                    f"Frida正在运行，发现脚本: {', '.join(frida_scripts)}",
                    {"frida_running": True, "scripts": frida_scripts}
                )
                return True
            else:
                self.add_test_result(
                    "Frida SSL绕过测试",
                    False,
                    "Frida未运行",
                    {"frida_running": False}
                )
                return False
                
        except Exception as e:
            self.add_test_result(
                "Frida SSL绕过测试",
                False,
                f"检查Frida状态异常: {e}"
            )
            return False
    
    def generate_test_report(self) -> str:
        """生成测试报告"""
        # 统计测试结果
        total_tests = len(self.test_results['tests'])
        passed_tests = sum(1 for test in self.test_results['tests'] if test['success'])
        failed_tests = total_tests - passed_tests
        
        self.test_results['summary'] = {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': failed_tests,
            'success_rate': round(passed_tests / total_tests * 100, 2) if total_tests > 0 else 0
        }
        
        # 保存详细报告
        report_file = f"android11_https_test_report_{self.test_results['timestamp']}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📋 详细测试报告已保存: {report_file}")
        return report_file
    
    def run_all_tests(self) -> bool:
        """运行所有测试"""
        logger.info("=" * 60)
        logger.info("🧪 Android 11 HTTPS流量捕获测试")
        logger.info("=" * 60)
        
        tests = [
            ("设备连接", self.test_device_connection),
            ("Root权限", self.test_root_access),
            ("系统证书安装", self.test_system_certificate_installation),
            ("代理配置", self.test_proxy_configuration),
            ("网络连接", self.test_network_connectivity),
            ("HTTPS拦截能力", self.test_https_interception_capability),
            ("应用HTTPS流量", lambda: self.test_app_https_traffic()),
            ("Frida SSL绕过", self.test_frida_ssl_bypass),
        ]
        
        logger.info(f"📱 测试设备: {self.device_id}")
        logger.info(f"🔍 测试项目: {len(tests)} 项")
        logger.info("")
        
        for test_name, test_func in tests:
            logger.info(f"执行测试: {test_name}")
            try:
                test_func()
            except Exception as e:
                self.add_test_result(test_name, False, f"测试异常: {e}")
            
            time.sleep(1)  # 测试间隔
        
        # 生成报告
        report_file = self.generate_test_report()
        
        # 打印总结
        summary = self.test_results['summary']
        logger.info("")
        logger.info("=" * 60)
        logger.info("📊 测试结果总结")
        logger.info("=" * 60)
        logger.info(f"总测试数: {summary['total_tests']}")
        logger.info(f"通过测试: {summary['passed_tests']}")
        logger.info(f"失败测试: {summary['failed_tests']}")
        logger.info(f"成功率: {summary['success_rate']}%")
        logger.info(f"详细报告: {report_file}")
        
        # 给出建议
        if summary['success_rate'] >= 80:
            logger.info("")
            logger.info("🎉 HTTPS流量捕获环境配置良好！")
            logger.info("💡 建议: 现在可以开始进行动态分析")
        elif summary['success_rate'] >= 60:
            logger.info("")
            logger.info("⚠️  HTTPS流量捕获环境基本可用，但有些问题")
            logger.info("💡 建议: 检查失败的测试项目并修复")
        else:
            logger.info("")
            logger.info("❌ HTTPS流量捕获环境配置存在较多问题")
            logger.info("💡 建议: 重新运行证书安装脚本")
        
        logger.info("=" * 60)
        
        return summary['success_rate'] >= 60

def main():
    """主函数"""
    device_id = sys.argv[1] if len(sys.argv) > 1 else "emulator-5554"
    
    tester = Android11HttpsCaptureTest(device_id)
    
    try:
        success = tester.run_all_tests()
        return 0 if success else 1
    except KeyboardInterrupt:
        logger.info("\n⏹️  测试被用户中断")
        return 1
    except Exception as e:
        logger.error(f"测试过程异常: {e}")
        return 1

if __name__ == "__main__":
    exit(main())