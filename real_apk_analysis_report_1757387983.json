{"apk_path": "/Users/<USER>/Desktop/project/apk_detect/apk/5577.com.iloda.beacon.apk", "package_name": "com.iloda.beacon", "main_activity": "com.iloda.beacon.activity.LoginActivity", "installation": true, "launch": true, "ui_elements": [{"class": "android.widget.TextView", "text": "Choose what to allow <PERSON><PERSON> to access", "resource_id": "com.android.permissioncontroller:id/permissions_message", "clickable": false, "bounds": "[165,121][1014,259]", "content_desc": ""}, {"class": "android.widget.LinearLayout", "text": "", "resource_id": "", "clickable": true, "bounds": "[22,303][1058,549]", "content_desc": ""}, {"class": "android.widget.TextView", "text": "Files and media", "resource_id": "android:id/title", "clickable": false, "bounds": "[220,347][529,406]", "content_desc": ""}, {"class": "android.widget.TextView", "text": "access photos, media, and files on your device", "resource_id": "android:id/summary", "clickable": false, "bounds": "[220,406][838,505]", "content_desc": ""}, {"class": "android.widget.Switch", "text": "", "resource_id": "android:id/switch_widget", "clickable": true, "bounds": "[882,360][1014,492]", "content_desc": ""}, {"class": "android.widget.LinearLayout", "text": "", "resource_id": "", "clickable": true, "bounds": "[22,549][1058,749]", "content_desc": ""}, {"class": "android.widget.TextView", "text": "Location", "resource_id": "android:id/title", "clickable": false, "bounds": "[220,593][390,652]", "content_desc": ""}, {"class": "android.widget.TextView", "text": "access this device's location", "resource_id": "android:id/summary", "clickable": false, "bounds": "[220,652][710,705]", "content_desc": ""}, {"class": "android.widget.Switch", "text": "", "resource_id": "android:id/switch_widget", "clickable": true, "bounds": "[882,583][1014,715]", "content_desc": ""}, {"class": "android.widget.LinearLayout", "text": "", "resource_id": "", "clickable": true, "bounds": "[22,749][1058,949]", "content_desc": ""}, {"class": "android.widget.TextView", "text": "Contacts", "resource_id": "android:id/title", "clickable": false, "bounds": "[220,793][396,852]", "content_desc": ""}, {"class": "android.widget.TextView", "text": "access your contacts", "resource_id": "android:id/summary", "clickable": false, "bounds": "[220,852][588,905]", "content_desc": ""}, {"class": "android.widget.Switch", "text": "", "resource_id": "android:id/switch_widget", "clickable": true, "bounds": "[882,783][1014,915]", "content_desc": ""}, {"class": "android.widget.LinearLayout", "text": "", "resource_id": "", "clickable": true, "bounds": "[22,949][1058,1149]", "content_desc": ""}, {"class": "android.widget.TextView", "text": "Camera", "resource_id": "android:id/title", "clickable": false, "bounds": "[220,993][374,1052]", "content_desc": ""}, {"class": "android.widget.TextView", "text": "take pictures and record video", "resource_id": "android:id/summary", "clickable": false, "bounds": "[220,1052][743,1105]", "content_desc": ""}, {"class": "android.widget.Switch", "text": "", "resource_id": "android:id/switch_widget", "clickable": true, "bounds": "[882,983][1014,1115]", "content_desc": ""}, {"class": "android.widget.LinearLayout", "text": "", "resource_id": "", "clickable": true, "bounds": "[22,1149][1058,1349]", "content_desc": ""}, {"class": "android.widget.TextView", "text": "Phone", "resource_id": "android:id/title", "clickable": false, "bounds": "[220,1193][344,1252]", "content_desc": ""}, {"class": "android.widget.TextView", "text": "make and manage phone calls", "resource_id": "android:id/summary", "clickable": false, "bounds": "[220,1252][749,1305]", "content_desc": ""}, {"class": "android.widget.Switch", "text": "", "resource_id": "android:id/switch_widget", "clickable": true, "bounds": "[882,1183][1014,1315]", "content_desc": ""}, {"class": "android.widget.Button", "text": "Cancel", "resource_id": "com.android.permissioncontroller:id/cancel_button", "clickable": true, "bounds": "[674,1939][858,2088]", "content_desc": ""}, {"class": "android.widget.Button", "text": "Continue", "resource_id": "com.android.permissioncontroller:id/continue_button", "clickable": true, "bounds": "[858,1939][1080,2088]", "content_desc": ""}], "network_urls": ["https://api.beacon.com/user/login", "https://api.beacon.com/app/config", "https://beacon.com/api/v1/data", "https://graph.facebook.com/v2.0/me", "https://api.weixin.qq.com/sns/oauth2/access_token"], "analysis_duration": 28.037641286849976, "screenshots": ["/Users/<USER>/Desktop/project/apk_detect/real_analysis_screenshot_1757387971.png"], "errors": []}