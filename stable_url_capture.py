#!/usr/bin/env python3
"""
稳定的 URL 捕获脚本 - 使用更可靠的方法
"""

import frida
import sys
import time
import json
import subprocess
import threading
from datetime import datetime

class URLCapture:
    def __init__(self, package_name):
        self.package_name = package_name
        self.captured_urls = []
        self.session = None
        self.script = None
        
    def get_pid(self):
        """获取应用 PID"""
        try:
            result = subprocess.run(
                ['./android-sdk/platform-tools/adb', '-s', 'emulator-5554', 'shell', 'pidof', self.package_name],
                capture_output=True, text=True
            )
            pid_str = result.stdout.strip()
            if pid_str:
                return int(pid_str)
        except:
            pass
        return None
    
    def start_app(self):
        """启动应用"""
        print(f"[*] 启动应用: {self.package_name}")
        subprocess.run([
            './android-sdk/platform-tools/adb', '-s', 'emulator-5554',
            'shell', 'am', 'start', '-n', f'{self.package_name}/.activity.MainActivity'
        ])
        time.sleep(3)
    
    def on_message(self, message, data):
        """处理来自脚本的消息"""
        if message['type'] == 'send':
            payload = message.get('payload', '')
            if isinstance(payload, str):
                print(f"[SCRIPT] {payload}")
                # 解析 URL
                if "[URL]" in payload or "[HTTP]" in payload or "[HTTPS]" in payload:
                    parts = payload.split("] ", 1)
                    if len(parts) > 1:
                        url = parts[1].strip()
                        self.captured_urls.append({
                            'url': url,
                            'time': datetime.now().isoformat()
                        })
        elif message['type'] == 'error':
            print(f"[ERROR] {message.get('description', message)}")
    
    def run(self):
        """主运行函数"""
        # 启动应用
        self.start_app()
        
        # 获取 PID
        pid = self.get_pid()
        if not pid:
            print(f"[!] 无法获取 {self.package_name} 的 PID")
            return
        
        print(f"[*] 找到 PID: {pid}")
        
        # Hook 脚本 - 更简单更稳定
        hook_script = """
        function hookURLs() {
            Java.perform(function() {
                console.log("[*] 开始 Hook...");
                
                // Hook URL 构造函数
                try {
                    var URL = Java.use('java.net.URL');
                    URL.$init.overload('java.lang.String').implementation = function(str) {
                        send("[URL] " + str);
                        return this.$init(str);
                    };
                    console.log("[✓] Hook URL 成功");
                } catch(e) {
                    console.log("[×] Hook URL 失败");
                }
                
                // Hook HttpURLConnection
                try {
                    var HttpURLConnection = Java.use('java.net.HttpURLConnection');
                    HttpURLConnection.connect.implementation = function() {
                        var url = this.getURL().toString();
                        send("[HTTP] " + url);
                        return this.connect();
                    };
                    console.log("[✓] Hook HttpURLConnection 成功");
                } catch(e) {
                    console.log("[×] Hook HttpURLConnection 失败");
                }
                
                // Hook HttpsURLConnection
                try {
                    var HttpsURLConnection = Java.use('javax.net.ssl.HttpsURLConnection');
                    HttpsURLConnection.connect.implementation = function() {
                        var url = this.getURL().toString();
                        send("[HTTPS] " + url);
                        return this.connect();
                    };
                    console.log("[✓] Hook HttpsURLConnection 成功");
                } catch(e) {
                    console.log("[×] Hook HttpsURLConnection 失败");
                }
                
                // Hook WebView
                try {
                    var WebView = Java.use('android.webkit.WebView');
                    WebView.loadUrl.overload('java.lang.String').implementation = function(url) {
                        send("[WebView] " + url);
                        return this.loadUrl(url);
                    };
                    console.log("[✓] Hook WebView 成功");
                } catch(e) {
                    console.log("[×] Hook WebView 失败");
                }
                
                console.log("[*] Hook 完成");
            });
        }
        
        // 等待 Java 可用
        if (Java.available) {
            hookURLs();
        } else {
            console.log("[*] 等待 Java...");
            setTimeout(function() {
                if (Java.available) {
                    hookURLs();
                } else {
                    console.log("[!] Java 仍不可用");
                }
            }, 2000);
        }
        """
        
        try:
            # 连接设备
            device = frida.get_usb_device()
            print(f"[*] 连接到设备: {device.name}")
            
            # 附加到进程
            self.session = device.attach(pid)
            print(f"[*] 附加到进程 {pid}")
            
            # 创建并加载脚本
            self.script = self.session.create_script(hook_script)
            self.script.on('message', self.on_message)
            self.script.load()
            print("[*] 脚本加载成功")
            
            # 等待脚本初始化
            time.sleep(2)
            
            # 触发不同的 Activity
            activities = [
                ".activity.GuideActivity",
                ".activity.LoginActivity",
                ".activity.MainActivity"
            ]
            
            print("\n[*] 触发 Activities...")
            for activity in activities:
                print(f"  → 启动 {activity}")
                subprocess.run([
                    './android-sdk/platform-tools/adb', '-s', 'emulator-5554',
                    'shell', 'am', 'start', '-n', f'{self.package_name}/{activity}'
                ], capture_output=True)
                time.sleep(3)
            
            # 模拟用户操作
            print("\n[*] 模拟用户操作...")
            # 点击
            subprocess.run([
                './android-sdk/platform-tools/adb', '-s', 'emulator-5554',
                'shell', 'input', 'tap', '540', '960'
            ])
            time.sleep(1)
            
            # 滑动
            subprocess.run([
                './android-sdk/platform-tools/adb', '-s', 'emulator-5554',
                'shell', 'input', 'swipe', '540', '1500', '540', '500'
            ])
            
            # 等待更多网络活动
            print("\n[*] 监控网络活动 (15秒)...")
            for i in range(3):
                time.sleep(5)
                print(f"  等待... ({(i+1)*5}/15秒)")
                if self.captured_urls:
                    print(f"  已捕获 {len(self.captured_urls)} 个 URLs")
            
            # 输出结果
            self.print_results()
            
        except KeyboardInterrupt:
            print("\n[*] 用户中断")
        except Exception as e:
            print(f"[!] 错误: {e}")
            import traceback
            traceback.print_exc()
        finally:
            self.cleanup()
    
    def print_results(self):
        """打印捕获的结果"""
        print("\n" + "="*60)
        if self.captured_urls:
            print(f"[✓] 成功捕获 {len(self.captured_urls)} 个 URLs:\n")
            
            # 去重
            unique_urls = {}
            for item in self.captured_urls:
                url = item['url']
                if url not in unique_urls:
                    unique_urls[url] = item
            
            for idx, (url, item) in enumerate(unique_urls.items(), 1):
                print(f"{idx}. {url}")
            
            # 保存到文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"captured_urls_{timestamp}.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(list(unique_urls.values()), f, indent=2, ensure_ascii=False)
            print(f"\n[*] 结果已保存到: {output_file}")
        else:
            print("[!] 未捕获到任何 URLs")
            print("\n可能的原因:")
            print("  1. 应用没有进行网络请求")
            print("  2. 应用使用了其他网络库")
            print("  3. Hook 时机不对")
        print("="*60)
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.script:
                self.script.unload()
            if self.session:
                self.session.detach()
            print("\n[*] 清理完成")
        except:
            pass

def main():
    print("="*60)
    print("URL 捕获工具 - 稳定版")
    print("="*60)
    
    capture = URLCapture("com.iloda.beacon")
    capture.run()

if __name__ == "__main__":
    main()




