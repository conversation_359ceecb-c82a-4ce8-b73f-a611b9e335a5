#!/usr/bin/env python3
"""
修复网络捕获问题并重新测试
基于诊断结果，逐一解决问题
"""

import subprocess
import sys
import time
import json
from pathlib import Path
import signal

def log(message, level="INFO"):
    """日志输出"""
    timestamp = time.strftime("%H:%M:%S")
    if level == "SUCCESS":
        print(f"🎉 [{timestamp}] {message}")
    elif level == "ERROR":
        print(f"❌ [{timestamp}] {message}")
    elif level == "WARNING":
        print(f"⚠️  [{timestamp}] {message}")
    else:
        print(f"📋 [{timestamp}] {message}")

def run_adb(cmd, timeout=30):
    """执行adb命令"""
    full_cmd = f"source android_env.sh && adb {cmd}"
    try:
        result = subprocess.run(full_cmd, shell=True, 
                              capture_output=True, text=True, 
                              timeout=timeout, check=True)
        return result.returncode, result.stdout.strip(), result.stderr.strip()
    except subprocess.CalledProcessError as e:
        return e.returncode, "", e.stderr
    except subprocess.TimeoutExpired:
        return -1, "", "Timeout"

def fix_all_issues():
    """修复所有发现的问题"""
    log("🔧 修复所有发现的问题...")
    
    # 1. 完全清理环境
    log("1. 完全清理环境...")
    subprocess.run("pkill -f mitmdump", shell=True, capture_output=True)
    run_adb("shell pkill frida-server")
    run_adb("shell settings delete global http_proxy")
    time.sleep(5)
    
    # 2. 重新启动mitmproxy
    log("2. 重新启动mitmproxy...")
    Path("mitm-logs").mkdir(exist_ok=True)
    
    capture_file = Path("mitm-logs/realtime_capture.json")
    with open(capture_file, 'w') as f:
        json.dump([], f)
    
    # 使用简化的mitmproxy启动
    mitm_cmd = "/Users/<USER>/Library/Python/3.9/bin/mitmdump -s mitm-scripts/capture.py --listen-port 8080 --set confdir=./mitm-config"
    
    mitmproxy_process = subprocess.Popen(
        mitm_cmd, shell=True,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    
    log("等待mitmproxy启动...")
    time.sleep(15)
    
    if mitmproxy_process.poll() is None:
        log("✅ mitmproxy启动成功", "SUCCESS")
    else:
        log("❌ mitmproxy启动失败", "ERROR")
        return None
    
    # 3. 正确设置代理
    log("3. 正确设置代理...")
    
    # 获取正确的主机IP
    try:
        result = subprocess.run("ifconfig en0 | grep 'inet ' | awk '{print $2}'", 
                              shell=True, capture_output=True, text=True)
        host_ip = result.stdout.strip()
        
        if not host_ip or "127." in host_ip:
            # 备用方法
            result = subprocess.run("ifconfig | grep 'inet ' | grep -v '127.0.0.1' | head -1 | awk '{print $2}'", 
                                  shell=True, capture_output=True, text=True)
            host_ip = result.stdout.strip()
        
        if not host_ip:
            host_ip = "********"  # Android模拟器默认主机IP
            
    except:
        host_ip = "********"
    
    log(f"使用主机IP: {host_ip}")
    
    proxy_setting = f"{host_ip}:8080"
    returncode, stdout, stderr = run_adb(f"shell settings put global http_proxy {proxy_setting}")
    
    if returncode == 0:
        log(f"✅ 代理设置成功: {proxy_setting}", "SUCCESS")
        
        # 验证代理设置
        returncode, stdout, stderr = run_adb("shell settings get global http_proxy")
        log(f"代理验证结果: {stdout}")
    else:
        log(f"❌ 代理设置失败: {stderr}", "ERROR")
    
    # 4. 重新启动frida-server
    log("4. 重新启动frida-server...")
    
    # 确保root权限
    subprocess.run("adb root", shell=True, capture_output=True)
    time.sleep(3)
    
    # 推送frida-server
    run_adb("push frida-server /data/local/tmp/frida-server")
    run_adb("shell chmod 755 /data/local/tmp/frida-server")
    run_adb("shell chown root:root /data/local/tmp/frida-server")
    
    # 启动frida-server
    subprocess.Popen("source android_env.sh && adb shell '/data/local/tmp/frida-server' &", shell=True)
    time.sleep(10)
    
    # 验证frida-server
    returncode, stdout, stderr = run_adb("shell ps | grep frida-server")
    if "frida-server" in stdout:
        log("✅ frida-server启动成功", "SUCCESS")
    else:
        log("❌ frida-server启动失败", "ERROR")
        return None
    
    # 5. 重新启动应用
    log("5. 重新启动应用...")
    
    package = "com.yjzx.yjzx2017"
    run_adb(f"shell am force-stop {package}")
    time.sleep(3)
    
    returncode, stdout, stderr = run_adb(f"shell am start -n {package}/.controller.activity.splash.SplashActivity")
    if returncode == 0:
        log("✅ 应用重新启动成功", "SUCCESS")
        time.sleep(10)
    else:
        log(f"❌ 应用启动失败: {stderr}", "ERROR")
        return None
    
    # 6. 启动SSL绕过
    log("6. 启动SSL绕过...")
    
    # 获取应用PID
    returncode, stdout, stderr = run_adb(f"shell ps | grep {package}")
    if package not in stdout:
        log("❌ 应用进程未找到", "ERROR")
        return None
    
    # 提取PID
    lines = stdout.split('\n')
    target_pid = None
    for line in lines:
        if package in line and 'pushcore' not in line:
            parts = line.split()
            if len(parts) >= 2:
                target_pid = parts[1]
                break
    
    if not target_pid:
        # 使用pushcore进程
        for line in lines:
            if package in line:
                parts = line.split()
                if len(parts) >= 2:
                    target_pid = parts[1]
                    break
    
    if not target_pid:
        log("❌ 无法获取应用PID", "ERROR")
        return None
    
    log(f"目标PID: {target_pid}")
    
    # 启动SSL绕过
    ssl_cmd = f"export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin && frida -U {target_pid} -l interactive_ssl_bypass.js"
    
    ssl_process = subprocess.Popen(
        ssl_cmd, shell=True,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    
    log("SSL绕过进程已启动，等待加载...")
    time.sleep(25)
    
    if ssl_process.poll() is None:
        log("✅ SSL绕过正在运行", "SUCCESS")
    else:
        log("⚠️  SSL绕过可能有问题", "WARNING")
    
    return {
        'mitmproxy': mitmproxy_process,
        'ssl': ssl_process,
        'proxy_ip': host_ip
    }

def test_network_with_external_triggers():
    """使用外部触发器测试网络"""
    log("🔍 使用外部触发器测试网络...")
    
    # 触发外部网络请求
    external_urls = [
        "http://httpbin.org/get",
        "https://httpbin.org/get",
        "https://www.baidu.com",
        "https://api.github.com"
    ]
    
    for url in external_urls:
        log(f"触发外部请求: {url}")
        run_adb(f"shell am start -a android.intent.action.VIEW -d {url}")
        time.sleep(8)
    
    # 应用内强制网络操作
    log("执行应用内强制网络操作...")
    
    package = "com.yjzx.yjzx2017"
    
    # 强制刷新操作
    actions = [
        ("强制停止重启", f"shell am force-stop {package} && sleep 2 && shell am start -n {package}/.controller.activity.splash.SplashActivity"),
        ("等待启动", "sleep 10"),
        ("多次下拉刷新", "shell input swipe 540 400 540 800 && sleep 3 && shell input swipe 540 400 540 800 && sleep 3 && shell input swipe 540 400 540 800"),
        ("等待", "sleep 10"),
        ("点击各种位置", "shell input tap 540 960 && sleep 2 && shell input tap 200 600 && sleep 2 && shell input tap 700 600"),
        ("等待", "sleep 8")
    ]
    
    for action_name, action_cmd in actions:
        log(f"   {action_name}")
        
        if action_cmd.startswith("sleep"):
            time.sleep(int(action_cmd.split()[1]))
        elif "&&" in action_cmd:
            parts = action_cmd.split(" && ")
            for part in parts:
                if part.startswith("sleep"):
                    time.sleep(int(part.split()[1]))
                else:
                    run_adb(part)
                    time.sleep(1)
        else:
            run_adb(action_cmd)
            time.sleep(1)

def analyze_final_results():
    """分析最终结果"""
    log("📊 分析最终结果...")
    
    capture_file = Path("mitm-logs/realtime_capture.json")
    
    try:
        if not capture_file.exists():
            log("❌ 捕获文件不存在", "ERROR")
            return False
        
        with open(capture_file, 'r') as f:
            data = json.load(f)
        
        total = len(data) if isinstance(data, list) else 0
        https_reqs = [req for req in data if req.get('url', '').startswith('https://')]
        http_reqs = [req for req in data if req.get('url', '').startswith('http://')]
        
        log("=" * 60)
        log("🎉 修复后的测试结果", "SUCCESS")
        log("=" * 60)
        
        log(f"📊 网络捕获统计:")
        log(f"   总请求数: {total}")
        log(f"   HTTPS请求: {len(https_reqs)}")
        log(f"   HTTP请求: {len(http_reqs)}")
        
        if len(https_reqs) > 0:
            log("🔒 HTTPS请求分析:", "SUCCESS")
            
            domains = {}
            for req in https_reqs:
                host = req.get('host', '')
                if host:
                    domains[host] = domains.get(host, 0) + 1
            
            log("🌐 访问的域名:")
            for domain, count in sorted(domains.items(), key=lambda x: x[1], reverse=True):
                log(f"   {domain}: {count} 请求")
            
            log("📋 HTTPS请求示例:")
            for i, req in enumerate(https_reqs[:8], 1):
                url = req.get('url', 'N/A')
                method = req.get('method', 'N/A')
                log(f"   {i}. {method} {url}")
            
            log("🎉 修复成功！SSL绕过和网络捕获正常工作！", "SUCCESS")
            return True
        elif len(http_reqs) > 0:
            log("⚠️  捕获到HTTP请求，但HTTPS仍需调试", "WARNING")
            for i, req in enumerate(http_reqs[:5], 1):
                url = req.get('url', 'N/A')
                method = req.get('method', 'N/A')
                log(f"   {i}. {method} {url}")
            return True
        elif total > 0:
            log("⚠️  捕获到请求但协议未识别", "WARNING")
            return True
        else:
            log("❌ 仍然没有捕获到网络请求", "ERROR")
            return False
            
    except Exception as e:
        log(f"分析失败: {e}", "ERROR")
        return False

def main():
    """主函数"""
    log("🚀 修复网络捕获问题并重新测试")
    log("🔧 基于诊断结果，逐一解决问题")
    log("=" * 70)
    
    processes = None
    
    try:
        # 步骤1: 修复所有问题
        processes = fix_all_issues()
        if not processes:
            log("❌ 修复失败", "ERROR")
            return False
        
        # 步骤2: 等待系统稳定
        log("⏳ 等待系统稳定...")
        time.sleep(20)
        
        # 步骤3: 使用外部触发器测试网络
        log("=" * 60)
        log("🔍 使用外部触发器测试网络")
        log("=" * 60)
        
        test_network_with_external_triggers()
        
        # 步骤4: 等待网络请求处理
        log("⏳ 等待网络请求处理...")
        time.sleep(30)
        
        # 步骤5: 分析结果
        success = analyze_final_results()
        
        if success:
            log("🎉 修复和重新测试成功！", "SUCCESS")
            return True
        else:
            log("⚠️  仍需进一步调试", "WARNING")
            return False
            
    except KeyboardInterrupt:
        log("用户中断测试")
        return False
    except Exception as e:
        log(f"测试异常: {e}", "ERROR")
        return False
    finally:
        # 清理
        if processes:
            log("🧹 清理资源...")
            if processes.get('mitmproxy') and processes['mitmproxy'].poll() is None:
                processes['mitmproxy'].terminate()
            if processes.get('ssl') and processes['ssl'].poll() is None:
                processes['ssl'].terminate()
        
        subprocess.run("pkill -f mitmdump", shell=True, capture_output=True)
        run_adb("shell pkill frida-server")

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎯 修复和重新测试成功！")
            print("💡 网络捕获功能现在应该正常工作！")
        else:
            print("\n🔧 仍需进一步调试")
            print("💡 但已解决了主要的配置问题")
    except KeyboardInterrupt:
        print("\n⚠️  用户中断")
    except Exception as e:
        print(f"\n❌ 异常: {e}")
