#!/usr/bin/env python3
"""
Frida SSL绕过完整指南
包含所有必要步骤和故障排除
"""

import subprocess
import sys
import time
import json
from pathlib import Path

def print_header():
    """打印标题"""
    print("🚀 Frida SSL绕过完整实现指南")
    print("🔧 解决HTTPS证书验证问题的终极方案")
    print("=" * 70)
    print()

def check_prerequisites():
    """检查前置条件"""
    print("📋 检查前置条件...")
    
    issues = []
    
    # 检查frida-server文件
    if not Path("frida-server").exists():
        issues.append("❌ frida-server文件不存在")
    else:
        print("✅ frida-server文件存在")
    
    # 检查SSL绕过脚本
    if not Path("ssl_bypass.js").exists():
        issues.append("❌ ssl_bypass.js文件不存在")
    else:
        print("✅ ssl_bypass.js脚本存在")
    
    # 检查Frida安装
    try:
        result = subprocess.run("export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin && frida --version", 
                              shell=True, capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ Frida已安装: {result.stdout.strip()}")
        else:
            issues.append("❌ Frida未正确安装")
    except:
        issues.append("❌ Frida命令无法执行")
    
    # 检查网络捕获目录
    if not Path("mitm-logs").exists():
        issues.append("❌ mitm-logs目录不存在")
    else:
        print("✅ 网络捕获目录存在")
    
    if issues:
        print("\n⚠️  发现以下问题:")
        for issue in issues:
            print(f"   {issue}")
        return False
    
    print("✅ 所有前置条件满足")
    return True

def wait_for_device():
    """等待设备连接"""
    print("\n⏳ 等待Android设备连接...")
    
    for i in range(60):  # 等待最多60次，每次2秒
        try:
            result = subprocess.run("source android_env.sh && adb devices", 
                                  shell=True, capture_output=True, text=True, timeout=10)
            if "device" in result.stdout and "emulator" in result.stdout:
                print("✅ Android设备已连接")
                return True
        except:
            pass
        
        if i % 10 == 0:  # 每20秒显示一次进度
            print(f"   等待中... ({i+1}/60)")
        time.sleep(2)
    
    print("❌ 设备连接超时")
    return False

def create_manual_instructions():
    """创建手动操作指南"""
    print("\n📋 Frida SSL绕过手动操作指南")
    print("=" * 50)
    
    instructions = """
🔧 手动执行步骤:

1. 确保Android模拟器运行:
   source android_env.sh && adb devices

2. 推送frida-server到设备:
   adb push frida-server /data/local/tmp/frida-server
   adb shell chmod 755 /data/local/tmp/frida-server

3. 启动frida-server (忽略SELinux警告):
   adb shell '/data/local/tmp/frida-server &'

4. 启动目标应用:
   adb shell am start -n com.yjzx.yjzx2017/.controller.activity.splash.SplashActivity

5. 运行Frida SSL绕过:
   export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin
   frida -U com.yjzx.yjzx2017 -l ssl_bypass.js

6. 测试HTTPS捕获:
   adb shell am start -a android.intent.action.VIEW -d https://httpbin.org/get

7. 检查捕获结果:
   查看 mitm-logs/realtime_capture.json 文件

🔧 故障排除:

• SELinux警告: 可以安全忽略，不影响功能
• 连接失败: 重启frida-server和应用
• 应用崩溃: 使用spawn模式启动
• 无HTTPS捕获: 检查代理设置和证书

💡 成功指标:
• frida-server进程运行
• Frida脚本加载成功
• 捕获到HTTPS请求
"""
    
    print(instructions)
    
    # 保存到文件
    with open("frida_manual_guide.txt", 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print("📄 手动指南已保存到: frida_manual_guide.txt")

def create_automated_script():
    """创建自动化脚本"""
    print("\n📝 创建自动化脚本...")
    
    script_content = '''#!/bin/bash
# Frida SSL绕过自动化脚本

echo "🚀 Frida SSL绕过自动化执行"
echo "================================"

# 设置环境
source android_env.sh
export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin

# 等待设备
echo "⏳ 等待设备连接..."
adb wait-for-device
echo "✅ 设备已连接"

# 推送frida-server
echo "📱 推送frida-server..."
adb push frida-server /data/local/tmp/frida-server
adb shell chmod 755 /data/local/tmp/frida-server

# 启动frida-server
echo "🚀 启动frida-server..."
adb shell pkill frida-server 2>/dev/null
sleep 2
adb shell '/data/local/tmp/frida-server' 2>/dev/null &
sleep 8

# 启动应用
echo "📱 启动应用..."
adb shell am force-stop com.yjzx.yjzx2017
sleep 2
adb shell am start -n com.yjzx.yjzx2017/.controller.activity.splash.SplashActivity
sleep 5

# 运行Frida SSL绕过
echo "🔧 运行Frida SSL绕过..."
echo "💡 忽略SELinux警告，这是正常的"
frida -U com.yjzx.yjzx2017 -l ssl_bypass.js &
FRIDA_PID=$!

echo "✅ Frida进程已启动 (PID: $FRIDA_PID)"
echo "⏳ 等待脚本加载..."
sleep 15

# 测试HTTPS
echo "🔍 测试HTTPS捕获..."
echo "[]" > mitm-logs/realtime_capture.json
sleep 2

adb shell am start -a android.intent.action.VIEW -d https://httpbin.org/get
sleep 8
adb shell am start -a android.intent.action.VIEW -d https://www.baidu.com
sleep 8

echo "⏳ 等待网络请求..."
sleep 15

# 检查结果
HTTPS_COUNT=$(python3 -c "
import json
try:
    with open('mitm-logs/realtime_capture.json', 'r') as f:
        data = json.load(f)
        if isinstance(data, list):
            https_count = len([req for req in data if req.get('scheme') == 'https'])
            print(https_count)
        else:
            print(0)
except:
    print(0)
")

if [ "$HTTPS_COUNT" -gt 0 ]; then
    echo "🎉 SSL绕过成功！捕获到 $HTTPS_COUNT 个HTTPS请求！"
    echo "✅ 系统现在可以完全分析HTTPS流量！"
else
    echo "⚠️  未捕获到HTTPS请求，但系统可能仍在工作"
fi

echo "💡 Frida进程正在运行，按Ctrl+C停止"
trap 'kill $FRIDA_PID 2>/dev/null; adb shell pkill frida-server; exit 0' INT

wait $FRIDA_PID
'''
    
    with open("auto_frida_bypass.sh", 'w') as f:
        f.write(script_content)
    
    # 设置执行权限
    subprocess.run("chmod +x auto_frida_bypass.sh", shell=True)
    
    print("✅ 自动化脚本已创建: auto_frida_bypass.sh")

def main():
    print_header()
    
    # 检查前置条件
    if not check_prerequisites():
        print("\n🔧 请先解决前置条件问题")
        return False
    
    # 等待设备连接
    if not wait_for_device():
        print("\n⚠️  设备连接超时，但可以手动操作")
    
    # 创建指南和脚本
    create_manual_instructions()
    create_automated_script()
    
    print("\n🎯 Frida SSL绕过实现总结")
    print("=" * 50)
    print("✅ 所有必要文件已准备完成:")
    print("   • frida-server (已下载)")
    print("   • ssl_bypass.js (SSL绕过脚本)")
    print("   • frida_manual_guide.txt (手动指南)")
    print("   • auto_frida_bypass.sh (自动化脚本)")
    
    print("\n💡 推荐执行方式:")
    print("   1. 手动执行: 按照 frida_manual_guide.txt 操作")
    print("   2. 自动执行: 运行 ./auto_frida_bypass.sh")
    
    print("\n🔧 关键点:")
    print("   • SELinux警告可以安全忽略")
    print("   • 确保模拟器稳定运行")
    print("   • 耐心等待Frida脚本加载")
    print("   • 成功后可捕获所有HTTPS流量")
    
    print("\n🎉 一旦成功，你将拥有:")
    print("   ✅ 完整的APK动态分析平台")
    print("   ✅ HTTPS流量完全透明")
    print("   ✅ SSL证书验证绕过")
    print("   ✅ 实时网络监控能力")
    
    return True

if __name__ == "__main__":
    main()
