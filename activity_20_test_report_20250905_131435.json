{"start_time": "2025-09-05T13:13:41.580932", "package": "com.yjzx.yjzx2017", "activities_tested": [{"name": "com.yjzx.yjzx2017.controller.camera.CameraNewActivity", "index": 1, "start_time": "2025-09-05T13:13:41.646576", "launch_success": false, "network_before": 2, "network_after": 0, "network_increase": 0, "error_message": "ERROR: \nException occurred while executing 'start':\njava.lang.SecurityException: Permission Denial: starting Intent { flg=0x10000000 cmp=com.yjzx.yjzx2017/.controller.camera.CameraNewActivity } from null (pid=4947, uid=2000) not exported from uid 10170\n\tat com.android.server.wm.ActivityStackSupervisor.checkStartAnyActivityPermission(ActivityStackSupervisor.java:1032)\n\tat com.android.server.wm.ActivityStarter.executeRequest(ActivityStarter.java:999)\n\tat com.android.server.wm.ActivityStarter.execute(ActivityStarter.java:669)\n\tat com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1100)\n\tat com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1072)\n\tat com.android.server.am.ActivityManagerService.startActivityAsUserWithFeature(ActivityManagerService.java:3678)\n\tat com.android.server.am.ActivityManagerShellCommand.runStartActivity(ActivityManagerShellCommand.java:544)\n\tat com.android.server.am.ActivityManagerShellCommand.onCommand(ActivityManagerShellCommand.java:186)\n\tat android.os.BasicShellCommandHandler.exec(BasicShellCommandHandler.java:98)\n\tat android.os.ShellCommand.exec(ShellCommand.java:44)\n\tat com.android.server.am.ActivityManagerService.onShellCommand(ActivityManagerService.java:10521)\n\tat android.os.Binder.shellCommand(Binder.java:929)\n\tat android.os.Binder.onTransact(Binder.java:813)\n\tat android.app.IActivityManager$Stub.onTransact(IActivityManager.java:5027)\n\tat com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2883)\n\tat android.os.Binder.execTransactInternal(Binder.java:1159)\n\tat android.os.Binder.execTransact(Binder.java:1123)\n", "end_time": "2025-09-05T13:13:41.791454"}, {"name": "com.yjzx.yjzx2017.controller.camera.CameraTakeShowActivity", "index": 2, "start_time": "2025-09-05T13:13:43.794813", "launch_success": false, "network_before": 2, "network_after": 0, "network_increase": 0, "error_message": "ERROR: \nException occurred while executing 'start':\njava.lang.SecurityException: Permission Denial: starting Intent { flg=0x10000000 cmp=com.yjzx.yjzx2017/.controller.camera.CameraTakeShowActivity } from null (pid=4956, uid=2000) not exported from uid 10170\n\tat com.android.server.wm.ActivityStackSupervisor.checkStartAnyActivityPermission(ActivityStackSupervisor.java:1032)\n\tat com.android.server.wm.ActivityStarter.executeRequest(ActivityStarter.java:999)\n\tat com.android.server.wm.ActivityStarter.execute(ActivityStarter.java:669)\n\tat com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1100)\n\tat com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1072)\n\tat com.android.server.am.ActivityManagerService.startActivityAsUserWithFeature(ActivityManagerService.java:3678)\n\tat com.android.server.am.ActivityManagerShellCommand.runStartActivity(ActivityManagerShellCommand.java:544)\n\tat com.android.server.am.ActivityManagerShellCommand.onCommand(ActivityManagerShellCommand.java:186)\n\tat android.os.BasicShellCommandHandler.exec(BasicShellCommandHandler.java:98)\n\tat android.os.ShellCommand.exec(ShellCommand.java:44)\n\tat com.android.server.am.ActivityManagerService.onShellCommand(ActivityManagerService.java:10521)\n\tat android.os.Binder.shellCommand(Binder.java:929)\n\tat android.os.Binder.onTransact(Binder.java:813)\n\tat android.app.IActivityManager$Stub.onTransact(IActivityManager.java:5027)\n\tat com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2883)\n\tat android.os.Binder.execTransactInternal(Binder.java:1159)\n\tat android.os.Binder.execTransact(Binder.java:1123)\n", "end_time": "2025-09-05T13:13:43.919118"}, {"name": "com.yjzx.yjzx2017.controller.selfAuction.CameraXTestActivity", "index": 3, "start_time": "2025-09-05T13:13:45.921207", "launch_success": false, "network_before": 2, "network_after": 0, "network_increase": 0, "error_message": "ERROR: \nException occurred while executing 'start':\njava.lang.SecurityException: Permission Denial: starting Intent { flg=0x10000000 cmp=com.yjzx.yjzx2017/.controller.selfAuction.CameraXTestActivity } from null (pid=4966, uid=2000) not exported from uid 10170\n\tat com.android.server.wm.ActivityStackSupervisor.checkStartAnyActivityPermission(ActivityStackSupervisor.java:1032)\n\tat com.android.server.wm.ActivityStarter.executeRequest(ActivityStarter.java:999)\n\tat com.android.server.wm.ActivityStarter.execute(ActivityStarter.java:669)\n\tat com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1100)\n\tat com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1072)\n\tat com.android.server.am.ActivityManagerService.startActivityAsUserWithFeature(ActivityManagerService.java:3678)\n\tat com.android.server.am.ActivityManagerShellCommand.runStartActivity(ActivityManagerShellCommand.java:544)\n\tat com.android.server.am.ActivityManagerShellCommand.onCommand(ActivityManagerShellCommand.java:186)\n\tat android.os.BasicShellCommandHandler.exec(BasicShellCommandHandler.java:98)\n\tat android.os.ShellCommand.exec(ShellCommand.java:44)\n\tat com.android.server.am.ActivityManagerService.onShellCommand(ActivityManagerService.java:10521)\n\tat android.os.Binder.shellCommand(Binder.java:929)\n\tat android.os.Binder.onTransact(Binder.java:813)\n\tat android.app.IActivityManager$Stub.onTransact(IActivityManager.java:5027)\n\tat com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2883)\n\tat android.os.Binder.execTransactInternal(Binder.java:1159)\n\tat android.os.Binder.execTransact(Binder.java:1123)\n", "end_time": "2025-09-05T13:13:46.036409"}, {"name": "com.yjzx.yjzx2017.controller.activity.auction.AuctionSelfBuyActivity", "index": 4, "start_time": "2025-09-05T13:13:48.041371", "launch_success": false, "network_before": 2, "network_after": 0, "network_increase": 0, "error_message": "ERROR: \nException occurred while executing 'start':\njava.lang.SecurityException: Permission Denial: starting Intent { flg=0x10000000 cmp=com.yjzx.yjzx2017/.controller.activity.auction.AuctionSelfBuyActivity } from null (pid=4971, uid=2000) not exported from uid 10170\n\tat com.android.server.wm.ActivityStackSupervisor.checkStartAnyActivityPermission(ActivityStackSupervisor.java:1032)\n\tat com.android.server.wm.ActivityStarter.executeRequest(ActivityStarter.java:999)\n\tat com.android.server.wm.ActivityStarter.execute(ActivityStarter.java:669)\n\tat com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1100)\n\tat com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1072)\n\tat com.android.server.am.ActivityManagerService.startActivityAsUserWithFeature(ActivityManagerService.java:3678)\n\tat com.android.server.am.ActivityManagerShellCommand.runStartActivity(ActivityManagerShellCommand.java:544)\n\tat com.android.server.am.ActivityManagerShellCommand.onCommand(ActivityManagerShellCommand.java:186)\n\tat android.os.BasicShellCommandHandler.exec(BasicShellCommandHandler.java:98)\n\tat android.os.ShellCommand.exec(ShellCommand.java:44)\n\tat com.android.server.am.ActivityManagerService.onShellCommand(ActivityManagerService.java:10521)\n\tat android.os.Binder.shellCommand(Binder.java:929)\n\tat android.os.Binder.onTransact(Binder.java:813)\n\tat android.app.IActivityManager$Stub.onTransact(IActivityManager.java:5027)\n\tat com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2883)\n\tat android.os.Binder.execTransactInternal(Binder.java:1159)\n\tat android.os.Binder.execTransact(Binder.java:1123)\n", "end_time": "2025-09-05T13:13:48.163143"}, {"name": "com.yjzx.yjzx2017.controller.selfAuction.SelfAuctionAddActivity", "index": 5, "start_time": "2025-09-05T13:13:50.165689", "launch_success": false, "network_before": 2, "network_after": 0, "network_increase": 0, "error_message": "ERROR: \nException occurred while executing 'start':\njava.lang.SecurityException: Permission Denial: starting Intent { flg=0x10000000 cmp=com.yjzx.yjzx2017/.controller.selfAuction.SelfAuctionAddActivity } from null (pid=4981, uid=2000) not exported from uid 10170\n\tat com.android.server.wm.ActivityStackSupervisor.checkStartAnyActivityPermission(ActivityStackSupervisor.java:1032)\n\tat com.android.server.wm.ActivityStarter.executeRequest(ActivityStarter.java:999)\n\tat com.android.server.wm.ActivityStarter.execute(ActivityStarter.java:669)\n\tat com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1100)\n\tat com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1072)\n\tat com.android.server.am.ActivityManagerService.startActivityAsUserWithFeature(ActivityManagerService.java:3678)\n\tat com.android.server.am.ActivityManagerShellCommand.runStartActivity(ActivityManagerShellCommand.java:544)\n\tat com.android.server.am.ActivityManagerShellCommand.onCommand(ActivityManagerShellCommand.java:186)\n\tat android.os.BasicShellCommandHandler.exec(BasicShellCommandHandler.java:98)\n\tat android.os.ShellCommand.exec(ShellCommand.java:44)\n\tat com.android.server.am.ActivityManagerService.onShellCommand(ActivityManagerService.java:10521)\n\tat android.os.Binder.shellCommand(Binder.java:929)\n\tat android.os.Binder.onTransact(Binder.java:813)\n\tat android.app.IActivityManager$Stub.onTransact(IActivityManager.java:5027)\n\tat com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2883)\n\tat android.os.Binder.execTransactInternal(Binder.java:1159)\n\tat android.os.Binder.execTransact(Binder.java:1123)\n", "end_time": "2025-09-05T13:13:50.280080"}, {"name": "com.yjzx.yjzx2017.controller.selfAuction.SelfAuctionConfirmActivity", "index": 6, "start_time": "2025-09-05T13:13:52.283994", "launch_success": false, "network_before": 2, "network_after": 0, "network_increase": 0, "error_message": "ERROR: \nException occurred while executing 'start':\njava.lang.SecurityException: Permission Denial: starting Intent { flg=0x10000000 cmp=com.yjzx.yjzx2017/.controller.selfAuction.SelfAuctionConfirmActivity } from null (pid=4987, uid=2000) not exported from uid 10170\n\tat com.android.server.wm.ActivityStackSupervisor.checkStartAnyActivityPermission(ActivityStackSupervisor.java:1032)\n\tat com.android.server.wm.ActivityStarter.executeRequest(ActivityStarter.java:999)\n\tat com.android.server.wm.ActivityStarter.execute(ActivityStarter.java:669)\n\tat com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1100)\n\tat com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1072)\n\tat com.android.server.am.ActivityManagerService.startActivityAsUserWithFeature(ActivityManagerService.java:3678)\n\tat com.android.server.am.ActivityManagerShellCommand.runStartActivity(ActivityManagerShellCommand.java:544)\n\tat com.android.server.am.ActivityManagerShellCommand.onCommand(ActivityManagerShellCommand.java:186)\n\tat android.os.BasicShellCommandHandler.exec(BasicShellCommandHandler.java:98)\n\tat android.os.ShellCommand.exec(ShellCommand.java:44)\n\tat com.android.server.am.ActivityManagerService.onShellCommand(ActivityManagerService.java:10521)\n\tat android.os.Binder.shellCommand(Binder.java:929)\n\tat android.os.Binder.onTransact(Binder.java:813)\n\tat android.app.IActivityManager$Stub.onTransact(IActivityManager.java:5027)\n\tat com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2883)\n\tat android.os.Binder.execTransactInternal(Binder.java:1159)\n\tat android.os.Binder.execTransact(Binder.java:1123)\n", "end_time": "2025-09-05T13:13:52.430110"}, {"name": "com.yjzx.yjzx2017.controller.selfAuction.SelfAuctionScanCodeActivity", "index": 7, "start_time": "2025-09-05T13:13:54.433856", "launch_success": false, "network_before": 2, "network_after": 0, "network_increase": 0, "error_message": "ERROR: \nException occurred while executing 'start':\njava.lang.SecurityException: Permission Denial: starting Intent { flg=0x10000000 cmp=com.yjzx.yjzx2017/.controller.selfAuction.SelfAuctionScanCodeActivity } from null (pid=4997, uid=2000) not exported from uid 10170\n\tat com.android.server.wm.ActivityStackSupervisor.checkStartAnyActivityPermission(ActivityStackSupervisor.java:1032)\n\tat com.android.server.wm.ActivityStarter.executeRequest(ActivityStarter.java:999)\n\tat com.android.server.wm.ActivityStarter.execute(ActivityStarter.java:669)\n\tat com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1100)\n\tat com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1072)\n\tat com.android.server.am.ActivityManagerService.startActivityAsUserWithFeature(ActivityManagerService.java:3678)\n\tat com.android.server.am.ActivityManagerShellCommand.runStartActivity(ActivityManagerShellCommand.java:544)\n\tat com.android.server.am.ActivityManagerShellCommand.onCommand(ActivityManagerShellCommand.java:186)\n\tat android.os.BasicShellCommandHandler.exec(BasicShellCommandHandler.java:98)\n\tat android.os.ShellCommand.exec(ShellCommand.java:44)\n\tat com.android.server.am.ActivityManagerService.onShellCommand(ActivityManagerService.java:10521)\n\tat android.os.Binder.shellCommand(Binder.java:929)\n\tat android.os.Binder.onTransact(Binder.java:813)\n\tat android.app.IActivityManager$Stub.onTransact(IActivityManager.java:5027)\n\tat com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2883)\n\tat android.os.Binder.execTransactInternal(Binder.java:1159)\n\tat android.os.Binder.execTransact(Binder.java:1123)\n", "end_time": "2025-09-05T13:13:54.553022"}, {"name": "com.yjzx.yjzx2017.controller.selfAuction.SelfAuctionManageActivity", "index": 8, "start_time": "2025-09-05T13:13:56.553686", "launch_success": false, "network_before": 2, "network_after": 0, "network_increase": 0, "error_message": "ERROR: \nException occurred while executing 'start':\njava.lang.SecurityException: Permission Denial: starting Intent { flg=0x10000000 cmp=com.yjzx.yjzx2017/.controller.selfAuction.SelfAuctionManageActivity } from null (pid=5002, uid=2000) not exported from uid 10170\n\tat com.android.server.wm.ActivityStackSupervisor.checkStartAnyActivityPermission(ActivityStackSupervisor.java:1032)\n\tat com.android.server.wm.ActivityStarter.executeRequest(ActivityStarter.java:999)\n\tat com.android.server.wm.ActivityStarter.execute(ActivityStarter.java:669)\n\tat com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1100)\n\tat com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1072)\n\tat com.android.server.am.ActivityManagerService.startActivityAsUserWithFeature(ActivityManagerService.java:3678)\n\tat com.android.server.am.ActivityManagerShellCommand.runStartActivity(ActivityManagerShellCommand.java:544)\n\tat com.android.server.am.ActivityManagerShellCommand.onCommand(ActivityManagerShellCommand.java:186)\n\tat android.os.BasicShellCommandHandler.exec(BasicShellCommandHandler.java:98)\n\tat android.os.ShellCommand.exec(ShellCommand.java:44)\n\tat com.android.server.am.ActivityManagerService.onShellCommand(ActivityManagerService.java:10521)\n\tat android.os.Binder.shellCommand(Binder.java:929)\n\tat android.os.Binder.onTransact(Binder.java:813)\n\tat android.app.IActivityManager$Stub.onTransact(IActivityManager.java:5027)\n\tat com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2883)\n\tat android.os.Binder.execTransactInternal(Binder.java:1159)\n\tat android.os.Binder.execTransact(Binder.java:1123)\n", "end_time": "2025-09-05T13:13:56.673259"}, {"name": "com.yjzx.yjzx2017.controller.activity.setting.AddressCompanyActivity", "index": 9, "start_time": "2025-09-05T13:13:58.676999", "launch_success": false, "network_before": 2, "network_after": 0, "network_increase": 0, "error_message": "ERROR: \nException occurred while executing 'start':\njava.lang.SecurityException: Permission Denial: starting Intent { flg=0x10000000 cmp=com.yjzx.yjzx2017/.controller.activity.setting.AddressCompanyActivity } from null (pid=5012, uid=2000) not exported from uid 10170\n\tat com.android.server.wm.ActivityStackSupervisor.checkStartAnyActivityPermission(ActivityStackSupervisor.java:1032)\n\tat com.android.server.wm.ActivityStarter.executeRequest(ActivityStarter.java:999)\n\tat com.android.server.wm.ActivityStarter.execute(ActivityStarter.java:669)\n\tat com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1100)\n\tat com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1072)\n\tat com.android.server.am.ActivityManagerService.startActivityAsUserWithFeature(ActivityManagerService.java:3678)\n\tat com.android.server.am.ActivityManagerShellCommand.runStartActivity(ActivityManagerShellCommand.java:544)\n\tat com.android.server.am.ActivityManagerShellCommand.onCommand(ActivityManagerShellCommand.java:186)\n\tat android.os.BasicShellCommandHandler.exec(BasicShellCommandHandler.java:98)\n\tat android.os.ShellCommand.exec(ShellCommand.java:44)\n\tat com.android.server.am.ActivityManagerService.onShellCommand(ActivityManagerService.java:10521)\n\tat android.os.Binder.shellCommand(Binder.java:929)\n\tat android.os.Binder.onTransact(Binder.java:813)\n\tat android.app.IActivityManager$Stub.onTransact(IActivityManager.java:5027)\n\tat com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2883)\n\tat android.os.Binder.execTransactInternal(Binder.java:1159)\n\tat android.os.Binder.execTransact(Binder.java:1123)\n", "end_time": "2025-09-05T13:13:58.871798"}, {"name": "com.yjzx.yjzx2017.controller.activity.splash.SplashActivity", "index": 10, "start_time": "2025-09-05T13:14:00.876068", "launch_success": true, "network_before": 2, "network_after": 2, "network_increase": 0, "error_message": null, "end_time": "2025-09-05T13:14:06.025581"}, {"name": "com.yjzx.yjzx2017.controller.activity.MainActivity", "index": 11, "start_time": "2025-09-05T13:14:08.033859", "launch_success": false, "network_before": 2, "network_after": 0, "network_increase": 0, "error_message": "ERROR: \nException occurred while executing 'start':\njava.lang.SecurityException: Permission Denial: starting Intent { flg=0x10000000 cmp=com.yjzx.yjzx2017/.controller.activity.MainActivity } from null (pid=5145, uid=2000) not exported from uid 10170\n\tat com.android.server.wm.ActivityStackSupervisor.checkStartAnyActivityPermission(ActivityStackSupervisor.java:1032)\n\tat com.android.server.wm.ActivityStarter.executeRequest(ActivityStarter.java:999)\n\tat com.android.server.wm.ActivityStarter.execute(ActivityStarter.java:669)\n\tat com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1100)\n\tat com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1072)\n\tat com.android.server.am.ActivityManagerService.startActivityAsUserWithFeature(ActivityManagerService.java:3678)\n\tat com.android.server.am.ActivityManagerShellCommand.runStartActivity(ActivityManagerShellCommand.java:544)\n\tat com.android.server.am.ActivityManagerShellCommand.onCommand(ActivityManagerShellCommand.java:186)\n\tat android.os.BasicShellCommandHandler.exec(BasicShellCommandHandler.java:98)\n\tat android.os.ShellCommand.exec(ShellCommand.java:44)\n\tat com.android.server.am.ActivityManagerService.onShellCommand(ActivityManagerService.java:10521)\n\tat android.os.Binder.shellCommand(Binder.java:929)\n\tat android.os.Binder.onTransact(Binder.java:813)\n\tat android.app.IActivityManager$Stub.onTransact(IActivityManager.java:5027)\n\tat com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2883)\n\tat android.os.Binder.execTransactInternal(Binder.java:1159)\n\tat android.os.Binder.execTransact(Binder.java:1123)\n", "end_time": "2025-09-05T13:14:08.617631"}, {"name": "com.yjzx.yjzx2017.common.MiddleActivity", "index": 12, "start_time": "2025-09-05T13:14:10.617783", "launch_success": false, "network_before": 2, "network_after": 0, "network_increase": 0, "error_message": "ERROR: \nException occurred while executing 'start':\njava.lang.SecurityException: Permission Denial: starting Intent { flg=0x10000000 cmp=com.yjzx.yjzx2017/.common.MiddleActivity } from null (pid=5163, uid=2000) not exported from uid 10170\n\tat com.android.server.wm.ActivityStackSupervisor.checkStartAnyActivityPermission(ActivityStackSupervisor.java:1032)\n\tat com.android.server.wm.ActivityStarter.executeRequest(ActivityStarter.java:999)\n\tat com.android.server.wm.ActivityStarter.execute(ActivityStarter.java:669)\n\tat com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1100)\n\tat com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1072)\n\tat com.android.server.am.ActivityManagerService.startActivityAsUserWithFeature(ActivityManagerService.java:3678)\n\tat com.android.server.am.ActivityManagerShellCommand.runStartActivity(ActivityManagerShellCommand.java:544)\n\tat com.android.server.am.ActivityManagerShellCommand.onCommand(ActivityManagerShellCommand.java:186)\n\tat android.os.BasicShellCommandHandler.exec(BasicShellCommandHandler.java:98)\n\tat android.os.ShellCommand.exec(ShellCommand.java:44)\n\tat com.android.server.am.ActivityManagerService.onShellCommand(ActivityManagerService.java:10521)\n\tat android.os.Binder.shellCommand(Binder.java:929)\n\tat android.os.Binder.onTransact(Binder.java:813)\n\tat android.app.IActivityManager$Stub.onTransact(IActivityManager.java:5027)\n\tat com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2883)\n\tat android.os.Binder.execTransactInternal(Binder.java:1159)\n\tat android.os.Binder.execTransact(Binder.java:1123)\n", "end_time": "2025-09-05T13:14:10.735238"}, {"name": "com.yjzx.yjzx2017.common.UriSchemeProcessActivity", "index": 13, "start_time": "2025-09-05T13:14:12.738942", "launch_success": true, "network_before": 2, "network_after": 2, "network_increase": 0, "error_message": null, "end_time": "2025-09-05T13:14:17.979504"}, {"name": "com.yjzx.yjzx2017.controller.login.activity.LoginActivity", "index": 14, "start_time": "2025-09-05T13:14:19.985494", "launch_success": false, "network_before": 2, "network_after": 0, "network_increase": 0, "error_message": "ERROR: \nException occurred while executing 'start':\njava.lang.SecurityException: Permission Denial: starting Intent { flg=0x10000000 cmp=com.yjzx.yjzx2017/.controller.login.activity.LoginActivity } from null (pid=5310, uid=2000) not exported from uid 10170\n\tat com.android.server.wm.ActivityStackSupervisor.checkStartAnyActivityPermission(ActivityStackSupervisor.java:1032)\n\tat com.android.server.wm.ActivityStarter.executeRequest(ActivityStarter.java:999)\n\tat com.android.server.wm.ActivityStarter.execute(ActivityStarter.java:669)\n\tat com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1100)\n\tat com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1072)\n\tat com.android.server.am.ActivityManagerService.startActivityAsUserWithFeature(ActivityManagerService.java:3678)\n\tat com.android.server.am.ActivityManagerShellCommand.runStartActivity(ActivityManagerShellCommand.java:544)\n\tat com.android.server.am.ActivityManagerShellCommand.onCommand(ActivityManagerShellCommand.java:186)\n\tat android.os.BasicShellCommandHandler.exec(BasicShellCommandHandler.java:98)\n\tat android.os.ShellCommand.exec(ShellCommand.java:44)\n\tat com.android.server.am.ActivityManagerService.onShellCommand(ActivityManagerService.java:10521)\n\tat android.os.Binder.shellCommand(Binder.java:929)\n\tat android.os.Binder.onTransact(Binder.java:813)\n\tat android.app.IActivityManager$Stub.onTransact(IActivityManager.java:5027)\n\tat com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2883)\n\tat android.os.Binder.execTransactInternal(Binder.java:1159)\n\tat android.os.Binder.execTransact(Binder.java:1123)\n", "end_time": "2025-09-05T13:14:20.763600"}, {"name": "com.yjzx.yjzx2017.controller.login.activity.LoginSmsCodeActivity", "index": 15, "start_time": "2025-09-05T13:14:22.763884", "launch_success": false, "network_before": 2, "network_after": 0, "network_increase": 0, "error_message": "ERROR: \nException occurred while executing 'start':\njava.lang.SecurityException: Permission Denial: starting Intent { flg=0x10000000 cmp=com.yjzx.yjzx2017/.controller.login.activity.LoginSmsCodeActivity } from null (pid=5368, uid=2000) not exported from uid 10170\n\tat com.android.server.wm.ActivityStackSupervisor.checkStartAnyActivityPermission(ActivityStackSupervisor.java:1032)\n\tat com.android.server.wm.ActivityStarter.executeRequest(ActivityStarter.java:999)\n\tat com.android.server.wm.ActivityStarter.execute(ActivityStarter.java:669)\n\tat com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1100)\n\tat com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1072)\n\tat com.android.server.am.ActivityManagerService.startActivityAsUserWithFeature(ActivityManagerService.java:3678)\n\tat com.android.server.am.ActivityManagerShellCommand.runStartActivity(ActivityManagerShellCommand.java:544)\n\tat com.android.server.am.ActivityManagerShellCommand.onCommand(ActivityManagerShellCommand.java:186)\n\tat android.os.BasicShellCommandHandler.exec(BasicShellCommandHandler.java:98)\n\tat android.os.ShellCommand.exec(ShellCommand.java:44)\n\tat com.android.server.am.ActivityManagerService.onShellCommand(ActivityManagerService.java:10521)\n\tat android.os.Binder.shellCommand(Binder.java:929)\n\tat android.os.Binder.onTransact(Binder.java:813)\n\tat android.app.IActivityManager$Stub.onTransact(IActivityManager.java:5027)\n\tat com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2883)\n\tat android.os.Binder.execTransactInternal(Binder.java:1159)\n\tat android.os.Binder.execTransact(Binder.java:1123)\n", "end_time": "2025-09-05T13:14:22.921625"}, {"name": "com.yjzx.yjzx2017.controller.login.activity.RegistActivity", "index": 16, "start_time": "2025-09-05T13:14:24.924875", "launch_success": false, "network_before": 2, "network_after": 0, "network_increase": 0, "error_message": "ERROR: \nException occurred while executing 'start':\njava.lang.SecurityException: Permission Denial: starting Intent { flg=0x10000000 cmp=com.yjzx.yjzx2017/.controller.login.activity.RegistActivity } from null (pid=5383, uid=2000) not exported from uid 10170\n\tat com.android.server.wm.ActivityStackSupervisor.checkStartAnyActivityPermission(ActivityStackSupervisor.java:1032)\n\tat com.android.server.wm.ActivityStarter.executeRequest(ActivityStarter.java:999)\n\tat com.android.server.wm.ActivityStarter.execute(ActivityStarter.java:669)\n\tat com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1100)\n\tat com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1072)\n\tat com.android.server.am.ActivityManagerService.startActivityAsUserWithFeature(ActivityManagerService.java:3678)\n\tat com.android.server.am.ActivityManagerShellCommand.runStartActivity(ActivityManagerShellCommand.java:544)\n\tat com.android.server.am.ActivityManagerShellCommand.onCommand(ActivityManagerShellCommand.java:186)\n\tat android.os.BasicShellCommandHandler.exec(BasicShellCommandHandler.java:98)\n\tat android.os.ShellCommand.exec(ShellCommand.java:44)\n\tat com.android.server.am.ActivityManagerService.onShellCommand(ActivityManagerService.java:10521)\n\tat android.os.Binder.shellCommand(Binder.java:929)\n\tat android.os.Binder.onTransact(Binder.java:813)\n\tat android.app.IActivityManager$Stub.onTransact(IActivityManager.java:5027)\n\tat com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2883)\n\tat android.os.Binder.execTransactInternal(Binder.java:1159)\n\tat android.os.Binder.execTransact(Binder.java:1123)\n", "end_time": "2025-09-05T13:14:25.047361"}, {"name": "com.yjzx.yjzx2017.controller.login.activity.ForgotPasswordActivity", "index": 17, "start_time": "2025-09-05T13:14:27.049989", "launch_success": false, "network_before": 2, "network_after": 0, "network_increase": 0, "error_message": "ERROR: \nException occurred while executing 'start':\njava.lang.SecurityException: Permission Denial: starting Intent { flg=0x10000000 cmp=com.yjzx.yjzx2017/.controller.login.activity.ForgotPasswordActivity } from null (pid=5394, uid=2000) not exported from uid 10170\n\tat com.android.server.wm.ActivityStackSupervisor.checkStartAnyActivityPermission(ActivityStackSupervisor.java:1032)\n\tat com.android.server.wm.ActivityStarter.executeRequest(ActivityStarter.java:999)\n\tat com.android.server.wm.ActivityStarter.execute(ActivityStarter.java:669)\n\tat com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1100)\n\tat com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1072)\n\tat com.android.server.am.ActivityManagerService.startActivityAsUserWithFeature(ActivityManagerService.java:3678)\n\tat com.android.server.am.ActivityManagerShellCommand.runStartActivity(ActivityManagerShellCommand.java:544)\n\tat com.android.server.am.ActivityManagerShellCommand.onCommand(ActivityManagerShellCommand.java:186)\n\tat android.os.BasicShellCommandHandler.exec(BasicShellCommandHandler.java:98)\n\tat android.os.ShellCommand.exec(ShellCommand.java:44)\n\tat com.android.server.am.ActivityManagerService.onShellCommand(ActivityManagerService.java:10521)\n\tat android.os.Binder.shellCommand(Binder.java:929)\n\tat android.os.Binder.onTransact(Binder.java:813)\n\tat android.app.IActivityManager$Stub.onTransact(IActivityManager.java:5027)\n\tat com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2883)\n\tat android.os.Binder.execTransactInternal(Binder.java:1159)\n\tat android.os.Binder.execTransact(Binder.java:1123)\n", "end_time": "2025-09-05T13:14:27.174565"}, {"name": "com.yjzx.yjzx2017.controller.activity.setting.SettingActivity", "index": 18, "start_time": "2025-09-05T13:14:29.178691", "launch_success": false, "network_before": 2, "network_after": 0, "network_increase": 0, "error_message": "ERROR: \nException occurred while executing 'start':\njava.lang.SecurityException: Permission Denial: starting Intent { flg=0x10000000 cmp=com.yjzx.yjzx2017/.controller.activity.setting.SettingActivity } from null (pid=5399, uid=2000) not exported from uid 10170\n\tat com.android.server.wm.ActivityStackSupervisor.checkStartAnyActivityPermission(ActivityStackSupervisor.java:1032)\n\tat com.android.server.wm.ActivityStarter.executeRequest(ActivityStarter.java:999)\n\tat com.android.server.wm.ActivityStarter.execute(ActivityStarter.java:669)\n\tat com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1100)\n\tat com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1072)\n\tat com.android.server.am.ActivityManagerService.startActivityAsUserWithFeature(ActivityManagerService.java:3678)\n\tat com.android.server.am.ActivityManagerShellCommand.runStartActivity(ActivityManagerShellCommand.java:544)\n\tat com.android.server.am.ActivityManagerShellCommand.onCommand(ActivityManagerShellCommand.java:186)\n\tat android.os.BasicShellCommandHandler.exec(BasicShellCommandHandler.java:98)\n\tat android.os.ShellCommand.exec(ShellCommand.java:44)\n\tat com.android.server.am.ActivityManagerService.onShellCommand(ActivityManagerService.java:10521)\n\tat android.os.Binder.shellCommand(Binder.java:929)\n\tat android.os.Binder.onTransact(Binder.java:813)\n\tat android.app.IActivityManager$Stub.onTransact(IActivityManager.java:5027)\n\tat com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2883)\n\tat android.os.Binder.execTransactInternal(Binder.java:1159)\n\tat android.os.Binder.execTransact(Binder.java:1123)\n", "end_time": "2025-09-05T13:14:29.381427"}, {"name": "com.yjzx.yjzx2017.controller.activity.setting.OpenFingerActivity", "index": 19, "start_time": "2025-09-05T13:14:31.384575", "launch_success": false, "network_before": 2, "network_after": 0, "network_increase": 0, "error_message": "ERROR: \nException occurred while executing 'start':\njava.lang.SecurityException: Permission Denial: starting Intent { flg=0x10000000 cmp=com.yjzx.yjzx2017/.controller.activity.setting.OpenFingerActivity } from null (pid=5409, uid=2000) not exported from uid 10170\n\tat com.android.server.wm.ActivityStackSupervisor.checkStartAnyActivityPermission(ActivityStackSupervisor.java:1032)\n\tat com.android.server.wm.ActivityStarter.executeRequest(ActivityStarter.java:999)\n\tat com.android.server.wm.ActivityStarter.execute(ActivityStarter.java:669)\n\tat com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1100)\n\tat com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1072)\n\tat com.android.server.am.ActivityManagerService.startActivityAsUserWithFeature(ActivityManagerService.java:3678)\n\tat com.android.server.am.ActivityManagerShellCommand.runStartActivity(ActivityManagerShellCommand.java:544)\n\tat com.android.server.am.ActivityManagerShellCommand.onCommand(ActivityManagerShellCommand.java:186)\n\tat android.os.BasicShellCommandHandler.exec(BasicShellCommandHandler.java:98)\n\tat android.os.ShellCommand.exec(ShellCommand.java:44)\n\tat com.android.server.am.ActivityManagerService.onShellCommand(ActivityManagerService.java:10521)\n\tat android.os.Binder.shellCommand(Binder.java:929)\n\tat android.os.Binder.onTransact(Binder.java:813)\n\tat android.app.IActivityManager$Stub.onTransact(IActivityManager.java:5027)\n\tat com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2883)\n\tat android.os.Binder.execTransactInternal(Binder.java:1159)\n\tat android.os.Binder.execTransact(Binder.java:1123)\n", "end_time": "2025-09-05T13:14:31.504682"}, {"name": "com.yjzx.yjzx2017.controller.activity.setting.OpenLoginFingerActivity", "index": 20, "start_time": "2025-09-05T13:14:33.504881", "launch_success": false, "network_before": 2, "network_after": 0, "network_increase": 0, "error_message": "ERROR: \nException occurred while executing 'start':\njava.lang.SecurityException: Permission Denial: starting Intent { flg=0x10000000 cmp=com.yjzx.yjzx2017/.controller.activity.setting.OpenLoginFingerActivity } from null (pid=5414, uid=2000) not exported from uid 10170\n\tat com.android.server.wm.ActivityStackSupervisor.checkStartAnyActivityPermission(ActivityStackSupervisor.java:1032)\n\tat com.android.server.wm.ActivityStarter.executeRequest(ActivityStarter.java:999)\n\tat com.android.server.wm.ActivityStarter.execute(ActivityStarter.java:669)\n\tat com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1100)\n\tat com.android.server.wm.ActivityTaskManagerService.startActivityAsUser(ActivityTaskManagerService.java:1072)\n\tat com.android.server.am.ActivityManagerService.startActivityAsUserWithFeature(ActivityManagerService.java:3678)\n\tat com.android.server.am.ActivityManagerShellCommand.runStartActivity(ActivityManagerShellCommand.java:544)\n\tat com.android.server.am.ActivityManagerShellCommand.onCommand(ActivityManagerShellCommand.java:186)\n\tat android.os.BasicShellCommandHandler.exec(BasicShellCommandHandler.java:98)\n\tat android.os.ShellCommand.exec(ShellCommand.java:44)\n\tat com.android.server.am.ActivityManagerService.onShellCommand(ActivityManagerService.java:10521)\n\tat android.os.Binder.shellCommand(Binder.java:929)\n\tat android.os.Binder.onTransact(Binder.java:813)\n\tat android.app.IActivityManager$Stub.onTransact(IActivityManager.java:5027)\n\tat com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2883)\n\tat android.os.Binder.execTransactInternal(Binder.java:1159)\n\tat android.os.Binder.execTransact(Binder.java:1123)\n", "end_time": "2025-09-05T13:14:33.639318"}], "network_captures": [], "summary": {"total_tested": 20, "successful_launches": 2, "failed_launches": 18, "network_activity_detected": 0}, "end_time": "2025-09-05T13:14:35.642942", "total_network_increase": 0}