#!/usr/bin/env python3
"""
深度UI遍历测试
通过更完整的用户交互模拟来触发更多网络请求
"""

import subprocess
import time
import json
import logging
import os
import threading
from datetime import datetime
import xml.etree.ElementTree as ET
import tempfile

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DeepUITraversalTest:
    """深度UI遍历测试器"""
    
    def __init__(self):
        self.device_id = "emulator-5554"
        self.package_name = "com.iloda.beacon"
        self.main_activity = "com.iloda.beacon.activity.LoginActivity"
        self.captured_requests = []
        self.ui_states_visited = []
        self.test_phone_numbers = [
            "13800138000",
            "15888888888", 
            "18612345678",
            "13912345678"
        ]
        self.mitm_log_file = f"deep_traversal_mitm_{int(time.time())}.log"
        self.running = True
        
    def run_adb_command(self, command, timeout=30):
        """执行ADB命令"""
        try:
            full_command = ['adb', '-s', self.device_id] + command
            result = subprocess.run(
                full_command, 
                capture_output=True, 
                text=True, 
                timeout=timeout
            )
            return result
        except Exception as e:
            logger.error(f"ADB命令执行异常: {e}")
            return None
    
    def get_current_ui_state(self):
        """获取当前UI状态"""
        try:
            # UI dump
            ui_dump_result = self.run_adb_command(['shell', 'uiautomator', 'dump', '/sdcard/ui_current.xml'])
            
            if ui_dump_result and ui_dump_result.returncode == 0:
                with tempfile.NamedTemporaryFile(mode='w+', suffix='.xml', delete=False) as tmp_file:
                    pull_result = subprocess.run([
                        'adb', '-s', self.device_id, 'pull', '/sdcard/ui_current.xml', tmp_file.name
                    ], capture_output=True, text=True)
                    
                    if pull_result.returncode == 0:
                        try:
                            tree = ET.parse(tmp_file.name)
                            root = tree.getroot()
                            
                            ui_elements = []
                            for node in root.iter('node'):
                                element = {
                                    "class": node.get('class', ''),
                                    "text": node.get('text', ''),
                                    "resource_id": node.get('resource-id', ''),
                                    "clickable": node.get('clickable', 'false') == 'true',
                                    "bounds": node.get('bounds', ''),
                                    "content_desc": node.get('content-desc', ''),
                                    "enabled": node.get('enabled', 'false') == 'true',
                                    "focused": node.get('focused', 'false') == 'true',
                                    "scrollable": node.get('scrollable', 'false') == 'true'
                                }
                                ui_elements.append(element)
                            
                            # 生成UI状态指纹
                            clickable_elements = [elem for elem in ui_elements if elem["clickable"]]
                            text_elements = [elem["text"] for elem in ui_elements if elem["text"]]
                            ui_fingerprint = hash(str(sorted(text_elements + [elem["resource_id"] for elem in clickable_elements])))
                            
                            return {
                                "fingerprint": ui_fingerprint,
                                "elements": ui_elements,
                                "clickable_count": len(clickable_elements),
                                "text_elements": text_elements[:10]  # 前10个文本元素作为标识
                            }
                        
                        except Exception as e:
                            logger.error(f"UI解析异常: {e}")
                            return None
                        finally:
                            os.unlink(tmp_file.name)
        except Exception as e:
            logger.error(f"UI状态获取异常: {e}")
            return None
    
    def find_input_fields(self, ui_state):
        """查找输入框"""
        if not ui_state:
            return []
        
        input_fields = []
        for element in ui_state["elements"]:
            if (element["class"] in ["android.widget.EditText", "android.widget.AutoCompleteTextView"] or
                "input" in element["resource_id"].lower() or
                "edit" in element["resource_id"].lower() or
                element["text"] in ["", "请输入手机号", "请输入验证码", "输入手机号"]):
                input_fields.append(element)
        
        return input_fields
    
    def find_clickable_elements(self, ui_state):
        """查找可点击元素"""
        if not ui_state:
            return []
        
        clickable = []
        for element in ui_state["elements"]:
            if (element["clickable"] and element["enabled"] and
                not any(skip in element["class"].lower() for skip in ["toolbar", "statusbar", "navigationbar"])):
                clickable.append(element)
        
        return clickable
    
    def parse_bounds(self, bounds_str):
        """解析bounds字符串"""
        try:
            import re
            match = re.search(r'\[(\d+),(\d+)\]\[(\d+),(\d+)\]', bounds_str)
            if match:
                left, top, right, bottom = map(int, match.groups())
                center_x = (left + right) // 2
                center_y = (top + bottom) // 2
                return center_x, center_y
        except:
            pass
        return None, None
    
    def comprehensive_ui_interaction(self):
        """全面的UI交互测试"""
        logger.info("🎯 开始深度UI遍历测试...")
        
        interaction_sequence = [
            {"action": "wait", "duration": 3, "description": "等待应用启动完成"},
            {"action": "get_ui_state", "description": "获取初始UI状态"},
            
            # 第一阶段：权限处理
            {"action": "handle_permissions", "description": "处理所有权限请求"},
            
            # 第二阶段：多种手机号测试
            {"action": "multiple_phone_tests", "description": "测试多个手机号码"},
            
            # 第三阶段：深度交互
            {"action": "deep_interaction", "description": "深度UI元素交互"},
            
            # 第四阶段：后台切换测试
            {"action": "background_test", "description": "后台切换测试"},
            
            # 第五阶段：长时间等待
            {"action": "long_wait", "description": "长时间等待网络请求"}
        ]
        
        for step in interaction_sequence:
            if not self.running:
                break
                
            logger.info(f"执行步骤: {step['description']}")
            
            if step["action"] == "wait":
                time.sleep(step["duration"])
            elif step["action"] == "get_ui_state":
                ui_state = self.get_current_ui_state()
                if ui_state:
                    logger.info(f"UI状态: {ui_state['clickable_count']}个可点击元素")
                    logger.info(f"文本元素: {ui_state['text_elements'][:3]}")
            elif step["action"] == "handle_permissions":
                self.handle_all_permissions()
            elif step["action"] == "multiple_phone_tests":
                self.test_multiple_phone_numbers()
            elif step["action"] == "deep_interaction":
                self.perform_deep_interactions()
            elif step["action"] == "background_test":
                self.test_background_switching()
            elif step["action"] == "long_wait":
                self.wait_for_background_requests()
    
    def handle_all_permissions(self):
        """处理所有权限对话框"""
        logger.info("🔐 处理权限请求...")
        
        for i in range(3):  # 最多尝试3次
            ui_state = self.get_current_ui_state()
            if not ui_state:
                continue
                
            # 查找权限相关按钮
            permission_buttons = []
            for element in ui_state["elements"]:
                text = element["text"].lower()
                if any(keyword in text for keyword in ["allow", "continue", "确定", "同意", "允许", "ok"]):
                    permission_buttons.append(element)
            
            if permission_buttons:
                logger.info(f"发现权限按钮: {len(permission_buttons)}个")
                for button in permission_buttons[:2]:  # 点击前2个
                    x, y = self.parse_bounds(button["bounds"])
                    if x and y:
                        logger.info(f"点击权限按钮: {button['text']}")
                        self.run_adb_command(['shell', 'input', 'tap', str(x), str(y)])
                        time.sleep(2)
            else:
                break  # 没有更多权限对话框
                
            time.sleep(2)
    
    def test_multiple_phone_numbers(self):
        """测试多个手机号码"""
        logger.info("📱 测试多个手机号码...")
        
        for phone_number in self.test_phone_numbers:
            logger.info(f"测试手机号: {phone_number}")
            
            # 获取当前UI状态
            ui_state = self.get_current_ui_state()
            if not ui_state:
                continue
            
            # 查找手机号输入框
            input_fields = self.find_input_fields(ui_state)
            phone_input = None
            
            for field in input_fields:
                if (field["resource_id"] and "phone" in field["resource_id"].lower()) or \
                   "手机号" in field.get("text", "") or \
                   "phone" in field.get("text", "").lower():
                    phone_input = field
                    break
            
            # 如果没找到特定的手机号输入框，使用第一个输入框
            if not phone_input and input_fields:
                phone_input = input_fields[0]
            
            if phone_input:
                # 点击输入框
                x, y = self.parse_bounds(phone_input["bounds"])
                if x and y:
                    self.run_adb_command(['shell', 'input', 'tap', str(x), str(y)])
                    time.sleep(1)
                    
                    # 清空输入框
                    self.run_adb_command(['shell', 'input', 'keyevent', 'KEYCODE_CTRL_A'])
                    time.sleep(0.5)
                    self.run_adb_command(['shell', 'input', 'keyevent', 'KEYCODE_DEL'])
                    time.sleep(0.5)
                    
                    # 输入手机号
                    self.run_adb_command(['shell', 'input', 'text', phone_number])
                    time.sleep(2)
                    
                    # 查找验证码按钮
                    ui_state = self.get_current_ui_state()
                    if ui_state:
                        for element in ui_state["elements"]:
                            text = element["text"].lower()
                            if element["clickable"] and any(keyword in text for keyword in 
                                ["验证码", "获取验证码", "send", "verify", "code", "确定", "登录"]):
                                x, y = self.parse_bounds(element["bounds"])
                                if x and y:
                                    logger.info(f"点击按钮: {element['text']}")
                                    self.run_adb_command(['shell', 'input', 'tap', str(x), str(y)])
                                    time.sleep(5)  # 等待网络请求
                                    break
            
            time.sleep(3)  # 等待响应
    
    def perform_deep_interactions(self):
        """执行深度交互"""
        logger.info("🔍 执行深度UI交互...")
        
        for round_num in range(3):  # 3轮深度交互
            logger.info(f"第{round_num+1}轮深度交互")
            
            ui_state = self.get_current_ui_state()
            if not ui_state:
                continue
            
            # 检查是否是新的UI状态
            if ui_state["fingerprint"] in self.ui_states_visited:
                logger.info("UI状态已访问过，跳过")
                continue
            
            self.ui_states_visited.append(ui_state["fingerprint"])
            
            # 找到所有可点击元素
            clickable_elements = self.find_clickable_elements(ui_state)
            
            logger.info(f"发现可点击元素: {len(clickable_elements)}个")
            
            # 点击每个元素
            for i, element in enumerate(clickable_elements[:5]):  # 限制前5个
                logger.info(f"点击元素 {i+1}: {element['text'] or element['resource_id'] or element['class']}")
                
                x, y = self.parse_bounds(element["bounds"])
                if x and y:
                    self.run_adb_command(['shell', 'input', 'tap', str(x), str(y)])
                    time.sleep(3)  # 等待响应
                    
                    # 检查是否有新的网络活动
                    time.sleep(2)
            
            # 尝试滑动操作
            self.run_adb_command(['shell', 'input', 'swipe', '500', '1000', '500', '500'])
            time.sleep(2)
            
            # 尝试按返回键
            self.run_adb_command(['shell', 'input', 'keyevent', 'KEYCODE_BACK'])
            time.sleep(2)
    
    def test_background_switching(self):
        """测试后台切换"""
        logger.info("🔄 测试后台切换...")
        
        # 切换到后台
        self.run_adb_command(['shell', 'input', 'keyevent', 'KEYCODE_HOME'])
        time.sleep(3)
        
        # 切换回前台
        self.run_adb_command([
            'shell', 'am', 'start', '-n', 
            f'{self.package_name}/{self.main_activity}'
        ])
        time.sleep(5)
        
        # 再次尝试交互
        ui_state = self.get_current_ui_state()
        if ui_state:
            clickable = self.find_clickable_elements(ui_state)
            if clickable:
                element = clickable[0]
                x, y = self.parse_bounds(element["bounds"])
                if x and y:
                    self.run_adb_command(['shell', 'input', 'tap', str(x), str(y)])
                    time.sleep(3)
    
    def wait_for_background_requests(self):
        """等待后台网络请求"""
        logger.info("⏳ 等待后台网络请求...")
        
        for i in range(10):  # 等待30秒，每3秒检查一次
            logger.info(f"等待后台请求... {i+1}/10")
            time.sleep(3)
            
            # 发送一些系统事件可能触发网络请求
            if i == 3:
                # 模拟网络状态变化
                self.run_adb_command(['shell', 'svc', 'wifi', 'disable'])
                time.sleep(1)
                self.run_adb_command(['shell', 'svc', 'wifi', 'enable'])
            
            if i == 7:
                # 发送推送消息广播
                self.run_adb_command([
                    'shell', 'am', 'broadcast', '-a', 'cn.jpush.android.intent.REGISTRATION',
                    '--es', 'cn.jpush.android.REGISTRATION_ID', 'test_registration_id'
                ])
    
    def monitor_network_during_test(self):
        """测试期间监控网络活动"""
        logger.info("📊 开始监控网络活动...")
        
        while self.running:
            try:
                # 检查网络连接状态
                result = self.run_adb_command(['shell', 'netstat', '-an'], timeout=10)
                if result and result.returncode == 0:
                    tcp_count = result.stdout.count('tcp')
                    if tcp_count > 10:  # 如果连接数突然增加
                        logger.info(f"检测到网络活动增加: {tcp_count}个TCP连接")
                
                time.sleep(5)
            except Exception as e:
                logger.error(f"网络监控异常: {e}")
                break
    
    def run_comprehensive_test(self):
        """运行综合深度测试"""
        logger.info("="*60)
        logger.info("🔍 深度UI遍历与网络捕获测试")
        logger.info("="*60)
        
        try:
            # 重启应用，清除所有状态
            logger.info("重启应用...")
            self.run_adb_command(['shell', 'am', 'force-stop', self.package_name])
            time.sleep(2)
            
            # 清除应用数据，触发初始化请求
            logger.info("清除应用数据...")
            self.run_adb_command(['shell', 'pm', 'clear', self.package_name])
            time.sleep(2)
            
            # 启动网络监控线程
            monitor_thread = threading.Thread(target=self.monitor_network_during_test)
            monitor_thread.daemon = True
            monitor_thread.start()
            
            # 启动应用
            logger.info("启动应用...")
            launch_result = self.run_adb_command([
                'shell', 'am', 'start', '-n', 
                f'{self.package_name}/{self.main_activity}'
            ])
            
            if launch_result and launch_result.returncode == 0:
                logger.info("✅ 应用启动成功")
                
                # 执行综合UI交互
                self.comprehensive_ui_interaction()
                
                # 停止监控
                self.running = False
                
                logger.info("📊 深度遍历测试完成")
                logger.info(f"访问的UI状态数: {len(self.ui_states_visited)}")
                
                return True
            else:
                logger.error("❌ 应用启动失败")
                return False
                
        except Exception as e:
            logger.error(f"深度测试异常: {e}")
            return False
        finally:
            self.running = False

def main():
    """主函数"""
    tester = DeepUITraversalTest()
    success = tester.run_comprehensive_test()
    
    if success:
        print("\n🎉 深度UI遍历测试完成!")
        print("💡 建议同时运行mitmproxy捕获网络流量:")
        print("   mitmproxy --listen-host 0.0.0.0 --listen-port 8080")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())




