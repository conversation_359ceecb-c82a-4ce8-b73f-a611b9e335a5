
// 优化的SSL绕过脚本 - 兼容Android模拟器
console.log("[SSL Bypass] Starting optimized SSL bypass...");

Java.perform(function() {
    console.log("[SSL Bypass] Java.perform started successfully");
    
    try {
        // 1. Hook SSLContext - 最重要的绕过
        try {
            var SSLContext = Java.use("javax.net.ssl.SSLContext");
            SSLContext.init.overload("[Ljavax.net.ssl.KeyManager;", "[Ljavax.net.ssl.TrustManager;", "java.security.SecureRandom").implementation = function(keyManagers, trustManagers, secureRandom) {
                console.log("[SSL Bypass] SSLContext.init() bypassed");
                this.init(keyManagers, null, secureRandom);
            };
            console.log("[SSL Bypass] ✅ SSLContext hooked successfully");
        } catch (e) {
            console.log("[SSL Bypass] ⚠️  SSLContext hook failed: " + e);
        }
        
        // 2. Hook HostnameVerifier
        try {
            var HostnameVerifier = Java.use("javax.net.ssl.HostnameVerifier");
            HostnameVerifier.verify.overload("java.lang.String", "javax.net.ssl.SSLSession").implementation = function(hostname, session) {
                console.log("[SSL Bypass] HostnameVerifier bypassed for: " + hostname);
                return true;
            };
            console.log("[SSL Bypass] ✅ HostnameVerifier hooked successfully");
        } catch (e) {
            console.log("[SSL Bypass] ⚠️  HostnameVerifier hook failed: " + e);
        }
        
        // 3. Hook X509TrustManager
        try {
            var X509TrustManager = Java.use("javax.net.ssl.X509TrustManager");
            X509TrustManager.checkClientTrusted.implementation = function(chain, authType) {
                console.log("[SSL Bypass] X509TrustManager.checkClientTrusted bypassed");
            };
            X509TrustManager.checkServerTrusted.implementation = function(chain, authType) {
                console.log("[SSL Bypass] X509TrustManager.checkServerTrusted bypassed");
            };
            X509TrustManager.getAcceptedIssuers.implementation = function() {
                console.log("[SSL Bypass] X509TrustManager.getAcceptedIssuers bypassed");
                return [];
            };
            console.log("[SSL Bypass] ✅ X509TrustManager hooked successfully");
        } catch (e) {
            console.log("[SSL Bypass] ⚠️  X509TrustManager hook failed: " + e);
        }
        
        // 4. Hook OkHttp (如果存在)
        try {
            var OkHttpClient = Java.use("okhttp3.OkHttpClient");
            console.log("[SSL Bypass] ✅ OkHttp detected, applying bypass...");
            
            var CertificatePinner = Java.use("okhttp3.CertificatePinner");
            CertificatePinner.check.overload("java.lang.String", "java.util.List").implementation = function(hostname, peerCertificates) {
                console.log("[SSL Bypass] OkHttp CertificatePinner bypassed for: " + hostname);
                return;
            };
            console.log("[SSL Bypass] ✅ OkHttp CertificatePinner hooked successfully");
        } catch (e) {
            console.log("[SSL Bypass] ℹ️  OkHttp not found or already bypassed");
        }
        
        // 5. Hook WebView SSL错误处理
        try {
            var WebViewClient = Java.use("android.webkit.WebViewClient");
            WebViewClient.onReceivedSslError.implementation = function(view, handler, error) {
                console.log("[SSL Bypass] WebView SSL error bypassed");
                handler.proceed();
            };
            console.log("[SSL Bypass] ✅ WebView SSL error handler hooked successfully");
        } catch (e) {
            console.log("[SSL Bypass] ⚠️  WebView hook failed: " + e);
        }
        
        console.log("[SSL Bypass] 🎉 SSL bypass initialization completed!");
        console.log("[SSL Bypass] 🔓 HTTPS traffic should now be capturable");
        
    } catch (e) {
        console.log("[SSL Bypass] ❌ Critical error: " + e);
    }
});

console.log("[SSL Bypass] Script loaded and ready");
