#!/usr/bin/env python3
"""
在Android模拟器中安装mitmproxy证书
解决HTTPS抓包的证书验证问题
"""

import subprocess
import sys
import time
import hashlib
from pathlib import Path

class CertificateInstaller:
    def __init__(self):
        self.cert_file = "mitmproxy-ca-cert.pem"
        
    def run_adb(self, cmd, timeout=30):
        """执行adb命令"""
        full_cmd = f"source android_env.sh && adb {cmd}"
        try:
            result = subprocess.run(full_cmd, shell=True, 
                                  capture_output=True, text=True, 
                                  timeout=timeout, check=True)
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            return f"ERROR: {e.stderr}"
        except subprocess.TimeoutExpired:
            return "ERROR: Timeout"
    
    def get_cert_hash(self):
        """获取证书的hash值，用于Android系统证书命名"""
        try:
            # 读取证书内容
            with open(self.cert_file, 'r') as f:
                cert_content = f.read()
            
            # 提取证书部分（去掉BEGIN和END行）
            cert_lines = []
            in_cert = False
            for line in cert_content.split('\n'):
                if '-----BEGIN CERTIFICATE-----' in line:
                    in_cert = True
                    continue
                elif '-----END CERTIFICATE-----' in line:
                    break
                elif in_cert:
                    cert_lines.append(line)
            
            # 计算hash
            cert_data = ''.join(cert_lines)
            import base64
            cert_bytes = base64.b64decode(cert_data)
            
            # 使用OpenSSL命令获取subject hash
            cmd = f"openssl x509 -inform PEM -subject_hash_old -in {self.cert_file} -noout"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                return result.stdout.strip()
            else:
                # 备用方法：使用简单hash
                return hashlib.md5(cert_bytes).hexdigest()[:8]
                
        except Exception as e:
            print(f"⚠️  获取证书hash失败: {e}")
            return "c8750f0d"  # 默认hash
    
    def install_as_system_cert(self):
        """将证书安装为系统证书（需要root权限）"""
        print("🔧 尝试安装为系统证书...")
        
        # 获取证书hash
        cert_hash = self.get_cert_hash()
        system_cert_name = f"{cert_hash}.0"
        
        print(f"📋 证书hash: {cert_hash}")
        print(f"📋 系统证书名: {system_cert_name}")
        
        # 检查是否有root权限
        result = self.run_adb("shell su -c 'id'")
        if "ERROR" in result or "uid=0" not in result:
            print("❌ 没有root权限，无法安装系统证书")
            return False
        
        print("✅ 检测到root权限")
        
        # 重新挂载系统分区为可写
        print("🔧 重新挂载系统分区...")
        self.run_adb("shell su -c 'mount -o rw,remount /system'")
        
        # 复制证书到系统证书目录
        print("📁 复制证书到系统目录...")
        self.run_adb(f"shell su -c 'cp /sdcard/{self.cert_file} /system/etc/security/cacerts/{system_cert_name}'")
        
        # 设置正确的权限
        print("🔐 设置证书权限...")
        self.run_adb(f"shell su -c 'chmod 644 /system/etc/security/cacerts/{system_cert_name}'")
        self.run_adb(f"shell su -c 'chown root:root /system/etc/security/cacerts/{system_cert_name}'")
        
        # 重新挂载为只读
        print("🔒 重新挂载系统分区为只读...")
        self.run_adb("shell su -c 'mount -o ro,remount /system'")
        
        print("✅ 系统证书安装完成")
        return True
    
    def install_as_user_cert(self):
        """通过设置界面安装用户证书"""
        print("🔧 尝试通过设置界面安装用户证书...")
        
        # 打开设置应用
        print("📱 打开设置应用...")
        self.run_adb("shell am start -a android.settings.SECURITY_SETTINGS")
        time.sleep(3)
        
        print("📋 请手动完成以下步骤:")
        print("   1. 在设置中找到 '安全' 或 'Security'")
        print("   2. 找到 '加密与凭据' 或 'Encryption & credentials'")
        print("   3. 点击 '从存储设备安装' 或 'Install from storage'")
        print("   4. 选择 'mitmproxy-ca-cert.pem' 文件")
        print("   5. 输入证书名称（如：mitmproxy）")
        print("   6. 选择用途为 'VPN和应用' 或 'VPN and apps'")
        
        return True
    
    def install_via_magisk(self):
        """通过Magisk模块安装证书（如果可用）"""
        print("🔧 检查Magisk是否可用...")
        
        # 检查Magisk
        result = self.run_adb("shell su -c 'which magisk'")
        if "ERROR" in result or not result:
            print("❌ Magisk不可用")
            return False
        
        print("✅ 检测到Magisk")
        
        # 创建Magisk模块目录
        module_dir = "/data/adb/modules/mitmproxy_cert"
        print(f"📁 创建模块目录: {module_dir}")
        
        self.run_adb(f"shell su -c 'mkdir -p {module_dir}/system/etc/security/cacerts'")
        
        # 获取证书hash并复制
        cert_hash = self.get_cert_hash()
        system_cert_name = f"{cert_hash}.0"
        
        print(f"📋 复制证书: {system_cert_name}")
        self.run_adb(f"shell su -c 'cp /sdcard/{self.cert_file} {module_dir}/system/etc/security/cacerts/{system_cert_name}'")
        self.run_adb(f"shell su -c 'chmod 644 {module_dir}/system/etc/security/cacerts/{system_cert_name}'")
        
        # 创建模块配置
        module_prop = f"""id=mitmproxy_cert
name=Mitmproxy Certificate
version=v1.0
versionCode=1
author=APK_Detect
description=Install mitmproxy certificate as system cert
"""
        
        # 写入模块配置
        self.run_adb(f"shell su -c 'echo \"{module_prop}\" > {module_dir}/module.prop'")
        
        print("✅ Magisk模块安装完成，需要重启生效")
        return True
    
    def verify_installation(self):
        """验证证书安装"""
        print("🔍 验证证书安装...")
        
        # 检查系统证书
        cert_hash = self.get_cert_hash()
        system_cert_name = f"{cert_hash}.0"
        
        result = self.run_adb(f"shell ls -la /system/etc/security/cacerts/{system_cert_name}")
        if "ERROR" not in result and system_cert_name in result:
            print("✅ 系统证书安装成功")
            return True
        
        # 检查用户证书
        result = self.run_adb("shell settings get secure user_setup_complete")
        if "1" in result:
            print("ℹ️  用户证书可能已安装，请检查设置")
            return True
        
        print("❌ 证书安装验证失败")
        return False
    
    def install_certificate(self):
        """主安装流程"""
        print("🎯 开始安装mitmproxy证书")
        print("=" * 60)
        
        # 检查证书文件
        if not Path(self.cert_file).exists():
            print(f"❌ 证书文件不存在: {self.cert_file}")
            return False
        
        print(f"✅ 找到证书文件: {self.cert_file}")
        
        # 检查设备连接
        result = self.run_adb("devices")
        if "device" not in result:
            print("❌ Android设备未连接")
            return False
        
        print("✅ Android设备已连接")
        
        # 尝试不同的安装方法
        methods = [
            ("Magisk模块", self.install_via_magisk),
            ("系统证书", self.install_as_system_cert),
            ("用户证书", self.install_as_user_cert)
        ]
        
        for method_name, method_func in methods:
            print(f"\n🔧 尝试方法: {method_name}")
            try:
                if method_func():
                    print(f"✅ {method_name} 安装成功")
                    
                    # 验证安装
                    if method_name != "用户证书":  # 用户证书需要手动验证
                        if self.verify_installation():
                            print("🎉 证书安装并验证成功！")
                            return True
                    else:
                        print("ℹ️  请手动验证用户证书安装")
                        return True
                else:
                    print(f"❌ {method_name} 安装失败")
            except Exception as e:
                print(f"❌ {method_name} 安装异常: {e}")
        
        print("❌ 所有安装方法都失败了")
        return False
    
    def restart_device(self):
        """重启设备使证书生效"""
        print("🔄 重启设备使证书生效...")
        self.run_adb("reboot")
        print("⏳ 设备重启中，请等待...")

def main():
    print("🚀 mitmproxy证书安装工具")
    print("🔧 解决HTTPS抓包证书验证问题")
    print("=" * 60)
    
    installer = CertificateInstaller()
    
    try:
        success = installer.install_certificate()
        
        if success:
            print("\n🎉 证书安装完成！")
            print("📋 接下来的步骤:")
            print("   1. 重启Android设备（如果使用系统证书）")
            print("   2. 重新运行网络抓包测试")
            print("   3. HTTPS请求应该能够成功捕获")
            
            # 询问是否重启
            restart = input("\n是否现在重启设备？(y/N): ").lower().strip()
            if restart == 'y':
                installer.restart_device()
        else:
            print("\n❌ 证书安装失败")
            print("📋 手动安装步骤:")
            print("   1. 在Android设备上打开设置")
            print("   2. 进入 安全 -> 加密与凭据")
            print("   3. 点击 从存储设备安装")
            print("   4. 选择 mitmproxy-ca-cert.pem")
            print("   5. 设置证书名称并选择用途")
            
    except KeyboardInterrupt:
        print("\n⚠️  安装被用户中断")
    except Exception as e:
        print(f"\n❌ 安装异常: {e}")

if __name__ == "__main__":
    main()
