#!/usr/bin/env python3
"""
捕获结果组织器
按APP分类整理动态分析的所有捕获结果
"""

import os
import shutil
import json
import time
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional
import glob

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CaptureResultOrganizer:
    """捕获结果组织器"""
    
    def __init__(self, base_dir="/Users/<USER>/Desktop/project/apk_detect"):
        self.base_dir = Path(base_dir)
        self.capture_results_dir = self.base_dir / "capture_results"
        
        # 创建主目录结构
        self.capture_results_dir.mkdir(exist_ok=True)
        
    def get_app_folder(self, package_name: str) -> Path:
        """获取或创建应用专用文件夹"""
        app_folder = self.capture_results_dir / package_name
        app_folder.mkdir(exist_ok=True)
        
        # 创建子目录结构
        subdirs = [
            "analysis_reports",    # 分析报告
            "network_logs",       # 网络日志
            "screenshots",        # 截图
            "ui_dumps",          # UI dump文件
            "frida_logs",        # Frida日志
            "certificates",      # 证书文件
            "raw_data"          # 原始数据
        ]
        
        for subdir in subdirs:
            (app_folder / subdir).mkdir(exist_ok=True)
        
        return app_folder
    
    def organize_files_for_app(self, package_name: str, session_id: Optional[str] = None) -> Dict:
        """为指定APP整理文件"""
        logger.info(f"🗂️  整理 {package_name} 的捕获结果...")
        
        if not session_id:
            session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        app_folder = self.get_app_folder(package_name)
        session_folder = app_folder / f"session_{session_id}"
        session_folder.mkdir(exist_ok=True)
        
        # 创建会话子目录
        for subdir in ["analysis_reports", "network_logs", "screenshots", "ui_dumps", "frida_logs", "raw_data"]:
            (session_folder / subdir).mkdir(exist_ok=True)
        
        organized_files = {
            "package_name": package_name,
            "session_id": session_id,
            "session_folder": str(session_folder),
            "organized_time": datetime.now().isoformat(),
            "files": {
                "analysis_reports": [],
                "network_logs": [],
                "screenshots": [],
                "ui_dumps": [],
                "frida_logs": [],
                "certificates": [],
                "raw_data": []
            }
        }
        
        # 定义文件匹配模式
        file_patterns = {
            "analysis_reports": [
                "*analysis_report*.json",
                "*test_report*.json", 
                "*https_blocked_domains_report*.json",
                "*capture_report*.json",
                "*dynamic_analysis*.json"
            ],
            "network_logs": [
                "mitm_*.log",
                "*_capture*.log",
                "captured_https_urls.json",
                "*capture_stream.log"
            ],
            "screenshots": [
                "*screenshot*.png",
                "*real_analysis_screenshot*.png"
            ],
            "ui_dumps": [
                "*ui_dump*.xml",
                "ui_*.xml"
            ],
            "frida_logs": [
                "*ssl_bypass*.log",
                "*frida*.log"
            ],
            "raw_data": [
                "*.pcap",
                "*.har",
                "*raw*.json"
            ]
        }
        
        # 整理文件
        for category, patterns in file_patterns.items():
            for pattern in patterns:
                for file_path in self.base_dir.glob(pattern):
                    if file_path.is_file():
                        # 检查文件是否与当前包名相关
                        if self._is_file_related_to_app(file_path, package_name):
                            dest_file = session_folder / category / file_path.name
                            try:
                                shutil.copy2(file_path, dest_file)
                                organized_files["files"][category].append(str(dest_file))
                                logger.info(f"✅ 移动文件: {file_path.name} → {category}/")
                            except Exception as e:
                                logger.error(f"❌ 移动文件失败: {file_path.name}, 错误: {e}")
        
        # 生成会话元数据
        metadata = self._generate_session_metadata(package_name, session_id, organized_files)
        metadata_file = session_folder / "session_metadata.json"
        
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
        
        organized_files["metadata_file"] = str(metadata_file)
        
        return organized_files
    
    def _is_file_related_to_app(self, file_path: Path, package_name: str) -> bool:
        """检查文件是否与指定APP相关"""
        file_content_indicators = [
            package_name in file_path.name,  # 文件名包含包名
        ]
        
        # 对于某些文件类型，检查文件内容
        if file_path.suffix == '.json':
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if package_name in content:
                        return True
            except:
                pass
        
        # 检查文件修改时间（最近1小时内的文件认为相关）
        recent_threshold = time.time() - 3600  # 1小时
        if file_path.stat().st_mtime > recent_threshold:
            return True
        
        return any(file_content_indicators)
    
    def _generate_session_metadata(self, package_name: str, session_id: str, organized_files: Dict) -> Dict:
        """生成会话元数据"""
        metadata = {
            "session_info": {
                "package_name": package_name,
                "session_id": session_id,
                "start_time": organized_files["organized_time"],
                "analysis_type": "dynamic_analysis"
            },
            "file_summary": {},
            "analysis_summary": {
                "total_files": 0,
                "file_types": {},
                "key_findings": []
            },
            "environment_info": {
                "android_device": "emulator-5554",
                "analysis_tools": ["mitmproxy", "frida", "adb", "uiautomator"],
                "capture_duration": "unknown"
            }
        }
        
        # 统计文件信息
        for category, files in organized_files["files"].items():
            file_count = len(files)
            metadata["file_summary"][category] = file_count
            metadata["analysis_summary"]["total_files"] += file_count
            
            if file_count > 0:
                metadata["analysis_summary"]["file_types"][category] = file_count
        
        # 分析关键发现
        findings = []
        if metadata["file_summary"].get("network_logs", 0) > 0:
            findings.append("网络流量已捕获")
        if metadata["file_summary"].get("screenshots", 0) > 0:
            findings.append("UI截图已保存")
        if metadata["file_summary"].get("analysis_reports", 0) > 0:
            findings.append("分析报告已生成")
        if metadata["file_summary"].get("frida_logs", 0) > 0:
            findings.append("SSL绕过已尝试")
        
        metadata["analysis_summary"]["key_findings"] = findings
        
        return metadata
    
    def create_app_index(self) -> Dict:
        """创建应用索引"""
        logger.info("📋 创建应用分析索引...")
        
        index = {
            "created_time": datetime.now().isoformat(),
            "total_apps": 0,
            "apps": {},
            "recent_sessions": []
        }
        
        # 扫描所有应用文件夹
        for app_folder in self.capture_results_dir.iterdir():
            if app_folder.is_dir() and not app_folder.name.startswith('.'):
                package_name = app_folder.name
                app_info = {
                    "package_name": package_name,
                    "folder_path": str(app_folder),
                    "sessions": [],
                    "total_sessions": 0,
                    "last_analysis": None
                }
                
                # 扫描会话
                session_pattern = app_folder / "session_*"
                for session_folder in app_folder.glob("session_*"):
                    if session_folder.is_dir():
                        session_id = session_folder.name.replace("session_", "")
                        
                        # 读取会话元数据
                        metadata_file = session_folder / "session_metadata.json"
                        if metadata_file.exists():
                            try:
                                with open(metadata_file, 'r', encoding='utf-8') as f:
                                    session_metadata = json.load(f)
                                
                                session_info = {
                                    "session_id": session_id,
                                    "start_time": session_metadata.get("session_info", {}).get("start_time"),
                                    "total_files": session_metadata.get("analysis_summary", {}).get("total_files", 0),
                                    "key_findings": session_metadata.get("analysis_summary", {}).get("key_findings", [])
                                }
                                
                                app_info["sessions"].append(session_info)
                                
                                # 更新最近会话
                                index["recent_sessions"].append({
                                    "package_name": package_name,
                                    "session_id": session_id,
                                    "start_time": session_info["start_time"]
                                })
                                
                            except Exception as e:
                                logger.error(f"读取会话元数据失败: {metadata_file}, 错误: {e}")
                
                app_info["total_sessions"] = len(app_info["sessions"])
                if app_info["sessions"]:
                    # 按时间排序，获取最新会话
                    app_info["sessions"].sort(key=lambda x: x["start_time"], reverse=True)
                    app_info["last_analysis"] = app_info["sessions"][0]["start_time"]
                
                index["apps"][package_name] = app_info
        
        index["total_apps"] = len(index["apps"])
        
        # 对最近会话按时间排序
        index["recent_sessions"].sort(key=lambda x: x["start_time"], reverse=True)
        index["recent_sessions"] = index["recent_sessions"][:10]  # 保留最近10个
        
        # 保存索引
        index_file = self.capture_results_dir / "apps_index.json"
        with open(index_file, 'w', encoding='utf-8') as f:
            json.dump(index, f, indent=2, ensure_ascii=False)
        
        logger.info(f"✅ 应用索引已创建: {index_file}")
        return index
    
    def generate_summary_report(self) -> str:
        """生成总结报告"""
        index = self.create_app_index()
        
        report_lines = [
            "# 📊 APK动态分析捕获结果总览",
            "",
            f"**生成时间**: {index['created_time']}",
            f"**分析应用总数**: {index['total_apps']}个",
            "",
            "## 📱 分析的应用列表",
            ""
        ]
        
        for package_name, app_info in index["apps"].items():
            report_lines.extend([
                f"### {package_name}",
                f"- **会话数量**: {app_info['total_sessions']}个",
                f"- **最后分析**: {app_info['last_analysis'] or '未知'}",
                f"- **文件夹路径**: `{app_info['folder_path']}`",
                ""
            ])
            
            if app_info["sessions"]:
                report_lines.append("**最近会话**:")
                for session in app_info["sessions"][:3]:  # 显示最近3个会话
                    findings = ", ".join(session["key_findings"]) if session["key_findings"] else "无特殊发现"
                    report_lines.append(f"- `{session['session_id']}`: {session['total_files']}个文件 - {findings}")
                report_lines.append("")
        
        if index["recent_sessions"]:
            report_lines.extend([
                "## 🕒 最近分析活动",
                ""
            ])
            
            for session in index["recent_sessions"][:5]:
                report_lines.append(f"- **{session['package_name']}** - {session['session_id']} ({session['start_time']})")
        
        report_lines.extend([
            "",
            "## 📂 目录结构说明",
            "",
            "```",
            "capture_results/",
            "├── {package_name}/",
            "│   ├── session_{timestamp}/",
            "│   │   ├── analysis_reports/    # 分析报告JSON文件",
            "│   │   ├── network_logs/        # 网络流量日志",
            "│   │   ├── screenshots/         # 应用截图", 
            "│   │   ├── ui_dumps/           # UI结构文件",
            "│   │   ├── frida_logs/         # Frida Hook日志",
            "│   │   ├── raw_data/           # 原始数据文件",
            "│   │   └── session_metadata.json # 会话元数据",
            "│   └── ...",
            "└── apps_index.json             # 应用索引文件",
            "```"
        ])
        
        report_content = "\n".join(report_lines)
        
        # 保存报告
        report_file = self.capture_results_dir / "CAPTURE_RESULTS_SUMMARY.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        logger.info(f"✅ 总结报告已生成: {report_file}")
        return report_content

def main():
    """主函数"""
    organizer = CaptureResultOrganizer()
    
    # 为当前分析的应用整理文件
    package_name = "com.iloda.beacon"  # 当前测试的应用
    
    logger.info("🗂️  开始整理捕获结果...")
    
    # 整理文件
    result = organizer.organize_files_for_app(package_name)
    
    logger.info(f"✅ 文件整理完成!")
    logger.info(f"📂 会话文件夹: {result['session_folder']}")
    logger.info(f"📄 元数据文件: {result['metadata_file']}")
    
    # 统计整理结果
    total_files = sum(len(files) for files in result["files"].values())
    logger.info(f"📊 整理文件统计:")
    for category, files in result["files"].items():
        if files:
            logger.info(f"   - {category}: {len(files)} 个文件")
    
    logger.info(f"📦 总计整理: {total_files} 个文件")
    
    # 生成应用索引和总结报告
    organizer.generate_summary_report()
    
    logger.info("🎉 所有文件整理完成!")
    logger.info(f"📁 查看结果: {organizer.capture_results_dir}")
    
    return 0

if __name__ == "__main__":
    exit(main())


