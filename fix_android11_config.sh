#!/bin/bash

# 修复Android 11模拟器配置
AVD_PATH="$HOME/.android/avd/android11_working.avd"

echo "修改AVD配置以解决图形渲染问题..."

# 备份原配置
cp "$AVD_PATH/config.ini" "$AVD_PATH/config.ini.backup"

# 修改配置文件
cat > "$AVD_PATH/config.ini" << 'EOF'
PlayStore.enabled=false
abi.type=x86_64
avd.ini.displayname=android11_working
avd.ini.encoding=UTF-8
disk.dataPartition.size=6442450944
fastboot.chosenSnapshotFile=
fastboot.forceChosenSnapshotBoot=no
fastboot.forceColdBoot=no
fastboot.forceFastBoot=yes
hw.accelerometer=yes
hw.arc=false
hw.audioInput=yes
hw.audioOutput=yes
hw.battery=yes
hw.camera.back=virtualscene
hw.camera.front=emulated
hw.cpu.arch=x86_64
hw.cpu.ncore=4
hw.dPad=no
hw.device.hash2=MD5:55acbc835978f326788ed66a5cd4c9a7
hw.device.manufacturer=Google
hw.device.name=pixel_2
hw.gps=yes
hw.gpu.enabled=yes
hw.gpu.mode=swiftshader_indirect
hw.initialOrientation=Portrait
hw.keyboard=yes
hw.lcd.density=420
hw.lcd.height=1920
hw.lcd.width=1080
hw.mainKeys=no
hw.ramSize=2048
hw.sdCard=yes
hw.sensors.orientation=yes
hw.sensors.proximity=yes
hw.trackBall=no
image.sysdir.1=system-images/android-30/google_apis/x86_64/
runtime.network.latency=none
runtime.network.speed=full
tag.display=Google APIs
tag.id=google_apis
vm.heapSize=228
EOF

echo "配置已更新，强制使用软件渲染模式"
