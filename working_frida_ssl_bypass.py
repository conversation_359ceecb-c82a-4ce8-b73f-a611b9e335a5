#!/usr/bin/env python3
"""
可工作的Frida SSL绕过
使用命令行方式绕过system_server问题
"""

import subprocess
import sys
import time
import json
import signal
from pathlib import Path

class WorkingFridaSSLBypass:
    def __init__(self):
        self.package = "com.yjzx.yjzx2017"
        self.frida_process = None
        self.running = False
        
    def log(self, message, level="INFO"):
        """日志输出"""
        timestamp = time.strftime("%H:%M:%S")
        if level == "SUCCESS":
            print(f"🎉 [{timestamp}] {message}")
        elif level == "ERROR":
            print(f"❌ [{timestamp}] {message}")
        elif level == "WARNING":
            print(f"⚠️  [{timestamp}] {message}")
        else:
            print(f"📋 [{timestamp}] {message}")
    
    def run_adb(self, cmd, timeout=30):
        """执行adb命令"""
        full_cmd = f"source android_env.sh && adb {cmd}"
        try:
            result = subprocess.run(full_cmd, shell=True, 
                                  capture_output=True, text=True, 
                                  timeout=timeout, check=True)
            return result.returncode, result.stdout.strip(), result.stderr.strip()
        except subprocess.CalledProcessError as e:
            return e.returncode, "", e.stderr
        except subprocess.TimeoutExpired:
            return -1, "", "Timeout"
    
    def setup_environment(self):
        """设置环境"""
        self.log("设置Frida SSL绕过环境...")
        
        # 检查设备
        returncode, stdout, stderr = self.run_adb("devices")
        if "device" not in stdout:
            self.log("设备未连接", "ERROR")
            return False
        self.log("设备已连接")
        
        # 启动frida-server
        self.log("启动frida-server...")
        self.run_adb("shell pkill frida-server")
        time.sleep(2)
        
        subprocess.Popen("source android_env.sh && adb shell '/data/local/tmp/frida-server' 2>/dev/null &", shell=True)
        time.sleep(8)
        
        # 验证frida-server
        returncode, stdout, stderr = self.run_adb("shell ps | grep frida-server")
        if "frida-server" not in stdout:
            self.log("frida-server启动失败", "ERROR")
            return False
        self.log("frida-server启动成功")
        
        return True
    
    def prepare_app(self):
        """准备应用"""
        self.log("准备目标应用...")
        
        # 重启应用
        self.run_adb(f"shell am force-stop {self.package}")
        time.sleep(2)
        
        # 启动应用
        returncode, stdout, stderr = self.run_adb(f"shell am start -n {self.package}/.controller.activity.splash.SplashActivity")
        if returncode == 0:
            self.log("应用启动成功")
            time.sleep(8)
            return True
        else:
            self.log(f"应用启动失败: {stderr}", "ERROR")
            return False
    
    def start_frida_ssl_bypass(self):
        """启动Frida SSL绕过（命令行方式）"""
        self.log("启动Frida SSL绕过...")
        
        # 使用命令行方式启动Frida
        cmd = f"export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin && frida -U {self.package} -l ssl_bypass.js"
        
        try:
            # 启动Frida进程
            self.frida_process = subprocess.Popen(
                cmd, shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            self.log("Frida进程已启动")
            
            # 等待连接并读取初始输出
            time.sleep(15)
            
            if self.frida_process.poll() is None:
                self.log("Frida SSL绕过脚本运行中")
                
                # 读取一些输出来确认状态
                try:
                    # 非阻塞读取
                    import select
                    import os
                    
                    if hasattr(select, 'select'):
                        ready, _, _ = select.select([self.frida_process.stdout], [], [], 2)
                        if ready:
                            output = self.frida_process.stdout.read(1000)
                            if output:
                                self.log("Frida输出:")
                                for line in output.split('\n')[:10]:
                                    if line.strip():
                                        self.log(f"  {line.strip()}")
                except:
                    pass
                
                return True
            else:
                stdout, stderr = self.frida_process.communicate()
                self.log("Frida启动失败", "ERROR")
                if stderr and 'system_server' not in stderr:
                    self.log(f"错误: {stderr}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"Frida启动异常: {e}", "ERROR")
            return False
    
    def test_ssl_bypass(self):
        """测试SSL绕过"""
        self.log("测试SSL绕过效果...")
        
        # 清空捕获文件
        capture_file = Path("mitm-logs/realtime_capture.json")
        capture_file.parent.mkdir(exist_ok=True)
        
        with open(capture_file, 'w') as f:
            json.dump([], f)
        
        self.log("清空网络捕获文件")
        time.sleep(3)
        
        # 触发HTTPS请求
        test_urls = [
            "https://httpbin.org/get",
            "https://www.baidu.com"
        ]
        
        self.log("触发HTTPS测试请求...")
        for i, url in enumerate(test_urls, 1):
            self.log(f"测试 {i}/{len(test_urls)}: {url}")
            self.run_adb(f"shell am start -a android.intent.action.VIEW -d {url}")
            time.sleep(10)
        
        # 应用内操作
        self.log("应用内操作...")
        self.run_adb("shell input tap 540 960")
        time.sleep(5)
        
        # 等待网络请求处理
        self.log("等待网络请求处理...")
        time.sleep(20)
        
        return self.check_capture_results()
    
    def check_capture_results(self):
        """检查捕获结果"""
        self.log("检查网络捕获结果...")
        
        capture_file = Path("mitm-logs/realtime_capture.json")
        
        try:
            with open(capture_file, 'r') as f:
                data = json.load(f)
            
            if not isinstance(data, list):
                self.log("捕获文件格式错误", "ERROR")
                return False
            
            total = len(data)
            https_reqs = [req for req in data if req.get('scheme') == 'https']
            http_reqs = [req for req in data if req.get('scheme') == 'http']
            
            self.log(f"捕获统计: 总请求={total}, HTTPS={len(https_reqs)}, HTTP={len(http_reqs)}")
            
            if len(https_reqs) > 0:
                self.log("SSL绕过成功！成功捕获HTTPS请求！", "SUCCESS")
                
                self.log("HTTPS请求示例:")
                for i, req in enumerate(https_reqs[:5], 1):
                    url = req.get('url', 'N/A')
                    method = req.get('method', 'N/A')
                    status = req.get('status_code', 'N/A')
                    host = req.get('host', 'N/A')
                    
                    self.log(f"  {i}. {method} {url}")
                    self.log(f"     主机: {host}, 状态: {status}")
                
                return True
            elif len(http_reqs) > 0:
                self.log("只捕获到HTTP请求，SSL绕过可能未完全生效", "WARNING")
                self.log("但基础网络捕获功能正常")
                return False
            elif total > 0:
                self.log("捕获到请求但协议未识别", "WARNING")
                return False
            else:
                self.log("没有捕获到网络请求", "WARNING")
                self.log("可能的原因:")
                self.log("  • mitmproxy未运行")
                self.log("  • 代理设置问题")
                self.log("  • 应用网络活动较少")
                return False
                
        except Exception as e:
            self.log(f"检查结果失败: {e}", "ERROR")
            return False
    
    def start_monitoring(self):
        """开始监控模式"""
        self.log("开始SSL绕过监控模式...")
        self.running = True
        
        try:
            while self.running:
                time.sleep(30)
                
                # 检查Frida进程状态
                if self.frida_process and self.frida_process.poll() is None:
                    self.log("SSL绕过监控中...")
                else:
                    self.log("Frida进程已退出", "WARNING")
                    break
                    
        except KeyboardInterrupt:
            self.log("用户中断监控")
    
    def cleanup(self):
        """清理资源"""
        self.log("清理资源...")
        self.running = False
        
        # 终止Frida进程
        if self.frida_process and self.frida_process.poll() is None:
            self.log("终止Frida进程...")
            self.frida_process.terminate()
            try:
                self.frida_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.frida_process.kill()
        
        # 停止frida-server
        self.run_adb("shell pkill frida-server")
        self.log("清理完成")
    
    def run_complete_bypass(self):
        """运行完整的SSL绕过"""
        self.log("🚀 启动可工作的Frida SSL绕过系统")
        self.log("🔧 使用命令行方式绕过system_server问题")
        self.log("=" * 70)
        
        try:
            # 步骤1: 设置环境
            if not self.setup_environment():
                return False
            
            # 步骤2: 准备应用
            if not self.prepare_app():
                return False
            
            # 步骤3: 启动Frida SSL绕过
            if not self.start_frida_ssl_bypass():
                return False
            
            # 步骤4: 测试SSL绕过
            self.log("=" * 60)
            self.log("🔍 测试SSL绕过效果")
            self.log("=" * 60)
            
            success = self.test_ssl_bypass()
            
            if success:
                self.log("🎉 Frida SSL绕过完全成功！", "SUCCESS")
                self.log("✅ HTTPS流量现在可以被完全捕获和分析！")
                self.log("🔒 SSL证书验证已被成功绕过！")
                
                self.log("📋 系统现在具备完整功能:")
                self.log("   ✅ APK动态分析环境")
                self.log("   ✅ HTTPS网络流量透明捕获")
                self.log("   ✅ SSL证书验证绕过")
                self.log("   ✅ 实时网络监控")
                self.log("   ✅ API端点发现和分析")
                
                self.log("💡 可以用于:")
                self.log("   • 移动应用安全测试")
                self.log("   • HTTPS API逆向工程")
                self.log("   • 加密通信分析")
                self.log("   • 隐私数据泄露检测")
                self.log("   • 恶意软件行为分析")
                
                # 进入监控模式
                self.start_monitoring()
                return True
            else:
                self.log("SSL绕过测试未完全成功", "WARNING")
                self.log("但Frida脚本可能仍在工作")
                self.log("可以手动测试更多HTTPS请求")
                
                # 短时间监控
                try:
                    self.log("保持运行2分钟进行观察...")
                    time.sleep(120)
                except KeyboardInterrupt:
                    pass
                
                return False
                
        except KeyboardInterrupt:
            self.log("用户中断操作")
            return False
        except Exception as e:
            self.log(f"SSL绕过异常: {e}", "ERROR")
            return False
        finally:
            self.cleanup()

def main():
    bypass_system = WorkingFridaSSLBypass()
    
    def signal_handler(sig, frame):
        print("\n⚠️  接收到中断信号...")
        bypass_system.cleanup()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    
    success = bypass_system.run_complete_bypass()
    
    if success:
        print("\n🎯 Frida SSL绕过系统实现成功！")
        print("💡 APK动态分析系统现在完全可用！")
        print("🔧 HTTPS流量可以被完全捕获和分析！")
    else:
        print("\n🔧 SSL绕过需要进一步调试")
        print("💡 但基础Frida环境已建立")
        print("🔧 可以手动运行Frida命令进行测试")

if __name__ == "__main__":
    main()
