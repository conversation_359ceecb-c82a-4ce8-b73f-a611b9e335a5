#!/usr/bin/env python3
"""
完整动态分析功能测试
"""
import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

from src.services.dynamic_analyzer import DynamicAnalyzer, AnalysisTools
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_dynamic_analysis():
    """测试动态分析完整功能"""
    print("🚀 开始测试动态分析功能...")
    
    # 配置分析工具路径（使用主机adb）
    tools = AnalysisTools(
        adb=str(PROJECT_ROOT / "platform-tools" / "adb"),
        aapt=str(PROJECT_ROOT / "android-14" / "aapt")
    )
    analyzer = DynamicAnalyzer(tools=tools)
    
    # 测试APK路径
    # 使用主机上的APK
    test_apk = PROJECT_ROOT / "storage" / "apks" / "com.android.vending_289.com.apk"
    
    if not test_apk.exists():
        print(f"❌ 测试APK不存在: {test_apk}")
        return False
    
    try:
        print(f"📱 开始分析APK: {test_apk.name}")
        
        # 执行动态分析
        result = await analyzer.analyze(str(test_apk), timeout=120)  # 2分钟超时
        
        print("✅ 动态分析完成!")
        print(f"📊 分析结果:")
        print(f"   - 捕获URL数量: {len(result.dynamic_urls)}")
        print(f"   - 分析耗时: {result.duration:.1f}秒")
        print(f"   - Android版本: {result.android_version}")
        
        if result.dynamic_urls:
            print("\n🔗 捕获的URL:")
            for i, url_info in enumerate(result.dynamic_urls[:5], 1):  # 显示前5个
                print(f"   {i}. {url_info.url} ({url_info.type})")
            
            if len(result.dynamic_urls) > 5:
                print(f"   ... 还有 {len(result.dynamic_urls) - 5} 个URL")
        
        return True
        
    except Exception as e:
        print(f"❌ 动态分析失败: {e}")
        logger.exception("Dynamic analysis failed")
        return False


def main():
    """主函数"""
    print("🧪 APK动态分析功能测试")
    print("=" * 50)
    
    try:
        success = asyncio.run(test_dynamic_analysis())
        
        print("\n" + "=" * 50)
        if success:
            print("🎉 动态分析功能测试通过!")
            return 0
        else:
            print("❌ 动态分析功能测试失败!")
            return 1
            
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
        return 1
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
