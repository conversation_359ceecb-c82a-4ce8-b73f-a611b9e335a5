#!/usr/bin/env python3
"""
修复Android证书安装错误
解决"无法安装 CA 证书"问题
"""

import subprocess
import sys
import time
from pathlib import Path

class CertInstallFixer:
    def __init__(self):
        self.cert_file = "mitmproxy-ca-cert.pem"
        
    def run_adb(self, cmd, timeout=30):
        """执行adb命令"""
        full_cmd = f"source android_env.sh && adb {cmd}"
        try:
            result = subprocess.run(full_cmd, shell=True, 
                                  capture_output=True, text=True, 
                                  timeout=timeout, check=True)
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            return f"ERROR: {e.stderr}"
        except subprocess.TimeoutExpired:
            return "ERROR: Timeout"
    
    def download_certificate_properly(self):
        """正确下载证书"""
        print("🔄 重新下载mitmproxy证书...")
        
        # 删除旧证书
        if Path(self.cert_file).exists():
            Path(self.cert_file).unlink()
        
        # 方法1: 直接从mitmproxy获取
        print("   方法1: 从mitmproxy直接获取...")
        cmd1 = "curl -x 127.0.0.1:8080 http://mitm.it/cert/pem -o mitmproxy-ca-cert.pem"
        result1 = subprocess.run(cmd1, shell=True, capture_output=True, text=True)
        
        if result1.returncode == 0 and Path(self.cert_file).exists():
            print("✅ 方法1成功")
            return True
        
        # 方法2: 从mitm.it获取
        print("   方法2: 从mitm.it获取...")
        cmd2 = "curl http://mitm.it/cert/pem -o mitmproxy-ca-cert.pem"
        result2 = subprocess.run(cmd2, shell=True, capture_output=True, text=True)
        
        if result2.returncode == 0 and Path(self.cert_file).exists():
            print("✅ 方法2成功")
            return True
        
        print("❌ 证书下载失败")
        return False
    
    def convert_certificate_format(self):
        """转换证书格式"""
        print("🔄 转换证书格式...")
        
        if not Path(self.cert_file).exists():
            print("❌ 证书文件不存在")
            return False
        
        # 检查证书内容
        with open(self.cert_file, 'r') as f:
            content = f.read()
        
        print(f"📋 证书内容长度: {len(content)} 字符")
        
        if "BEGIN CERTIFICATE" not in content:
            print("❌ 证书格式不正确")
            return False
        
        # 创建DER格式证书（某些Android版本需要）
        print("🔄 创建DER格式证书...")
        cmd = f"openssl x509 -outform der -in {self.cert_file} -out mitmproxy-ca-cert.crt"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ DER格式证书创建成功")
            return True
        else:
            print("⚠️  DER格式转换失败，使用原始PEM格式")
            return True
    
    def push_certificates_to_device(self):
        """推送证书到设备的多个位置"""
        print("📱 推送证书到设备...")
        
        # 推送到多个位置
        locations = [
            "/sdcard/Download/",
            "/sdcard/",
            "/storage/emulated/0/Download/",
            "/storage/emulated/0/"
        ]
        
        success_count = 0
        
        for location in locations:
            # 推送PEM格式
            result1 = self.run_adb(f"push {self.cert_file} {location}{self.cert_file}")
            if "ERROR" not in result1:
                print(f"✅ PEM证书推送到 {location}")
                success_count += 1
            
            # 推送CRT格式（如果存在）
            if Path("mitmproxy-ca-cert.crt").exists():
                result2 = self.run_adb(f"push mitmproxy-ca-cert.crt {location}mitmproxy-ca-cert.crt")
                if "ERROR" not in result2:
                    print(f"✅ CRT证书推送到 {location}")
        
        return success_count > 0
    
    def install_via_settings_intent(self):
        """通过设置Intent安装证书"""
        print("🔧 通过设置Intent安装证书...")
        
        # 方法1: 直接打开证书文件
        print("   方法1: 直接打开证书文件...")
        self.run_adb("shell am start -a android.intent.action.VIEW -t application/x-x509-ca-cert -d file:///sdcard/Download/mitmproxy-ca-cert.pem")
        time.sleep(3)
        
        # 方法2: 打开安全设置
        print("   方法2: 打开安全设置...")
        self.run_adb("shell am start -a android.settings.SECURITY_SETTINGS")
        time.sleep(2)
        
        # 方法3: 打开证书管理
        print("   方法3: 打开证书管理...")
        self.run_adb("shell am start -a android.credentials.INSTALL")
        time.sleep(2)
        
        return True
    
    def install_via_keychain(self):
        """通过Android Keychain安装"""
        print("🔧 通过Android Keychain安装...")
        
        # 使用keychain命令安装
        result = self.run_adb("shell keychain -i /sdcard/Download/mitmproxy-ca-cert.pem")
        if "ERROR" not in result:
            print("✅ Keychain安装成功")
            return True
        else:
            print("⚠️  Keychain安装失败")
            return False
    
    def manual_installation_guide(self):
        """手动安装指南"""
        print("\n" + "="*60)
        print("📋 手动安装证书详细步骤")
        print("="*60)
        
        print("🔧 解决'无法安装 CA 证书'错误的方法:")
        print()
        
        print("方法1: 通过文件管理器安装")
        print("   1. 打开文件管理器")
        print("   2. 导航到 Download 文件夹")
        print("   3. 找到 mitmproxy-ca-cert.pem 文件")
        print("   4. 点击文件，选择'安装'")
        print("   5. 如果提示选择应用，选择'证书安装器'")
        print()
        
        print("方法2: 通过设置安装")
        print("   1. 打开 设置 -> 安全")
        print("   2. 找到 '加密与凭据' 或 'Encryption & credentials'")
        print("   3. 点击 '从存储设备安装' 或 'Install from storage'")
        print("   4. 浏览到 Download 文件夹")
        print("   5. 选择 mitmproxy-ca-cert.pem")
        print("   6. 输入证书名称: mitmproxy")
        print("   7. 选择用途: VPN和应用")
        print()
        
        print("方法3: 重命名文件")
        print("   1. 将文件重命名为 mitmproxy.crt")
        print("   2. 再次尝试安装")
        print()
        
        print("⚠️  注意事项:")
        print("   - 确保设备已设置屏幕锁定（PIN/密码/图案）")
        print("   - 某些设备可能需要重启后才能生效")
        print("   - 如果仍然失败，可能需要root权限")
    
    def rename_certificate_file(self):
        """重命名证书文件"""
        print("🔄 重命名证书文件...")
        
        # 在设备上重命名文件
        self.run_adb("shell mv /sdcard/Download/mitmproxy-ca-cert.pem /sdcard/Download/mitmproxy.crt")
        self.run_adb("shell mv /sdcard/mitmproxy-ca-cert.pem /sdcard/mitmproxy.crt")
        
        print("✅ 证书文件已重命名为 mitmproxy.crt")
        
        # 再次尝试安装
        print("🔧 尝试安装重命名后的证书...")
        self.run_adb("shell am start -a android.intent.action.VIEW -t application/x-x509-ca-cert -d file:///sdcard/Download/mitmproxy.crt")
        
        return True
    
    def check_screen_lock(self):
        """检查屏幕锁定设置"""
        print("🔍 检查屏幕锁定设置...")
        
        # 检查是否设置了屏幕锁定
        result = self.run_adb("shell settings get secure lockscreen.disabled")
        
        if "1" in result:
            print("⚠️  屏幕锁定已禁用")
            print("📋 Android要求设置屏幕锁定才能安装CA证书")
            print("🔧 请在设备上设置PIN、密码或图案锁定")
            
            # 打开屏幕锁定设置
            self.run_adb("shell am start -a android.settings.SECURITY_SETTINGS")
            return False
        else:
            print("✅ 屏幕锁定已设置")
            return True
    
    def run_fix(self):
        """运行完整的修复流程"""
        print("🚀 Android证书安装错误修复工具")
        print("🔧 解决'无法安装 CA 证书'问题")
        print("=" * 60)
        
        # 步骤1: 检查屏幕锁定
        print("\n步骤1: 检查屏幕锁定设置")
        print("-" * 40)
        if not self.check_screen_lock():
            print("❌ 请先设置屏幕锁定，然后重新运行此脚本")
            return False
        
        # 步骤2: 重新下载证书
        print("\n步骤2: 重新下载证书")
        print("-" * 40)
        if not self.download_certificate_properly():
            print("❌ 证书下载失败")
            return False
        
        # 步骤3: 转换证书格式
        print("\n步骤3: 转换证书格式")
        print("-" * 40)
        self.convert_certificate_format()
        
        # 步骤4: 推送证书到设备
        print("\n步骤4: 推送证书到设备")
        print("-" * 40)
        if not self.push_certificates_to_device():
            print("❌ 证书推送失败")
            return False
        
        # 步骤5: 重命名证书文件
        print("\n步骤5: 重命名证书文件")
        print("-" * 40)
        self.rename_certificate_file()
        
        # 步骤6: 尝试多种安装方法
        print("\n步骤6: 尝试安装证书")
        print("-" * 40)
        self.install_via_settings_intent()
        time.sleep(2)
        self.install_via_keychain()
        
        # 步骤7: 显示手动安装指南
        self.manual_installation_guide()
        
        print("\n🎯 修复完成！请按照手动安装指南完成证书安装。")
        return True

def main():
    fixer = CertInstallFixer()
    
    try:
        success = fixer.run_fix()
        
        if success:
            print("\n✅ 修复流程完成！")
            print("📋 请在Android设备上手动完成证书安装")
        else:
            print("\n❌ 修复失败，请检查设备设置")
            
    except KeyboardInterrupt:
        print("\n⚠️  修复被用户中断")
    except Exception as e:
        print(f"\n❌ 修复异常: {e}")

if __name__ == "__main__":
    main()
