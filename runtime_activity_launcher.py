#!/usr/bin/env python3
"""
运行时Activity启动器 - 绕过导出限制的多种方法
不需要修改系统框架，使用运行时技术
"""

import subprocess
import sys
import time
import json
from pathlib import Path

class RuntimeActivityLauncher:
    def __init__(self):
        self.adb_path = "adb"
        
    def run_adb(self, cmd, timeout=30):
        """执行adb命令"""
        full_cmd = f"{self.adb_path} {cmd}"
        try:
            result = subprocess.run(full_cmd, shell=True, 
                                  capture_output=True, text=True, 
                                  timeout=timeout, check=True)
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            print(f"ADB命令失败: {full_cmd}")
            print(f"错误: {e.stderr}")
            return None
        except subprocess.TimeoutExpired:
            print(f"ADB命令超时: {full_cmd}")
            return None
    
    def get_all_activities(self, package):
        """获取应用的所有Activity"""
        print(f"📱 分析应用: {package}")

        activities = []

        # 方法1: 使用aapt分析APK文件
        apk_files = list(Path("apk").glob(f"*{package.split('.')[-1]}*.apk"))
        if apk_files:
            apk_file = apk_files[0]
            print(f"🔍 分析APK文件: {apk_file}")

            # 使用aapt获取Activity列表
            cmd = f"source android_env.sh && $ANDROID_HOME/build-tools/*/aapt dump xmltree {apk_file} AndroidManifest.xml | grep -A 2 -B 2 'activity' | grep 'android:name'"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

            if result.returncode == 0:
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'android:name' in line and 'Raw:' in line:
                        # 提取Activity名称
                        try:
                            name_part = line.split('Raw: "')[1].split('"')[0]
                            if name_part.startswith('.'):
                                full_name = package + name_part
                            elif '.' in name_part and package.split('.')[0] in name_part:
                                full_name = name_part
                            else:
                                full_name = package + '.' + name_part

                            # 过滤掉非Activity的组件
                            if ('Activity' in full_name and
                                'Provider' not in full_name and
                                'Service' not in full_name and
                                'Receiver' not in full_name and
                                full_name not in activities):
                                activities.append(full_name)
                        except IndexError:
                            continue

        # 方法2: 使用dumpsys package
        result = self.run_adb(f"shell dumpsys package {package}")
        if result:
            lines = result.split('\n')
            for line in lines:
                line = line.strip()
                if 'Activity #' in line and package in line:
                    # 提取Activity完整名称
                    parts = line.split()
                    for part in parts:
                        if package in part and '/' in part:
                            activity_name = part.replace('/', '')
                            if activity_name not in activities:
                                activities.append(activity_name)

        # 清理和去重
        unique_activities = []
        for activity in activities:
            if activity and 'Provider' not in activity and 'Service' not in activity:
                if activity not in unique_activities:
                    unique_activities.append(activity)

        return unique_activities
    
    def method1_direct_launch(self, package, activity):
        """方法1: 直接启动（基础方法）"""
        print(f"🚀 方法1: 直接启动 {activity}")
        
        cmd = f"shell am start -n {package}/{activity}"
        result = self.run_adb(cmd)
        
        if result and "Error" not in result:
            print("✅ 直接启动成功")
            return True
        else:
            print("❌ 直接启动失败")
            return False
    
    def method2_intent_flags(self, package, activity):
        """方法2: 使用特殊Intent标志"""
        print(f"🚀 方法2: 使用Intent标志启动 {activity}")
        
        flags = [
            "--activity-clear-task --activity-new-task",
            "--activity-single-top",
            "--activity-clear-top",
            "--activity-multiple-task",
            "--activity-reset-task-if-needed"
        ]
        
        for flag in flags:
            cmd = f"shell am start {flag} -n {package}/{activity}"
            result = self.run_adb(cmd)
            
            if result and "Error" not in result and "Exception" not in result:
                print(f"✅ 使用标志 {flag} 启动成功")
                return True
        
        print("❌ Intent标志方法失败")
        return False
    
    def method3_broadcast_trigger(self, package, activity):
        """方法3: 通过广播触发Activity"""
        print(f"🚀 方法3: 广播触发 {activity}")
        
        # 发送自定义广播，可能触发Activity
        broadcasts = [
            "android.intent.action.MAIN",
            "android.intent.action.VIEW",
            "android.intent.action.SEND",
            "android.intent.action.BOOT_COMPLETED"
        ]
        
        for broadcast in broadcasts:
            cmd = f"shell am broadcast -a {broadcast} -n {package}/{activity}"
            result = self.run_adb(cmd)
            
            if result and "Broadcast completed" in result:
                print(f"✅ 广播 {broadcast} 发送成功")
                time.sleep(2)  # 等待可能的Activity启动
                return True
        
        print("❌ 广播触发方法失败")
        return False
    
    def method4_service_proxy(self, package, activity):
        """方法4: 通过Service代理启动"""
        print(f"🚀 方法4: Service代理启动 {activity}")
        
        # 尝试启动应用的Service，Service可能会启动Activity
        result = self.run_adb(f"shell dumpsys package {package}")
        if result:
            services = []
            for line in result.split('\n'):
                if 'Service #' in line and package in line:
                    parts = line.split()
                    for part in parts:
                        if package in part and '/' in part:
                            service = part.split('/')[-1]
                            services.append(service)
            
            for service in services[:3]:  # 只尝试前3个Service
                cmd = f"shell am startservice -n {package}/{service}"
                result = self.run_adb(cmd)
                if result and "Error" not in result:
                    print(f"✅ Service {service} 启动成功，可能触发Activity")
                    time.sleep(3)
                    return True
        
        print("❌ Service代理方法失败")
        return False
    
    def method5_monkey_trigger(self, package, activity):
        """方法5: 使用Monkey随机触发"""
        print(f"🚀 方法5: Monkey随机触发")
        
        # 先启动应用
        self.run_adb(f"shell monkey -p {package} -c android.intent.category.LAUNCHER 1")
        time.sleep(2)
        
        # 使用Monkey生成随机事件，可能触发目标Activity
        cmd = f"shell monkey -p {package} --throttle 100 -v 50"
        result = self.run_adb(cmd, timeout=15)
        
        if result:
            print("✅ Monkey事件生成完成，检查是否触发目标Activity")
            return True
        
        print("❌ Monkey触发方法失败")
        return False
    
    def method6_deeplink_injection(self, package, activity):
        """方法6: 深度链接注入"""
        print(f"🚀 方法6: 深度链接注入")
        
        # 尝试各种深度链接格式
        schemes = [
            f"{package.split('.')[-1]}://",
            f"http://{package}/",
            f"https://{package}/",
            f"content://{package}/",
            f"custom://{activity}"
        ]
        
        for scheme in schemes:
            cmd = f"shell am start -a android.intent.action.VIEW -d '{scheme}{activity}'"
            result = self.run_adb(cmd)
            
            if result and "Error" not in result:
                print(f"✅ 深度链接 {scheme} 成功")
                return True
        
        print("❌ 深度链接注入失败")
        return False
    
    def launch_activity_comprehensive(self, package, activity):
        """综合方法启动Activity"""
        print(f"\n🎯 尝试启动Activity: {package}/{activity}")
        print("=" * 60)
        
        methods = [
            self.method1_direct_launch,
            self.method2_intent_flags,
            self.method3_broadcast_trigger,
            self.method4_service_proxy,
            self.method5_monkey_trigger,
            self.method6_deeplink_injection
        ]
        
        for i, method in enumerate(methods, 1):
            print(f"\n📍 尝试方法 {i}/6:")
            try:
                if method(package, activity):
                    print(f"🎉 成功！Activity {activity} 已启动")
                    return True
            except Exception as e:
                print(f"❌ 方法 {i} 异常: {e}")
            
            time.sleep(1)  # 方法间间隔
        
        print(f"\n💔 所有方法都失败了，无法启动 {activity}")
        return False
    
    def launch_all_activities(self, package):
        """尝试启动应用的所有Activity"""
        print(f"\n🚀 开始全面Activity启动测试: {package}")
        print("=" * 80)
        
        activities = self.get_all_activities(package)
        if not activities:
            print("❌ 未找到任何Activity")
            return
        
        print(f"📋 找到 {len(activities)} 个Activity:")
        for i, activity in enumerate(activities, 1):
            print(f"  {i}. {activity}")
        
        successful = []
        failed = []
        
        for activity in activities:
            if self.launch_activity_comprehensive(package, activity):
                successful.append(activity)
            else:
                failed.append(activity)
            
            time.sleep(2)  # Activity间间隔
        
        print(f"\n📊 启动结果统计:")
        print(f"✅ 成功启动: {len(successful)}/{len(activities)}")
        print(f"❌ 启动失败: {len(failed)}/{len(activities)}")
        
        if successful:
            print(f"\n🎉 成功启动的Activity:")
            for activity in successful:
                print(f"  ✅ {activity}")
        
        if failed:
            print(f"\n💔 启动失败的Activity:")
            for activity in failed:
                print(f"  ❌ {activity}")

def main():
    if len(sys.argv) < 2:
        print("用法:")
        print("  启动单个Activity: python3 runtime_activity_launcher.py <package> <activity>")
        print("  启动所有Activity: python3 runtime_activity_launcher.py <package> --all")
        print("  列出所有Activity: python3 runtime_activity_launcher.py <package> --list")
        print("\n示例:")
        print("  python3 runtime_activity_launcher.py com.yjzx.yjzx2017 --all")
        print("  python3 runtime_activity_launcher.py com.yjzx.yjzx2017 .controller.login.activity.LoginActivity")
        sys.exit(1)
    
    launcher = RuntimeActivityLauncher()
    package = sys.argv[1]
    
    if len(sys.argv) == 2 or sys.argv[2] == "--all":
        launcher.launch_all_activities(package)
    elif sys.argv[2] == "--list":
        activities = launcher.get_all_activities(package)
        print(f"📱 {package} 的所有Activity:")
        for i, activity in enumerate(activities, 1):
            print(f"  {i}. {activity}")
    else:
        activity = sys.argv[2]
        launcher.launch_activity_comprehensive(package, activity)

if __name__ == "__main__":
    main()
