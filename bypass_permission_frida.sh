#!/bin/bash
# 绕过权限问题的Frida SSL绕过脚本

echo "🚀 启动绕过权限问题的Frida SSL绕过"
echo "=================================="

# 设置环境
source android_env.sh
export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin

# 方法1: 尝试root模式启动adb
echo "📋 尝试root模式..."
adb root
sleep 3

# 重新启动frida-server
echo "🔧 重新启动frida-server..."
adb shell pkill frida-server
sleep 2
adb shell '/data/local/tmp/frida-server &'
sleep 5

# 方法2: 使用spawn模式
echo "🔧 使用spawn模式启动Frida..."
echo "💡 这种模式可以绕过大部分权限问题"

frida -U -f com.yjzx.yjzx2017 --no-pause -l optimized_ssl_bypass.js

echo "✅ 如果看到SSL bypass initialization completed，说明成功！"
