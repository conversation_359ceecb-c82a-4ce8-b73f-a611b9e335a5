[15:36:53.340] Loading script mitm_url_capture.py
[15:36:53.342] HTTP(S) proxy listening at *:8888.
[15:37:07.978][127.0.0.1:57573] client connect
[15:37:07.989][127.0.0.1:57573] server connect youtubei.googleapis.com:443 (***********:443)
🔒 HTTPS: POST https://youtubei.googleapis.com/deviceregistration/v1/devices?key=AIzaSyA8eiZmM1FaDVjRy-df2KTyQ_vz_yYM39w&rawDeviceId=4c218ff6c5fee58
   ✅ 200 
127.0.0.1:57573: POST https://youtubei.googleapis.com/deviceregistration/v1/d… HTTP/2.0
     << HTTP/2.0 200 OK 230b
🔒 HTTPS: POST https://youtubei.googleapis.com/deviceregistration/v1/devices?key=AIzaSyA8eiZmM1FaDVjRy-df2KTyQ_vz_yYM39w&rawDeviceId=4c218ff6c5fee58
🔒 HTTPS: POST https://youtubei.googleapis.com/youtubei/v1/mobiledataplan/get_mobile_data_plan_api_config?key=AIzaSyA8eiZmM1FaDVjRy-df2KTyQ_vz_yYM39w
   🎯 重要API: /youtubei/v1/mobiledataplan/get_mobile_data_plan_api_config?key=AIzaSyA8eiZmM1FaDVjRy-df2KTyQ_vz_yYM39w
   ✅ 200 
127.0.0.1:57573: POST https://youtubei.googleapis.com/deviceregistration/v1/d… HTTP/2.0
     << HTTP/2.0 200 OK 228b
🔒 HTTPS: POST https://youtubei.googleapis.com/youtubei/v1/browse?key=AIzaSyA8eiZmM1FaDVjRy-df2KTyQ_vz_yYM39w
   ❌ 400 
127.0.0.1:57573: POST https://youtubei.googleapis.com/youtubei/v1/browse?key=… HTTP/2.0
     << HTTP/2.0 400 Bad Request 171b
127.0.0.1:57573: POST https://youtubei.googleapis.com/youtubei/v1/mobiledatap… HTTP/2.0
 << peer closed connection
[15:37:10.568][127.0.0.1:57573] client disconnect
[15:37:10.569][127.0.0.1:57573] server disconnect youtubei.googleapis.com:443 (***********:443)
[15:37:11.516][127.0.0.1:57583] client connect
[15:37:11.521][127.0.0.1:57583] server connect www.googleapis.com:443 (198.18.0.222:443)
🔒 HTTPS: POST https://www.googleapis.com/androidantiabuse/v1/x/create?alt=PROTO&key=AIzaSyBofcZsgLSS7BOnBjZPEkk4rYwzOIz-lTI
   ✅ 200 OK
127.0.0.1:57583: POST https://www.googleapis.com/androidantiabuse/v1/x/create…
              << 200 OK 285k
🔒 HTTPS: POST https://www.googleapis.com/androidantiabuse/v1/x/create?alt=PROTO&key=AIzaSyBofcZsgLSS7BOnBjZPEkk4rYwzOIz-lTI
[15:37:13.991][127.0.0.1:57587] client connect
[15:37:13.995][127.0.0.1:57587] server connect android.googleapis.com:443 (198.18.0.137:443)
   ✅ 200 OK
127.0.0.1:57583: POST https://www.googleapis.com/androidantiabuse/v1/x/create…
              << 200 OK 78.8k
🔒 HTTPS: POST https://www.googleapis.com/androidantiabuse/v1/x/create?alt=PROTO&key=AIzaSyBofcZsgLSS7BOnBjZPEkk4rYwzOIz-lTI
[15:37:15.178][127.0.0.1:57593] client connect
[15:37:15.179][127.0.0.1:57595] client connect
🌐 HTTP: GET http://connectivitycheck.gstatic.com/generate_204
[15:37:15.187][127.0.0.1:57593] server connect connectivitycheck.gstatic.com:80 (198.18.0.110:80)
[15:37:15.188][127.0.0.1:57595] server connect connectivitycheck.gstatic.com:443 (198.18.0.110:443)
   ✅ 204 No Content
127.0.0.1:57593: GET http://connectivitycheck.gstatic.com/generate_204
              << 204 No Content 0b
🔒 HTTPS: GET https://connectivitycheck.gstatic.com/generate_204
   ✅ 200 OK
127.0.0.1:57583: POST https://www.googleapis.com/androidantiabuse/v1/x/create…
              << 200 OK 79.2k
🔒 HTTPS: POST https://android.googleapis.com/checkin
   ✅ 204 No Content
127.0.0.1:57595: GET https://connectivitycheck.gstatic.com/generate_204
              << 204 No Content 0b
   ✅ 200 OK
127.0.0.1:57587: POST https://android.googleapis.com/checkin
              << 200 OK 39.1k
[15:37:18.344][127.0.0.1:57601] client connect
[15:37:18.349][127.0.0.1:57601] server connect ssl.google-analytics.com:443 (198.18.1.71:443)
🔒 HTTPS: POST https://ssl.google-analytics.com/batch
   ✅ 200 OK
127.0.0.1:57601: POST https://ssl.google-analytics.com/batch
              << 200 OK 35b
[15:37:18.937][127.0.0.1:57604] client connect
[15:37:18.944][127.0.0.1:57604] server connect www.gstatic.com:443 (198.18.0.138:443)
[15:37:18.960][127.0.0.1:57607] client connect
[15:37:18.969][127.0.0.1:57607] server connect www.gstatic.com:443 (198.18.0.138:443)
🔒 HTTPS: GET https://www.gstatic.com/android/config_update/08202014-metadata.txt
   ✅ 200 OK
127.0.0.1:57604: GET https://www.gstatic.com/android/config_update/08202014-…
              << 200 OK 384b
[15:37:19.230][127.0.0.1:57604] server disconnect www.gstatic.com:443 (198.18.0.138:443)
[15:37:19.231][127.0.0.1:57604] client disconnect
🔒 HTTPS: GET https://www.gstatic.com/android/config_update/07282025-sms-denylist-metadata.txt
   ✅ 200 OK
127.0.0.1:57607: GET https://www.gstatic.com/android/config_update/07282025-…
              << 200 OK 384b
[15:37:19.414][127.0.0.1:57607] server disconnect www.gstatic.com:443 (198.18.0.138:443)
[15:37:19.415][127.0.0.1:57607] client disconnect
[15:37:20.288][127.0.0.1:57611] client connect
[15:37:20.297][127.0.0.1:57611] server connect play.googleapis.com:443 (198.18.0.111:443)
🔒 HTTPS: POST https://play.googleapis.com/log/batch
[15:37:20.756][127.0.0.1:57614] client connect
[15:37:20.765][127.0.0.1:57614] server connect www.gstatic.com:443 (198.18.0.138:443)
[15:37:20.797][127.0.0.1:57583] client disconnect
[15:37:20.799][127.0.0.1:57583] server disconnect www.googleapis.com:443 (198.18.0.222:443)
   ✅ 200 OK
127.0.0.1:57611: POST https://play.googleapis.com/log/batch
              << 200 OK 102b
[15:37:20.971][127.0.0.1:57617] client connect
[15:37:20.985][127.0.0.1:57617] server connect dl.google.com:443 (198.18.1.24:443)
🔒 HTTPS: GET https://dl.google.com/vision/1/creditcard/params_expdate_c45e5b7.dat
🔒 HTTPS: POST https://play.googleapis.com/log/batch
🔒 HTTPS: GET https://www.gstatic.com/android/voicesearch/production_2018_04_18_16_50_26_e76d587f4278d32f483226ff066c1173fb8bfb3586abce6fa2fa4c86
   ✅ 200 OK
127.0.0.1:57617: GET https://dl.google.com/vision/1/creditcard/params_expdat…
              << 200 OK 130k
[15:37:21.599][127.0.0.1:57617] server disconnect dl.google.com:443 (198.18.1.24:443)
[15:37:21.601][127.0.0.1:57617] client disconnect
   ✅ 200 
127.0.0.1:57614: GET https://www.gstatic.com/android/voicesearch/production_… HTTP/2.0
     << HTTP/2.0 200 OK 7.2k
   ✅ 200 OK
127.0.0.1:57611: POST https://play.googleapis.com/log/batch
              << 200 OK 102b
[15:37:21.867][127.0.0.1:57621] client connect
[15:37:21.873][127.0.0.1:57621] server connect play.googleapis.com:443 (198.18.0.111:443)
[15:37:22.069][127.0.0.1:57625] client connect
[15:37:22.074][127.0.0.1:57625] server connect www.googleapis.com:443 (198.18.0.222:443)
🔒 HTTPS: POST https://play.googleapis.com/log/batch
🔒 HTTPS: POST https://www.googleapis.com/experimentsandconfigs/v1/getExperimentsAndConfigs?r=12&c=1
   ✅ 200 OK
127.0.0.1:57621: POST https://play.googleapis.com/log/batch
              << 200 OK 102b
   ✅ 200 OK
127.0.0.1:57625: POST https://www.googleapis.com/experimentsandconfigs/v1/get…
              << 200 OK 529k
[15:37:32.583][127.0.0.1:57646] client connect
[15:37:32.591][127.0.0.1:57646] server connect android.apis.google.com:443 (198.18.1.23:443)
🔒 HTTPS: POST https://android.apis.google.com/c2dm/register3
   ❌ 400 Bad Request
127.0.0.1:57646: POST https://android.apis.google.com/c2dm/register3
              << 400 Bad Request 39b
[15:37:33.171][127.0.0.1:57652] client connect
[15:37:33.176][127.0.0.1:57652] server connect android.googleapis.com:443 (198.18.0.137:443)
🔒 HTTPS: POST https://android.googleapis.com/auth/devicekey
   ❌ 400 Bad Request
127.0.0.1:57652: POST https://android.googleapis.com/auth/devicekey
              << 400 Bad Request 801b
[15:37:37.739][127.0.0.1:57614] client disconnect
[15:37:37.741][127.0.0.1:57614] server disconnect www.gstatic.com:443 (198.18.0.138:443)
🔒 HTTPS: POST https://android.apis.google.com/c2dm/register3
   ✅ 200 OK
127.0.0.1:57646: POST https://android.apis.google.com/c2dm/register3
              << 200 OK 152b
[15:37:56.960][127.0.0.1:57688] client connect
[15:37:56.967][127.0.0.1:57688] server connect digitalassetlinks.googleapis.com:443 (198.18.0.140:443)
🔒 HTTPS: POST https://android.apis.google.com/c2dm/register3
   ❌ 301 Moved Permanently
127.0.0.1:57646: POST https://android.apis.google.com/c2dm/register3
              << 301 Moved Permanently 45b
[15:38:40.576][127.0.0.1:57742] client connect
[15:38:40.581][127.0.0.1:57742] server connect www.googleapis.com:443 (198.18.0.222:443)
🔒 HTTPS: POST https://android.apis.google.com/c2dm/register3
   ❌ 400 Bad Request
127.0.0.1:57646: POST https://android.apis.google.com/c2dm/register3
              << 400 Bad Request 39b
[15:38:40.825][127.0.0.1:57745] client connect
🔒 HTTPS: POST https://www.googleapis.com/experimentsandconfigs/v1/getExperimentsAndConfigs?r=6&c=1
[15:38:40.836][127.0.0.1:57745] server connect android.googleapis.com:443 (198.18.0.137:443)
🔒 HTTPS: POST https://android.googleapis.com/auth/devicekey
   ✅ 200 OK
127.0.0.1:57742: POST https://www.googleapis.com/experimentsandconfigs/v1/get…
              << 200 OK 4.7k
[15:38:41.209][127.0.0.1:57611] client disconnect
[15:38:41.210][127.0.0.1:57611] server disconnect play.googleapis.com:443 (198.18.0.111:443)
   ❌ 400 Bad Request
127.0.0.1:57745: POST https://android.googleapis.com/auth/devicekey
              << 400 Bad Request 801b
[15:38:41.225][127.0.0.1:57621] client disconnect
[15:38:41.228][127.0.0.1:57621] server disconnect play.googleapis.com:443 (198.18.0.111:443)
🔒 HTTPS: POST https://android.apis.google.com/c2dm/register3
   ❌ 429 unknown
127.0.0.1:57646: POST https://android.apis.google.com/c2dm/register3
              << 429 unknown 40b
🔒 HTTPS: POST https://android.apis.google.com/c2dm/register3
   ❌ 400 Bad Request
127.0.0.1:57646: POST https://android.apis.google.com/c2dm/register3
              << 400 Bad Request 39b
[15:40:20.967][127.0.0.1:57933] client connect
[15:40:20.987][127.0.0.1:57933] server connect android.googleapis.com:443 (198.18.0.137:443)
🔒 HTTPS: POST https://android.googleapis.com/auth/devicekey
   ❌ 400 Bad Request
127.0.0.1:57933: POST https://android.googleapis.com/auth/devicekey
              << 400 Bad Request 801b
[15:40:21.383][127.0.0.1:57625] client disconnect
[15:40:21.385][127.0.0.1:57625] server disconnect www.googleapis.com:443 (198.18.0.222:443)
🔒 HTTPS: POST https://android.apis.google.com/c2dm/register3
   ✅ 200 OK
127.0.0.1:57646: POST https://android.apis.google.com/c2dm/register3
              << 200 OK 61b
[15:41:15.630][127.0.0.1:57593] server disconnect connectivitycheck.gstatic.com:80 (198.18.0.110:80)
[15:41:16.996][127.0.0.1:57595] server disconnect connectivitycheck.gstatic.com:443 (198.18.0.110:443)
[15:41:17.096][127.0.0.1:57587] server disconnect android.googleapis.com:443 (198.18.0.137:443)
[15:41:18.856][127.0.0.1:57601] server disconnect ssl.google-analytics.com:443 (198.18.1.71:443)
[15:41:33.700][127.0.0.1:57652] server disconnect android.googleapis.com:443 (198.18.0.137:443)
[15:41:57.274][127.0.0.1:57688] 198.18.0.140:443: HTTP/2 protocol error: Invalid input ConnectionInputs.RECV_PING in state ConnectionState.CLOSED
[15:41:57.275][127.0.0.1:57688] server disconnect digitalassetlinks.googleapis.com:443 (198.18.0.140:443)
[15:42:15.770][127.0.0.1:57593] client disconnect
[15:42:17.027][127.0.0.1:57595] client disconnect
[15:42:17.377][127.0.0.1:57587] client disconnect
[15:42:18.682][127.0.0.1:57601] client disconnect
[15:42:33.648][127.0.0.1:57652] client disconnect
[15:42:41.193][127.0.0.1:57742] server disconnect www.googleapis.com:443 (198.18.0.222:443)
[15:42:41.215][127.0.0.1:57745] server disconnect android.googleapis.com:443 (198.18.0.137:443)
🔒 HTTPS: POST https://android.apis.google.com/c2dm/register3
   ❌ 400 Bad Request
127.0.0.1:57646: POST https://android.apis.google.com/c2dm/register3
              << 400 Bad Request 39b
🔒 HTTPS: POST https://android.googleapis.com/auth/devicekey
   ❌ 400 Bad Request
127.0.0.1:57933: POST https://android.googleapis.com/auth/devicekey
              << 400 Bad Request 801b
[15:43:41.214][127.0.0.1:57742] client disconnect
[15:43:41.227][127.0.0.1:57745] client disconnect
[15:47:00.866][127.0.0.1:57646] server disconnect android.apis.google.com:443 (198.18.1.23:443)
[15:47:00.946][127.0.0.1:57933] server disconnect android.googleapis.com:443 (198.18.0.137:443)
[15:48:00.841][127.0.0.1:57646] client disconnect
[15:48:00.937][127.0.0.1:57933] client disconnect
[15:48:47.941][127.0.0.1:58847] client connect
[15:48:47.954][127.0.0.1:58847] server connect android.apis.google.com:443 (198.18.1.23:443)
🔒 HTTPS: POST https://android.apis.google.com/c2dm/register3
   ❌ 400 Bad Request
127.0.0.1:58847: POST https://android.apis.google.com/c2dm/register3
              << 400 Bad Request 39b
[15:48:48.762][127.0.0.1:58850] client connect
[15:48:48.769][127.0.0.1:58850] server connect android.googleapis.com:443 (198.18.0.137:443)
🔒 HTTPS: POST https://android.googleapis.com/auth/devicekey
   ❌ 400 Bad Request
127.0.0.1:58850: POST https://android.googleapis.com/auth/devicekey
              << 400 Bad Request 801b
[15:52:47.273][127.0.0.1:57688] Closing connection due to inactivity: Client(127.0.0.1:57688, state=open, alpn=h2)
[15:52:47.281][127.0.0.1:57688] client disconnect
[15:52:48.752][127.0.0.1:58847] server disconnect android.apis.google.com:443 (198.18.1.23:443)
[15:52:49.626][127.0.0.1:58850] server disconnect android.googleapis.com:443 (198.18.0.137:443)
[15:53:48.755][127.0.0.1:58847] client disconnect
[15:53:49.622][127.0.0.1:58850] client disconnect

📊 捕获完成!
总请求数: 34
HTTPS请求: 33
唯一主机: 9
数据已保存到: captured_https_urls.json
