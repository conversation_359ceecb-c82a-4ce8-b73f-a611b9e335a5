{"start_time": "2025-09-05T13:47:06.595266", "package": "com.yjzx.yjzx2017", "method": "broadcast_trigger", "activities_tested": [{"name": "com.yjzx.yjzx2017.controller.activity.MainActivity", "index": 1, "method": "broadcast_trigger", "start_time": "2025-09-05T13:47:06.595635", "launch_success": true, "network_before": 0, "network_after": 0, "network_increase": 0, "error_message": null, "captured_requests": [], "new_hosts": [], "end_time": "2025-09-05T13:47:24.497868"}, {"name": "com.yjzx.yjzx2017.controller.login.activity.LoginActivity", "index": 2, "method": "broadcast_trigger", "start_time": "2025-09-05T13:47:26.502835", "launch_success": true, "network_before": 0, "network_after": 0, "network_increase": 0, "error_message": null, "captured_requests": [], "new_hosts": [], "end_time": "2025-09-05T13:47:45.093095"}, {"name": "com.yjzx.yjzx2017.controller.login.activity.RegistActivity", "index": 3, "method": "broadcast_trigger", "start_time": "2025-09-05T13:47:47.098081", "launch_success": true, "network_before": 0, "network_after": 0, "network_increase": 0, "error_message": null, "captured_requests": [], "new_hosts": [], "end_time": "2025-09-05T13:48:05.646601"}, {"name": "com.yjzx.yjzx2017.controller.activity.setting.SettingActivity", "index": 4, "method": "broadcast_trigger", "start_time": "2025-09-05T13:48:07.649687", "launch_success": true, "network_before": 0, "network_after": 0, "network_increase": 0, "error_message": null, "captured_requests": [], "new_hosts": [], "end_time": "2025-09-05T13:48:26.037052"}, {"name": "com.yjzx.yjzx2017.controller.camera.CameraNewActivity", "index": 5, "method": "broadcast_trigger", "start_time": "2025-09-05T13:48:28.042221", "launch_success": true, "network_before": 0, "network_after": 0, "network_increase": 0, "error_message": null, "captured_requests": [], "new_hosts": [], "end_time": "2025-09-05T13:48:46.717735"}, {"name": "com.yjzx.yjzx2017.controller.activity.auction.AuctionSelfBuyActivity", "index": 6, "method": "broadcast_trigger", "start_time": "2025-09-05T13:48:48.720175", "launch_success": true, "network_before": 0, "network_after": 0, "network_increase": 0, "error_message": null, "captured_requests": [], "new_hosts": [], "end_time": "2025-09-05T13:49:08.775152"}, {"name": "com.yjzx.yjzx2017.controller.selfAuction.SelfAuctionAddActivity", "index": 7, "method": "broadcast_trigger", "start_time": "2025-09-05T13:49:10.777690", "launch_success": true, "network_before": 0, "network_after": 0, "network_increase": 0, "error_message": null, "captured_requests": [], "new_hosts": [], "end_time": "2025-09-05T13:49:29.518679"}, {"name": "com.yjzx.yjzx2017.controller.login.activity.ForgotPasswordActivity", "index": 8, "method": "broadcast_trigger", "start_time": "2025-09-05T13:49:31.523566", "launch_success": true, "network_before": 0, "network_after": 0, "network_increase": 0, "error_message": null, "captured_requests": [], "new_hosts": [], "end_time": "2025-09-05T13:49:50.619330"}, {"name": "com.yjzx.yjzx2017.controller.activity.setting.AddressCompanyActivity", "index": 9, "method": "broadcast_trigger", "start_time": "2025-09-05T13:49:52.622952", "launch_success": true, "network_before": 0, "network_after": 0, "network_increase": 0, "error_message": null, "captured_requests": [], "new_hosts": [], "end_time": "2025-09-05T13:50:11.210828"}, {"name": "com.yjzx.yjzx2017.controller.selfAuction.SelfAuctionManageActivity", "index": 10, "method": "broadcast_trigger", "start_time": "2025-09-05T13:50:13.214386", "launch_success": true, "network_before": 0, "network_after": 0, "network_increase": 0, "error_message": null, "captured_requests": [], "new_hosts": [], "end_time": "2025-09-05T13:50:31.123223"}], "network_captures": [], "summary": {"total_tested": 10, "successful_launches": 10, "failed_launches": 0, "network_requests_captured": 0, "unique_hosts": [], "request_methods": {}, "status_codes": {}}, "end_time": "2025-09-05T13:50:33.125682", "total_network_increase": 0}