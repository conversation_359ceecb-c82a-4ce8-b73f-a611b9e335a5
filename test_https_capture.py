#!/usr/bin/env python3
"""
专门测试HTTPS捕获
验证SSL绕过是否真正工作
"""

import subprocess
import sys
import time
import json
from pathlib import Path

def log(message, level="INFO"):
    """日志输出"""
    timestamp = time.strftime("%H:%M:%S")
    if level == "SUCCESS":
        print(f"🎉 [{timestamp}] {message}")
    elif level == "ERROR":
        print(f"❌ [{timestamp}] {message}")
    elif level == "WARNING":
        print(f"⚠️  [{timestamp}] {message}")
    else:
        print(f"📋 [{timestamp}] {message}")

def run_adb(cmd, timeout=30):
    """执行adb命令"""
    full_cmd = f"source android_env.sh && adb {cmd}"
    try:
        result = subprocess.run(full_cmd, shell=True, 
                              capture_output=True, text=True, 
                              timeout=timeout, check=True)
        return result.returncode, result.stdout.strip(), result.stderr.strip()
    except subprocess.CalledProcessError as e:
        return e.returncode, "", e.stderr
    except subprocess.TimeoutExpired:
        return -1, "", "Timeout"

def setup_https_test_environment():
    """设置HTTPS测试环境"""
    log("设置HTTPS测试环境...")
    
    # 清理环境
    subprocess.run("pkill -f mitmdump", shell=True, capture_output=True)
    run_adb("shell pkill frida-server")
    run_adb("shell settings delete global http_proxy")
    time.sleep(3)
    
    # 启动mitmproxy
    log("启动mitmproxy...")
    Path("mitm-logs").mkdir(exist_ok=True)
    
    capture_file = Path("mitm-logs/realtime_capture.json")
    with open(capture_file, 'w') as f:
        json.dump([], f)
    
    # 使用更详细的mitmproxy配置
    mitm_cmd = "/Users/<USER>/Library/Python/3.9/bin/mitmdump -s mitm-scripts/capture.py --listen-port 8080 --set confdir=./mitm-config --ssl-insecure"
    
    mitmproxy_process = subprocess.Popen(
        mitm_cmd, shell=True,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    
    time.sleep(15)
    
    if mitmproxy_process.poll() is None:
        log("✅ mitmproxy启动成功", "SUCCESS")
    else:
        log("❌ mitmproxy启动失败", "ERROR")
        return None
    
    # 设置代理
    log("设置Android代理...")
    host_ip = "********"
    proxy_setting = f"{host_ip}:8080"
    
    returncode, stdout, stderr = run_adb(f"shell settings put global http_proxy {proxy_setting}")
    if returncode == 0:
        log(f"✅ 代理设置成功: {proxy_setting}", "SUCCESS")
    else:
        log(f"代理设置失败: {stderr}", "WARNING")
    
    return mitmproxy_process

def install_ca_certificate():
    """安装CA证书到Android系统"""
    log("安装CA证书到Android系统...")
    
    # 检查是否有mitmproxy CA证书
    ca_cert_path = Path("mitm-config/mitmproxy-ca-cert.pem")
    if not ca_cert_path.exists():
        log("CA证书不存在，生成新证书...", "WARNING")
        # mitmproxy会自动生成证书
        time.sleep(5)
    
    if ca_cert_path.exists():
        log("找到CA证书，推送到Android...")
        
        # 推送证书到Android
        run_adb("push mitm-config/mitmproxy-ca-cert.pem /sdcard/mitmproxy-ca-cert.pem")
        
        # 尝试安装证书（需要用户手动确认）
        log("⚠️  需要手动安装CA证书", "WARNING")
        log("   1. 在Android设置中找到'安全'->'加密与凭据'->'从存储设备安装'")
        log("   2. 选择 /sdcard/mitmproxy-ca-cert.pem")
        log("   3. 设置证书名称并确认安装")
        
        return True
    else:
        log("❌ CA证书生成失败", "ERROR")
        return False

def start_enhanced_ssl_bypass():
    """启动增强的SSL绕过"""
    log("启动增强的SSL绕过...")
    
    # 确保root权限
    subprocess.run("adb root", shell=True, capture_output=True)
    time.sleep(3)
    
    # 推送frida-server
    run_adb("push frida-server /data/local/tmp/frida-server")
    run_adb("shell chmod 755 /data/local/tmp/frida-server")
    run_adb("shell chown root:root /data/local/tmp/frida-server")
    
    # 启动frida-server
    subprocess.Popen("source android_env.sh && adb shell '/data/local/tmp/frida-server' &", shell=True)
    time.sleep(10)
    
    # 验证frida-server
    returncode, stdout, stderr = run_adb("shell ps | grep frida-server")
    if "frida-server" not in stdout:
        log("❌ frida-server启动失败", "ERROR")
        return None
    
    log("✅ frida-server启动成功", "SUCCESS")
    
    # 启动应用
    package = "com.yjzx.yjzx2017"
    run_adb(f"shell am force-stop {package}")
    time.sleep(3)
    
    returncode, stdout, stderr = run_adb(f"shell am start -n {package}/.controller.activity.splash.SplashActivity")
    if returncode != 0:
        log(f"应用启动失败: {stderr}", "ERROR")
        return None
    
    log("✅ 应用启动成功", "SUCCESS")
    time.sleep(10)
    
    # 获取PID
    returncode, stdout, stderr = run_adb(f"shell ps | grep {package}")
    if package not in stdout:
        log("应用进程未找到", "ERROR")
        return None
    
    # 提取PID
    lines = stdout.split('\n')
    target_pid = None
    for line in lines:
        if package in line and 'pushcore' not in line:
            parts = line.split()
            if len(parts) >= 2:
                target_pid = parts[1]
                break
    
    if not target_pid:
        # 使用任何找到的进程
        for line in lines:
            if package in line:
                parts = line.split()
                if len(parts) >= 2:
                    target_pid = parts[1]
                    break
    
    if not target_pid:
        log("无法获取PID", "ERROR")
        return None
    
    log(f"目标PID: {target_pid}")
    
    # 启动增强的SSL绕过
    ssl_cmd = f"export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin && frida -U {target_pid} -l interactive_ssl_bypass.js --no-pause"
    
    ssl_process = subprocess.Popen(
        ssl_cmd, shell=True,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    
    log("SSL绕过进程已启动...")
    time.sleep(25)
    
    if ssl_process.poll() is None:
        log("✅ SSL绕过正在运行", "SUCCESS")
    else:
        stdout, stderr = ssl_process.communicate()
        log("⚠️  SSL绕过可能有问题", "WARNING")
        if stdout:
            log(f"输出: {stdout[:200]}")
        if stderr:
            log(f"错误: {stderr[:200]}")
    
    return ssl_process

def test_https_requests_directly():
    """直接测试HTTPS请求"""
    log("🔍 直接测试HTTPS请求...")
    
    # 使用浏览器访问HTTPS网站
    https_urls = [
        "https://httpbin.org/get",
        "https://www.baidu.com",
        "https://api.github.com",
        "https://jsonplaceholder.typicode.com/posts/1",
        "https://www.google.com"
    ]
    
    for i, url in enumerate(https_urls, 1):
        log(f"测试HTTPS请求 {i}/{len(https_urls)}: {url}")
        
        # 使用浏览器访问
        run_adb(f"shell am start -a android.intent.action.VIEW -d {url}")
        time.sleep(10)
        
        # 检查是否有新的请求
        current_count = get_request_count()
        log(f"   当前总请求数: {current_count}")
    
    # 应用内操作
    log("执行应用内操作...")
    package = "com.yjzx.yjzx2017"
    
    # 强制触发网络操作
    operations = [
        ("重启应用", f"shell am force-stop {package} && sleep 3 && shell am start -n {package}/.controller.activity.splash.SplashActivity"),
        ("等待", "sleep 10"),
        ("多次刷新", "shell input swipe 540 400 540 800 && sleep 5 && shell input swipe 540 400 540 800 && sleep 5"),
        ("点击各种位置", "shell input tap 540 960 && sleep 3 && shell input tap 200 600 && sleep 3 && shell input tap 700 600"),
        ("等待", "sleep 8")
    ]
    
    for op_name, op_cmd in operations:
        log(f"   {op_name}")
        
        if op_cmd.startswith("sleep"):
            time.sleep(int(op_cmd.split()[1]))
        elif "&&" in op_cmd:
            parts = op_cmd.split(" && ")
            for part in parts:
                if part.startswith("sleep"):
                    time.sleep(int(part.split()[1]))
                else:
                    run_adb(part)
                    time.sleep(1)
        else:
            run_adb(op_cmd)
            time.sleep(1)

def get_request_count():
    """获取请求数量"""
    try:
        capture_file = Path("mitm-logs/realtime_capture.json")
        if capture_file.exists():
            with open(capture_file, 'r') as f:
                data = json.load(f)
            return len(data) if isinstance(data, list) else 0
        return 0
    except:
        return 0

def analyze_https_results():
    """分析HTTPS结果"""
    log("📊 分析HTTPS测试结果...")
    
    capture_file = Path("mitm-logs/realtime_capture.json")
    
    try:
        if not capture_file.exists():
            log("捕获文件不存在", "ERROR")
            return False
        
        with open(capture_file, 'r') as f:
            data = json.load(f)
        
        total = len(data) if isinstance(data, list) else 0
        https_reqs = [req for req in data if req.get('url', '').startswith('https://')]
        http_reqs = [req for req in data if req.get('url', '').startswith('http://')]
        
        log("=" * 60)
        log("🎉 HTTPS捕获测试结果", "SUCCESS")
        log("=" * 60)
        
        log(f"📊 网络捕获统计:")
        log(f"   总请求数: {total}")
        log(f"   HTTPS请求: {len(https_reqs)}")
        log(f"   HTTP请求: {len(http_reqs)}")
        
        if len(https_reqs) > 0:
            log("🔒 HTTPS请求分析:", "SUCCESS")
            
            # 域名统计
            domains = {}
            for req in https_reqs:
                host = req.get('host', '')
                if host:
                    domains[host] = domains.get(host, 0) + 1
            
            log("🌐 HTTPS域名:")
            for domain, count in sorted(domains.items(), key=lambda x: x[1], reverse=True):
                log(f"   {domain}: {count} 请求")
            
            log("📋 HTTPS请求详情:")
            for i, req in enumerate(https_reqs[:8], 1):
                url = req.get('url', 'N/A')
                method = req.get('method', 'N/A')
                status = req.get('status_code', 'N/A')
                
                log(f"   {i}. {method} {url}")
                log(f"      状态: {status}")
            
            log("🎉 HTTPS捕获成功！SSL绕过正常工作！", "SUCCESS")
            return True
        else:
            log("❌ 没有捕获到HTTPS请求", "ERROR")
            
            if len(http_reqs) > 0:
                log("⚠️  但捕获到HTTP请求:", "WARNING")
                for i, req in enumerate(http_reqs[:5], 1):
                    url = req.get('url', 'N/A')
                    method = req.get('method', 'N/A')
                    log(f"   {i}. {method} {url}")
                
                log("💡 这说明网络捕获机制正常，但SSL绕过可能需要调整")
            
            log("💡 可能的原因:")
            log("   1. SSL绕过脚本没有正确加载")
            log("   2. 应用使用了证书固定")
            log("   3. 需要安装CA证书到系统信任存储")
            log("   4. 应用检测到了代理环境")
            
            return False
            
    except Exception as e:
        log(f"分析失败: {e}", "ERROR")
        return False

def main():
    """主函数"""
    log("🚀 HTTPS捕获专项测试")
    log("🔧 验证SSL绕过是否真正工作")
    log("=" * 60)
    
    mitmproxy_process = None
    ssl_process = None
    
    try:
        # 步骤1: 设置HTTPS测试环境
        mitmproxy_process = setup_https_test_environment()
        if not mitmproxy_process:
            log("HTTPS测试环境设置失败", "ERROR")
            return False
        
        # 步骤2: 安装CA证书
        install_ca_certificate()
        
        # 步骤3: 启动增强的SSL绕过
        ssl_process = start_enhanced_ssl_bypass()
        if not ssl_process:
            log("SSL绕过启动失败", "ERROR")
            return False
        
        # 步骤4: 等待系统稳定
        log("⏳ 等待系统稳定...")
        time.sleep(20)
        
        # 步骤5: 直接测试HTTPS请求
        log("=" * 60)
        log("🔍 开始HTTPS请求测试")
        log("=" * 60)
        
        test_https_requests_directly()
        
        # 步骤6: 等待处理
        log("⏳ 等待网络请求处理...")
        time.sleep(30)
        
        # 步骤7: 分析结果
        success = analyze_https_results()
        
        if success:
            log("🎉 HTTPS捕获测试成功！", "SUCCESS")
            return True
        else:
            log("⚠️  HTTPS捕获需要进一步调试", "WARNING")
            return False
            
    except KeyboardInterrupt:
        log("用户中断测试")
        return False
    except Exception as e:
        log(f"测试异常: {e}", "ERROR")
        return False
    finally:
        # 清理
        log("🧹 清理资源...")
        if mitmproxy_process and mitmproxy_process.poll() is None:
            mitmproxy_process.terminate()
        if ssl_process and ssl_process.poll() is None:
            ssl_process.terminate()
        
        subprocess.run("pkill -f mitmdump", shell=True, capture_output=True)
        run_adb("shell pkill frida-server")

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎯 HTTPS捕获测试成功！")
            print("💡 SSL绕过功能正常工作！")
        else:
            print("\n🔧 HTTPS捕获需要进一步配置")
            print("💡 但HTTP捕获功能已验证正常")
    except KeyboardInterrupt:
        print("\n⚠️  用户中断")
    except Exception as e:
        print(f"\n❌ 异常: {e}")
