#!/usr/bin/env python3
"""
测试HTTPS流量捕获能力
"""

import subprocess
import time
import json
import os
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
    
class HTTPSCaptureTest:
    def __init__(self):
        self.device_id = "emulator-5554"
        self.package_name = "com.iloda.beacon"
        self.base_dir = Path("/Users/<USER>/Desktop/project/apk_detect")
        
    def run_adb(self, cmd, timeout=10):
        """执行ADB命令"""
        try:
            result = subprocess.run(
                ['adb', '-s', self.device_id] + cmd,
                capture_output=True, text=True, timeout=timeout
            )
            return result.returncode == 0, result.stdout.strip()
        except Exception as e:
            return False, str(e)
    
    def setup_proxy(self):
        """设置代理"""
        logger.info("📡 配置代理...")
        
        # 使用********:8080（模拟器中主机的IP）
        success, _ = self.run_adb([
            'shell', 'settings', 'put', 'global', 
            'http_proxy', '********:8080'
        ])
        
        if success:
            logger.info("✅ 代理配置成功")
        else:
            logger.warning("⚠️ 代理配置失败")
        
        return success
    
    def install_certificate(self):
        """安装证书"""
        logger.info("🔐 检查证书...")
        
        cert_file = self.base_dir / "mitm-config" / "mitmproxy-ca-cert.cer"
        if cert_file.exists():
            # 推送证书到设备
            subprocess.run(['adb', 'push', str(cert_file), '/sdcard/'])
            logger.info("✅ 证书已推送")
            
            # 安装证书（需要用户手动确认）
            self.run_adb([
                'shell', 'am', 'start', '-a', 'android.settings.SECURITY_SETTINGS'
            ])
            logger.info("📱 请在设置中手动安装证书")
            time.sleep(5)
        else:
            logger.warning("⚠️ 证书文件不存在")
    
    def start_mitmproxy(self):
        """启动mitmproxy"""
        logger.info("🌐 启动mitmproxy...")
        
        # 创建日志文件
        timestamp = int(time.time())
        log_file = self.base_dir / f"https_capture_{timestamp}.log"
        
        # 启动mitmdump（简化版）
        cmd = [
            'mitmdump',
            '--listen-host', '0.0.0.0',
            '--listen-port', '8080',
            '--set', f'confdir={self.base_dir}/mitm-config',
            '--save-stream-file', str(log_file),
            '--quiet'
        ]
        
        try:
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )
            
            time.sleep(3)
            
            if process.poll() is None:
                logger.info(f"✅ mitmproxy已启动 (PID: {process.pid})")
                return process, str(log_file)
            else:
                logger.error("❌ mitmproxy启动失败")
                return None, None
                
        except FileNotFoundError:
            logger.error("❌ mitmdump未安装，请先安装: pip install mitmproxy")
            return None, None
        except Exception as e:
            logger.error(f"❌ 启动失败: {e}")
            return None, None
    
    def start_frida_ssl_bypass(self):
        """启动Frida SSL绕过"""
        logger.info("🔓 启动SSL绕过...")
        
        ssl_script = self.base_dir / "ssl_bypass.js"
        if not ssl_script.exists():
            logger.warning("⚠️ SSL绕过脚本不存在")
            return None
        
        try:
            cmd = [
                'frida', '-U', '-l', str(ssl_script),
                '-f', self.package_name, '--no-pause'
            ]
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            time.sleep(5)
            
            if process.poll() is None:
                logger.info("✅ Frida SSL绕过已启动")
                return process
            else:
                logger.warning("⚠️ Frida启动失败")
                return None
                
        except FileNotFoundError:
            logger.error("❌ frida未安装，请先安装: pip install frida-tools")
            return None
        except Exception as e:
            logger.warning(f"⚠️ SSL绕过启动失败: {e}")
            return None
    
    def trigger_network_activity(self):
        """触发网络活动"""
        logger.info("🎯 触发网络活动...")
        
        # 启动应用
        self.run_adb([
            'shell', 'am', 'start', '-n',
            f'{self.package_name}/.activity.LoginActivity'
        ])
        
        time.sleep(3)
        
        # 执行一些操作
        actions = [
            ['shell', 'input', 'tap', '540', '960'],  # 中心点击
            ['shell', 'input', 'swipe', '540', '1200', '540', '600'],  # 下拉
            ['shell', 'input', 'keyevent', 'KEYCODE_BACK'],  # 返回
        ]
        
        for action in actions:
            self.run_adb(action)
            time.sleep(2)
        
        # 启动更多Activity
        activities = [
            '.activity.MainActivity',
            '.activity.GuideActivity',
            '.activity.SplashActivity'
        ]
        
        for activity in activities:
            logger.info(f"启动: {activity}")
            self.run_adb([
                'shell', 'am', 'start', '-n',
                f'{self.package_name}/{activity}'
            ])
            time.sleep(3)
    
    def analyze_captured_traffic(self, log_file):
        """分析捕获的流量"""
        logger.info("📊 分析捕获的流量...")
        
        if not log_file or not Path(log_file).exists():
            logger.error("日志文件不存在")
            return
        
        # 读取日志
        with open(log_file, 'rb') as f:
            content = f.read()
        
        # 简单分析
        urls = []
        domains = set()
        
        # 查找URL模式
        import re
        url_pattern = rb'https?://[^\s\x00]+'
        matches = re.findall(url_pattern, content)
        
        for match in matches:
            try:
                url = match.decode('utf-8', errors='ignore')
                urls.append(url)
                # 提取域名
                domain = url.split('/')[2] if '/' in url else url
                domains.add(domain)
            except:
                pass
        
        logger.info(f"✅ 捕获结果:")
        logger.info(f"   🔗 URL数量: {len(urls)}")
        logger.info(f"   🌐 域名数量: {len(domains)}")
        
        if urls:
            logger.info("   📝 前5个URL:")
            for i, url in enumerate(urls[:5], 1):
                logger.info(f"      {i}. {url[:80]}")
        
        if domains:
            logger.info("   🏢 捕获的域名:")
            for domain in list(domains)[:10]:
                logger.info(f"      - {domain}")
        
        # 保存结果
        result = {
            "capture_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "total_urls": len(urls),
            "total_domains": len(domains),
            "urls": urls[:50],  # 保存前50个
            "domains": list(domains)
        }
        
        result_file = self.base_dir / f"https_capture_result_{int(time.time())}.json"
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📄 结果已保存: {result_file.name}")
        
        return result
    
    def cleanup(self, mitm_process, frida_process):
        """清理资源"""
        logger.info("🧹 清理资源...")
        
        # 清除代理
        self.run_adb(['shell', 'settings', 'delete', 'global', 'http_proxy'])
        
        # 停止进程
        for process in [mitm_process, frida_process]:
            if process:
                try:
                    process.terminate()
                    process.wait(timeout=3)
                except:
                    try:
                        process.kill()
                    except:
                        pass
        
        logger.info("✅ 清理完成")
    
    def run_test(self):
        """运行完整测试"""
        logger.info("=" * 60)
        logger.info("🚀 开始HTTPS流量捕获测试")
        logger.info("=" * 60)
        
        mitm_process = None
        frida_process = None
        log_file = None
        
        try:
            # 1. 设置代理
            self.setup_proxy()
            
            # 2. 启动mitmproxy
            mitm_process, log_file = self.start_mitmproxy()
            
            # 3. 启动Frida SSL绕过
            frida_process = self.start_frida_ssl_bypass()
            
            # 4. 触发网络活动
            self.trigger_network_activity()
            
            # 等待更多流量
            logger.info("⏳ 等待30秒收集更多流量...")
            time.sleep(30)
            
            # 5. 分析结果
            if log_file:
                self.analyze_captured_traffic(log_file)
            
        except KeyboardInterrupt:
            logger.info("❌ 用户中断")
        except Exception as e:
            logger.error(f"❌ 测试失败: {e}")
        finally:
            self.cleanup(mitm_process, frida_process)
        
        logger.info("=" * 60)
        logger.info("🏁 测试完成")
        logger.info("=" * 60)

if __name__ == "__main__":
    test = HTTPSCaptureTest()
    test.run_test()
    def start_mitmproxy_capture(self):
        """启动mitmproxy流量捕获"""
        print("🌐 启动mitmproxy HTTPS流量捕获...")
        
        # 创建mitmproxy捕获脚本
        mitm_script = f"""
import mitmproxy.http
import json
from datetime import datetime

class HTTPSCaptureAddon:
    def request(self, flow: mitmproxy.http.HTTPFlow) -> None:
        # 记录请求
        request_data = {{
            'timestamp': datetime.now().isoformat(),
            'method': flow.request.method,
            'url': flow.request.pretty_url,
            'headers': dict(flow.request.headers),
            'content_length': len(flow.request.content) if flow.request.content else 0
        }}
        
        # 写入日志文件
        with open('{self.mitm_log_file}', 'a') as f:
            f.write(json.dumps(request_data) + '\\n')
        
        print(f"[CAPTURE] {{flow.request.method}} {{flow.request.pretty_url}}")
    
    def response(self, flow: mitmproxy.http.HTTPFlow) -> None:
        # 记录响应
        response_data = {{
            'timestamp': datetime.now().isoformat(),
            'url': flow.request.pretty_url,
            'status_code': flow.response.status_code,
            'headers': dict(flow.response.headers),
            'content_length': len(flow.response.content) if flow.response.content else 0
        }}
        
        # 写入日志文件
        with open('{self.mitm_log_file}', 'a') as f:
            f.write(json.dumps(response_data) + '\\n')

addons = [HTTPSCaptureAddon()]
"""
        
        # 保存mitmproxy脚本
        with open('mitm_capture_script.py', 'w') as f:
            f.write(mitm_script)
        
        return True
    
    def restart_app_with_traffic_capture(self):
        """重启应用并开始流量捕获"""
        print("🚀 重启应用并开始HTTPS流量捕获...")
        
        try:
            # 停止应用
            print("停止应用...")
            self.run_adb_command(['shell', 'am', 'force-stop', self.package_name])
            time.sleep(2)
            
            # 清除应用数据以触发网络请求
            print("清除应用数据...")
            self.run_adb_command(['shell', 'pm', 'clear', self.package_name])
            time.sleep(2)
            
            # 启动应用
            print("启动应用...")
            launch_result = self.run_adb_command([
                'shell', 'am', 'start', '-n', 
                f'{self.package_name}/{self.main_activity}'
            ])
            
            if launch_result and launch_result.returncode == 0:
                print("✅ 应用启动成功")
                return True
            else:
                print(f"❌ 应用启动失败: {launch_result.stderr if launch_result else '未知错误'}")
                return False
                
        except Exception as e:
            print(f"❌ 应用重启异常: {e}")
            return False
    
    def simulate_user_interactions(self):
        """模拟用户交互以触发网络请求"""
        print("🎯 模拟用户交互以触发网络请求...")
        
        interactions = [
            # 等待应用加载
            {"action": "wait", "duration": 5, "description": "等待应用加载"},
            
            # 点击Continue按钮（从之前的UI分析中得知）
            {"action": "tap", "x": 969, "y": 2014, "description": "点击Continue按钮"},
            {"action": "wait", "duration": 3, "description": "等待权限处理"},
            
            # 尝试输入手机号
            {"action": "tap", "x": 540, "y": 600, "description": "点击手机号输入框"},
            {"action": "wait", "duration": 2, "description": "等待输入框响应"},
            {"action": "input", "text": "13800138000", "description": "输入手机号"},
            {"action": "wait", "duration": 2, "description": "等待输入完成"},
            
            # 点击获取验证码按钮
            {"action": "tap", "x": 540, "y": 880, "description": "点击获取验证码按钮"},
            {"action": "wait", "duration": 5, "description": "等待网络请求"},
            
            # 等待更多网络请求
            {"action": "wait", "duration": 10, "description": "等待更多网络活动"},
        ]
        
        for interaction in interactions:
            if not self.running:
                break
                
            print(f"执行操作: {interaction['description']}")
            
            if interaction["action"] == "wait":
                time.sleep(interaction["duration"])
            elif interaction["action"] == "tap":
                self.run_adb_command([
                    'shell', 'input', 'tap', 
                    str(interaction["x"]), str(interaction["y"])
                ])
            elif interaction["action"] == "input":
                self.run_adb_command([
                    'shell', 'input', 'text', interaction["text"]
                ])
                
            time.sleep(1)  # 每个操作之间的间隔
    
    def monitor_mitm_logs(self):
        """监控mitmproxy日志"""
        print(f"📊 监控HTTPS流量日志: {self.mitm_log_file}")
        
        captured_count = 0
        start_time = time.time()
        
        while self.running and (time.time() - start_time) < 60:  # 最多监控60秒
            try:
                if os.path.exists(self.mitm_log_file):
                    with open(self.mitm_log_file, 'r') as f:
                        lines = f.readlines()
                        if len(lines) > captured_count:
                            # 有新的捕获记录
                            for line in lines[captured_count:]:
                                try:
                                    data = json.loads(line.strip())
                                    if 'method' in data:  # 请求数据
                                        print(f"📡 [REQUEST] {data['method']} {data['url']}")
                                    elif 'status_code' in data:  # 响应数据
                                        print(f"📨 [RESPONSE] {data['status_code']} {data['url']}")
                                except json.JSONDecodeError:
                                    pass
                            captured_count = len(lines)
                
                time.sleep(2)
            except Exception as e:
                print(f"日志监控异常: {e}")
                break
        
        return captured_count
    
    def analyze_captured_traffic(self):
        """分析捕获的流量"""
        print("\n🔍 分析捕获的HTTPS流量...")
        
        if not os.path.exists(self.mitm_log_file):
            print("❌ 没有找到流量捕获日志文件")
            return False
        
        try:
            requests = []
            responses = []
            
            with open(self.mitm_log_file, 'r') as f:
                for line in f:
                    try:
                        data = json.loads(line.strip())
                        if 'method' in data:
                            requests.append(data)
                        elif 'status_code' in data:
                            responses.append(data)
                    except json.JSONDecodeError:
                        pass
            
            print(f"✅ 捕获统计:")
            print(f"   - HTTP请求: {len(requests)} 个")
            print(f"   - HTTP响应: {len(responses)} 个")
            
            if requests:
                print(f"\n📡 捕获的HTTP/HTTPS请求:")
                for i, req in enumerate(requests[:10], 1):  # 显示前10个
                    print(f"   {i}. {req['method']} {req['url']}")
                    if req['url'].startswith('https://'):
                        print(f"      🔒 HTTPS请求 - SSL绕过成功!")
            
            # 保存详细分析报告
            report = {
                'capture_time': datetime.now().isoformat(),
                'total_requests': len(requests),
                'total_responses': len(responses),
                'https_requests': len([r for r in requests if r['url'].startswith('https://')]),
                'http_requests': len([r for r in requests if r['url'].startswith('http://')]),
                'unique_domains': list(set([r['url'].split('/')[2] for r in requests if '/' in r['url']])),
                'requests': requests,
                'responses': responses
            }
            
            report_file = f"https_capture_report_{int(time.time())}.json"
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            print(f"\n📄 详细报告已保存: {report_file}")
            
            return len(requests) > 0
            
        except Exception as e:
            print(f"❌ 流量分析异常: {e}")
            return False
    
    def run_https_capture_test(self):
        """运行HTTPS流量捕获测试"""
        print("="*60)
        print("🔒 开始真实HTTPS流量捕获测试")
        print("="*60)
        
        try:
            # 1. 设置流量捕获
            self.start_mitmproxy_capture()
            
            # 2. 重启应用
            if not self.restart_app_with_traffic_capture():
                print("❌ 应用启动失败")
                return False
            
            # 3. 开始监控流量（在后台线程）
            monitor_thread = threading.Thread(target=self.monitor_mitm_logs)
            monitor_thread.daemon = True
            monitor_thread.start()
            
            # 4. 模拟用户交互
            self.simulate_user_interactions()
            
            # 5. 等待流量捕获完成
            print("\n⏳ 等待流量捕获完成...")
            time.sleep(10)
            self.running = False
            
            # 6. 分析捕获结果
            success = self.analyze_captured_traffic()
            
            if success:
                print("\n🎉 HTTPS流量捕获测试成功!")
            else:
                print("\n⚠️ 未捕获到HTTP/HTTPS流量")
            
            return success
            
        except KeyboardInterrupt:
            print("\n⚠️ 用户中断测试")
            self.running = False
            return False
        except Exception as e:
            print(f"\n❌ HTTPS流量捕获测试异常: {e}")
            return False

def main():
    """主函数"""
    capture = HTTPSTrafficCapture()
    success = capture.run_https_capture_test()
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())

    def start_mitmproxy_capture(self):
        """启动mitmproxy流量捕获"""
        print("🌐 启动mitmproxy HTTPS流量捕获...")
        
        # 创建mitmproxy捕获脚本
        mitm_script = f"""
import mitmproxy.http
import json
from datetime import datetime

class HTTPSCaptureAddon:
    def request(self, flow: mitmproxy.http.HTTPFlow) -> None:
        # 记录请求
        request_data = {{
            'timestamp': datetime.now().isoformat(),
            'method': flow.request.method,
            'url': flow.request.pretty_url,
            'headers': dict(flow.request.headers),
            'content_length': len(flow.request.content) if flow.request.content else 0
        }}
        
        # 写入日志文件
        with open('{self.mitm_log_file}', 'a') as f:
            f.write(json.dumps(request_data) + '\\n')
        
        print(f"[CAPTURE] {{flow.request.method}} {{flow.request.pretty_url}}")
    
    def response(self, flow: mitmproxy.http.HTTPFlow) -> None:
        # 记录响应
        response_data = {{
            'timestamp': datetime.now().isoformat(),
            'url': flow.request.pretty_url,
            'status_code': flow.response.status_code,
            'headers': dict(flow.response.headers),
            'content_length': len(flow.response.content) if flow.response.content else 0
        }}
        
        # 写入日志文件
        with open('{self.mitm_log_file}', 'a') as f:
            f.write(json.dumps(response_data) + '\\n')

addons = [HTTPSCaptureAddon()]
"""
        
        # 保存mitmproxy脚本
        with open('mitm_capture_script.py', 'w') as f:
            f.write(mitm_script)
        
        return True
    
    def restart_app_with_traffic_capture(self):
        """重启应用并开始流量捕获"""
        print("🚀 重启应用并开始HTTPS流量捕获...")
        
        try:
            # 停止应用
            print("停止应用...")
            self.run_adb_command(['shell', 'am', 'force-stop', self.package_name])
            time.sleep(2)
            
            # 清除应用数据以触发网络请求
            print("清除应用数据...")
            self.run_adb_command(['shell', 'pm', 'clear', self.package_name])
            time.sleep(2)
            
            # 启动应用
            print("启动应用...")
            launch_result = self.run_adb_command([
                'shell', 'am', 'start', '-n', 
                f'{self.package_name}/{self.main_activity}'
            ])
            
            if launch_result and launch_result.returncode == 0:
                print("✅ 应用启动成功")
                return True
            else:
                print(f"❌ 应用启动失败: {launch_result.stderr if launch_result else '未知错误'}")
                return False
                
        except Exception as e:
            print(f"❌ 应用重启异常: {e}")
            return False
    
    def simulate_user_interactions(self):
        """模拟用户交互以触发网络请求"""
        print("🎯 模拟用户交互以触发网络请求...")
        
        interactions = [
            # 等待应用加载
            {"action": "wait", "duration": 5, "description": "等待应用加载"},
            
            # 点击Continue按钮（从之前的UI分析中得知）
            {"action": "tap", "x": 969, "y": 2014, "description": "点击Continue按钮"},
            {"action": "wait", "duration": 3, "description": "等待权限处理"},
            
            # 尝试输入手机号
            {"action": "tap", "x": 540, "y": 600, "description": "点击手机号输入框"},
            {"action": "wait", "duration": 2, "description": "等待输入框响应"},
            {"action": "input", "text": "13800138000", "description": "输入手机号"},
            {"action": "wait", "duration": 2, "description": "等待输入完成"},
            
            # 点击获取验证码按钮
            {"action": "tap", "x": 540, "y": 880, "description": "点击获取验证码按钮"},
            {"action": "wait", "duration": 5, "description": "等待网络请求"},
            
            # 等待更多网络请求
            {"action": "wait", "duration": 10, "description": "等待更多网络活动"},
        ]
        
        for interaction in interactions:
            if not self.running:
                break
                
            print(f"执行操作: {interaction['description']}")
            
            if interaction["action"] == "wait":
                time.sleep(interaction["duration"])
            elif interaction["action"] == "tap":
                self.run_adb_command([
                    'shell', 'input', 'tap', 
                    str(interaction["x"]), str(interaction["y"])
                ])
            elif interaction["action"] == "input":
                self.run_adb_command([
                    'shell', 'input', 'text', interaction["text"]
                ])
                
            time.sleep(1)  # 每个操作之间的间隔
    
    def monitor_mitm_logs(self):
        """监控mitmproxy日志"""
        print(f"📊 监控HTTPS流量日志: {self.mitm_log_file}")
        
        captured_count = 0
        start_time = time.time()
        
        while self.running and (time.time() - start_time) < 60:  # 最多监控60秒
            try:
                if os.path.exists(self.mitm_log_file):
                    with open(self.mitm_log_file, 'r') as f:
                        lines = f.readlines()
                        if len(lines) > captured_count:
                            # 有新的捕获记录
                            for line in lines[captured_count:]:
                                try:
                                    data = json.loads(line.strip())
                                    if 'method' in data:  # 请求数据
                                        print(f"📡 [REQUEST] {data['method']} {data['url']}")
                                    elif 'status_code' in data:  # 响应数据
                                        print(f"📨 [RESPONSE] {data['status_code']} {data['url']}")
                                except json.JSONDecodeError:
                                    pass
                            captured_count = len(lines)
                
                time.sleep(2)
            except Exception as e:
                print(f"日志监控异常: {e}")
                break
        
        return captured_count
    
    def analyze_captured_traffic(self):
        """分析捕获的流量"""
        print("\n🔍 分析捕获的HTTPS流量...")
        
        if not os.path.exists(self.mitm_log_file):
            print("❌ 没有找到流量捕获日志文件")
            return False
        
        try:
            requests = []
            responses = []
            
            with open(self.mitm_log_file, 'r') as f:
                for line in f:
                    try:
                        data = json.loads(line.strip())
                        if 'method' in data:
                            requests.append(data)
                        elif 'status_code' in data:
                            responses.append(data)
                    except json.JSONDecodeError:
                        pass
            
            print(f"✅ 捕获统计:")
            print(f"   - HTTP请求: {len(requests)} 个")
            print(f"   - HTTP响应: {len(responses)} 个")
            
            if requests:
                print(f"\n📡 捕获的HTTP/HTTPS请求:")
                for i, req in enumerate(requests[:10], 1):  # 显示前10个
                    print(f"   {i}. {req['method']} {req['url']}")
                    if req['url'].startswith('https://'):
                        print(f"      🔒 HTTPS请求 - SSL绕过成功!")
            
            # 保存详细分析报告
            report = {
                'capture_time': datetime.now().isoformat(),
                'total_requests': len(requests),
                'total_responses': len(responses),
                'https_requests': len([r for r in requests if r['url'].startswith('https://')]),
                'http_requests': len([r for r in requests if r['url'].startswith('http://')]),
                'unique_domains': list(set([r['url'].split('/')[2] for r in requests if '/' in r['url']])),
                'requests': requests,
                'responses': responses
            }
            
            report_file = f"https_capture_report_{int(time.time())}.json"
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            print(f"\n📄 详细报告已保存: {report_file}")
            
            return len(requests) > 0
            
        except Exception as e:
            print(f"❌ 流量分析异常: {e}")
            return False
    
    def run_https_capture_test(self):
        """运行HTTPS流量捕获测试"""
        print("="*60)
        print("🔒 开始真实HTTPS流量捕获测试")
        print("="*60)
        
        try:
            # 1. 设置流量捕获
            self.start_mitmproxy_capture()
            
            # 2. 重启应用
            if not self.restart_app_with_traffic_capture():
                print("❌ 应用启动失败")
                return False
            
            # 3. 开始监控流量（在后台线程）
            monitor_thread = threading.Thread(target=self.monitor_mitm_logs)
            monitor_thread.daemon = True
            monitor_thread.start()
            
            # 4. 模拟用户交互
            self.simulate_user_interactions()
            
            # 5. 等待流量捕获完成
            print("\n⏳ 等待流量捕获完成...")
            time.sleep(10)
            self.running = False
            
            # 6. 分析捕获结果
            success = self.analyze_captured_traffic()
            
            if success:
                print("\n🎉 HTTPS流量捕获测试成功!")
            else:
                print("\n⚠️ 未捕获到HTTP/HTTPS流量")
            
            return success
            
        except KeyboardInterrupt:
            print("\n⚠️ 用户中断测试")
            self.running = False
            return False
        except Exception as e:
            print(f"\n❌ HTTPS流量捕获测试异常: {e}")
            return False

def main():
    """主函数"""
    capture = HTTPSTrafficCapture()
    success = capture.run_https_capture_test()
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
