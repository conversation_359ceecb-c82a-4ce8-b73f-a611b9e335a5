#!/usr/bin/env python3
"""
简化的SSL绕过测试
专门测试SSL绕过和网络捕获功能
"""

import subprocess
import sys
import time
import json
from pathlib import Path
import threading
import signal

def log(message, level="INFO"):
    """日志输出"""
    timestamp = time.strftime("%H:%M:%S")
    if level == "SUCCESS":
        print(f"🎉 [{timestamp}] {message}")
    elif level == "ERROR":
        print(f"❌ [{timestamp}] {message}")
    elif level == "WARNING":
        print(f"⚠️  [{timestamp}] {message}")
    else:
        print(f"📋 [{timestamp}] {message}")

def run_adb(cmd, timeout=30):
    """执行adb命令"""
    full_cmd = f"source android_env.sh && adb {cmd}"
    try:
        result = subprocess.run(full_cmd, shell=True, 
                              capture_output=True, text=True, 
                              timeout=timeout, check=True)
        return result.returncode, result.stdout.strip(), result.stderr.strip()
    except subprocess.CalledProcessError as e:
        return e.returncode, "", e.stderr
    except subprocess.TimeoutExpired:
        return -1, "", "Timeout"

def start_mitmproxy_simple():
    """启动简化的mitmproxy"""
    log("启动mitmproxy...")
    
    # 确保目录存在
    Path("mitm-logs").mkdir(exist_ok=True)
    
    # 清空实时捕获文件
    capture_file = Path("mitm-logs/realtime_capture.json")
    with open(capture_file, 'w') as f:
        json.dump([], f)
    
    # 启动mitmproxy
    cmd = "mitmdump -s mitm-scripts/capture.py --listen-port 8080"
    
    try:
        process = subprocess.Popen(
            cmd, shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        log("mitmproxy进程已启动")
        time.sleep(10)  # 等待启动
        
        if process.poll() is None:
            log("✅ mitmproxy运行正常", "SUCCESS")
            return process
        else:
            stdout, stderr = process.communicate()
            log(f"mitmproxy启动失败: {stderr}", "ERROR")
            return None
            
    except Exception as e:
        log(f"mitmproxy启动异常: {e}", "ERROR")
        return None

def setup_proxy():
    """设置代理"""
    log("设置Android代理...")
    
    # 使用本地IP
    host_ip = "127.0.0.1"  # 简化使用localhost
    proxy_setting = f"{host_ip}:8080"
    
    returncode, stdout, stderr = run_adb(f"shell settings put global http_proxy {proxy_setting}")
    
    if returncode == 0:
        log(f"✅ 代理设置成功: {proxy_setting}", "SUCCESS")
        return True
    else:
        log(f"代理设置失败: {stderr}", "ERROR")
        return False

def start_frida_ssl_bypass_simple():
    """启动简化的Frida SSL绕过"""
    log("启动Frida SSL绕过...")
    
    # 确保环境准备
    subprocess.run("adb root", shell=True, capture_output=True)
    time.sleep(2)
    
    # 推送并启动frida-server
    run_adb("push frida-server /data/local/tmp/frida-server")
    run_adb("shell chmod 755 /data/local/tmp/frida-server")
    run_adb("shell pkill frida-server")
    time.sleep(2)
    
    subprocess.Popen("source android_env.sh && adb shell '/data/local/tmp/frida-server' &", shell=True)
    time.sleep(8)
    
    # 验证frida-server
    returncode, stdout, stderr = run_adb("shell ps | grep frida-server")
    if "frida-server" not in stdout:
        log("frida-server启动失败", "ERROR")
        return None
    
    log("✅ frida-server启动成功", "SUCCESS")
    
    # 准备应用
    package = "com.yjzx.yjzx2017"
    run_adb(f"shell am force-stop {package}")
    time.sleep(2)
    
    returncode, stdout, stderr = run_adb(f"shell am start -n {package}/.controller.activity.splash.SplashActivity")
    if returncode != 0:
        log(f"应用启动失败: {stderr}", "ERROR")
        return None
    
    log("✅ 应用启动成功", "SUCCESS")
    time.sleep(8)
    
    # 获取应用PID
    returncode, stdout, stderr = run_adb(f"shell ps | grep {package}")
    if package not in stdout:
        log("未找到应用进程", "ERROR")
        return None
    
    # 提取PID
    lines = stdout.split('\n')
    target_pid = None
    for line in lines:
        if package in line:
            parts = line.split()
            if len(parts) >= 2:
                target_pid = parts[1]
                break
    
    if not target_pid:
        log("无法获取PID", "ERROR")
        return None
    
    log(f"目标PID: {target_pid}")
    
    # 启动Frida SSL绕过
    cmd = f"export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin && frida -U {target_pid} -l interactive_ssl_bypass.js"
    
    try:
        process = subprocess.Popen(
            cmd, shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        log("Frida SSL绕过进程已启动")
        time.sleep(20)  # 等待SSL绕过加载
        
        if process.poll() is None:
            log("✅ Frida SSL绕过正在运行", "SUCCESS")
            return process
        else:
            stdout, stderr = process.communicate()
            log("Frida SSL绕过启动失败", "ERROR")
            if stdout:
                log(f"输出: {stdout[:200]}")
            if stderr:
                log(f"错误: {stderr[:200]}")
            return None
            
    except Exception as e:
        log(f"Frida SSL绕过异常: {e}", "ERROR")
        return None

def test_https_requests():
    """测试HTTPS请求"""
    log("🔍 测试HTTPS请求...")
    
    # 测试URL列表
    test_urls = [
        "https://httpbin.org/get",
        "https://www.baidu.com",
        "https://api.github.com",
        "https://jsonplaceholder.typicode.com/posts/1"
    ]
    
    for i, url in enumerate(test_urls, 1):
        log(f"测试 {i}/{len(test_urls)}: {url}")
        run_adb(f"shell am start -a android.intent.action.VIEW -d {url}")
        time.sleep(8)
    
    # 应用内操作
    log("执行应用内操作...")
    actions = [
        ("点击中心", "shell input tap 540 960"),
        ("向下滑动", "shell input swipe 540 800 540 400"),
        ("点击右上角", "shell input tap 700 300"),
        ("返回", "shell input keyevent 4")
    ]
    
    for action_name, action_cmd in actions:
        log(f"   {action_name}")
        run_adb(action_cmd)
        time.sleep(3)
    
    log("✅ HTTPS请求测试完成")

def analyze_captured_traffic():
    """分析捕获的流量"""
    log("📊 分析捕获的流量...")
    
    capture_file = Path("mitm-logs/realtime_capture.json")
    
    try:
        if not capture_file.exists():
            log("捕获文件不存在", "ERROR")
            return False
        
        with open(capture_file, 'r') as f:
            data = json.load(f)
        
        if not isinstance(data, list):
            log("捕获文件格式错误", "ERROR")
            return False
        
        total = len(data)
        https_reqs = [req for req in data if req.get('url', '').startswith('https://')]
        http_reqs = [req for req in data if req.get('url', '').startswith('http://')]
        
        log("=" * 50)
        log("🎉 SSL绕过测试结果", "SUCCESS")
        log("=" * 50)
        
        log(f"📊 捕获统计:")
        log(f"   总请求数: {total}")
        log(f"   HTTPS请求: {len(https_reqs)}")
        log(f"   HTTP请求: {len(http_reqs)}")
        
        if len(https_reqs) > 0:
            log("🔒 HTTPS请求详情:", "SUCCESS")
            
            # 显示HTTPS请求
            for i, req in enumerate(https_reqs[:10], 1):
                url = req.get('url', 'N/A')
                method = req.get('method', 'N/A')
                host = req.get('host', 'N/A')
                
                log(f"   {i}. {method} {host}")
                log(f"      URL: {url}")
            
            # 分析域名
            domains = {}
            for req in https_reqs:
                host = req.get('host', '')
                if host:
                    domains[host] = domains.get(host, 0) + 1
            
            log("🌐 访问的HTTPS域名:")
            for domain, count in sorted(domains.items(), key=lambda x: x[1], reverse=True):
                log(f"   {domain}: {count} 请求")
            
            log("🎉 SSL绕过完全成功！", "SUCCESS")
            log("✅ HTTPS流量已被成功捕获！")
            log("✅ SSL证书验证已被绕过！")
            
            return True
        else:
            log("❌ 没有捕获到HTTPS请求", "ERROR")
            
            if len(http_reqs) > 0:
                log("⚠️  但捕获到HTTP请求:", "WARNING")
                for i, req in enumerate(http_reqs[:3], 1):
                    url = req.get('url', 'N/A')
                    method = req.get('method', 'N/A')
                    log(f"   {i}. {method} {url}")
            
            return False
            
    except Exception as e:
        log(f"分析失败: {e}", "ERROR")
        return False

def run_simple_ssl_bypass_test():
    """运行简化的SSL绕过测试"""
    log("🚀 简化的SSL绕过测试")
    log("🔧 测试SSL绕过和网络捕获功能")
    log("=" * 60)
    
    mitmproxy_process = None
    frida_process = None
    
    try:
        # 步骤1: 启动mitmproxy
        mitmproxy_process = start_mitmproxy_simple()
        if not mitmproxy_process:
            log("mitmproxy启动失败", "ERROR")
            return False
        
        # 步骤2: 设置代理
        if not setup_proxy():
            log("代理设置失败", "WARNING")
        
        # 步骤3: 启动Frida SSL绕过
        frida_process = start_frida_ssl_bypass_simple()
        if not frida_process:
            log("Frida SSL绕过启动失败", "ERROR")
            return False
        
        # 步骤4: 等待系统稳定
        log("⏳ 等待系统稳定...")
        time.sleep(15)
        
        # 步骤5: 测试HTTPS请求
        log("=" * 50)
        log("🔍 开始测试HTTPS请求")
        log("=" * 50)
        
        test_https_requests()
        
        # 步骤6: 等待网络请求处理
        log("⏳ 等待网络请求处理...")
        time.sleep(20)
        
        # 步骤7: 分析结果
        success = analyze_captured_traffic()
        
        if success:
            log("🎉 简化SSL绕过测试完全成功！", "SUCCESS")
            log("✅ 系统现在可以完全捕获HTTPS流量！")
            return True
        else:
            log("⚠️  SSL绕过测试未完全成功", "WARNING")
            return False
            
    except KeyboardInterrupt:
        log("用户中断测试")
        return False
    except Exception as e:
        log(f"测试异常: {e}", "ERROR")
        return False
    finally:
        # 清理资源
        log("🧹 清理测试资源...")
        
        if mitmproxy_process and mitmproxy_process.poll() is None:
            mitmproxy_process.terminate()
        
        if frida_process and frida_process.poll() is None:
            frida_process.terminate()
        
        # 清除代理设置
        run_adb("shell settings delete global http_proxy")
        
        # 停止frida-server
        run_adb("shell pkill frida-server")
        
        log("✅ 清理完成")

def main():
    """主函数"""
    try:
        success = run_simple_ssl_bypass_test()
        
        if success:
            print("\n🎯 简化SSL绕过测试成功！")
            print("💡 HTTPS流量捕获功能正常！")
            print("🔧 SSL绕过系统工作正常！")
            print("📊 现在可以进行完整的金融APP测试！")
        else:
            print("\n🔧 测试需要进一步调试")
            print("💡 请检查错误信息")
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断")
    except Exception as e:
        print(f"\n❌ 异常: {e}")

if __name__ == "__main__":
    main()
