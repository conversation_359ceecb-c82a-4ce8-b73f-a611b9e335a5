#!/usr/bin/env python3
"""
真实本地模拟器动态分析测试
使用真实的Android模拟器进行动态分析测试
"""
import asyncio
import logging
import sys
from pathlib import Path
import time
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.local_emulator_manager import LocalEmulatorManager
from src.services.local_dynamic_analyzer import LocalDynamicAnalyzer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class RealDynamicAnalysisTester:
    """真实动态分析测试类"""
    
    def __init__(self):
        self.test_results = {}
        self.test_apk = project_root / "apk" / "com.android.vending_289.com.apk"
        
        # 确保测试APK存在
        if not self.test_apk.exists():
            logger.error(f"Test APK not found: {self.test_apk}")
            raise FileNotFoundError(f"Test APK not found: {self.test_apk}")
        
        logger.info(f"Using test APK: {self.test_apk}")
    
    async def run_real_tests(self):
        """运行真实动态分析测试"""
        logger.info("🚀 Starting Real Dynamic Analysis Tests")
        print("=" * 80)
        
        tests = [
            ("Test Emulator Connection", self.test_emulator_connection),
            ("Test APK Installation", self.test_apk_installation),
            ("Test Basic UI Interaction", self.test_basic_ui_interaction),
            ("Test Short Dynamic Analysis", self.test_short_dynamic_analysis),
        ]
        
        for test_name, test_func in tests:
            try:
                logger.info(f"Running: {test_name}")
                result = await test_func()
                self.test_results[test_name] = {
                    'status': 'PASS' if result else 'FAIL',
                    'details': result if isinstance(result, dict) else {}
                }
                status = "✅ PASS" if result else "❌ FAIL"
                logger.info(f"{test_name}: {status}")
            except Exception as e:
                logger.error(f"{test_name}: ❌ ERROR - {e}")
                self.test_results[test_name] = {
                    'status': 'ERROR',
                    'error': str(e)
                }
            
            print("-" * 40)
        
        # 生成测试报告
        self.generate_test_report()
    
    async def test_emulator_connection(self) -> dict:
        """测试模拟器连接"""
        try:
            logger.info("Testing emulator connection...")
            
            # 创建模拟器管理器
            emulator_manager = LocalEmulatorManager(max_instances=1)
            
            # 检查是否有运行中的模拟器
            running_instances = emulator_manager.get_running_instances()
            if running_instances:
                logger.info(f"Found running instances: {list(running_instances.keys())}")
                return {
                    'running_instances': len(running_instances),
                    'instance_names': list(running_instances.keys())
                }
            
            # 尝试获取或创建实例
            instance = await emulator_manager.get_or_create_instance("test_avd", timeout=60)
            if instance:
                logger.info(f"Successfully connected to emulator: {instance.name}")
                return {
                    'instance_name': instance.name,
                    'device_id': instance.adb_device_id,
                    'status': instance.status.value
                }
            else:
                logger.error("Failed to connect to emulator")
                return False
                
        except Exception as e:
            logger.error(f"Emulator connection test failed: {e}")
            return False
    
    async def test_apk_installation(self) -> dict:
        """测试APK安装"""
        try:
            logger.info("Testing APK installation...")
            
            # 创建动态分析器
            analyzer = LocalDynamicAnalyzer()
            
            # 获取模拟器实例
            emulator_instance = await analyzer.emulator_manager.get_or_create_instance("test_avd", timeout=30)
            if not emulator_instance:
                logger.error("No emulator instance available")
                return False
            
            # 提取包名
            package_name = await analyzer._extract_package_name(str(self.test_apk))
            if not package_name:
                logger.error("Failed to extract package name")
                return False
            
            # 安装APK
            success = await analyzer._install_apk(str(self.test_apk), emulator_instance)
            
            result = {
                'package_name': package_name,
                'installation_success': success,
                'emulator_device': emulator_instance.adb_device_id
            }
            
            # 清理：卸载APK
            if success:
                try:
                    await analyzer._uninstall_apk(package_name, emulator_instance)
                    result['cleanup_success'] = True
                except Exception as e:
                    logger.warning(f"Failed to uninstall APK: {e}")
                    result['cleanup_success'] = False
            
            return result
            
        except Exception as e:
            logger.error(f"APK installation test failed: {e}")
            return False
    
    async def test_basic_ui_interaction(self) -> dict:
        """测试基本UI交互"""
        try:
            logger.info("Testing basic UI interaction...")
            
            # 创建动态分析器
            analyzer = LocalDynamicAnalyzer()
            
            # 获取模拟器实例
            emulator_instance = await analyzer.emulator_manager.get_or_create_instance("test_avd", timeout=30)
            if not emulator_instance:
                logger.error("No emulator instance available")
                return False
            
            # 测试基本UI操作
            ui_actions = await analyzer.ui_automator.explore_ui(
                emulator_instance, 
                "com.android.settings",  # 使用系统设置应用
                timeout=30  # 短时间测试
            )
            
            interaction_summary = analyzer.ui_automator.get_interaction_summary()
            
            return {
                'ui_actions_executed': len(ui_actions),
                'interaction_summary': interaction_summary,
                'test_duration': 30
            }
            
        except Exception as e:
            logger.error(f"UI interaction test failed: {e}")
            return False
    
    async def test_short_dynamic_analysis(self) -> dict:
        """测试短时间动态分析"""
        try:
            logger.info("Testing short dynamic analysis...")
            
            # 创建动态分析器
            analyzer = LocalDynamicAnalyzer()
            
            # 执行短时间分析（60秒）
            start_time = time.time()
            result = await analyzer.analyze(str(self.test_apk), timeout=60)
            duration = time.time() - start_time
            
            return {
                'analysis_duration': duration,
                'analysis_success': result.error is None,
                'dynamic_urls_found': len(result.dynamic_urls),
                'network_requests_found': len(result.network_requests) if result.network_requests else 0,
                'android_version': result.android_version,
                'error': result.error
            }
            
        except Exception as e:
            logger.error(f"Short dynamic analysis test failed: {e}")
            return False
    
    def generate_test_report(self):
        """生成测试报告"""
        logger.info("📊 Generating Real Test Report")
        print("=" * 80)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result['status'] == 'PASS')
        failed_tests = sum(1 for result in self.test_results.values() if result['status'] == 'FAIL')
        error_tests = sum(1 for result in self.test_results.values() if result['status'] == 'ERROR')
        
        print(f"📈 Real Test Summary:")
        print(f"   Total Tests: {total_tests}")
        print(f"   ✅ Passed: {passed_tests}")
        print(f"   ❌ Failed: {failed_tests}")
        print(f"   🚨 Errors: {error_tests}")
        print(f"   📊 Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        print()
        
        print("📋 Detailed Results:")
        for test_name, result in self.test_results.items():
            status_icon = {
                'PASS': '✅',
                'FAIL': '❌',
                'ERROR': '🚨'
            }.get(result['status'], '❓')
            
            print(f"   {status_icon} {test_name}: {result['status']}")
            
            if result['status'] == 'ERROR' and 'error' in result:
                print(f"      Error: {result['error']}")
            elif 'details' in result and result['details']:
                if isinstance(result['details'], dict):
                    for key, value in result['details'].items():
                        if isinstance(value, (list, dict)):
                            print(f"      {key}: {type(value).__name__} with {len(value)} items")
                        else:
                            print(f"      {key}: {value}")
        
        print()
        
        # 保存测试报告到文件
        report_file = project_root / "real_dynamic_test_report.json"
        try:
            with open(report_file, 'w') as f:
                json.dump({
                    'timestamp': time.time(),
                    'test_type': 'real_emulator',
                    'summary': {
                        'total': total_tests,
                        'passed': passed_tests,
                        'failed': failed_tests,
                        'errors': error_tests,
                        'success_rate': (passed_tests/total_tests)*100
                    },
                    'results': self.test_results
                }, f, indent=2)
            
            logger.info(f"📄 Real test report saved to: {report_file}")
        except Exception as e:
            logger.error(f"Failed to save test report: {e}")
        
        # 总结
        if passed_tests == total_tests:
            print("🎉 All real tests passed! Local dynamic analysis is fully functional.")
        elif passed_tests > 0:
            print("⚠️  Some real tests passed. System partially functional.")
        else:
            print("🚨 All real tests failed. System needs attention.")
        
        print("=" * 80)


async def main():
    """主函数"""
    try:
        tester = RealDynamicAnalysisTester()
        await tester.run_real_tests()
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
    except Exception as e:
        logger.error(f"Test execution failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
