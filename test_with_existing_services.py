#!/usr/bin/env python3
"""
使用现有服务进行测试
不重新启动任何服务，直接使用当前运行的配置
"""

import subprocess
import sys
import time
import json
from pathlib import Path

def log(message):
    timestamp = time.strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def run_adb(cmd):
    full_cmd = f"source android_env.sh && adb {cmd}"
    result = subprocess.run(full_cmd, shell=True, capture_output=True, text=True)
    return result.returncode, result.stdout.strip(), result.stderr.strip()

def get_current_request_count():
    """获取当前请求数量"""
    try:
        capture_file = Path("mitm-logs/realtime_capture.json")
        if capture_file.exists():
            with open(capture_file, 'r') as f:
                data = json.load(f)
            return len(data) if isinstance(data, list) else 0
        return 0
    except:
        return 0

def main():
    log("🔍 使用现有服务进行测试")
    log("⚠️  不重新启动任何服务，保持当前配置")
    
    # 1. 检查当前请求数量
    initial_count = get_current_request_count()
    log(f"初始请求数量: {initial_count}")
    
    # 2. 启动金融应用并进行操作
    log("启动金融应用...")
    package = "com.yjzx.yjzx2017"
    
    run_adb(f"shell am force-stop {package}")
    time.sleep(2)
    
    returncode, stdout, stderr = run_adb(f"shell am start -n {package}/.controller.activity.splash.SplashActivity")
    if returncode == 0:
        log("✅ 应用启动成功")
        time.sleep(8)
    else:
        log("❌ 应用启动失败")
        return
    
    # 3. 执行一系列操作来触发网络请求
    log("执行应用操作...")
    
    operations = [
        ("点击继续", "shell input tap 540 960"),
        ("等待", 5),
        ("下拉刷新1", "shell input swipe 540 400 540 800"),
        ("等待", 8),
        ("点击中心", "shell input tap 540 960"),
        ("等待", 3),
        ("下拉刷新2", "shell input swipe 540 400 540 800"),
        ("等待", 8),
        ("点击右上角", "shell input tap 700 300"),
        ("等待", 3),
        ("点击底部导航", "shell input tap 200 1500"),
        ("等待", 5),
        ("再次刷新", "shell input swipe 540 400 540 800"),
        ("等待", 8)
    ]
    
    for i, (op_name, op_cmd) in enumerate(operations):
        log(f"  {i+1}. {op_name}")
        
        if op_name == "等待":
            time.sleep(op_cmd)
        else:
            run_adb(op_cmd)
            time.sleep(2)
        
        # 每隔几个操作检查一次请求数量
        if i % 4 == 3:
            current_count = get_current_request_count()
            new_requests = current_count - initial_count
            log(f"     当前新增请求: {new_requests}")
    
    # 4. 使用浏览器访问一些网站
    log("使用浏览器访问网站...")
    
    test_urls = [
        "http://httpbin.org/get",
        "http://www.baidu.com",
        "https://httpbin.org/get",
        "https://www.baidu.com"
    ]
    
    for url in test_urls:
        log(f"访问: {url}")
        run_adb(f"shell am start -a android.intent.action.VIEW -d {url}")
        time.sleep(8)
        
        current_count = get_current_request_count()
        new_requests = current_count - initial_count
        log(f"  当前新增请求: {new_requests}")
    
    # 5. 等待所有请求处理完成
    log("等待所有请求处理完成...")
    time.sleep(15)
    
    # 6. 分析结果
    final_count = get_current_request_count()
    total_new_requests = final_count - initial_count
    
    log("=" * 50)
    log("📊 测试结果")
    log("=" * 50)
    log(f"初始请求数: {initial_count}")
    log(f"最终请求数: {final_count}")
    log(f"新增请求数: {total_new_requests}")
    
    if total_new_requests > 0:
        log("✅ 成功捕获到新的网络请求！")
        
        # 显示最新的请求
        try:
            capture_file = Path("mitm-logs/realtime_capture.json")
            with open(capture_file, 'r') as f:
                data = json.load(f)
            
            if len(data) > initial_count:
                log("🔍 新捕获的请求:")
                new_requests = data[initial_count:]
                
                https_count = 0
                http_count = 0
                
                for i, req in enumerate(new_requests[:10], 1):  # 显示最多10个
                    url = req.get('url', 'N/A')
                    method = req.get('method', 'N/A')
                    host = req.get('host', 'N/A')
                    
                    if url.startswith('https://'):
                        https_count += 1
                        log(f"  {i}. [HTTPS] {method} {host}")
                    elif url.startswith('http://'):
                        http_count += 1
                        log(f"  {i}. [HTTP] {method} {host}")
                    else:
                        log(f"  {i}. [OTHER] {method} {url}")
                
                log(f"📊 新请求统计:")
                log(f"   HTTP请求: {http_count}")
                log(f"   HTTPS请求: {https_count}")
                log(f"   其他请求: {total_new_requests - http_count - https_count}")
                
                if https_count > 0:
                    log("🎉 成功捕获HTTPS请求！SSL绕过工作正常！")
                elif http_count > 0:
                    log("✅ 成功捕获HTTP请求！网络代理工作正常！")
                    log("💡 HTTPS可能需要SSL绕过或CA证书配置")
                else:
                    log("⚠️  捕获到请求但协议未识别")
        
        except Exception as e:
            log(f"分析请求详情时出错: {e}")
    
    else:
        log("❌ 没有捕获到新的网络请求")
        log("💡 可能的原因:")
        log("   1. 应用没有发起网络请求")
        log("   2. 应用绕过了系统代理设置")
        log("   3. 网络请求被阻止或过滤")
    
    log("=" * 50)
    log("🎯 测试完成")

if __name__ == "__main__":
    main()
