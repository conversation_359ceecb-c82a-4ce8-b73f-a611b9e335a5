#!/usr/bin/env python3
"""
准确测试2个Activity的网络抓包
修复网络活动检测逻辑
"""

import subprocess
import sys
import time
import json
from pathlib import Path
from datetime import datetime

class AccurateActivity2Tester:
    def __init__(self):
        self.package = "com.yjzx.yjzx2017"
        self.test_results = {
            "start_time": datetime.now().isoformat(),
            "package": self.package,
            "method": "broadcast_trigger",
            "activities_tested": [],
            "network_captures": [],
            "summary": {
                "total_tested": 0,
                "successful_launches": 0,
                "failed_launches": 0,
                "network_requests_captured": 0
            }
        }
        
    def run_adb(self, cmd, timeout=30):
        """执行adb命令"""
        full_cmd = f"source android_env.sh && adb {cmd}"
        try:
            result = subprocess.run(full_cmd, shell=True, 
                                  capture_output=True, text=True, 
                                  timeout=timeout, check=True)
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            return f"ERROR: {e.stderr}"
        except subprocess.TimeoutExpired:
            return "ERROR: Timeout"
    
    def get_network_requests_count(self):
        """准确获取网络请求数量"""
        try:
            # 检查实时捕获文件
            realtime_file = Path("mitm-logs/realtime_capture.json")
            if realtime_file.exists():
                with open(realtime_file, 'r') as f:
                    data = json.load(f)
                    if isinstance(data, list):
                        return len(data)
            return 0
        except Exception as e:
            print(f"⚠️  读取网络捕获文件失败: {e}")
            return 0
    
    def get_network_requests_details(self):
        """获取网络请求详情"""
        try:
            realtime_file = Path("mitm-logs/realtime_capture.json")
            if realtime_file.exists():
                with open(realtime_file, 'r') as f:
                    data = json.load(f)
                    if isinstance(data, list):
                        return data
            return []
        except Exception as e:
            print(f"⚠️  读取网络请求详情失败: {e}")
            return []
    
    def launch_activity_broadcast(self, activity, index):
        """使用广播方法启动单个Activity"""
        print(f"\n🚀 [{index}/2] 广播启动Activity: {activity}")
        print("-" * 80)
        
        activity_result = {
            "name": activity,
            "index": index,
            "method": "broadcast_trigger",
            "start_time": datetime.now().isoformat(),
            "launch_success": False,
            "network_before": 0,
            "network_after": 0,
            "network_increase": 0,
            "error_message": None,
            "captured_requests": []
        }
        
        # 记录启动前的网络活动
        activity_result["network_before"] = self.get_network_requests_count()
        print(f"📡 启动前网络请求数: {activity_result['network_before']}")
        
        # 使用广播方法启动Activity
        cmd = f"shell am broadcast -a android.intent.action.MAIN -n {self.package}/{activity}"
        result = self.run_adb(cmd)
        
        if result and "ERROR" not in result and "Exception" not in result:
            print(f"✅ 广播发送成功")
            activity_result["launch_success"] = True
            self.test_results["summary"]["successful_launches"] += 1
            
            # 等待Activity加载和可能的网络请求
            print("⏳ 等待网络活动...")
            time.sleep(8)  # 增加等待时间
            
            # 记录启动后的网络活动
            activity_result["network_after"] = self.get_network_requests_count()
            activity_result["network_increase"] = activity_result["network_after"] - activity_result["network_before"]
            
            print(f"📡 启动后网络请求数: {activity_result['network_after']}")
            
            if activity_result["network_increase"] > 0:
                print(f"🎉 检测到网络活动: +{activity_result['network_increase']} 个请求")
                self.test_results["summary"]["network_requests_captured"] += activity_result["network_increase"]
                
                # 获取新增的网络请求详情
                all_requests = self.get_network_requests_details()
                if len(all_requests) >= activity_result["network_after"]:
                    new_requests = all_requests[-activity_result["network_increase"]:]
                    activity_result["captured_requests"] = new_requests
                    
                    print("📋 新捕获的网络请求:")
                    for i, req in enumerate(new_requests, 1):
                        print(f"   {i}. {req.get('method', 'N/A')} {req.get('url', 'N/A')}")
                        print(f"      状态: {req.get('status_code', 'N/A')} {req.get('status_message', 'N/A')}")
                        if 'response_time' in req:
                            print(f"      响应时间: {req['response_time']:.3f}s")
                
            else:
                print("📡 未检测到新的网络活动")
            
        else:
            print(f"❌ 广播发送失败")
            activity_result["error_message"] = result
            self.test_results["summary"]["failed_launches"] += 1
        
        activity_result["end_time"] = datetime.now().isoformat()
        self.test_results["activities_tested"].append(activity_result)
        
        # 短暂休息
        time.sleep(3)
        
        return activity_result["launch_success"]
    
    def run_test(self):
        """运行2个Activity测试"""
        print("🎯 开始2个Activity广播网络抓包测试")
        print("📡 使用广播触发方法 + 准确的网络检测")
        print("=" * 80)
        
        # 测试的Activity
        test_activities = [
            "com.yjzx.yjzx2017.controller.activity.MainActivity",
            "com.yjzx.yjzx2017.controller.login.activity.LoginActivity"
        ]
        
        self.test_results["summary"]["total_tested"] = len(test_activities)
        
        print(f"📊 将测试 {len(test_activities)} 个Activity")
        print(f"🌐 网络抓包已启动")
        print("\n开始测试...")
        
        # 记录初始网络状态
        initial_network_count = self.get_network_requests_count()
        print(f"📡 初始网络请求数: {initial_network_count}")
        
        # 显示已有的网络请求
        if initial_network_count > 0:
            existing_requests = self.get_network_requests_details()
            print(f"📋 已有的网络请求:")
            for i, req in enumerate(existing_requests, 1):
                print(f"   {i}. {req.get('method', 'N/A')} {req.get('url', 'N/A')}")
        
        # 测试每个Activity
        for i, activity in enumerate(test_activities, 1):
            try:
                self.launch_activity_broadcast(activity, i)
                
            except KeyboardInterrupt:
                print(f"\n⚠️  用户中断测试，已完成 {i-1}/2 个Activity")
                break
            except Exception as e:
                print(f"❌ 测试异常: {e}")
                continue
        
        # 记录最终网络状态
        final_network_count = self.get_network_requests_count()
        total_network_increase = final_network_count - initial_network_count
        
        print(f"\n📡 最终网络请求数: {final_network_count}")
        print(f"📡 总网络增长: +{total_network_increase} 个请求")
        
        # 完成测试
        self.test_results["end_time"] = datetime.now().isoformat()
        self.test_results["total_network_increase"] = total_network_increase
        self.generate_report()
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "=" * 80)
        print("📊 2个Activity广播网络抓包测试报告")
        print("=" * 80)
        
        summary = self.test_results["summary"]
        
        print(f"📱 测试应用: {self.package}")
        print(f"🔧 启动方法: 广播触发 (android.intent.action.MAIN)")
        print(f"🕐 测试时间: {self.test_results['start_time']} - {self.test_results.get('end_time', '进行中')}")
        print(f"📋 测试Activity数: {summary['total_tested']}")
        print(f"✅ 启动成功: {summary['successful_launches']}")
        print(f"❌ 启动失败: {summary['failed_launches']}")
        print(f"📡 捕获网络请求: {summary['network_requests_captured']}")
        print(f"📈 总网络增长: +{self.test_results.get('total_network_increase', 0)} 个请求")
        
        if summary['total_tested'] > 0:
            success_rate = (summary['successful_launches'] / summary['total_tested']) * 100
            print(f"📊 启动成功率: {success_rate:.1f}%")
        
        # 保存详细报告
        report_file = f"activity_accurate_2_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, indent=2, ensure_ascii=False)
        
        print(f"📄 详细报告已保存: {report_file}")
        
        # 显示捕获的网络请求详情
        all_captured = []
        for activity in self.test_results["activities_tested"]:
            if activity.get("captured_requests"):
                all_captured.extend(activity["captured_requests"])
        
        if all_captured:
            print(f"\n🌐 捕获的网络请求详情:")
            for i, req in enumerate(all_captured, 1):
                print(f"   {i}. {req.get('method', 'N/A')} {req.get('url', 'N/A')}")
                print(f"      主机: {req.get('host', 'N/A')}")
                print(f"      状态: {req.get('status_code', 'N/A')} {req.get('status_message', 'N/A')}")
                if 'response_time' in req:
                    print(f"      响应时间: {req['response_time']:.3f}s")
                if 'user_agent' in req:
                    print(f"      User-Agent: {req['user_agent']}")
                print()

def main():
    print("🚀 易金在线APK - 2个Activity准确网络抓包测试")
    print("🔧 使用广播触发方法 + 准确的网络检测逻辑")
    print("=" * 80)
    
    tester = AccurateActivity2Tester()
    
    try:
        tester.run_test()
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
        tester.generate_report()
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        tester.generate_report()

if __name__ == "__main__":
    main()
