#!/usr/bin/env python3
"""
交互式Frida解决方案
使用交互式模式绕过权限问题
"""

import subprocess
import sys
import time
import threading
from pathlib import Path

def log(message, level="INFO"):
    """日志输出"""
    timestamp = time.strftime("%H:%M:%S")
    if level == "SUCCESS":
        print(f"🎉 [{timestamp}] {message}")
    elif level == "ERROR":
        print(f"❌ [{timestamp}] {message}")
    elif level == "WARNING":
        print(f"⚠️  [{timestamp}] {message}")
    else:
        print(f"📋 [{timestamp}] {message}")

def run_adb(cmd, timeout=30):
    """执行adb命令"""
    full_cmd = f"source android_env.sh && adb {cmd}"
    try:
        result = subprocess.run(full_cmd, shell=True, 
                              capture_output=True, text=True, 
                              timeout=timeout, check=True)
        return result.returncode, result.stdout.strip(), result.stderr.strip()
    except subprocess.CalledProcessError as e:
        return e.returncode, "", e.stderr
    except subprocess.TimeoutExpired:
        return -1, "", "Timeout"

def create_frida_script_for_interactive():
    """创建用于交互式模式的Frida脚本"""
    log("创建交互式Frida脚本...")
    
    script_content = '''
// 交互式SSL绕过脚本
console.log("\\n🚀 开始SSL绕过...");

Java.perform(function() {
    console.log("✅ Java.perform 启动成功");
    
    try {
        // Hook SSLContext
        var SSLContext = Java.use("javax.net.ssl.SSLContext");
        SSLContext.init.overload("[Ljavax.net.ssl.KeyManager;", "[Ljavax.net.ssl.TrustManager;", "java.security.SecureRandom").implementation = function(keyManagers, trustManagers, secureRandom) {
            console.log("🔓 SSLContext.init() 已绕过");
            this.init(keyManagers, null, secureRandom);
        };
        console.log("✅ SSLContext hook 成功");
        
        // Hook HostnameVerifier
        var HostnameVerifier = Java.use("javax.net.ssl.HostnameVerifier");
        HostnameVerifier.verify.overload("java.lang.String", "javax.net.ssl.SSLSession").implementation = function(hostname, session) {
            console.log("🔓 HostnameVerifier 已绕过: " + hostname);
            return true;
        };
        console.log("✅ HostnameVerifier hook 成功");
        
        // Hook X509TrustManager
        var X509TrustManager = Java.use("javax.net.ssl.X509TrustManager");
        X509TrustManager.checkClientTrusted.implementation = function(chain, authType) {
            console.log("🔓 X509TrustManager.checkClientTrusted 已绕过");
        };
        X509TrustManager.checkServerTrusted.implementation = function(chain, authType) {
            console.log("🔓 X509TrustManager.checkServerTrusted 已绕过");
        };
        X509TrustManager.getAcceptedIssuers.implementation = function() {
            console.log("🔓 X509TrustManager.getAcceptedIssuers 已绕过");
            return [];
        };
        console.log("✅ X509TrustManager hook 成功");
        
        console.log("\\n🎉 SSL绕过初始化完成！");
        console.log("🔒 HTTPS流量现在可以被捕获");
        
    } catch (e) {
        console.log("❌ SSL绕过错误: " + e);
    }
});

console.log("📋 SSL绕过脚本已加载，等待Java环境...");
'''
    
    script_file = Path("interactive_ssl_bypass.js")
    with open(script_file, 'w') as f:
        f.write(script_content)
    
    log("✅ 交互式脚本已创建")
    return script_file

def setup_environment():
    """设置环境"""
    log("设置Frida环境...")
    
    # 确保root权限
    subprocess.run("adb root", shell=True, capture_output=True)
    time.sleep(2)
    
    # 启动frida-server
    run_adb("shell pkill frida-server")
    time.sleep(2)
    subprocess.Popen("source android_env.sh && adb shell '/data/local/tmp/frida-server' &", shell=True)
    time.sleep(5)
    
    # 验证frida-server
    returncode, stdout, stderr = run_adb("shell ps | grep frida-server")
    if "frida-server" not in stdout:
        log("❌ frida-server启动失败", "ERROR")
        return False
    
    log("✅ frida-server运行正常")
    
    # 准备应用
    package = "com.yjzx.yjzx2017"
    run_adb(f"shell am force-stop {package}")
    time.sleep(2)
    
    returncode, stdout, stderr = run_adb(f"shell am start -n {package}/.controller.activity.splash.SplashActivity")
    if returncode != 0:
        log(f"应用启动失败: {stderr}", "ERROR")
        return False
    
    log("✅ 应用启动成功")
    time.sleep(8)
    
    return True

def run_interactive_frida():
    """运行交互式Frida"""
    log("启动交互式Frida SSL绕过...")
    
    package = "com.yjzx.yjzx2017"
    
    # 创建交互式命令
    frida_commands = f'''
%load interactive_ssl_bypass.js
console.log("\\n💡 SSL绕过脚本已手动加载");
console.log("🔍 现在可以测试HTTPS请求");
console.log("⚠️  在另一个终端运行测试命令");
'''
    
    # 保存命令到文件
    commands_file = Path("frida_commands.txt")
    with open(commands_file, 'w') as f:
        f.write(frida_commands)
    
    log("🔧 启动交互式Frida会话...")
    log("💡 将自动加载SSL绕过脚本")
    
    # 启动交互式Frida
    cmd = f"export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin && frida -U {package}"
    
    try:
        # 使用expect或类似工具自动输入命令
        full_cmd = f'''
export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin
echo "启动Frida交互式会话..."
(
echo "%load interactive_ssl_bypass.js"
sleep 2
echo "console.log('💡 SSL绕过脚本已手动加载');"
sleep 1
echo "console.log('🔍 现在可以测试HTTPS请求');"
sleep 1
cat
) | frida -U {package}
'''
        
        process = subprocess.Popen(
            full_cmd, shell=True,
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        log("⏳ 等待Frida连接...")
        time.sleep(10)
        
        if process.poll() is None:
            log("✅ 交互式Frida启动成功！", "SUCCESS")
            
            # 发送脚本加载命令
            try:
                process.stdin.write("%load interactive_ssl_bypass.js\n")
                process.stdin.flush()
                time.sleep(3)
                
                process.stdin.write("console.log('🎉 SSL绕过脚本已加载！');\n")
                process.stdin.flush()
                time.sleep(2)
                
                log("✅ SSL绕过脚本已发送到Frida", "SUCCESS")
            except:
                log("⚠️  无法自动发送命令", "WARNING")
            
            # 启动测试
            test_thread = threading.Thread(target=test_ssl_bypass_in_background)
            test_thread.daemon = True
            test_thread.start()
            
            log("🎉 交互式SSL绕过系统运行中！", "SUCCESS")
            log("💡 系统正在后台测试SSL绕过效果")
            log("⚠️  按Ctrl+C停止系统")
            
            try:
                # 保持运行
                while process.poll() is None:
                    time.sleep(30)
                    log("💡 交互式SSL绕过仍在运行...")
            except KeyboardInterrupt:
                log("⚠️  用户中断，停止系统...")
                process.terminate()
            
            return True
        else:
            stdout, stderr = process.communicate()
            log("❌ 交互式Frida启动失败", "ERROR")
            
            if stdout:
                log("输出:")
                for line in stdout.split('\n')[:5]:
                    if line.strip():
                        log(f"   {line.strip()}")
            
            if stderr:
                log("错误:")
                for line in stderr.split('\n')[:3]:
                    if line.strip():
                        log(f"   {line.strip()}")
            
            return False
            
    except Exception as e:
        log(f"交互式Frida异常: {e}", "ERROR")
        return False

def test_ssl_bypass_in_background():
    """在后台测试SSL绕过效果"""
    time.sleep(15)  # 等待SSL绕过脚本加载
    
    log("🔍 开始后台测试SSL绕过效果...")
    
    # 触发HTTPS请求
    test_urls = [
        "https://httpbin.org/get",
        "https://www.baidu.com",
        "https://api.github.com"
    ]
    
    for i, url in enumerate(test_urls, 1):
        log(f"测试 {i}/{len(test_urls)}: {url}")
        run_adb(f"shell am start -a android.intent.action.VIEW -d {url}")
        time.sleep(8)
    
    # 应用内操作
    log("执行应用内操作...")
    actions = [
        "shell input tap 540 960",
        "shell input tap 400 800",
        "shell input swipe 540 800 540 400",
        "shell input keyevent 4"
    ]
    
    for action in actions:
        run_adb(action)
        time.sleep(3)
    
    log("✅ 后台SSL绕过测试完成")

def create_manual_instructions():
    """创建手动操作说明"""
    log("创建手动操作说明...")
    
    instructions = '''
# 🎯 交互式Frida SSL绕过手动操作指南

## 当前状态
✅ 环境已准备完成
✅ 交互式脚本已创建
✅ frida-server正在运行
✅ 目标应用正在运行

## 🔧 手动执行步骤

### 步骤1: 启动交互式Frida
```bash
export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin
frida -U com.yjzx.yjzx2017
```

### 步骤2: 在Frida控制台中加载脚本
```javascript
%load interactive_ssl_bypass.js
```

### 步骤3: 验证脚本加载
看到以下输出说明成功：
- "✅ Java.perform 启动成功"
- "✅ SSLContext hook 成功"
- "✅ HostnameVerifier hook 成功"
- "🎉 SSL绕过初始化完成！"

### 步骤4: 测试SSL绕过（另一个终端）
```bash
# 启动mitmproxy
mitmdump -s mitm-scripts/capture.py --listen-port 8080

# 设置代理
adb shell settings put global http_proxy *************:8080

# 触发HTTPS请求
adb shell am start -a android.intent.action.VIEW -d https://httpbin.org/get
adb shell am start -a android.intent.action.VIEW -d https://www.baidu.com

# 检查捕获结果
cat mitm-logs/realtime_capture.json
```

## 🎉 成功指标
- Frida控制台显示SSL hook成功信息
- mitmproxy捕获到HTTPS请求
- realtime_capture.json包含HTTPS流量数据

## 💡 关键优势
- 绕过所有权限问题
- 交互式调试和验证
- 实时查看SSL绕过效果
- 完全手动控制流程
'''
    
    with open("interactive_frida_manual.md", 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    log("✅ 手动操作说明已保存")

def main():
    """主函数"""
    log("🚀 交互式Frida SSL绕过解决方案")
    log("🔧 使用交互式模式绕过权限问题")
    log("=" * 60)
    
    try:
        # 创建脚本
        create_frida_script_for_interactive()
        
        # 设置环境
        if not setup_environment():
            log("环境设置失败", "ERROR")
            return False
        
        # 创建手动说明
        create_manual_instructions()
        
        # 运行交互式Frida
        log("=" * 50)
        log("🔍 启动交互式Frida SSL绕过")
        log("=" * 50)
        
        success = run_interactive_frida()
        
        if success:
            log("🎉 交互式SSL绕过成功！", "SUCCESS")
            return True
        else:
            log("❌ 交互式模式失败", "ERROR")
            log("💡 请查看手动操作指南: interactive_frida_manual.md")
            return False
        
    except KeyboardInterrupt:
        log("用户中断")
        return False
    except Exception as e:
        log(f"系统异常: {e}", "ERROR")
        return False
    finally:
        # 清理
        log("清理系统资源...")
        run_adb("shell pkill frida-server")

if __name__ == "__main__":
    try:
        success = main()
        
        if success:
            print("\n🎯 交互式Frida解决方案成功！")
            print("💡 SSL绕过现在可以通过交互式模式工作！")
            print("🔧 权限问题已通过交互式方法解决！")
        else:
            print("\n🔧 请使用手动方法")
            print("💡 查看 interactive_frida_manual.md 获取详细指导")
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断")
    except Exception as e:
        print(f"\n❌ 异常: {e}")
