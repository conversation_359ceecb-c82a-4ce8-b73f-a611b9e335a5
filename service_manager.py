#!/usr/bin/env python3
"""
服务管理器
检查和维持模拟器、mitmproxy、frida的运行状态
避免每次都重新启动
"""

import subprocess
import sys
import time
import json
from pathlib import Path
import psutil

def log(message, level="INFO"):
    """日志输出"""
    timestamp = time.strftime("%H:%M:%S")
    if level == "SUCCESS":
        print(f"🎉 [{timestamp}] {message}")
    elif level == "ERROR":
        print(f"❌ [{timestamp}] {message}")
    elif level == "WARNING":
        print(f"⚠️  [{timestamp}] {message}")
    else:
        print(f"📋 [{timestamp}] {message}")

def run_adb(cmd, timeout=10):
    """执行adb命令"""
    full_cmd = f"source android_env.sh && adb {cmd}"
    try:
        result = subprocess.run(full_cmd, shell=True, 
                              capture_output=True, text=True, 
                              timeout=timeout, check=True)
        return result.returncode, result.stdout.strip(), result.stderr.strip()
    except subprocess.CalledProcessError as e:
        return e.returncode, "", e.stderr
    except subprocess.TimeoutExpired:
        return -1, "", "Timeout"

def check_emulator_status():
    """检查模拟器状态"""
    log("检查模拟器状态...")
    
    # 检查adb设备
    returncode, stdout, stderr = run_adb("devices")
    
    if returncode == 0 and "emulator-5554" in stdout and "device" in stdout:
        log("✅ 模拟器正在运行", "SUCCESS")
        return True
    else:
        log("❌ 模拟器未运行", "ERROR")
        return False

def start_emulator():
    """启动模拟器"""
    log("启动模拟器...")
    
    # 检查是否已经有模拟器进程
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if 'emulator' in proc.info['name'] and any('test_avd' in arg for arg in proc.info['cmdline']):
                log("模拟器进程已存在，等待启动完成...")
                # 等待模拟器完全启动
                for i in range(30):
                    if check_emulator_status():
                        return True
                    time.sleep(2)
                return False
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    # 启动新的模拟器
    cmd = "cd /Users/<USER>/Desktop/project/apk_detect && source android_env.sh && emulator -avd test_avd -no-audio -no-window"
    
    subprocess.Popen(cmd, shell=True, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
    
    log("等待模拟器启动...")
    for i in range(60):  # 等待最多2分钟
        time.sleep(2)
        if check_emulator_status():
            log("✅ 模拟器启动成功", "SUCCESS")
            return True
        if i % 10 == 0:
            log(f"等待中... ({i*2}秒)")
    
    log("❌ 模拟器启动超时", "ERROR")
    return False

def check_mitmproxy_status():
    """检查mitmproxy状态"""
    log("检查mitmproxy状态...")

    # 检查进程
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            name = proc.info.get('name', '')
            cmdline = proc.info.get('cmdline', [])

            if name and 'mitmdump' in name:
                # 检查端口是否监听
                try:
                    import socket
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    result = sock.connect_ex(('127.0.0.1', 8080))
                    sock.close()
                    if result == 0:
                        log("✅ mitmproxy正在运行", "SUCCESS")
                        return True
                except:
                    pass
            elif cmdline and any('mitmdump' in str(arg) for arg in cmdline):
                # 检查端口是否监听
                try:
                    import socket
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    result = sock.connect_ex(('127.0.0.1', 8080))
                    sock.close()
                    if result == 0:
                        log("✅ mitmproxy正在运行", "SUCCESS")
                        return True
                except:
                    pass
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue

    log("❌ mitmproxy未运行", "ERROR")
    return False

def start_mitmproxy():
    """启动mitmproxy"""
    log("启动mitmproxy...")
    
    # 确保目录存在
    Path("mitm-logs").mkdir(exist_ok=True)
    
    # 清空捕获文件
    capture_file = Path("mitm-logs/realtime_capture.json")
    with open(capture_file, 'w') as f:
        json.dump([], f)
    
    # 启动mitmproxy
    cmd = "/Users/<USER>/Library/Python/3.9/bin/mitmdump -s mitm-scripts/capture.py --listen-port 8080"
    
    subprocess.Popen(cmd, shell=True, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
    
    # 等待启动
    for i in range(15):
        time.sleep(1)
        if check_mitmproxy_status():
            log("✅ mitmproxy启动成功", "SUCCESS")
            return True
    
    log("❌ mitmproxy启动失败", "ERROR")
    return False

def check_frida_server_status():
    """检查frida-server状态"""
    log("检查frida-server状态...")
    
    if not check_emulator_status():
        log("模拟器未运行，无法检查frida-server", "ERROR")
        return False
    
    returncode, stdout, stderr = run_adb("shell ps | grep frida-server")
    
    if returncode == 0 and "frida-server" in stdout:
        log("✅ frida-server正在运行", "SUCCESS")
        return True
    else:
        log("❌ frida-server未运行", "ERROR")
        return False

def start_frida_server():
    """启动frida-server"""
    log("启动frida-server...")
    
    if not check_emulator_status():
        log("模拟器未运行，无法启动frida-server", "ERROR")
        return False
    
    # 确保root权限
    subprocess.run("adb root", shell=True, capture_output=True)
    time.sleep(2)
    
    # 推送frida-server（如果需要）
    run_adb("push frida-server /data/local/tmp/frida-server")
    run_adb("shell chmod 755 /data/local/tmp/frida-server")
    
    # 启动frida-server
    subprocess.Popen("source android_env.sh && adb shell '/data/local/tmp/frida-server' &", shell=True)
    
    # 等待启动
    for i in range(10):
        time.sleep(1)
        if check_frida_server_status():
            log("✅ frida-server启动成功", "SUCCESS")
            return True
    
    log("❌ frida-server启动失败", "ERROR")
    return False

def check_proxy_settings():
    """检查代理设置"""
    log("检查代理设置...")
    
    if not check_emulator_status():
        return False
    
    returncode, stdout, stderr = run_adb("shell settings get global http_proxy")
    
    if returncode == 0 and "10.0.2.2:8080" in stdout:
        log("✅ 代理设置正确", "SUCCESS")
        return True
    else:
        log("❌ 代理设置不正确", "ERROR")
        return False

def set_proxy():
    """设置代理"""
    log("设置代理...")
    
    if not check_emulator_status():
        return False
    
    returncode, stdout, stderr = run_adb("shell settings put global http_proxy 10.0.2.2:8080")
    
    if returncode == 0:
        log("✅ 代理设置成功", "SUCCESS")
        return True
    else:
        log("❌ 代理设置失败", "ERROR")
        return False

def check_all_services():
    """检查所有服务状态"""
    log("🔍 检查所有服务状态")
    log("=" * 50)
    
    services_status = {
        "emulator": check_emulator_status(),
        "mitmproxy": check_mitmproxy_status(),
        "frida_server": check_frida_server_status(),
        "proxy_settings": check_proxy_settings()
    }
    
    log("=" * 50)
    log("📊 服务状态总结:")
    for service, status in services_status.items():
        status_text = "✅ 运行中" if status else "❌ 未运行"
        log(f"   {service}: {status_text}")
    
    all_running = all(services_status.values())
    
    if all_running:
        log("🎉 所有服务都在正常运行！", "SUCCESS")
    else:
        log("⚠️  部分服务需要启动", "WARNING")
    
    return services_status

def start_all_services():
    """启动所有服务"""
    log("🚀 启动所有必要的服务")
    log("=" * 50)
    
    # 1. 启动模拟器
    if not check_emulator_status():
        if not start_emulator():
            log("模拟器启动失败，无法继续", "ERROR")
            return False
    
    # 2. 启动mitmproxy
    if not check_mitmproxy_status():
        if not start_mitmproxy():
            log("mitmproxy启动失败", "ERROR")
            return False
    
    # 3. 启动frida-server
    if not check_frida_server_status():
        if not start_frida_server():
            log("frida-server启动失败", "ERROR")
            return False
    
    # 4. 设置代理
    if not check_proxy_settings():
        if not set_proxy():
            log("代理设置失败", "ERROR")
            return False
    
    log("=" * 50)
    log("🎉 所有服务启动完成！", "SUCCESS")
    return True

def stop_all_services():
    """停止所有服务"""
    log("🛑 停止所有服务")
    log("=" * 50)
    
    # 停止mitmproxy
    subprocess.run("pkill -f mitmdump", shell=True, capture_output=True)
    
    # 停止frida-server
    if check_emulator_status():
        run_adb("shell pkill frida-server")
        run_adb("shell settings delete global http_proxy")
    
    # 停止模拟器
    subprocess.run("pkill -f emulator", shell=True, capture_output=True)
    
    log("✅ 所有服务已停止", "SUCCESS")

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法:")
        print("  python3 service_manager.py check    # 检查服务状态")
        print("  python3 service_manager.py start    # 启动所有服务")
        print("  python3 service_manager.py stop     # 停止所有服务")
        print("  python3 service_manager.py restart  # 重启所有服务")
        return
    
    command = sys.argv[1].lower()
    
    if command == "check":
        check_all_services()
    elif command == "start":
        start_all_services()
    elif command == "stop":
        stop_all_services()
    elif command == "restart":
        stop_all_services()
        time.sleep(5)
        start_all_services()
    else:
        print(f"未知命令: {command}")

if __name__ == "__main__":
    main()
