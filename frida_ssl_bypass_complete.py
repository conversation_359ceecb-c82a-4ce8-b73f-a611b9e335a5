#!/usr/bin/env python3
"""
完整的Frida SSL绕过自动化脚本
自动设置frida-server、运行SSL绕过脚本、测试HTTPS抓包
"""

import subprocess
import sys
import time
import json
from pathlib import Path
import threading
import signal

class FridaSSLBypass:
    def __init__(self):
        self.package = "com.yjzx.yjzx2017"
        self.frida_server_process = None
        self.frida_script_process = None
        
    def run_adb(self, cmd, timeout=30):
        """执行adb命令"""
        full_cmd = f"source android_env.sh && adb {cmd}"
        try:
            result = subprocess.run(full_cmd, shell=True, 
                                  capture_output=True, text=True, 
                                  timeout=timeout, check=True)
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            return f"ERROR: {e.stderr}"
        except subprocess.TimeoutExpired:
            return "ERROR: Timeout"
    
    def check_frida_installation(self):
        """检查Frida安装"""
        print("🔍 检查Frida安装状态...")
        
        try:
            result = subprocess.run("frida --version", shell=True, 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print(f"✅ Frida已安装: {result.stdout.strip()}")
                return True
            else:
                print("❌ Frida未安装")
                return False
        except Exception as e:
            print(f"❌ Frida检查失败: {e}")
            return False
    
    def download_frida_server(self):
        """下载frida-server"""
        print("📥 下载frida-server...")
        
        # 检查设备架构
        arch = self.run_adb("shell getprop ro.product.cpu.abi")
        print(f"📋 设备架构: {arch}")
        
        if "x86_64" in arch:
            server_arch = "x86_64"
        elif "x86" in arch:
            server_arch = "x86"
        elif "arm64" in arch:
            server_arch = "arm64"
        else:
            server_arch = "arm"
        
        # 获取Frida版本
        try:
            result = subprocess.run("python3 -c \"import frida; print(frida.__version__)\"", 
                                  shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                frida_version = result.stdout.strip()
                print(f"📋 Frida版本: {frida_version}")
            else:
                frida_version = "16.1.4"  # 默认版本
                print(f"📋 使用默认Frida版本: {frida_version}")
        except:
            frida_version = "16.1.4"
        
        # 下载URL
        download_url = f"https://github.com/frida/frida/releases/download/{frida_version}/frida-server-{frida_version}-android-{server_arch}.xz"
        
        print(f"📥 下载URL: {download_url}")
        print("⚠️  由于网络限制，请手动下载frida-server")
        print("📋 手动下载步骤:")
        print(f"   1. 访问: {download_url}")
        print("   2. 下载并解压文件")
        print("   3. 重命名为 frida-server")
        print("   4. 放在当前目录")
        
        # 检查是否已存在
        if Path("frida-server").exists():
            print("✅ 发现frida-server文件")
            return True
        
        # 尝试自动下载
        print("🔄 尝试自动下载...")
        try:
            # 下载压缩文件
            cmd = f"curl -L -o frida-server.xz '{download_url}'"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print("✅ 下载成功，解压中...")
                
                # 解压
                subprocess.run("xz -d frida-server.xz", shell=True)
                
                if Path("frida-server").exists():
                    print("✅ frida-server准备完成")
                    return True
            
            print("⚠️  自动下载失败，请手动下载")
            return False
            
        except Exception as e:
            print(f"⚠️  下载异常: {e}")
            return False
    
    def setup_frida_server(self):
        """设置frida-server"""
        print("🔧 设置frida-server...")
        
        # 推送到设备
        if not Path("frida-server").exists():
            print("❌ frida-server文件不存在")
            return False
        
        print("📱 推送frida-server到设备...")
        result = self.run_adb("push frida-server /data/local/tmp/frida-server")
        if "ERROR" in result:
            print(f"❌ 推送失败: {result}")
            return False
        
        # 设置权限
        print("🔧 设置执行权限...")
        result = self.run_adb("shell chmod 755 /data/local/tmp/frida-server")
        if "ERROR" in result:
            print(f"❌ 权限设置失败: {result}")
            return False
        
        print("✅ frida-server设置完成")
        return True
    
    def start_frida_server(self):
        """启动frida-server"""
        print("🚀 启动frida-server...")
        
        # 检查是否已经运行
        result = self.run_adb("shell ps | grep frida-server")
        if "frida-server" in result and "ERROR" not in result:
            print("✅ frida-server已在运行")
            return True
        
        # 启动frida-server
        print("🔄 启动frida-server进程...")
        try:
            # 使用后台进程启动
            cmd = "source android_env.sh && adb shell '/data/local/tmp/frida-server &'"
            self.frida_server_process = subprocess.Popen(cmd, shell=True)
            
            # 等待启动
            time.sleep(5)
            
            # 验证启动
            result = self.run_adb("shell ps | grep frida-server")
            if "frida-server" in result:
                print("✅ frida-server启动成功")
                return True
            else:
                print("❌ frida-server启动失败")
                return False
                
        except Exception as e:
            print(f"❌ frida-server启动异常: {e}")
            return False
    
    def run_ssl_bypass_script(self):
        """运行SSL绕过脚本"""
        print("🔧 运行SSL绕过脚本...")
        
        if not Path("ssl_bypass.js").exists():
            print("❌ SSL绕过脚本不存在")
            return False
        
        try:
            # 启动应用
            print("🚀 启动目标应用...")
            self.run_adb(f"shell am start -n {self.package}/.controller.activity.MainActivity")
            time.sleep(3)
            
            # 运行Frida脚本
            print("🔧 注入SSL绕过脚本...")
            cmd = f"frida -U {self.package} -l ssl_bypass.js --no-pause"
            
            self.frida_script_process = subprocess.Popen(
                cmd, shell=True, 
                stdout=subprocess.PIPE, 
                stderr=subprocess.PIPE,
                text=True
            )
            
            # 等待脚本加载
            time.sleep(5)
            
            if self.frida_script_process.poll() is None:
                print("✅ SSL绕过脚本运行中...")
                return True
            else:
                print("❌ SSL绕过脚本启动失败")
                return False
                
        except Exception as e:
            print(f"❌ SSL绕过脚本异常: {e}")
            return False
    
    def test_https_capture(self):
        """测试HTTPS抓包"""
        print("🔍 测试HTTPS抓包功能...")
        
        # 清空捕获文件
        realtime_file = Path("mitm-logs/realtime_capture.json")
        if realtime_file.exists():
            with open(realtime_file, 'w') as f:
                json.dump([], f)
        
        time.sleep(2)
        initial_count = 0
        
        try:
            with open(realtime_file, 'r') as f:
                data = json.load(f)
                initial_count = len(data) if isinstance(data, list) else 0
        except:
            initial_count = 0
        
        print(f"📡 初始网络请求数: {initial_count}")
        
        # 触发网络活动
        print("🌐 触发HTTPS网络请求...")
        
        test_actions = [
            ("启动主Activity", f"shell am broadcast -a android.intent.action.MAIN -n {self.package}/{self.package}.controller.activity.MainActivity"),
            ("访问HTTPS网站", "shell am start -a android.intent.action.VIEW -d https://httpbin.org/get"),
            ("启动登录Activity", f"shell am broadcast -a android.intent.action.MAIN -n {self.package}/{self.package}.controller.login.activity.LoginActivity"),
            ("模拟用户交互", "shell input tap 540 960"),
            ("访问Google", "shell am start -a android.intent.action.VIEW -d https://www.google.com")
        ]
        
        for action_name, cmd in test_actions:
            print(f"   🔄 {action_name}...")
            self.run_adb(cmd)
            time.sleep(3)
        
        # 等待网络请求
        print("⏳ 等待网络请求处理...")
        time.sleep(10)
        
        # 检查结果
        try:
            with open(realtime_file, 'r') as f:
                data = json.load(f)
                final_count = len(data) if isinstance(data, list) else 0
                new_requests = final_count - initial_count
                
                print(f"📡 最终网络请求数: {final_count}")
                print(f"📡 新增网络请求: {new_requests}")
                
                if new_requests > 0:
                    https_requests = [req for req in data if req.get('scheme') == 'https']
                    http_requests = [req for req in data if req.get('scheme') == 'http']
                    
                    print(f"📊 请求统计:")
                    print(f"   HTTPS请求: {len(https_requests)}")
                    print(f"   HTTP请求: {len(http_requests)}")
                    
                    if https_requests:
                        print("🎉 SSL绕过成功！成功捕获HTTPS请求！")
                        print("📋 HTTPS请求示例:")
                        for i, req in enumerate(https_requests[:3], 1):
                            print(f"   {i}. {req.get('method', 'N/A')} {req.get('url', 'N/A')}")
                            print(f"      状态: {req.get('status_code', 'N/A')}")
                        return True
                    else:
                        print("⚠️  只捕获到HTTP请求，SSL绕过可能未完全生效")
                        return False
                else:
                    print("❌ 没有捕获到新的网络请求")
                    return False
                    
        except Exception as e:
            print(f"❌ 检查捕获结果失败: {e}")
            return False
    
    def cleanup(self):
        """清理资源"""
        print("🧹 清理资源...")
        
        # 终止Frida脚本
        if self.frida_script_process and self.frida_script_process.poll() is None:
            print("🔄 终止Frida脚本...")
            self.frida_script_process.terminate()
            try:
                self.frida_script_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.frida_script_process.kill()
        
        # 终止frida-server
        print("🔄 终止frida-server...")
        self.run_adb("shell pkill frida-server")
        
        print("✅ 清理完成")
    
    def run_complete_bypass(self):
        """运行完整的SSL绕过流程"""
        print("🚀 Frida SSL绕过完整流程")
        print("🔧 自动化HTTPS证书绕过和网络抓包")
        print("=" * 60)
        
        try:
            # 步骤1: 检查Frida安装
            if not self.check_frida_installation():
                print("❌ 请先安装Frida: pip3 install frida-tools")
                return False
            
            # 步骤2: 下载frida-server
            if not self.download_frida_server():
                print("❌ frida-server准备失败")
                return False
            
            # 步骤3: 设置frida-server
            if not self.setup_frida_server():
                print("❌ frida-server设置失败")
                return False
            
            # 步骤4: 启动frida-server
            if not self.start_frida_server():
                print("❌ frida-server启动失败")
                return False
            
            # 步骤5: 运行SSL绕过脚本
            if not self.run_ssl_bypass_script():
                print("❌ SSL绕过脚本运行失败")
                return False
            
            # 步骤6: 测试HTTPS抓包
            print("\n" + "="*50)
            print("🔍 测试HTTPS抓包功能")
            print("="*50)
            
            success = self.test_https_capture()
            
            if success:
                print("\n🎉 Frida SSL绕过完全成功！")
                print("✅ HTTPS流量现在可以被mitmproxy完全捕获！")
                print("🔧 SSL证书验证已被完全绕过！")
                
                print("\n📋 接下来可以:")
                print("   1. 运行完整的网络分析测试")
                print("   2. 分析捕获的HTTPS流量")
                print("   3. 进行深度安全分析")
                
                return True
            else:
                print("\n⚠️  SSL绕过部分成功")
                print("🔧 可能需要进一步调试")
                return False
                
        except KeyboardInterrupt:
            print("\n⚠️  用户中断操作")
            return False
        except Exception as e:
            print(f"\n❌ SSL绕过异常: {e}")
            return False
        finally:
            # 清理资源
            self.cleanup()

def signal_handler(sig, frame):
    """信号处理器"""
    print("\n⚠️  接收到中断信号，正在清理...")
    sys.exit(0)

def main():
    # 设置信号处理
    signal.signal(signal.SIGINT, signal_handler)
    
    bypass = FridaSSLBypass()
    
    try:
        success = bypass.run_complete_bypass()
        
        if success:
            print("\n🎯 Frida SSL绕过成功完成！")
            print("💡 现在可以进行完整的HTTPS网络抓包分析了！")
        else:
            print("\n🔧 SSL绕过需要进一步调试")
            print("💡 请检查frida-server和脚本运行状态")
            
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
    finally:
        bypass.cleanup()

if __name__ == "__main__":
    main()
