#!/usr/bin/env python3
"""
最简化的Frida SSL绕过
忽略SELinux警告，专注于SSL绕过功能
"""

import subprocess
import sys
import time
import json
from pathlib import Path

def run_cmd(cmd, timeout=30):
    """执行命令，忽略SELinux警告"""
    try:
        result = subprocess.run(cmd, shell=True, 
                              capture_output=True, text=True, 
                              timeout=timeout)
        # 过滤掉SELinux警告
        stdout = result.stdout
        stderr = result.stderr
        
        # 移除SELinux相关的警告行
        if stdout:
            lines = stdout.split('\n')
            filtered_lines = [line for line in lines if 'SELinux' not in line and 'Permission denied' not in line]
            stdout = '\n'.join(filtered_lines)
        
        return result.returncode, stdout, stderr
    except subprocess.TimeoutExpired:
        return -1, "", "Timeout"

def setup_frida():
    """设置Frida环境"""
    print("🔧 设置Frida环境（忽略SELinux警告）...")
    
    # 检查设备
    returncode, stdout, stderr = run_cmd("source android_env.sh && adb devices")
    if "device" not in stdout:
        print("❌ 设备未连接")
        return False
    print("✅ 设备已连接")
    
    # 启动frida-server（忽略SELinux警告）
    print("🚀 启动frida-server...")
    run_cmd("source android_env.sh && adb shell pkill frida-server")
    time.sleep(2)
    
    # 后台启动frida-server
    subprocess.Popen("source android_env.sh && adb shell '/data/local/tmp/frida-server' 2>/dev/null &", shell=True)
    time.sleep(8)
    
    # 检查frida-server（忽略SELinux输出）
    returncode, stdout, stderr = run_cmd("source android_env.sh && adb shell ps | grep frida-server")
    if "frida-server" in stdout:
        print("✅ frida-server启动成功")
        return True
    else:
        print("❌ frida-server启动失败")
        return False

def run_frida_ssl_bypass():
    """运行Frida SSL绕过"""
    print("🔧 运行Frida SSL绕过...")
    
    package = "com.yjzx.yjzx2017"
    
    # 重启应用
    print("📱 重启目标应用...")
    run_cmd(f"source android_env.sh && adb shell am force-stop {package}")
    time.sleep(2)
    
    run_cmd(f"source android_env.sh && adb shell am start -n {package}/.controller.activity.splash.SplashActivity")
    time.sleep(8)
    
    # 检查应用是否运行
    returncode, stdout, stderr = run_cmd(f"source android_env.sh && adb shell ps | grep {package}")
    if package not in stdout:
        print("⚠️  应用可能未完全启动，但继续尝试...")
    else:
        print("✅ 应用正在运行")
    
    # 启动Frida SSL绕过
    print("🔧 启动Frida SSL绕过脚本...")
    cmd = f"export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin && frida -U {package} -l ssl_bypass.js 2>/dev/null"
    
    # 启动Frida进程
    process = subprocess.Popen(
        cmd, shell=True,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    
    print("⏳ 等待Frida脚本加载...")
    time.sleep(15)
    
    if process.poll() is None:
        print("✅ Frida SSL绕过脚本正在运行")
        return process
    else:
        stdout, stderr = process.communicate()
        print("❌ Frida脚本启动失败")
        # 只显示非SELinux相关的错误
        if stderr and 'SELinux' not in stderr:
            print(f"错误: {stderr}")
        return None

def test_https_capture():
    """测试HTTPS捕获"""
    print("🔍 测试HTTPS捕获...")
    
    # 清空捕获文件
    realtime_file = Path("mitm-logs/realtime_capture.json")
    if realtime_file.exists():
        with open(realtime_file, 'w') as f:
            json.dump([], f)
    
    time.sleep(3)
    
    # 触发HTTPS请求
    print("🌐 触发HTTPS请求...")
    
    test_urls = [
        "https://httpbin.org/get",
        "https://www.baidu.com"
    ]
    
    for url in test_urls:
        print(f"   🔄 访问: {url}")
        run_cmd(f"source android_env.sh && adb shell am start -a android.intent.action.VIEW -d {url}")
        time.sleep(8)
    
    # 应用内操作
    print("   🔄 应用内操作...")
    run_cmd("source android_env.sh && adb shell input tap 540 960")
    time.sleep(5)
    
    # 等待网络请求
    print("⏳ 等待网络请求处理...")
    time.sleep(15)
    
    # 检查结果
    try:
        with open(realtime_file, 'r') as f:
            data = json.load(f)
            
            if isinstance(data, list) and len(data) > 0:
                https_requests = [req for req in data if req.get('scheme') == 'https']
                http_requests = [req for req in data if req.get('scheme') == 'http']
                
                print(f"📊 捕获结果:")
                print(f"   总请求: {len(data)}")
                print(f"   HTTPS请求: {len(https_requests)}")
                print(f"   HTTP请求: {len(http_requests)}")
                
                if https_requests:
                    print("🎉 SSL绕过成功！捕获到HTTPS请求！")
                    print("📋 HTTPS请求示例:")
                    for i, req in enumerate(https_requests[:3], 1):
                        url = req.get('url', 'N/A')
                        method = req.get('method', 'N/A')
                        print(f"   {i}. {method} {url}")
                    return True
                elif http_requests:
                    print("⚠️  只捕获到HTTP请求")
                    return False
                else:
                    print("⚠️  捕获到请求但协议未识别")
                    return False
            else:
                print("❌ 没有捕获到网络请求")
                return False
                
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    print("🚀 最简化Frida SSL绕过")
    print("🔧 忽略SELinux警告，专注SSL绕过")
    print("=" * 60)
    
    try:
        # 设置Frida
        if not setup_frida():
            return False
        
        # 运行SSL绕过
        frida_process = run_frida_ssl_bypass()
        if not frida_process:
            return False
        
        # 测试HTTPS捕获
        print("\n" + "="*50)
        print("🔍 测试HTTPS捕获")
        print("="*50)
        
        success = test_https_capture()
        
        if success:
            print("\n🎉 Frida SSL绕过完全成功！")
            print("✅ HTTPS流量现在可以被mitmproxy捕获！")
            print("🔒 SSL证书验证已被绕过！")
            
            print("\n💡 APK动态分析系统现在具备:")
            print("   ✅ 完整的网络流量捕获")
            print("   ✅ HTTPS SSL绕过")
            print("   ✅ 实时流量分析")
            print("   ✅ API端点发现")
            
            print("\n⚠️  Frida进程正在运行...")
            print("💡 按Ctrl+C停止")
            
            try:
                while frida_process.poll() is None:
                    time.sleep(30)
                    print("💡 SSL绕过仍在运行...")
            except KeyboardInterrupt:
                print("\n⚠️  停止Frida进程...")
                frida_process.terminate()
                
        else:
            print("\n⚠️  HTTPS捕获测试未完全成功")
            print("🔧 但SSL绕过脚本可能仍在工作")
            
            if frida_process:
                try:
                    time.sleep(60)
                    frida_process.terminate()
                except KeyboardInterrupt:
                    frida_process.terminate()
        
        return success
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断")
        return False
    except Exception as e:
        print(f"\n❌ 异常: {e}")
        return False
    finally:
        # 清理
        print("🧹 清理frida-server...")
        run_cmd("source android_env.sh && adb shell pkill frida-server")

if __name__ == "__main__":
    main()
