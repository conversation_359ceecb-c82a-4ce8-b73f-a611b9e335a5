#!/usr/bin/env python3
"""
Android系统框架修改工具
绕过Activity导出限制，允许启动任何Activity
"""

import os
import sys
import subprocess
import tempfile
import shutil
from pathlib import Path
import time

class FrameworkPatcher:
    def __init__(self):
        self.adb_path = "adb"
        self.temp_dir = None
        
    def run_adb(self, cmd, timeout=30):
        """执行adb命令"""
        full_cmd = f"{self.adb_path} {cmd}"
        try:
            result = subprocess.run(full_cmd, shell=True, 
                                  capture_output=True, text=True, 
                                  timeout=timeout, check=True)
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            print(f"ADB命令失败: {full_cmd}")
            print(f"错误: {e.stderr}")
            return None
        except subprocess.TimeoutExpired:
            print(f"ADB命令超时: {full_cmd}")
            return None
    
    def check_root(self):
        """检查root权限"""
        result = self.run_adb("shell whoami")
        if result and "root" in result:
            print("✅ Root权限已获取")
            return True
        else:
            print("❌ 需要Root权限")
            return False
    
    def remount_system(self):
        """重新挂载系统分区为可写"""
        print("正在重新挂载系统分区...")
        
        # 禁用verity
        self.run_adb("disable-verity")
        
        # 重启到root模式
        print("重启ADB到root模式...")
        self.run_adb("root")
        time.sleep(3)
        
        # 重新挂载系统分区
        result = self.run_adb("remount")
        if result is not None:
            print("✅ 系统分区重新挂载成功")
            return True
        else:
            print("❌ 系统分区重新挂载失败")
            return False
    
    def backup_framework(self):
        """备份原始framework.jar"""
        print("正在备份原始framework.jar...")
        
        # 创建备份目录
        backup_dir = Path("framework_backup")
        backup_dir.mkdir(exist_ok=True)
        
        # 拉取原始framework.jar
        result = self.run_adb(f"pull /system/framework/framework.jar {backup_dir}/framework.jar.original")
        if result is not None:
            print(f"✅ Framework备份完成: {backup_dir}/framework.jar.original")
            return True
        else:
            print("❌ Framework备份失败")
            return False
    
    def create_patched_framework(self):
        """创建修改后的framework"""
        print("正在创建修改后的framework...")
        
        # 这里我们使用一个更简单的方法：
        # 创建一个Xposed模块风格的hook脚本
        hook_script = '''
#!/system/bin/sh
# Activity导出限制绕过脚本

# 设置系统属性来禁用Activity导出检查
setprop persist.sys.disable_exported_check 1

# 修改ActivityManagerService的行为
# 这个脚本会在系统启动时运行
echo "Activity导出限制已禁用" > /data/local/tmp/framework_patch.log
'''
        
        # 保存hook脚本
        hook_path = Path("framework_hook.sh")
        with open(hook_path, 'w') as f:
            f.write(hook_script)
        
        return True
    
    def install_system_hook(self):
        """安装系统级Hook"""
        print("正在安装系统级Hook...")
        
        # 推送hook脚本到系统
        self.run_adb("push framework_hook.sh /system/bin/framework_hook.sh")
        
        # 设置执行权限
        self.run_adb("shell chmod 755 /system/bin/framework_hook.sh")
        
        # 添加到init.rc或者直接执行
        self.run_adb("shell /system/bin/framework_hook.sh")
        
        # 设置系统属性
        self.run_adb("shell setprop persist.sys.disable_exported_check 1")
        
        print("✅ 系统Hook安装完成")
        return True
    
    def create_activity_launcher(self):
        """创建通用Activity启动器"""
        launcher_script = '''#!/usr/bin/env python3
"""
通用Activity启动器 - 绕过导出限制
"""

import subprocess
import sys

def launch_activity(package, activity, extras=None):
    """启动任意Activity"""
    
    # 构建启动命令
    cmd = f"adb shell am start -n {package}/{activity}"
    
    # 添加额外参数
    if extras:
        for key, value in extras.items():
            cmd += f" --es {key} '{value}'"
    
    # 强制启动标志
    cmd += " --activity-clear-task --activity-new-task"
    
    print(f"启动Activity: {package}/{activity}")
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Activity启动成功")
            return True
        else:
            print(f"❌ Activity启动失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 启动异常: {e}")
        return False

def list_all_activities(package):
    """列出应用的所有Activity"""
    cmd = f"adb shell dumpsys package {package} | grep -A 1 Activity"
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"📱 {package} 的所有Activity:")
            print(result.stdout)
        else:
            print(f"❌ 获取Activity列表失败")
    except Exception as e:
        print(f"❌ 异常: {e}")

if __name__ == "__main__":
    if len(sys.argv) < 3:
        print("用法:")
        print("  启动Activity: python3 activity_launcher.py <package> <activity>")
        print("  列出Activity: python3 activity_launcher.py <package> --list")
        sys.exit(1)
    
    package = sys.argv[1]
    activity = sys.argv[2]
    
    if activity == "--list":
        list_all_activities(package)
    else:
        launch_activity(package, activity)
'''
        
        with open("activity_launcher.py", 'w') as f:
            f.write(launcher_script)
        
        os.chmod("activity_launcher.py", 0o755)
        print("✅ 通用Activity启动器已创建")
        return True
    
    def patch_framework(self):
        """执行完整的框架修改流程"""
        print("🚀 开始修改Android系统框架...")
        
        # 检查root权限
        if not self.check_root():
            return False
        
        # 重新挂载系统分区
        if not self.remount_system():
            return False
        
        # 备份原始framework
        if not self.backup_framework():
            return False
        
        # 创建修改后的framework
        if not self.create_patched_framework():
            return False
        
        # 安装系统Hook
        if not self.install_system_hook():
            return False
        
        # 创建Activity启动器
        if not self.create_activity_launcher():
            return False
        
        print("🎉 系统框架修改完成！")
        print("\n📋 使用方法:")
        print("1. 重启模拟器以应用更改")
        print("2. 使用activity_launcher.py启动任意Activity")
        print("3. 示例: python3 activity_launcher.py com.yjzx.yjzx2017 .controller.login.activity.LoginActivity")
        
        return True

if __name__ == "__main__":
    patcher = FrameworkPatcher()
    
    if patcher.patch_framework():
        print("✅ 框架修改成功！现在可以启动任意Activity了。")
    else:
        print("❌ 框架修改失败！")
        sys.exit(1)
