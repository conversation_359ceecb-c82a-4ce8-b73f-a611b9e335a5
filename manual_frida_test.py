#!/usr/bin/env python3
"""
手动Frida测试脚本
分步骤执行，便于调试
"""

import subprocess
import sys
import time
import json
from pathlib import Path

def run_adb(cmd, timeout=30):
    """执行adb命令"""
    full_cmd = f"source android_env.sh && adb {cmd}"
    try:
        result = subprocess.run(full_cmd, shell=True, 
                              capture_output=True, text=True, 
                              timeout=timeout, check=True)
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        return f"ERROR: {e.stderr}"
    except subprocess.TimeoutExpired:
        return "ERROR: Timeout"

def step1_check_device():
    """步骤1: 检查设备"""
    print("🔍 步骤1: 检查设备连接")
    result = run_adb("devices")
    print(f"设备列表: {result}")
    
    if "device" in result and "ERROR" not in result:
        print("✅ 设备连接正常")
        return True
    else:
        print("❌ 设备连接失败")
        return False

def step2_start_frida_server():
    """步骤2: 启动frida-server"""
    print("\n🚀 步骤2: 启动frida-server")
    
    # 杀死现有进程
    run_adb("shell pkill frida-server")
    time.sleep(2)
    
    # 启动frida-server
    subprocess.Popen("source android_env.sh && adb shell '/data/local/tmp/frida-server &'", shell=True)
    time.sleep(8)
    
    # 检查进程
    result = run_adb("shell ps | grep frida-server")
    print(f"frida-server进程: {result}")
    
    if "frida-server" in result:
        print("✅ frida-server启动成功")
        return True
    else:
        print("❌ frida-server启动失败")
        return False

def step3_check_app():
    """步骤3: 检查应用"""
    print("\n📱 步骤3: 检查目标应用")
    
    package = "com.yjzx.yjzx2017"
    
    # 检查应用是否安装
    result = run_adb(f"shell pm list packages | grep {package}")
    print(f"应用安装检查: {result}")
    
    if package in result:
        print("✅ 目标应用已安装")
        
        # 启动应用
        print("🚀 启动应用...")
        launch_result = run_adb(f"shell am start -n {package}/.controller.activity.MainActivity")
        print(f"启动结果: {launch_result}")
        
        time.sleep(5)
        
        # 检查应用是否运行
        ps_result = run_adb(f"shell ps | grep {package}")
        print(f"应用进程: {ps_result}")
        
        if package in ps_result:
            print("✅ 应用启动成功")
            return True
        else:
            print("⚠️  应用可能未完全启动")
            return True  # 继续尝试
    else:
        print("❌ 目标应用未安装")
        return False

def step4_list_processes():
    """步骤4: 列出可用进程"""
    print("\n📋 步骤4: 列出Frida可用进程")
    
    try:
        # 使用frida-ps列出进程
        result = subprocess.run("frida-ps -U", shell=True, 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ Frida进程列表:")
            lines = result.stdout.split('\n')
            for line in lines[:20]:  # 显示前20行
                if line.strip():
                    print(f"   {line}")
            
            # 查找目标应用
            if "yjzx" in result.stdout.lower():
                print("✅ 找到目标应用进程")
                return True
            else:
                print("⚠️  未找到目标应用进程")
                return True  # 继续尝试
        else:
            print(f"❌ frida-ps失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 列出进程异常: {e}")
        return False

def step5_inject_script():
    """步骤5: 注入脚本"""
    print("\n🔧 步骤5: 注入SSL绕过脚本")
    
    package = "com.yjzx.yjzx2017"
    
    # 检查脚本文件
    if not Path("ssl_bypass.js").exists():
        print("❌ ssl_bypass.js文件不存在")
        return False
    
    print("✅ SSL绕过脚本文件存在")
    
    # 尝试不同的注入方式
    injection_methods = [
        f"frida -U {package} -l ssl_bypass.js",
        f"frida -U -n {package} -l ssl_bypass.js",
        "frida -U -f com.yjzx.yjzx2017 -l ssl_bypass.js"
    ]
    
    for i, cmd in enumerate(injection_methods, 1):
        print(f"\n🔄 尝试方法 {i}: {cmd}")
        
        try:
            # 启动Frida进程
            process = subprocess.Popen(
                cmd, shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True
            )
            
            # 等待一段时间
            time.sleep(10)
            
            if process.poll() is None:
                print("✅ Frida脚本运行中...")
                
                # 读取输出
                try:
                    output = process.stdout.read(2000)
                    if output:
                        print("📋 Frida输出:")
                        print(output[:1000])  # 显示前1000字符
                except:
                    pass
                
                return process
            else:
                stdout, _ = process.communicate()
                print(f"❌ 方法 {i} 失败")
                print(f"输出: {stdout[:500]}")  # 显示前500字符
                
        except Exception as e:
            print(f"❌ 方法 {i} 异常: {e}")
    
    print("❌ 所有注入方法都失败了")
    return None

def step6_test_capture():
    """步骤6: 测试网络捕获"""
    print("\n🔍 步骤6: 测试网络捕获")
    
    # 清空捕获文件
    realtime_file = Path("mitm-logs/realtime_capture.json")
    if realtime_file.exists():
        with open(realtime_file, 'w') as f:
            json.dump([], f)
        print("✅ 清空了捕获文件")
    
    time.sleep(2)
    
    # 触发网络活动
    print("🌐 触发网络活动...")
    
    test_actions = [
        ("访问HTTP网站", "shell am start -a android.intent.action.VIEW -d http://httpbin.org/get"),
        ("访问HTTPS网站", "shell am start -a android.intent.action.VIEW -d https://httpbin.org/get"),
        ("用户交互", "shell input tap 540 960"),
    ]
    
    for action_name, cmd in test_actions:
        print(f"   🔄 {action_name}...")
        result = run_adb(cmd)
        print(f"      结果: {result}")
        time.sleep(8)
    
    # 等待网络请求
    print("⏳ 等待网络请求处理...")
    time.sleep(15)
    
    # 检查结果
    try:
        with open(realtime_file, 'r') as f:
            data = json.load(f)
            
            print(f"📊 捕获结果:")
            print(f"   总请求数: {len(data) if isinstance(data, list) else 0}")
            
            if isinstance(data, list) and len(data) > 0:
                https_count = len([req for req in data if req.get('scheme') == 'https'])
                http_count = len([req for req in data if req.get('scheme') == 'http'])
                
                print(f"   HTTPS请求: {https_count}")
                print(f"   HTTP请求: {http_count}")
                
                print("📋 请求示例:")
                for i, req in enumerate(data[:5], 1):
                    url = req.get('url', 'N/A')
                    method = req.get('method', 'N/A')
                    scheme = req.get('scheme', 'N/A')
                    print(f"   {i}. {scheme.upper()} {method} {url}")
                
                if https_count > 0:
                    print("🎉 SSL绕过成功！捕获到HTTPS请求！")
                    return True
                elif http_count > 0:
                    print("⚠️  捕获到HTTP请求，SSL绕过可能部分生效")
                    return True
                else:
                    print("⚠️  捕获到请求但无法识别协议")
                    return True
            else:
                print("❌ 没有捕获到网络请求")
                return False
                
    except Exception as e:
        print(f"❌ 检查捕获结果失败: {e}")
        return False

def main():
    print("🚀 手动Frida SSL绕过测试")
    print("🔧 分步骤执行，便于调试")
    print("=" * 60)
    
    try:
        # 步骤1: 检查设备
        if not step1_check_device():
            return False
        
        # 步骤2: 启动frida-server
        if not step2_start_frida_server():
            return False
        
        # 步骤3: 检查应用
        if not step3_check_app():
            return False
        
        # 步骤4: 列出进程
        if not step4_list_processes():
            return False
        
        # 步骤5: 注入脚本
        frida_process = step5_inject_script()
        if not frida_process:
            print("⚠️  脚本注入失败，但继续测试网络捕获")
        
        # 步骤6: 测试捕获
        success = step6_test_capture()
        
        if success:
            print("\n🎉 测试成功！")
            if frida_process:
                print("📋 Frida进程正在运行")
                print("⚠️  按Ctrl+C停止")
                try:
                    frida_process.wait()
                except KeyboardInterrupt:
                    print("\n⚠️  停止Frida进程...")
                    frida_process.terminate()
        else:
            print("\n⚠️  测试未完全成功")
            if frida_process:
                frida_process.terminate()
        
        return success
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断")
        return False
    except Exception as e:
        print(f"\n❌ 异常: {e}")
        return False
    finally:
        # 清理
        print("🧹 清理资源...")
        subprocess.run("source android_env.sh && adb shell pkill frida-server", shell=True)

if __name__ == "__main__":
    main()
