#!/bin/bash
# Frida SSL绕过自动化脚本

echo "🚀 Frida SSL绕过自动化执行"
echo "================================"

# 设置环境
source android_env.sh
export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin

# 等待设备
echo "⏳ 等待设备连接..."
adb wait-for-device
echo "✅ 设备已连接"

# 推送frida-server
echo "📱 推送frida-server..."
adb push frida-server /data/local/tmp/frida-server
adb shell chmod 755 /data/local/tmp/frida-server

# 启动frida-server
echo "🚀 启动frida-server..."
adb shell pkill frida-server 2>/dev/null
sleep 2
adb shell '/data/local/tmp/frida-server' 2>/dev/null &
sleep 8

# 启动应用
echo "📱 启动应用..."
adb shell am force-stop com.yjzx.yjzx2017
sleep 2
adb shell am start -n com.yjzx.yjzx2017/.controller.activity.splash.SplashActivity
sleep 5

# 运行Frida SSL绕过
echo "🔧 运行Frida SSL绕过..."
echo "💡 忽略SELinux警告，这是正常的"
frida -U com.yjzx.yjzx2017 -l ssl_bypass.js &
FRIDA_PID=$!

echo "✅ Frida进程已启动 (PID: $FRIDA_PID)"
echo "⏳ 等待脚本加载..."
sleep 15

# 测试HTTPS
echo "🔍 测试HTTPS捕获..."
echo "[]" > mitm-logs/realtime_capture.json
sleep 2

adb shell am start -a android.intent.action.VIEW -d https://httpbin.org/get
sleep 8
adb shell am start -a android.intent.action.VIEW -d https://www.baidu.com
sleep 8

echo "⏳ 等待网络请求..."
sleep 15

# 检查结果
HTTPS_COUNT=$(python3 -c "
import json
try:
    with open('mitm-logs/realtime_capture.json', 'r') as f:
        data = json.load(f)
        if isinstance(data, list):
            https_count = len([req for req in data if req.get('scheme') == 'https'])
            print(https_count)
        else:
            print(0)
except:
    print(0)
")

if [ "$HTTPS_COUNT" -gt 0 ]; then
    echo "🎉 SSL绕过成功！捕获到 $HTTPS_COUNT 个HTTPS请求！"
    echo "✅ 系统现在可以完全分析HTTPS流量！"
else
    echo "⚠️  未捕获到HTTPS请求，但系统可能仍在工作"
fi

echo "💡 Frida进程正在运行，按Ctrl+C停止"
trap 'kill $FRIDA_PID 2>/dev/null; adb shell pkill frida-server; exit 0' INT

wait $FRIDA_PID
