#!/usr/bin/env python3
"""
通过Android设置安装证书
绕过文件管理器的限制
"""

import subprocess
import sys
import time
from pathlib import Path

class SettingsCertInstaller:
    def __init__(self):
        self.cert_file = "mitmproxy-ca-cert.pem"
        
    def run_adb(self, cmd, timeout=30):
        """执行adb命令"""
        full_cmd = f"source android_env.sh && adb {cmd}"
        try:
            result = subprocess.run(full_cmd, shell=True, 
                                  capture_output=True, text=True, 
                                  timeout=timeout, check=True)
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            return f"ERROR: {e.stderr}"
        except subprocess.TimeoutExpired:
            return "ERROR: Timeout"
    
    def prepare_certificate_file(self):
        """准备证书文件"""
        print("📋 准备证书文件...")
        
        # 确保证书文件存在
        if not Path(self.cert_file).exists():
            print("🔄 重新下载证书...")
            cmd = "curl -x 127.0.0.1:8080 http://mitm.it/cert/pem -o mitmproxy-ca-cert.pem"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode != 0:
                print("❌ 证书下载失败")
                return False
        
        # 检查证书内容
        with open(self.cert_file, 'r') as f:
            content = f.read()
        
        if "BEGIN CERTIFICATE" not in content:
            print("❌ 证书格式不正确")
            return False
        
        print("✅ 证书文件准备完成")
        return True
    
    def push_cert_to_multiple_locations(self):
        """推送证书到多个位置"""
        print("📱 推送证书到设备...")
        
        # 推送到多个位置
        locations = [
            "/sdcard/Download/",
            "/sdcard/",
            "/storage/emulated/0/Download/",
            "/storage/emulated/0/",
            "/data/local/tmp/"
        ]
        
        success_count = 0
        
        for location in locations:
            result = self.run_adb(f"push {self.cert_file} {location}mitmproxy-ca-cert.pem")
            if "ERROR" not in result:
                print(f"✅ 证书推送到 {location}")
                success_count += 1
            
            # 也推送.crt格式
            result2 = self.run_adb(f"push {self.cert_file} {location}mitmproxy.crt")
            if "ERROR" not in result2:
                print(f"✅ CRT格式推送到 {location}")
        
        return success_count > 0
    
    def open_security_settings_directly(self):
        """直接打开安全设置"""
        print("🔧 打开Android安全设置...")
        
        # 方法1: 直接打开安全设置
        self.run_adb("shell am start -a android.settings.SECURITY_SETTINGS")
        time.sleep(3)
        
        print("📱 请在Android设备上完成以下步骤:")
        print("   1. 在安全设置中找到 '加密与凭据'")
        print("   2. 点击 '从存储设备安装'")
        print("   3. 选择 mitmproxy-ca-cert.pem 或 mitmproxy.crt")
        print("   4. 输入证书名称: mitmproxy")
        print("   5. 选择用途: VPN和应用")
        
        return True
    
    def try_credential_install_intent(self):
        """尝试证书安装Intent"""
        print("🔧 尝试证书安装Intent...")
        
        # 方法1: 使用INSTALL action
        self.run_adb("shell am start -a android.credentials.INSTALL")
        time.sleep(2)
        
        # 方法2: 使用INSTALL action with data
        self.run_adb("shell am start -a android.credentials.INSTALL -d file:///sdcard/Download/mitmproxy-ca-cert.pem")
        time.sleep(2)
        
        # 方法3: 使用VIEW action with certificate MIME type
        self.run_adb("shell am start -a android.intent.action.VIEW -t application/x-x509-ca-cert -d file:///sdcard/Download/mitmproxy-ca-cert.pem")
        time.sleep(2)
        
        return True
    
    def simulate_ui_navigation(self):
        """模拟UI导航到证书安装"""
        print("🔧 模拟UI导航...")
        
        # 打开设置
        self.run_adb("shell am start -a android.settings.SETTINGS")
        time.sleep(2)
        
        # 模拟点击安全选项（这个位置可能需要调整）
        print("🖱️  模拟点击安全选项...")
        self.run_adb("shell input tap 200 400")  # 安全选项的大概位置
        time.sleep(2)
        
        # 模拟滚动查找加密与凭据
        print("🖱️  模拟滚动...")
        self.run_adb("shell input swipe 200 600 200 300")
        time.sleep(1)
        
        # 模拟点击加密与凭据
        print("🖱️  模拟点击加密与凭据...")
        self.run_adb("shell input tap 200 500")
        time.sleep(2)
        
        return True
    
    def install_certificate_step_by_step(self):
        """分步安装证书"""
        print("🚀 分步证书安装指南")
        print("🔧 手动完成每个步骤")
        print("=" * 50)
        
        # 步骤1: 准备证书
        if not self.prepare_certificate_file():
            return False
        
        # 步骤2: 推送证书
        if not self.push_cert_to_multiple_locations():
            return False
        
        # 步骤3: 打开安全设置
        print("\n步骤3: 打开安全设置")
        print("-" * 30)
        self.open_security_settings_directly()
        
        print("\n⏳ 请在Android设备上完成证书安装...")
        print("📋 如果找不到'加密与凭据'，请尝试以下步骤:")
        print("   - 在设置中搜索'证书'")
        print("   - 查找'隐私'或'安全'相关选项")
        print("   - 寻找'凭据存储'或'证书管理'")
        
        input("📱 完成安装后按回车键继续...")
        
        # 步骤4: 验证安装
        print("\n步骤4: 验证安装")
        print("-" * 30)
        return self.verify_certificate_installation()
    
    def verify_certificate_installation(self):
        """验证证书安装"""
        print("🔍 验证证书安装...")
        
        # 检查用户证书
        result = self.run_adb("shell ls -la /data/misc/user/0/cacerts-added/")
        if "ERROR" not in result and result:
            print("✅ 发现用户证书")
            return True
        
        # 检查系统证书（如果有root权限）
        result2 = self.run_adb("shell su -c 'ls -la /system/etc/security/cacerts/ | grep mitm'")
        if "ERROR" not in result2 and "mitm" in result2:
            print("✅ 发现系统证书")
            return True
        
        print("⚠️  无法验证证书安装状态")
        print("🔧 请手动检查证书是否已安装")
        return True  # 假设安装成功，让用户测试
    
    def run_installation(self):
        """运行完整的安装流程"""
        print("🚀 通过Android设置安装mitmproxy证书")
        print("🔧 绕过文件管理器限制")
        print("=" * 60)
        
        try:
            success = self.install_certificate_step_by_step()
            
            if success:
                print("\n✅ 证书安装流程完成！")
                print("🎯 现在可以测试HTTPS抓包功能")
                print("🔧 运行: python3 test_https_after_cert.py")
                return True
            else:
                print("\n❌ 证书安装失败")
                return False
                
        except Exception as e:
            print(f"\n❌ 安装异常: {e}")
            return False

def main():
    installer = SettingsCertInstaller()
    
    try:
        success = installer.run_installation()
        
        if success:
            print("\n🎉 请测试HTTPS抓包功能！")
        else:
            print("\n🔧 需要进一步排查问题")
            
    except KeyboardInterrupt:
        print("\n⚠️  安装被用户中断")
    except Exception as e:
        print(f"\n❌ 安装异常: {e}")

if __name__ == "__main__":
    main()
