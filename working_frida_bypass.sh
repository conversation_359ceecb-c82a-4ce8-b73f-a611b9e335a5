#!/bin/bash
# 工作的Frida SSL绕过脚本
# 使用命令行方式，简单直接

echo "🚀 Frida SSL绕过 - 命令行版本"
echo "🔧 解决HTTPS证书验证问题"
echo "============================================"

# 设置环境变量
source android_env.sh
export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin

# 检查设备连接
echo "🔍 检查设备连接..."
adb devices
if [ $? -ne 0 ]; then
    echo "❌ 设备连接失败"
    exit 1
fi
echo "✅ 设备连接正常"

# 启动frida-server
echo "🚀 启动frida-server..."
adb shell pkill frida-server
sleep 2
adb shell '/data/local/tmp/frida-server &'
sleep 8

# 检查frida-server
echo "🔍 检查frida-server状态..."
adb shell ps | grep frida-server
if [ $? -ne 0 ]; then
    echo "❌ frida-server启动失败"
    exit 1
fi
echo "✅ frida-server运行正常"

# 准备应用
echo "📱 准备目标应用..."
PACKAGE="com.yjzx.yjzx2017"
adb shell am force-stop $PACKAGE
sleep 2

# 启动应用
echo "🚀 启动应用..."
adb shell am start -n $PACKAGE/.controller.activity.splash.SplashActivity
sleep 5

# 检查应用是否运行
echo "🔍 检查应用状态..."
APP_PID=$(adb shell ps | grep $PACKAGE | grep -v pushcore | awk '{print $2}' | head -1)
if [ -z "$APP_PID" ]; then
    echo "⚠️  应用可能未完全启动，继续尝试..."
else
    echo "✅ 应用正在运行，PID: $APP_PID"
fi

# 使用Frida附加到应用
echo "🔧 使用Frida附加到应用..."
echo "📋 执行命令: frida -U $PACKAGE -l ssl_bypass.js"

# 启动Frida（后台运行）
frida -U $PACKAGE -l ssl_bypass.js &
FRIDA_PID=$!

echo "✅ Frida进程已启动，PID: $FRIDA_PID"
echo "⏳ 等待SSL绕过脚本加载..."
sleep 15

# 检查Frida进程是否还在运行
if kill -0 $FRIDA_PID 2>/dev/null; then
    echo "✅ Frida进程运行正常"
else
    echo "❌ Frida进程已退出"
    exit 1
fi

# 测试HTTPS捕获
echo ""
echo "=================================================="
echo "🔍 测试HTTPS捕获功能"
echo "=================================================="

# 清空捕获文件
echo "🗑️  清空网络捕获文件..."
echo "[]" > mitm-logs/realtime_capture.json
sleep 2

# 触发HTTPS请求
echo "🌐 触发HTTPS网络请求..."

echo "   🔄 访问HTTPS测试站点..."
adb shell am start -a android.intent.action.VIEW -d https://httpbin.org/get
sleep 8

echo "   🔄 访问百度HTTPS..."
adb shell am start -a android.intent.action.VIEW -d https://www.baidu.com
sleep 8

echo "   🔄 应用内操作..."
adb shell input tap 540 960
sleep 5

echo "⏳ 等待网络请求处理..."
sleep 15

# 检查捕获结果
echo "📊 检查网络捕获结果..."
CAPTURE_FILE="mitm-logs/realtime_capture.json"

if [ -f "$CAPTURE_FILE" ]; then
    # 统计请求数量
    TOTAL_REQUESTS=$(python3 -c "
import json
try:
    with open('$CAPTURE_FILE', 'r') as f:
        data = json.load(f)
        if isinstance(data, list):
            print(len(data))
        else:
            print(0)
except:
    print(0)
")
    
    HTTPS_REQUESTS=$(python3 -c "
import json
try:
    with open('$CAPTURE_FILE', 'r') as f:
        data = json.load(f)
        if isinstance(data, list):
            https_count = len([req for req in data if req.get('scheme') == 'https'])
            print(https_count)
        else:
            print(0)
except:
    print(0)
")
    
    echo "📊 捕获统计:"
    echo "   总请求数: $TOTAL_REQUESTS"
    echo "   HTTPS请求数: $HTTPS_REQUESTS"
    
    if [ "$HTTPS_REQUESTS" -gt 0 ]; then
        echo ""
        echo "🎉 SSL绕过成功！成功捕获HTTPS请求！"
        echo "✅ HTTPS流量现在可以被mitmproxy完全捕获！"
        echo "🔒 SSL证书验证已被完全绕过！"
        
        echo ""
        echo "📋 HTTPS请求示例:"
        python3 -c "
import json
try:
    with open('$CAPTURE_FILE', 'r') as f:
        data = json.load(f)
        if isinstance(data, list):
            https_reqs = [req for req in data if req.get('scheme') == 'https']
            for i, req in enumerate(https_reqs[:3], 1):
                url = req.get('url', 'N/A')
                method = req.get('method', 'N/A')
                status = req.get('status_code', 'N/A')
                print(f'   {i}. {method} {url}')
                print(f'      状态: {status}')
except:
    pass
"
        
        echo ""
        echo "💡 系统现在具备完整的HTTPS抓包能力！"
        echo "🔧 可以进行完整的APK安全分析！"
        
        SUCCESS=true
    elif [ "$TOTAL_REQUESTS" -gt 0 ]; then
        echo "⚠️  只捕获到HTTP请求，SSL绕过可能未完全生效"
        SUCCESS=false
    else
        echo "❌ 没有捕获到网络请求"
        SUCCESS=false
    fi
else
    echo "❌ 捕获文件不存在"
    SUCCESS=false
fi

# 保持Frida运行或清理
if [ "$SUCCESS" = true ]; then
    echo ""
    echo "⚠️  Frida SSL绕过正在后台运行..."
    echo "💡 你可以继续进行其他分析工作"
    echo "🔧 按Ctrl+C停止脚本"
    
    # 保持运行
    trap 'echo ""; echo "⚠️  停止Frida进程..."; kill $FRIDA_PID 2>/dev/null; adb shell pkill frida-server; exit 0' INT
    
    while kill -0 $FRIDA_PID 2>/dev/null; do
        sleep 30
        echo "💡 SSL绕过仍在运行... (PID: $FRIDA_PID)"
    done
    
    echo "⚠️  Frida进程意外退出"
else
    echo ""
    echo "⚠️  SSL绕过测试未完全成功"
    echo "🔧 停止Frida进程..."
    kill $FRIDA_PID 2>/dev/null
fi

# 清理
echo "🧹 清理frida-server..."
adb shell pkill frida-server

echo "✅ 脚本执行完成"
