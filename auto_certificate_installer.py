#!/usr/bin/env python3
"""
自动化mitmproxy证书安装器
一次配置，永久生效，无需重复手动安装
"""

import subprocess
import os
import time
import hashlib
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

class AutoCertificateInstaller:
    """自动证书安装器"""
    
    def __init__(self, device_id="emulator-5554"):
        self.device_id = device_id
        self.cert_dir = "/Users/<USER>/Desktop/project/apk_detect/mitm-config"
        self.android_cert_dir = "/system/etc/security/cacerts"
        self.cert_installed = False
        
    def run_adb_command(self, command, timeout=30):
        """执行ADB命令"""
        try:
            full_command = ['adb', '-s', self.device_id] + command
            result = subprocess.run(
                full_command, 
                capture_output=True, 
                text=True, 
                timeout=timeout
            )
            return result
        except Exception as e:
            logger.error(f"ADB命令执行异常: {e}")
            return None
    
    def check_certificate_installed(self):
        """检查证书是否已安装"""
        logger.info("🔍 检查mitmproxy证书安装状态...")
        
        try:
            # 检查系统证书目录
            result = self.run_adb_command([
                'shell', 'su', '-c', 'ls /system/etc/security/cacerts/ | grep mitm'
            ])
            
            if result and result.returncode == 0 and 'mitm' in result.stdout:
                logger.info("✅ mitmproxy证书已安装")
                self.cert_installed = True
                return True
            
            # 检查用户证书存储
            result = self.run_adb_command([
                'shell', 'settings', 'get', 'secure', 'trust_agents_initialized'
            ])
            
            logger.info("⚠️  证书未安装或未在系统证书存储中")
            return False
            
        except Exception as e:
            logger.error(f"证书检查异常: {e}")
            return False
    
    def get_mitmproxy_cert_hash(self):
        """获取mitmproxy证书并计算哈希"""
        logger.info("📜 获取mitmproxy证书...")
        
        try:
            # 确保mitmproxy配置目录存在
            os.makedirs(self.cert_dir, exist_ok=True)
            
            # 启动临时mitmproxy生成证书
            logger.info("启动临时mitmproxy生成证书...")
            proc = subprocess.Popen([
                'mitmproxy', '--listen-host', '127.0.0.1', '--listen-port', '8999',
                '--set', f'confdir={self.cert_dir}'
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            
            # 等待证书生成
            time.sleep(5)
            proc.terminate()
            proc.wait()
            
            # 检查证书文件
            cert_file = os.path.join(self.cert_dir, 'mitmproxy-ca-cert.pem')
            if os.path.exists(cert_file):
                # 计算证书哈希（用于Android系统证书命名）
                with open(cert_file, 'rb') as f:
                    cert_data = f.read()
                    cert_hash = hashlib.sha256(cert_data).hexdigest()[:8]
                
                logger.info(f"✅ 证书生成成功，哈希: {cert_hash}")
                return cert_file, cert_hash
            else:
                logger.error("❌ 证书文件生成失败")
                return None, None
                
        except Exception as e:
            logger.error(f"证书生成异常: {e}")
            return None, None
    
    def install_certificate_to_system(self, cert_file, cert_hash):
        """将证书安装到Android系统证书存储"""
        logger.info("🔐 安装证书到Android系统...")
        
        try:
            # 1. 重新挂载系统分区为可写
            logger.info("重新挂载系统分区...")
            result = self.run_adb_command([
                'shell', 'su', '-c', 'mount -o rw,remount /system'
            ])
            
            if not (result and result.returncode == 0):
                logger.warning("系统分区挂载可能失败，尝试继续...")
            
            # 2. 复制证书到系统目录
            android_cert_name = f"{cert_hash}.0"
            android_cert_path = f"{self.android_cert_dir}/{android_cert_name}"
            
            logger.info(f"复制证书: {android_cert_path}")
            
            # 推送证书到临时目录
            self.run_adb_command(['push', cert_file, '/sdcard/mitmproxy-cert.pem'])
            
            # 移动到系统证书目录
            result = self.run_adb_command([
                'shell', 'su', '-c', f'cp /sdcard/mitmproxy-cert.pem {android_cert_path}'
            ])
            
            if result and result.returncode == 0:
                # 3. 设置正确的权限
                self.run_adb_command([
                    'shell', 'su', '-c', f'chmod 644 {android_cert_path}'
                ])
                self.run_adb_command([
                    'shell', 'su', '-c', f'chown root:root {android_cert_path}'
                ])
                
                logger.info("✅ 证书安装到系统存储成功")
                return True
            else:
                logger.error("❌ 证书复制到系统目录失败")
                return False
                
        except Exception as e:
            logger.error(f"系统证书安装异常: {e}")
            return False
    
    def install_certificate_to_user_store(self, cert_file):
        """将证书安装到用户证书存储（兜底方案）"""
        logger.info("👤 安装证书到用户存储...")
        
        try:
            # 推送证书到设备
            self.run_adb_command(['push', cert_file, '/sdcard/mitmproxy-ca.crt'])
            
            # 启动证书安装界面（需要用户确认）
            logger.info("启动证书安装界面...")
            result = self.run_adb_command([
                'shell', 'am', 'start', '-n', 
                'com.android.certinstaller/.CertInstallerMain',
                '-a', 'android.intent.action.VIEW',
                '-d', 'file:///sdcard/mitmproxy-ca.crt'
            ])
            
            if result and result.returncode == 0:
                logger.info("✅ 证书安装界面已启动")
                logger.info("请在模拟器中手动确认证书安装（一次性操作）")
                return True
            else:
                logger.error("❌ 证书安装界面启动失败")
                return False
                
        except Exception as e:
            logger.error(f"用户证书安装异常: {e}")
            return False
    
    def configure_permanent_proxy(self):
        """配置永久代理设置"""
        logger.info("🌐 配置永久代理设置...")
        
        try:
            # 设置全局HTTP代理
            result = self.run_adb_command([
                'shell', 'settings', 'put', 'global', 'http_proxy', '127.0.0.1:8080'
            ])
            
            if result and result.returncode == 0:
                logger.info("✅ 全局代理设置成功")
                
                # 验证代理设置
                verify_result = self.run_adb_command([
                    'shell', 'settings', 'get', 'global', 'http_proxy'
                ])
                
                if verify_result and '127.0.0.1:8080' in verify_result.stdout:
                    logger.info("✅ 代理设置验证成功")
                    return True
            
            logger.error("❌ 代理设置失败")
            return False
            
        except Exception as e:
            logger.error(f"代理配置异常: {e}")
            return False
    
    def create_startup_script(self):
        """创建启动脚本，实现一键启动"""
        logger.info("📝 创建自动化启动脚本...")
        
        script_content = f"""#!/bin/bash
# 自动化HTTPS流量捕获启动脚本
# 一键启动mitmproxy + Frida SSL绕过

set -e

echo "🚀 启动HTTPS流量捕获环境..."

# 检查设备连接
if ! adb -s {self.device_id} get-state >/dev/null 2>&1; then
    echo "❌ Android设备未连接"
    exit 1
fi

# 启动mitmproxy (后台)
echo "启动mitmproxy代理服务器..."
source .venv/bin/activate
mitmproxy --listen-host 0.0.0.0 --listen-port 8080 \\
    --set confdir={self.cert_dir} \\
    --set flow_detail=1 \\
    --showhost \\
    --save-stream-file mitm_capture.log &

MITM_PID=$!
echo "mitmproxy PID: $MITM_PID"

# 等待mitmproxy启动
sleep 3

# 启动Frida SSL绕过 (后台)
echo "启动Frida SSL绕过..."
frida -U -l ssl_bypass.js -f com.iloda.beacon --no-pause &
FRIDA_PID=$!
echo "Frida PID: $FRIDA_PID"

echo "✅ HTTPS流量捕获环境已启动"
echo "📊 查看捕获日志: tail -f mitm_capture.log"
echo "🌐 Web界面: http://127.0.0.1:8081"
echo "🛑 停止服务: kill $MITM_PID $FRIDA_PID"

# 保存PID以便后续停止
echo "$MITM_PID $FRIDA_PID" > .https_capture_pids

echo "🎯 环境就绪，开始分析..."
"""
        
        script_path = '/Users/<USER>/Desktop/project/apk_detect/start_https_capture.sh'
        with open(script_path, 'w') as f:
            f.write(script_content)
        
        os.chmod(script_path, 0o755)
        logger.info(f"✅ 启动脚本已创建: {script_path}")
        
        # 创建停止脚本
        stop_script = """#!/bin/bash
# 停止HTTPS流量捕获

if [ -f .https_capture_pids ]; then
    PIDS=$(cat .https_capture_pids)
    echo "停止HTTPS捕获服务: $PIDS"
    kill $PIDS 2>/dev/null || true
    rm .https_capture_pids
    echo "✅ 服务已停止"
else
    echo "⚠️  未找到运行中的服务"
fi
"""
        
        stop_script_path = '/Users/<USER>/Desktop/project/apk_detect/stop_https_capture.sh'
        with open(stop_script_path, 'w') as f:
            f.write(stop_script)
        
        os.chmod(stop_script_path, 0o755)
        logger.info(f"✅ 停止脚本已创建: {stop_script_path}")
        
        return True
    
    def run_auto_install(self):
        """运行完整的自动安装流程"""
        logger.info("="*60)
        logger.info("🔐 自动化mitmproxy证书安装")
        logger.info("="*60)
        
        success_count = 0
        total_steps = 5
        
        # 步骤1: 检查现有证书
        if self.check_certificate_installed():
            logger.info("✅ 证书已安装，跳过安装步骤")
            success_count += 3  # 跳过证书相关步骤
        else:
            # 步骤2: 生成证书
            cert_file, cert_hash = self.get_mitmproxy_cert_hash()
            if cert_file and cert_hash:
                logger.info("✅ 证书生成成功")
                success_count += 1
                
                # 步骤3: 系统级安装
                if self.install_certificate_to_system(cert_file, cert_hash):
                    logger.info("✅ 系统证书安装成功")
                    success_count += 1
                else:
                    # 步骤4: 用户级安装（兜底）
                    if self.install_certificate_to_user_store(cert_file):
                        logger.info("✅ 用户证书安装成功")
                        success_count += 1
        
        # 步骤5: 配置代理
        if self.configure_permanent_proxy():
            logger.info("✅ 代理配置成功")
            success_count += 1
        
        # 步骤6: 创建启动脚本
        if self.create_startup_script():
            logger.info("✅ 启动脚本创建成功")
            success_count += 1
        
        # 总结
        logger.info(f"\n📊 安装结果:")
        logger.info(f"   - 完成步骤: {success_count}/{total_steps}")
        
        if success_count >= 4:
            logger.info("\n🎉 自动化证书安装完成!")
            logger.info("💡 使用方法:")
            logger.info("   - 启动捕获: ./start_https_capture.sh")
            logger.info("   - 停止捕获: ./stop_https_capture.sh")
            logger.info("   - 无需再次手动安装证书!")
        else:
            logger.info("\n⚠️  安装过程中遇到问题，请检查日志")
        
        logger.info("="*60)
        
        return success_count >= 4

def main():
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    installer = AutoCertificateInstaller()
    success = installer.run_auto_install()
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())


