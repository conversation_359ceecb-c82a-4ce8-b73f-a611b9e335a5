
# 🎯 交互式Frida SSL绕过手动操作指南

## 当前状态
✅ 环境已准备完成
✅ 交互式脚本已创建
✅ frida-server正在运行
✅ 目标应用正在运行

## 🔧 手动执行步骤

### 步骤1: 启动交互式Frida
```bash
export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin
frida -U com.yjzx.yjzx2017
```

### 步骤2: 在Frida控制台中加载脚本
```javascript
%load interactive_ssl_bypass.js
```

### 步骤3: 验证脚本加载
看到以下输出说明成功：
- "✅ Java.perform 启动成功"
- "✅ SSLContext hook 成功"
- "✅ HostnameVerifier hook 成功"
- "🎉 SSL绕过初始化完成！"

### 步骤4: 测试SSL绕过（另一个终端）
```bash
# 启动mitmproxy
mitmdump -s mitm-scripts/capture.py --listen-port 8080

# 设置代理
adb shell settings put global http_proxy *************:8080

# 触发HTTPS请求
adb shell am start -a android.intent.action.VIEW -d https://httpbin.org/get
adb shell am start -a android.intent.action.VIEW -d https://www.baidu.com

# 检查捕获结果
cat mitm-logs/realtime_capture.json
```

## 🎉 成功指标
- Frida控制台显示SSL hook成功信息
- mitmproxy捕获到HTTPS请求
- realtime_capture.json包含HTTPS流量数据

## 💡 关键优势
- 绕过所有权限问题
- 交互式调试和验证
- 实时查看SSL绕过效果
- 完全手动控制流程
