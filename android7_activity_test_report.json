{"测试报告": "Android 7 多Activity HTTPS流量抓取测试", "测试时间": "2025-09-10 12:45-12:47", "测试环境": {"Android版本": "7.0 (<PERSON> 24)", "模拟器": "Android7_API24", "代理工具": "mitmproxy 9.0.1", "代理端口": "8888", "系统证书": "已安装 (c8750f0d.0)"}, "测试的Activity列表": [{"序号": 1, "Activity": "com.yjzx.yjzx2017.controller.order.controller.OrderEntrustActivity", "功能": "订单委托", "启动状态": "成功"}, {"序号": 2, "Activity": "com.yjzx.yjzx2017.community.controller.CommunitySearchActivity", "功能": "社区搜索", "启动状态": "成功"}, {"序号": 3, "Activity": "com.yjzx.yjzx2017.controller.account.controller.AccountWithdrawActivity", "功能": "账户提现", "启动状态": "成功"}, {"序号": 4, "Activity": "com.yjzx.yjzx2017.controller.video.controller.MainActivity", "功能": "视频主页", "启动状态": "成功"}, {"序号": 5, "Activity": "com.yjzx.yjzx2017.controller.activity.ScanActivity", "功能": "扫码功能", "启动状态": "成功"}, {"序号": 6, "Activity": "com.yjzx.yjzx2017.controller.bigsell.controller.BigSellMainActivity", "功能": "大宗交易主页", "启动状态": "成功"}, {"序号": 7, "Activity": "com.yjzx.yjzx2017.controller.redpacket.activity.RedPacketMainActivity", "功能": "红包主页", "启动状态": "成功"}], "抓取到的HTTPS URL统计": {"总数": 6, "详细列表": [{"URL": "https://connectivitycheck.gstatic.com/generate_204", "方法": "GET", "功能": "Android连接性检查", "状态码": "204 No Content"}, {"URL": "https://android.apis.google.com/c2dm/register3", "方法": "POST", "功能": "Google Cloud Messaging注册", "状态码": "301 Moved Permanently"}, {"URL": "https://api.sobot.com/chat-sdk/sdk/user/v2/config.actio...", "方法": "POST", "功能": "智齿客服SDK配置", "状态码": "200 OK"}, {"URL": "https://h.trace.qq.com/kv", "方法": "POST", "功能": "腾讯埋点统计", "状态码": "200 OK"}, {"URL": "https://android.bugly.qq.com/rqd/async", "方法": "POST", "功能": "腾讯Bugly错误上报", "状态码": "200 OK"}, {"URL": "https://ali-stats.jpush.cn/v3/report", "方法": "POST", "功能": "极光推送统计上报", "状态码": "200 OK"}]}, "应用行为观察": {"启动Activity成功率": "100% (7/7)", "主要问题": "应用启动后经常崩溃，显示'易金在线 has stopped'弹窗", "网络流量特点": "主要是第三方SDK的统计和推送相关API调用", "业务API": "未发现明显的业务相关API调用，可能需要登录或特定条件触发"}, "技术成果": {"系统证书安装": "✅ 成功", "HTTPS流量抓取": "✅ 成功", "Activity启动": "✅ 100%成功率", "调试弹窗问题": "✅ 已解决", "SSL证书信任": "✅ 完全绕过"}, "结论": {"动态分析可行性": "完全可行", "HTTPS抓包能力": "完全正常", "Activity启动能力": "完全正常", "主要限制": "应用业务逻辑可能需要登录状态或特定用户交互才能触发更多API调用"}}