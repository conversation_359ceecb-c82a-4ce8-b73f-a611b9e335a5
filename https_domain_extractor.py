#!/usr/bin/env python3
"""
HTTPS域名提取器
从mitmproxy日志中提取无法解密的HTTPS域名和对应IP地址
专门用于SSL pinning场景下的网络行为分析
"""

import re
import json
import socket
import logging
import time
from datetime import datetime
from pathlib import Path
import subprocess
from typing import Dict, List, Set, Optional
from collections import defaultdict

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HTTPSDomainExtractor:
    """HTTPS域名提取和分析器"""
    
    def __init__(self):
        self.blocked_domains = set()  # 被SSL pinning阻止的域名
        self.domain_ips = {}  # 域名到IP的映射
        self.domain_stats = defaultdict(int)  # 域名访问统计
        self.connection_attempts = []  # 所有连接尝试
        
    def extract_domains_from_mitm_log(self, log_file_path: str) -> Dict:
        """从mitmproxy日志中提取域名信息"""
        logger.info(f"🔍 分析日志文件: {log_file_path}")
        
        if not Path(log_file_path).exists():
            logger.error(f"日志文件不存在: {log_file_path}")
            return {}
        
        ssl_fail_pattern = r"Client TLS handshake failed.*for ([a-zA-Z0-9.-]+)"
        connect_pattern = r"\[(\d{2}:\d{2}:\d{2}\.\d+)\].*client connect"
        timestamp_pattern = r"\[(\d{2}:\d{2}:\d{2}\.\d+)\]"
        
        try:
            with open(log_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            logger.info(f"📄 日志总行数: {len(lines)}")
            
            for i, line in enumerate(lines):
                # 提取SSL握手失败的域名
                ssl_match = re.search(ssl_fail_pattern, line)
                if ssl_match:
                    domain = ssl_match.group(1)
                    self.blocked_domains.add(domain)
                    self.domain_stats[domain] += 1
                    
                    # 提取时间戳
                    time_match = re.search(timestamp_pattern, line)
                    timestamp = time_match.group(1) if time_match else "unknown"
                    
                    self.connection_attempts.append({
                        "timestamp": timestamp,
                        "domain": domain,
                        "status": "ssl_handshake_failed",
                        "reason": "certificate_pinning",
                        "line_number": i + 1
                    })
            
            logger.info(f"✅ 发现被阻止的域名: {len(self.blocked_domains)} 个")
            logger.info(f"📊 总连接尝试: {len(self.connection_attempts)} 次")
            
            return {
                "blocked_domains": list(self.blocked_domains),
                "domain_stats": dict(self.domain_stats),
                "connection_attempts": self.connection_attempts
            }
            
        except Exception as e:
            logger.error(f"日志分析异常: {e}")
            return {}
    
    def resolve_domain_ips(self, domains: List[str]) -> Dict[str, Dict]:
        """解析域名对应的IP地址"""
        logger.info(f"🌐 开始解析 {len(domains)} 个域名的IP地址...")
        
        results = {}
        
        for domain in domains:
            logger.info(f"解析域名: {domain}")
            domain_info = {
                "domain": domain,
                "ipv4_addresses": [],
                "ipv6_addresses": [],
                "resolution_time": None,
                "error": None,
                "cname": None,
                "mx_records": [],
                "txt_records": []
            }
            
            try:
                start_time = time.time()
                
                # 解析IPv4地址
                try:
                    ipv4_result = socket.getaddrinfo(domain, None, socket.AF_INET)
                    domain_info["ipv4_addresses"] = list(set([addr[4][0] for addr in ipv4_result]))
                except socket.gaierror:
                    pass
                
                # 解析IPv6地址
                try:
                    ipv6_result = socket.getaddrinfo(domain, None, socket.AF_INET6)
                    domain_info["ipv6_addresses"] = list(set([addr[4][0] for addr in ipv6_result]))
                except socket.gaierror:
                    pass
                
                domain_info["resolution_time"] = time.time() - start_time
                
                # 使用nslookup获取更详细信息
                try:
                    nslookup_result = subprocess.run(
                        ['nslookup', domain],
                        capture_output=True,
                        text=True,
                        timeout=10
                    )
                    
                    if nslookup_result.returncode == 0:
                        output = nslookup_result.stdout
                        
                        # 提取CNAME
                        cname_match = re.search(r'canonical name = ([^\n]+)', output)
                        if cname_match:
                            domain_info["cname"] = cname_match.group(1).strip()
                
                except subprocess.TimeoutExpired:
                    logger.warning(f"nslookup超时: {domain}")
                except Exception as e:
                    logger.warning(f"nslookup失败: {domain}, 错误: {e}")
                
                logger.info(f"✅ {domain}: {len(domain_info['ipv4_addresses'])} IPv4, {len(domain_info['ipv6_addresses'])} IPv6")
                
            except Exception as e:
                domain_info["error"] = str(e)
                logger.error(f"❌ 域名解析失败: {domain}, 错误: {e}")
            
            results[domain] = domain_info
            time.sleep(0.1)  # 避免DNS查询过快
        
        return results
    
    def get_ip_geolocation(self, ip_address: str) -> Dict:
        """获取IP地址的地理位置信息（简化版）"""
        # 这里可以集成IP地理位置服务，如MaxMind GeoIP
        # 为简化，这里只做基础的IP类型识别
        
        info = {
            "ip": ip_address,
            "type": "unknown",
            "is_private": False,
            "is_reserved": False
        }
        
        try:
            import ipaddress
            ip_obj = ipaddress.ip_address(ip_address)
            
            info["type"] = "ipv4" if ip_obj.version == 4 else "ipv6"
            info["is_private"] = ip_obj.is_private
            info["is_reserved"] = ip_obj.is_reserved
            info["is_multicast"] = ip_obj.is_multicast
            info["is_loopback"] = ip_obj.is_loopback
            
        except ValueError:
            logger.warning(f"无效IP地址: {ip_address}")
        
        return info
    
    def analyze_domain_patterns(self, domains: List[str]) -> Dict:
        """分析域名模式"""
        logger.info("🔍 分析域名模式...")
        
        patterns = {
            "google_services": [],
            "push_services": [],
            "cdn_services": [],
            "analytics_services": [],
            "ad_services": [],
            "security_services": [],
            "social_services": [],
            "other_services": []
        }
        
        service_patterns = {
            "google_services": [
                r".*\.googleapis\.com$",
                r".*\.google\.com$",
                r".*\.gstatic\.com$",
                r".*\.googleusercontent\.com$"
            ],
            "push_services": [
                r".*\.jpush\.cn$",
                r".*\.jiguang\.cn$",
                r".*fcm\.googleapis\.com$",
                r".*\.push\..*"
            ],
            "cdn_services": [
                r".*\.cloudflare\.com$",
                r".*\.fastly\.com$",
                r".*\.amazonaws\.com$",
                r".*\.alicdn\.com$"
            ],
            "analytics_services": [
                r".*analytics.*",
                r".*\.umeng\.com$",
                r".*\.baidu\.com$"
            ],
            "ad_services": [
                r".*\.ads\..*",
                r".*\.doubleclick\..*",
                r".*\.googlesyndication\..*"
            ]
        }
        
        for domain in domains:
            categorized = False
            for category, pattern_list in service_patterns.items():
                for pattern in pattern_list:
                    if re.match(pattern, domain, re.IGNORECASE):
                        patterns[category].append(domain)
                        categorized = True
                        break
                if categorized:
                    break
            
            if not categorized:
                patterns["other_services"].append(domain)
        
        # 统计信息
        pattern_stats = {}
        for category, domains_in_category in patterns.items():
            pattern_stats[category] = {
                "count": len(domains_in_category),
                "domains": domains_in_category
            }
        
        return pattern_stats
    
    def generate_comprehensive_report(self, log_files: List[str]) -> Dict:
        """生成综合分析报告"""
        logger.info("📊 生成综合HTTPS域名分析报告...")
        
        all_domains = set()
        all_attempts = []
        
        # 分析所有日志文件
        for log_file in log_files:
            if Path(log_file).exists():
                result = self.extract_domains_from_mitm_log(log_file)
                if result:
                    all_domains.update(result.get("blocked_domains", []))
                    all_attempts.extend(result.get("connection_attempts", []))
        
        if not all_domains:
            logger.warning("没有发现被阻止的HTTPS域名")
            return {}
        
        # 解析IP地址
        domain_ips = self.resolve_domain_ips(list(all_domains))
        
        # 分析域名模式
        domain_patterns = self.analyze_domain_patterns(list(all_domains))
        
        # 生成IP地理信息
        ip_geoinfo = {}
        for domain, info in domain_ips.items():
            for ip in info.get("ipv4_addresses", []) + info.get("ipv6_addresses", []):
                if ip not in ip_geoinfo:
                    ip_geoinfo[ip] = self.get_ip_geolocation(ip)
        
        # 生成最终报告
        report = {
            "analysis_time": datetime.now().isoformat(),
            "summary": {
                "total_blocked_domains": len(all_domains),
                "total_connection_attempts": len(all_attempts),
                "unique_ips": len(ip_geoinfo),
                "log_files_analyzed": len([f for f in log_files if Path(f).exists()])
            },
            "blocked_domains": {
                "list": list(all_domains),
                "by_frequency": dict(sorted(
                    self.domain_stats.items(), 
                    key=lambda x: x[1], 
                    reverse=True
                ))
            },
            "domain_resolution": domain_ips,
            "domain_patterns": domain_patterns,
            "ip_geolocation": ip_geoinfo,
            "connection_attempts": all_attempts[:50],  # 限制前50个尝试
            "technical_analysis": {
                "ssl_pinning_domains": list(all_domains),
                "common_services": {
                    "google_services": len(domain_patterns.get("google_services", {}).get("domains", [])),
                    "push_services": len(domain_patterns.get("push_services", {}).get("domains", [])),
                    "other_services": len(domain_patterns.get("other_services", {}).get("domains", []))
                }
            }
        }
        
        return report
    
    def save_report(self, report: Dict, output_file: str):
        """保存分析报告"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✅ 报告已保存: {output_file}")
            
            # 打印摘要
            summary = report.get("summary", {})
            logger.info(f"📊 分析摘要:")
            logger.info(f"   - 被阻止的域名: {summary.get('total_blocked_domains', 0)} 个")
            logger.info(f"   - 连接尝试: {summary.get('total_connection_attempts', 0)} 次")  
            logger.info(f"   - 唯一IP地址: {summary.get('unique_ips', 0)} 个")
            
            # 显示热门域名
            blocked = report.get("blocked_domains", {})
            if blocked.get("by_frequency"):
                logger.info(f"🔥 访问频率最高的域名:")
                for domain, count in list(blocked["by_frequency"].items())[:5]:
                    logger.info(f"   - {domain}: {count} 次")
            
        except Exception as e:
            logger.error(f"保存报告失败: {e}")

def main():
    """主函数"""
    extractor = HTTPSDomainExtractor()
    
    # 查找所有相关的日志文件
    log_files = [
        "mitm_url_capture.log",
        "deep_capture_stream.log", 
        "mitm_ssl_bypass_test.log",
        "mitm_test_capture.log"
    ]
    
    # 生成报告
    report = extractor.generate_comprehensive_report(log_files)
    
    if report:
        # 保存到文件
        output_file = f"https_blocked_domains_report_{int(time.time())}.json"
        extractor.save_report(report, output_file)
        
        logger.info("🎉 HTTPS域名分析完成!")
        logger.info(f"📄 详细报告: {output_file}")
    else:
        logger.info("⚠️  未发现被阻止的HTTPS域名")
    
    return 0

if __name__ == "__main__":
    exit(main())


