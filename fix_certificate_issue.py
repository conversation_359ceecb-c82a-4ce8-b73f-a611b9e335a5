#!/usr/bin/env python3
"""
修复mitmproxy证书问题
使用多种方法确保证书被正确安装和信任
"""

import subprocess
import sys
import time
import hashlib
from pathlib import Path

class CertificateFixer:
    def __init__(self):
        self.cert_file = "mitmproxy-ca-cert.pem"
        
    def run_adb(self, cmd, timeout=30):
        """执行adb命令"""
        full_cmd = f"source android_env.sh && adb {cmd}"
        try:
            result = subprocess.run(full_cmd, shell=True, 
                                  capture_output=True, text=True, 
                                  timeout=timeout, check=True)
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            return f"ERROR: {e.stderr}"
        except subprocess.TimeoutExpired:
            return "ERROR: Timeout"
    
    def get_cert_subject_hash(self):
        """获取证书的subject hash"""
        try:
            # 使用openssl获取证书hash
            cmd = f"openssl x509 -inform PEM -subject_hash_old -in {self.cert_file} -noout"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                return result.stdout.strip()
            else:
                # 备用方法
                return "c8750f0d"
        except Exception:
            return "c8750f0d"
    
    def download_fresh_certificate(self):
        """下载新的证书"""
        print("🔄 下载新的mitmproxy证书...")
        
        # 删除旧证书
        if Path(self.cert_file).exists():
            Path(self.cert_file).unlink()
        
        # 下载新证书
        cmd = "curl -x 127.0.0.1:8080 http://mitm.it/cert/pem -o mitmproxy-ca-cert.pem"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0 and Path(self.cert_file).exists():
            print("✅ 新证书下载成功")
            return True
        else:
            print("❌ 证书下载失败")
            return False
    
    def install_system_certificate_with_root(self):
        """使用root权限安装系统证书"""
        print("🔧 尝试使用root权限安装系统证书...")
        
        # 检查root权限
        result = self.run_adb("shell su -c 'id'")
        if "uid=0" not in result:
            print("❌ 没有root权限")
            return False
        
        print("✅ 检测到root权限")
        
        # 获取证书hash
        cert_hash = self.get_cert_subject_hash()
        system_cert_name = f"{cert_hash}.0"
        
        print(f"📋 证书hash: {cert_hash}")
        
        # 推送证书到设备
        self.run_adb(f"push {self.cert_file} /sdcard/{self.cert_file}")
        
        # 重新挂载系统分区为可写
        print("🔧 重新挂载系统分区...")
        self.run_adb("shell su -c 'mount -o rw,remount /system'")
        
        # 复制证书到系统目录
        print("📁 安装系统证书...")
        self.run_adb(f"shell su -c 'cp /sdcard/{self.cert_file} /system/etc/security/cacerts/{system_cert_name}'")
        
        # 设置权限
        self.run_adb(f"shell su -c 'chmod 644 /system/etc/security/cacerts/{system_cert_name}'")
        self.run_adb(f"shell su -c 'chown root:root /system/etc/security/cacerts/{system_cert_name}'")
        
        # 重新挂载为只读
        self.run_adb("shell su -c 'mount -o ro,remount /system'")
        
        # 验证安装
        result = self.run_adb(f"shell ls -la /system/etc/security/cacerts/{system_cert_name}")
        if system_cert_name in result and "ERROR" not in result:
            print("✅ 系统证书安装成功")
            return True
        else:
            print("❌ 系统证书安装失败")
            return False
    
    def install_user_certificate_automated(self):
        """自动化安装用户证书"""
        print("🔧 尝试自动化安装用户证书...")
        
        # 推送证书到Downloads目录
        self.run_adb(f"push {self.cert_file} /sdcard/Download/{self.cert_file}")
        
        # 尝试通过Intent安装证书
        print("📱 通过Intent安装证书...")
        
        # 方法1: 直接打开证书文件
        self.run_adb(f"shell am start -a android.intent.action.VIEW -t application/x-x509-ca-cert -d file:///sdcard/Download/{self.cert_file}")
        time.sleep(3)
        
        # 方法2: 打开安全设置
        self.run_adb("shell am start -a android.settings.SECURITY_SETTINGS")
        time.sleep(2)
        
        print("📋 请在Android设备上完成证书安装:")
        print("   1. 如果弹出证书安装对话框，请点击'安装'")
        print("   2. 或者在安全设置中找到'从存储设备安装'")
        print("   3. 选择Download目录中的mitmproxy-ca-cert.pem")
        print("   4. 输入证书名称: mitmproxy")
        print("   5. 选择用途: VPN和应用")
        
        return True
    
    def enable_user_cert_for_apps(self):
        """启用应用信任用户证书"""
        print("🔧 尝试启用应用信任用户证书...")
        
        # 检查是否有Magisk
        result = self.run_adb("shell su -c 'which magisk'")
        if "ERROR" not in result and result:
            print("✅ 检测到Magisk，创建证书模块...")
            
            # 创建Magisk模块
            module_dir = "/data/adb/modules/mitmproxy_cert"
            self.run_adb(f"shell su -c 'mkdir -p {module_dir}/system/etc/security/cacerts'")
            
            # 复制证书
            cert_hash = self.get_cert_subject_hash()
            system_cert_name = f"{cert_hash}.0"
            
            self.run_adb(f"shell su -c 'cp /sdcard/{self.cert_file} {module_dir}/system/etc/security/cacerts/{system_cert_name}'")
            self.run_adb(f"shell su -c 'chmod 644 {module_dir}/system/etc/security/cacerts/{system_cert_name}'")
            
            # 创建模块配置
            module_prop = """id=mitmproxy_cert
name=Mitmproxy Certificate
version=v1.0
versionCode=1
author=APK_Detect
description=Install mitmproxy certificate as system cert"""
            
            self.run_adb(f"shell su -c 'echo \"{module_prop}\" > {module_dir}/module.prop'")
            
            print("✅ Magisk模块创建完成")
            return True
        else:
            print("⚠️  未检测到Magisk")
            return False
    
    def restart_android_system(self):
        """重启Android系统"""
        print("🔄 重启Android系统使证书生效...")
        self.run_adb("reboot")
        print("⏳ 设备重启中，请等待...")
        
        # 等待设备重启
        time.sleep(60)
        
        # 等待设备连接
        for i in range(30):
            result = self.run_adb("devices")
            if "device" in result:
                print("✅ 设备重启完成")
                return True
            time.sleep(5)
        
        print("⚠️  设备重启超时")
        return False
    
    def test_certificate_after_fix(self):
        """修复后测试证书"""
        print("🔍 测试证书修复效果...")
        
        # 重新设置代理
        self.run_adb("shell settings put global http_proxy 192.168.1.123:8080")
        
        # 清空捕获文件
        import json
        realtime_file = Path("mitm-logs/realtime_capture.json")
        if realtime_file.exists():
            with open(realtime_file, 'w') as f:
                json.dump([], f)
        
        time.sleep(2)
        
        # 启动应用测试
        print("🚀 启动应用测试HTTPS连接...")
        self.run_adb("shell am broadcast -a android.intent.action.MAIN -n com.yjzx.yjzx2017/com.yjzx.yjzx2017.controller.activity.MainActivity")
        time.sleep(5)
        
        # 检查结果
        try:
            if realtime_file.exists():
                with open(realtime_file, 'r') as f:
                    data = json.load(f)
                    if isinstance(data, list) and len(data) > 0:
                        https_requests = [req for req in data if req.get('scheme') == 'https']
                        if https_requests:
                            print(f"🎉 证书修复成功！捕获到 {len(https_requests)} 个HTTPS请求！")
                            for req in https_requests[:3]:
                                print(f"   ✅ {req.get('method', 'N/A')} {req.get('url', 'N/A')}")
                            return True
        except Exception as e:
            print(f"⚠️  检查异常: {e}")
        
        print("❌ 证书修复后仍然无法捕获HTTPS请求")
        return False
    
    def run_certificate_fix(self):
        """运行完整的证书修复流程"""
        print("🚀 mitmproxy证书问题修复工具")
        print("🔧 使用多种方法确保证书正确安装")
        print("=" * 60)
        
        # 步骤1: 下载新证书
        if not self.download_fresh_certificate():
            print("❌ 无法下载证书，请检查mitmproxy是否运行")
            return False
        
        # 步骤2: 尝试root方法安装系统证书
        print("\n" + "="*40)
        print("步骤1: 尝试root方法安装系统证书")
        print("="*40)
        
        root_success = self.install_system_certificate_with_root()
        
        if root_success:
            print("✅ 系统证书安装成功，重启设备...")
            self.restart_android_system()
            
            # 重新设置代理
            self.run_adb("shell settings put global http_proxy 192.168.1.123:8080")
            
            # 测试效果
            if self.test_certificate_after_fix():
                print("🎉 证书问题完全解决！")
                return True
        
        # 步骤3: 尝试用户证书方法
        print("\n" + "="*40)
        print("步骤2: 尝试用户证书方法")
        print("="*40)
        
        self.install_user_certificate_automated()
        
        # 步骤4: 尝试Magisk方法
        print("\n" + "="*40)
        print("步骤3: 尝试Magisk方法")
        print("="*40)
        
        magisk_success = self.enable_user_cert_for_apps()
        
        if magisk_success:
            print("✅ Magisk模块安装成功，重启设备...")
            self.restart_android_system()
            
            # 重新设置代理
            self.run_adb("shell settings put global http_proxy 192.168.1.123:8080")
            
            # 测试效果
            if self.test_certificate_after_fix():
                print("🎉 证书问题完全解决！")
                return True
        
        # 最终建议
        print("\n" + "="*60)
        print("📋 手动安装建议")
        print("="*60)
        print("如果自动安装失败，请手动完成以下步骤:")
        print("1. 在Android设备上打开设置")
        print("2. 进入 安全 -> 加密与凭据")
        print("3. 点击 从存储设备安装")
        print("4. 选择 Download/mitmproxy-ca-cert.pem")
        print("5. 输入名称: mitmproxy")
        print("6. 选择用途: VPN和应用")
        print("7. 重启设备")
        
        return False

def main():
    fixer = CertificateFixer()
    
    try:
        success = fixer.run_certificate_fix()
        
        if success:
            print("\n🎯 证书问题已解决！现在可以进行完整的HTTPS网络抓包了！")
        else:
            print("\n🔧 需要手动完成证书安装")
            
    except KeyboardInterrupt:
        print("\n⚠️  修复被用户中断")
    except Exception as e:
        print(f"\n❌ 修复异常: {e}")

if __name__ == "__main__":
    main()
