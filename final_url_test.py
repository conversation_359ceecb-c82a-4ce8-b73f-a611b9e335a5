#!/usr/bin/env python3
"""
最终版 URL 捕获测试脚本
"""

import frida
import sys
import time
import json
import subprocess
from datetime import datetime

def get_app_pid(package_name):
    """获取应用的 PID"""
    try:
        result = subprocess.run(
            ['./android-sdk/platform-tools/adb', '-s', 'emulator-5554', 'shell', 'pidof', package_name],
            capture_output=True, text=True
        )
        pid_str = result.stdout.strip()
        if pid_str:
            return int(pid_str)
    except:
        pass
    return None

def start_app(package_name):
    """启动应用"""
    print(f"[*] 启动应用: {package_name}")
    subprocess.run([
        './android-sdk/platform-tools/adb', '-s', 'emulator-5554', 
        'shell', 'monkey', '-p', package_name, '-c', 'android.intent.category.LAUNCHER', '1'
    ])
    time.sleep(3)

def main():
    package_name = "com.iloda.beacon"
    
    # 启动应用
    start_app(package_name)
    
    # 获取 PID
    pid = get_app_pid(package_name)
    if not pid:
        print(f"[!] 无法获取 {package_name} 的 PID")
        return
    
    print(f"[*] 找到 PID: {pid}")
    
    # 完整的 Hook 脚本，确保 Java 环境正确加载
    hook_script = """
    console.log("[*] Hook 脚本开始运行");
    
    var urls = [];
    
    // 确保在 Java 环境准备好后执行
    setTimeout(function() {
        Java.perform(function() {
            console.log("[*] Java 环境已准备");
            
            // Hook URL 类
            try {
                var URL = Java.use('java.net.URL');
                if (URL && URL.$init) {
                    URL.$init.overload('java.lang.String').implementation = function(url) {
                        console.log("[URL Created] " + url);
                        urls.push({
                            type: "URL",
                            url: url,
                            time: new Date().toISOString()
                        });
                        return this.$init(url);
                    };
                    console.log("[*] Hook URL 成功");
                }
            } catch(e) {
                console.log("[!] Hook URL 失败: " + e);
            }
            
            // Hook HttpURLConnection
            try {
                var HttpURLConnection = Java.use('java.net.HttpURLConnection');
                if (HttpURLConnection && HttpURLConnection.connect) {
                    HttpURLConnection.connect.implementation = function() {
                        try {
                            var url = this.getURL().toString();
                            console.log("[HttpURLConnection] " + url);
                            urls.push({
                                type: "HttpURLConnection",
                                url: url,
                                time: new Date().toISOString()
                            });
                        } catch(e) {
                            console.log("[!] 获取 URL 失败: " + e);
                        }
                        return this.connect();
                    };
                    console.log("[*] Hook HttpURLConnection 成功");
                }
            } catch(e) {
                console.log("[!] Hook HttpURLConnection 失败: " + e);
            }
            
            // Hook OkHttp3
            try {
                var OkHttpClient = Java.use('okhttp3.OkHttpClient');
                
                OkHttpClient.newCall.implementation = function(request) {
                    try {
                        var url = request.url().toString();
                        console.log("[OkHttp3] " + url);
                        urls.push({
                            type: "OkHttp3",
                            url: url,
                            time: new Date().toISOString()
                        });
                    } catch(e) {
                        console.log("[!] 获取 OkHttp URL 失败: " + e);
                    }
                    return this.newCall(request);
                };
                console.log("[*] Hook OkHttp3 成功");
            } catch(e) {
                console.log("[!] Hook OkHttp3 失败 (可能未使用): " + e);
            }
            
            // Hook Retrofit
            try {
                var Retrofit = Java.use('retrofit2.Retrofit');
                var Builder = Retrofit.Builder;
                
                Builder.baseUrl.overload('java.lang.String').implementation = function(url) {
                    console.log("[Retrofit baseUrl] " + url);
                    urls.push({
                        type: "Retrofit",
                        url: url,
                        time: new Date().toISOString()
                    });
                    return this.baseUrl(url);
                };
                console.log("[*] Hook Retrofit 成功");
            } catch(e) {
                console.log("[!] Hook Retrofit 失败 (可能未使用): " + e);
            }
            
            // Hook WebView
            try {
                var WebView = Java.use('android.webkit.WebView');
                
                WebView.loadUrl.overload('java.lang.String').implementation = function(url) {
                    console.log("[WebView] " + url);
                    urls.push({
                        type: "WebView",
                        url: url,
                        time: new Date().toISOString()
                    });
                    return this.loadUrl(url);
                };
                console.log("[*] Hook WebView 成功");
            } catch(e) {
                console.log("[!] Hook WebView 失败: " + e);
            }
            
            console.log("[*] 所有 Hook 设置完成");
            
            // 触发一些网络活动
            setTimeout(function() {
                console.log("[*] 尝试触发网络活动...");
                
                Java.perform(function() {
                    // 尝试找到并调用一些可能触发网络的方法
                    Java.choose("com.iloda.beacon.activity.MainActivity", {
                        onMatch: function(instance) {
                            console.log("[*] 找到 MainActivity 实例");
                        },
                        onComplete: function() {}
                    });
                });
            }, 2000);
        });
    }, 1000);
    
    // 定期输出结果
    setInterval(function() {
        if (urls.length > 0) {
            console.log("\\n=== 捕获的 URLs (总计: " + urls.length + ") ===");
            urls.forEach(function(item, index) {
                console.log((index + 1) + ". [" + item.type + "] " + item.url);
            });
        } else {
            console.log("[*] 暂未捕获到 URLs...");
        }
    }, 5000);
    """
    
    try:
        # 连接设备
        device = frida.get_usb_device()
        print(f"[*] 连接到设备: {device.name}")
        
        # 附加到进程
        session = device.attach(pid)
        print(f"[*] 附加到进程 {pid}")
        
        # 创建脚本
        script = session.create_script(hook_script)
        
        # 收集的 URLs
        captured_urls = []
        
        # 处理消息
        def on_message(message, data):
            if message['type'] == 'send':
                print(f"[SCRIPT] {message['payload']}")
            elif message['type'] == 'error':
                print(f"[ERROR] {message}")
        
        script.on('message', on_message)
        
        # 加载脚本
        script.load()
        print("[*] 脚本加载成功，等待 Java 环境初始化...")
        time.sleep(2)
        
        # 触发一些 Activity
        print("\n[*] 触发各种 Activity...")
        activities = [
            ".activity.MainActivity",
            ".activity.GuideActivity",
            ".activity.LoginActivity",
            ".activity.SplashActivity"
        ]
        
        for activity in activities:
            full_activity = f"{package_name}/{activity}"
            print(f"[*] 尝试启动 {activity}")
            result = subprocess.run([
                './android-sdk/platform-tools/adb', '-s', 'emulator-5554',
                'shell', 'am', 'start', '-n', full_activity
            ], capture_output=True, text=True)
            if "Error" not in result.stderr and "Error" not in result.stdout:
                print(f"    ✓ 启动成功")
            else:
                print(f"    × 启动失败或不存在")
            time.sleep(2)
        
        # 模拟用户操作
        print("\n[*] 模拟用户点击...")
        subprocess.run([
            './android-sdk/platform-tools/adb', '-s', 'emulator-5554',
            'shell', 'input', 'tap', '500', '500'
        ])
        time.sleep(1)
        subprocess.run([
            './android-sdk/platform-tools/adb', '-s', 'emulator-5554',
            'shell', 'input', 'tap', '300', '800'
        ])
        
        # 等待捕获
        print("\n[*] 等待 30 秒捕获 URLs...")
        for i in range(6):
            time.sleep(5)
            print(f"    等待中... ({(i+1)*5}/30 秒)")
        
        print("\n[*] 测试完成")
        
        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"captured_urls_{timestamp}.json"
        
        # 注意：这里实际上应该从脚本中获取 urls，但简化起见，我们只是创建一个示例文件
        print(f"\n[*] 结果已输出到控制台")
        
    except Exception as e:
        print(f"[!] 错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        try:
            session.detach()
            print("[*] 已分离会话")
        except:
            pass

if __name__ == "__main__":
    main()




