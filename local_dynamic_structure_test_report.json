{"timestamp": 1756973996.082091, "test_type": "structure", "summary": {"total": 4, "passed": 4, "failed": 0, "errors": 0, "success_rate": 100.0}, "results": {"Import Test": {"status": "PASS", "details": {"LocalEmulatorManager": "SUCCESS", "LocalDynamicAnalyzer": "SUCCESS", "LocalUIAutomator": "SUCCESS", "local_dynamic API": "SUCCESS"}}, "Class Instantiation Test": {"status": "PASS", "details": {"LocalUIAutomator": "SUCCESS", "LocalEmulatorManager": "EXPECTED_FAILURE: Android emulator not found", "LocalDynamicAnalyzer": "EXPECTED_FAILURE: Android emulator not found"}}, "API Route Test": {"status": "PASS", "details": {"local_dynamic_import": "SUCCESS", "router_exists": "SUCCESS", "route_count": 9, "route_paths": ["/local-dynamic/analyze", "/local-dynamic/emulator/status", "/local-dynamic/emulator/start", "/local-dynamic/emulator/stop/{instance_name}", "/local-dynamic/emulator/stop-all", "/local-dynamic/emulator/avds", "/local-dynamic/emulator/create-avd", "/local-dynamic/cleanup", "/local-dynamic/health"]}}, "Model Structure Test": {"status": "PASS", "details": {"RuntimeInfo": "SUCCESS", "EmulatorInstance": "SUCCESS", "EmulatorStatus": "SUCCESS", "UIElement": "SUCCESS", "InteractionAction": "SUCCESS", "InteractionType": "SUCCESS", "DynamicAnalysisResult": "SUCCESS"}}}}