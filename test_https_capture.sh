#!/bin/bash
# HTTPS捕获测试脚本

echo "🔍 测试HTTPS捕获..."

# 清空捕获文件
echo "[]" > mitm-logs/realtime_capture.json
echo "🗑️  清空捕获文件"

# 设置环境
source android_env.sh

# 触发HTTPS请求
echo "🌐 触发HTTPS请求..."
adb shell am start -a android.intent.action.VIEW -d https://httpbin.org/get
sleep 8
adb shell am start -a android.intent.action.VIEW -d https://www.baidu.com
sleep 8

# 应用内操作
echo "📱 应用内操作..."
adb shell input tap 540 960
sleep 5

# 等待处理
echo "⏳ 等待网络请求处理..."
sleep 20

# 检查结果
echo "📊 检查捕获结果..."
HTTPS_COUNT=$(python3 -c "
import json
try:
    with open('mitm-logs/realtime_capture.json', 'r') as f:
        data = json.load(f)
        if isinstance(data, list):
            https_count = len([req for req in data if req.get('scheme') == 'https'])
            print(https_count)
        else:
            print(0)
except:
    print(0)
")

TOTAL_COUNT=$(python3 -c "
import json
try:
    with open('mitm-logs/realtime_capture.json', 'r') as f:
        data = json.load(f)
        if isinstance(data, list):
            print(len(data))
        else:
            print(0)
except:
    print(0)
")

echo "📊 捕获统计:"
echo "   总请求数: $TOTAL_COUNT"
echo "   HTTPS请求数: $HTTPS_COUNT"

if [ "$HTTPS_COUNT" -gt 0 ]; then
    echo "🎉 SSL绕过成功！捕获到 $HTTPS_COUNT 个HTTPS请求！"
    echo "✅ 系统现在可以完全分析HTTPS流量！"
else
    echo "⚠️  未捕获到HTTPS请求"
    echo "🔧 请检查Frida SSL绕过是否正常运行"
fi
