{"start_time": "2025-09-05T13:20:30.111047", "package": "com.yjzx.yjzx2017", "method": "broadcast_trigger", "activities_tested": [{"name": "com.yjzx.yjzx2017.controller.camera.CameraNewActivity", "index": 1, "method": "broadcast_trigger", "start_time": "2025-09-05T13:20:30.185369", "launch_success": true, "network_before": 2, "network_after": 2, "network_increase": 0, "error_message": null, "end_time": "2025-09-05T13:20:35.314290"}, {"name": "com.yjzx.yjzx2017.controller.camera.CameraTakeShowActivity", "index": 2, "method": "broadcast_trigger", "start_time": "2025-09-05T13:20:37.316876", "launch_success": true, "network_before": 2, "network_after": 2, "network_increase": 0, "error_message": null, "end_time": "2025-09-05T13:20:42.544662"}, {"name": "com.yjzx.yjzx2017.controller.selfAuction.CameraXTestActivity", "index": 3, "method": "broadcast_trigger", "start_time": "2025-09-05T13:20:44.547743", "launch_success": true, "network_before": 2, "network_after": 2, "network_increase": 0, "error_message": null, "end_time": "2025-09-05T13:20:49.686957"}, {"name": "com.yjzx.yjzx2017.controller.activity.auction.AuctionSelfBuyActivity", "index": 4, "method": "broadcast_trigger", "start_time": "2025-09-05T13:20:51.690597", "launch_success": true, "network_before": 2, "network_after": 2, "network_increase": 0, "error_message": null, "end_time": "2025-09-05T13:20:56.821154"}, {"name": "com.yjzx.yjzx2017.controller.selfAuction.SelfAuctionAddActivity", "index": 5, "method": "broadcast_trigger", "start_time": "2025-09-05T13:20:58.826059", "launch_success": true, "network_before": 2, "network_after": 2, "network_increase": 0, "error_message": null, "end_time": "2025-09-05T13:21:03.952799"}, {"name": "com.yjzx.yjzx2017.controller.selfAuction.SelfAuctionConfirmActivity", "index": 6, "method": "broadcast_trigger", "start_time": "2025-09-05T13:21:05.953832", "launch_success": true, "network_before": 2, "network_after": 2, "network_increase": 0, "error_message": null, "end_time": "2025-09-05T13:21:11.089991"}, {"name": "com.yjzx.yjzx2017.controller.selfAuction.SelfAuctionScanCodeActivity", "index": 7, "method": "broadcast_trigger", "start_time": "2025-09-05T13:21:13.093541", "launch_success": true, "network_before": 2, "network_after": 2, "network_increase": 0, "error_message": null, "end_time": "2025-09-05T13:21:18.243833"}, {"name": "com.yjzx.yjzx2017.controller.selfAuction.SelfAuctionManageActivity", "index": 8, "method": "broadcast_trigger", "start_time": "2025-09-05T13:21:20.243974", "launch_success": true, "network_before": 2, "network_after": 2, "network_increase": 0, "error_message": null, "end_time": "2025-09-05T13:21:25.373556"}, {"name": "com.yjzx.yjzx2017.controller.activity.setting.AddressCompanyActivity", "index": 9, "method": "broadcast_trigger", "start_time": "2025-09-05T13:21:27.377960", "launch_success": true, "network_before": 2, "network_after": 2, "network_increase": 0, "error_message": null, "end_time": "2025-09-05T13:21:32.503146"}, {"name": "com.yjzx.yjzx2017.controller.activity.splash.SplashActivity", "index": 10, "method": "broadcast_trigger", "start_time": "2025-09-05T13:21:34.505471", "launch_success": true, "network_before": 2, "network_after": 2, "network_increase": 0, "error_message": null, "end_time": "2025-09-05T13:21:39.632257"}, {"name": "com.yjzx.yjzx2017.controller.activity.MainActivity", "index": 11, "method": "broadcast_trigger", "start_time": "2025-09-05T13:21:41.636569", "launch_success": true, "network_before": 2, "network_after": 2, "network_increase": 0, "error_message": null, "end_time": "2025-09-05T13:21:46.762338"}, {"name": "com.yjzx.yjzx2017.common.MiddleActivity", "index": 12, "method": "broadcast_trigger", "start_time": "2025-09-05T13:21:48.765858", "launch_success": true, "network_before": 2, "network_after": 2, "network_increase": 0, "error_message": null, "end_time": "2025-09-05T13:21:53.915455"}, {"name": "com.yjzx.yjzx2017.common.UriSchemeProcessActivity", "index": 13, "method": "broadcast_trigger", "start_time": "2025-09-05T13:21:55.917629", "launch_success": true, "network_before": 2, "network_after": 2, "network_increase": 0, "error_message": null, "end_time": "2025-09-05T13:22:01.064503"}, {"name": "com.yjzx.yjzx2017.controller.login.activity.LoginActivity", "index": 14, "method": "broadcast_trigger", "start_time": "2025-09-05T13:22:03.069243", "launch_success": true, "network_before": 2, "network_after": 2, "network_increase": 0, "error_message": null, "end_time": "2025-09-05T13:22:08.198354"}, {"name": "com.yjzx.yjzx2017.controller.login.activity.LoginSmsCodeActivity", "index": 15, "method": "broadcast_trigger", "start_time": "2025-09-05T13:22:10.201330", "launch_success": true, "network_before": 2, "network_after": 2, "network_increase": 0, "error_message": null, "end_time": "2025-09-05T13:22:15.319644"}, {"name": "com.yjzx.yjzx2017.controller.login.activity.RegistActivity", "index": 16, "method": "broadcast_trigger", "start_time": "2025-09-05T13:22:17.320842", "launch_success": true, "network_before": 2, "network_after": 2, "network_increase": 0, "error_message": null, "end_time": "2025-09-05T13:22:22.512488"}, {"name": "com.yjzx.yjzx2017.controller.login.activity.ForgotPasswordActivity", "index": 17, "method": "broadcast_trigger", "start_time": "2025-09-05T13:22:24.514012", "launch_success": true, "network_before": 2, "network_after": 2, "network_increase": 0, "error_message": null, "end_time": "2025-09-05T13:22:29.634847"}, {"name": "com.yjzx.yjzx2017.controller.activity.setting.SettingActivity", "index": 18, "method": "broadcast_trigger", "start_time": "2025-09-05T13:22:31.639261", "launch_success": true, "network_before": 2, "network_after": 2, "network_increase": 0, "error_message": null, "end_time": "2025-09-05T13:22:36.767961"}, {"name": "com.yjzx.yjzx2017.controller.activity.setting.OpenFingerActivity", "index": 19, "method": "broadcast_trigger", "start_time": "2025-09-05T13:22:38.772751", "launch_success": true, "network_before": 2, "network_after": 2, "network_increase": 0, "error_message": null, "end_time": "2025-09-05T13:22:43.892397"}, {"name": "com.yjzx.yjzx2017.controller.activity.setting.OpenLoginFingerActivity", "index": 20, "method": "broadcast_trigger", "start_time": "2025-09-05T13:22:45.894371", "launch_success": true, "network_before": 2, "network_after": 2, "network_increase": 0, "error_message": null, "end_time": "2025-09-05T13:22:51.030215"}], "network_captures": [], "summary": {"total_tested": 20, "successful_launches": 20, "failed_launches": 0, "network_activity_detected": 0}, "end_time": "2025-09-05T13:22:53.034897", "total_network_increase": 0}