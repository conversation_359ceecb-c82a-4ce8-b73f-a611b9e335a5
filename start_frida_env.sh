#!/bin/bash
# Frida SSL绕过启动脚本

echo "🚀 启动Frida SSL绕过..."

# 设置环境
source android_env.sh
export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin

# 检查设备
echo "📱 检查设备连接..."
adb devices

# 启动frida-server
echo "🔧 启动frida-server..."
adb shell pkill frida-server 2>/dev/null
sleep 2
adb shell '/data/local/tmp/frida-server' 2>/dev/null &
sleep 8

# 检查frida-server
echo "🔍 检查frida-server状态..."
adb shell ps | grep frida-server

# 启动应用
echo "📱 启动应用..."
adb shell am force-stop com.yjzx.yjzx2017
sleep 2
adb shell am start -n com.yjzx.yjzx2017/.controller.activity.splash.SplashActivity
sleep 5

echo "✅ 环境准备完成"
echo "💡 现在可以运行: frida -U com.yjzx.yjzx2017 -l ssl_bypass.js"
