#!/bin/bash
# 终极可工作的Frida SSL绕过解决方案
# 基于实际测试，确保每一步都能工作

echo "🚀 终极可工作的Frida SSL绕过解决方案"
echo "🔧 解决所有权限问题，确保系统可用"
echo "=============================================="

# 设置环境
source android_env.sh
export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin

echo "📋 步骤1: 确保root权限和环境"
echo "--------------------------------"

# 确保adb root模式
echo "🔧 启动adb root模式..."
adb root
sleep 3

# 验证root权限
ROOT_CHECK=$(adb shell id | grep "uid=0")
if [ -z "$ROOT_CHECK" ]; then
    echo "❌ 无法获取root权限"
    echo "💡 请确保使用支持root的Android模拟器"
    exit 1
fi
echo "✅ 已获得root权限"

echo "📋 步骤2: 设置frida-server"
echo "-------------------------"

# 推送frida-server
echo "🔧 推送frida-server..."
adb push frida-server /data/local/tmp/frida-server
adb shell chmod 755 /data/local/tmp/frida-server
adb shell chown root:root /data/local/tmp/frida-server

# 杀死现有frida-server
echo "🔧 清理现有frida-server进程..."
adb shell pkill -9 frida-server
sleep 3

# 启动frida-server
echo "🔧 启动frida-server..."
adb shell '/data/local/tmp/frida-server' &
FRIDA_PID=$!
sleep 8

# 验证frida-server
FRIDA_CHECK=$(adb shell ps | grep frida-server)
if [ -z "$FRIDA_CHECK" ]; then
    echo "❌ frida-server启动失败"
    exit 1
fi
echo "✅ frida-server启动成功"
echo "   进程信息: $FRIDA_CHECK"

echo "📋 步骤3: 准备目标应用"
echo "---------------------"

PACKAGE="com.yjzx.yjzx2017"

# 强制停止应用
echo "🔧 重启目标应用..."
adb shell am force-stop $PACKAGE
sleep 2

# 启动应用
adb shell am start -n $PACKAGE/.controller.activity.splash.SplashActivity
if [ $? -ne 0 ]; then
    echo "❌ 应用启动失败"
    exit 1
fi
echo "✅ 应用启动成功"
sleep 10

# 获取应用PID
APP_PIDS=$(adb shell ps | grep $PACKAGE)
if [ -z "$APP_PIDS" ]; then
    echo "❌ 未找到应用进程"
    exit 1
fi

echo "✅ 找到应用进程:"
echo "$APP_PIDS"

# 提取主PID
MAIN_PID=$(echo "$APP_PIDS" | grep -v pushcore | head -1 | awk '{print $2}')
PUSHCORE_PID=$(echo "$APP_PIDS" | grep pushcore | head -1 | awk '{print $2}')

if [ -n "$MAIN_PID" ]; then
    TARGET_PID=$MAIN_PID
    echo "✅ 选择主进程 PID: $TARGET_PID"
elif [ -n "$PUSHCORE_PID" ]; then
    TARGET_PID=$PUSHCORE_PID
    echo "✅ 选择pushcore进程 PID: $TARGET_PID"
else
    echo "❌ 无法确定目标PID"
    exit 1
fi

echo "📋 步骤4: 测试Frida连接"
echo "---------------------"

# 测试基础连接
echo "🔧 测试Frida基础连接..."
FRIDA_TEST=$(echo "console.log('Frida连接测试成功!');" | timeout 10 frida -U $TARGET_PID 2>&1)

if echo "$FRIDA_TEST" | grep -q "Frida连接测试成功"; then
    echo "✅ Frida连接测试成功！"
elif echo "$FRIDA_TEST" | grep -q "Connected to Android Emulator"; then
    echo "✅ Frida可以连接到模拟器"
else
    echo "⚠️  Frida连接可能有问题，但继续尝试..."
    echo "   测试输出: $FRIDA_TEST"
fi

echo "📋 步骤5: 启动SSL绕过"
echo "-------------------"

# 检查SSL绕过脚本
if [ ! -f "interactive_ssl_bypass.js" ]; then
    echo "❌ SSL绕过脚本不存在"
    exit 1
fi

echo "🔧 启动Frida SSL绕过..."
echo "💡 使用PID: $TARGET_PID"
echo "💡 脚本: interactive_ssl_bypass.js"
echo ""
echo "🎯 如果看到以下信息说明成功:"
echo "   - '✅ Java.perform 启动成功'"
echo "   - '✅ SSLContext hook 成功'"
echo "   - '🎉 SSL绕过初始化完成！'"
echo ""
echo "⚠️  按Ctrl+C停止SSL绕过"
echo "=============================================="

# 启动SSL绕过（交互式）
frida -U $TARGET_PID -l interactive_ssl_bypass.js

echo ""
echo "📋 SSL绕过已停止"
echo "🧹 清理资源..."

# 清理
adb shell pkill frida-server
echo "✅ 清理完成"
