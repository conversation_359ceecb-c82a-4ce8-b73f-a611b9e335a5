[13:27:49.060] HTTP(S) proxy listening at *:8888.
[13:27:58.447][127.0.0.1:57115] client connect
[13:27:58.459][127.0.0.1:57115] server connect appapi.yjzx.com:443 (198.18.0.80:443)
[13:28:08.824][127.0.0.1:57134] client connect
[13:28:08.833][127.0.0.1:57134] server connect h.trace.qq.com:443 (198.18.0.76:443)
[13:28:08.880][127.0.0.1:57137] client connect
[13:28:08.885][127.0.0.1:57137] server connect api.sobot.com:443 (198.18.0.81:443)
[13:28:08.946][127.0.0.1:57140] client connect
[13:28:08.953][127.0.0.1:57140] server connect android.bugly.qq.com:443 (198.18.0.61:443)
127.0.0.1:57134: POST https://h.trace.qq.com/kv
              << 200 OK 2b
[13:28:09.232][127.0.0.1:57145] client connect
127.0.0.1:57140: POST https://android.bugly.qq.com/rqd/async?aid=44a48e9b-731…
              << 200 OK 95b
[13:28:09.239][127.0.0.1:57145] server connect android.bugly.qq.com:443 (198.18.0.61:443)
[13:28:09.332][127.0.0.1:57148] client connect
[13:28:09.342][127.0.0.1:57148] server connect ce3e75d5.jpush.cn:443 (***********:443)
[13:28:09.354][127.0.0.1:57151] client connect
[13:28:09.365][127.0.0.1:57151] server connect ali-stats.jpush.cn:443 (***********:443)
127.0.0.1:57137: POST https://api.sobot.com/chat-sdk/sdk/user/v2/config.actio…
              << 200  137b
127.0.0.1:57145: POST https://android.bugly.qq.com/rqd/async?aid=506b4c17-3fd…
              << 200 OK 95b
[13:28:09.548][127.0.0.1:57156] client connect
[13:28:09.566][127.0.0.1:57156] server connect status-ipv6.jpush.cn:443 (************:443)
[13:28:09.577][127.0.0.1:57156] Server TLS handshake failed. connection closed
[13:28:09.578][127.0.0.1:57156] Unable to establish TLS connection with server (connection closed). Trying to establish TLS with client anyway. If you plan to redirect requests away from this server, consider setting `connection_strategy` to `lazy` to suppress early connections.
[13:28:09.594][127.0.0.1:57156] server disconnect status-ipv6.jpush.cn:443 (************:443)
127.0.0.1:57156: POST https://status-ipv6.jpush.cn/wi/jx/6ae71c
 << connection closed
[13:28:09.615][127.0.0.1:57156] client disconnect
127.0.0.1:57151: POST https://ali-stats.jpush.cn/v3/report
              << 200 OK 29b
127.0.0.1:57148: POST https://ce3e75d5.jpush.cn/wi/bd0r1q
              << 200 OK 556b
127.0.0.1:57134: POST https://h.trace.qq.com/kv
              << 200 OK 2b
[13:28:09.749][127.0.0.1:57158] client connect
[13:28:09.757][127.0.0.1:57158] server connect msg.cmpassport.com:443 (***********:443)
127.0.0.1:57140: POST https://android.bugly.qq.com/rqd/async?aid=a95dd267-3a9…
              << 200 OK 95b
127.0.0.1:57134: POST https://h.trace.qq.com/kv
              << 200 OK 2b
127.0.0.1:57158: POST https://msg.cmpassport.com/csc/localnum/api/getToken
              << 200 OK 50b
[13:28:10.167][127.0.0.1:57162] client connect
[13:28:10.175][127.0.0.1:57162] server connect ce3e75d5.jpush.cn:443 (***********:443)
127.0.0.1:57134: POST https://h.trace.qq.com/kv
              << 200 OK 2b
[13:28:10.287][127.0.0.1:57165] client connect
[13:28:10.297][127.0.0.1:57165] server connect ali-stats.jpush.cn:443 (***********:443)
127.0.0.1:57165: POST https://ali-stats.jpush.cn/v3/report
              << 200 OK 29b
127.0.0.1:57162: POST https://ce3e75d5.jpush.cn/wi/ra0cde
              << 200 OK 64b
[13:28:40.266][127.0.0.1:57134] server disconnect h.trace.qq.com:443 (198.18.0.76:443)
[13:28:42.860][127.0.0.1:57193] client connect
[13:28:42.864][127.0.0.1:57193] server connect www.gstatic.com:443 (198.18.0.104:443)
127.0.0.1:57193: GET https://www.gstatic.com/android/config_update/07282025-…
              << 200 OK 384b
Server TLS handshake failed. connection closed
[13:28:43.195][127.0.0.1:57193] server disconnect www.gstatic.com:443 (198.18.0.104:443)
[13:28:43.196][127.0.0.1:57193] client disconnect
