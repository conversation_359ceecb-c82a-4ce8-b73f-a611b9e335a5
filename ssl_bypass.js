// Universal SSL Kill Switch for Android
// Bypasses SSL certificate validation for HTTPS traffic capture
// Compatible with most Android apps and HTTP libraries

Java.perform(function() {
    console.log("[+] SSL Kill Switch started");
    console.log("[+] Hooking SSL/TLS certificate validation functions...");
    
    // Hook SSLContext.init()
    try {
        var SSLContext = Java.use("javax.net.ssl.SSLContext");
        SSLContext.init.overload("[Ljavax.net.ssl.KeyManager;", "[Ljavax.net.ssl.TrustManager;", "java.security.SecureRandom").implementation = function(keyManagers, trustManagers, secureRandom) {
            console.log("[+] SSLContext.init() called - bypassing certificate validation");
            
            // Create a custom TrustManager that accepts all certificates
            var TrustManager = Java.use("javax.net.ssl.X509TrustManager");
            var EmptyTrustManager = Java.registerClass({
                name: "com.frida.EmptyTrustManager",
                implements: [TrustManager],
                methods: {
                    checkClientTrusted: function(chain, authType) {
                        console.log("[+] checkClientTrusted called - accepting all client certificates");
                    },
                    checkServerTrusted: function(chain, authType) {
                        console.log("[+] checkServerTrusted called - accepting all server certificates");
                    },
                    getAcceptedIssuers: function() {
                        console.log("[+] getAcceptedIssuers called - returning empty array");
                        return [];
                    }
                }
            });
            
            var emptyTrustManager = EmptyTrustManager.$new();
            this.init(keyManagers, [emptyTrustManager], secureRandom);
        };
        console.log("[+] SSLContext.init() hooked successfully");
    } catch(e) {
        console.log("[-] Failed to hook SSLContext.init(): " + e);
    }
    
    // Hook HostnameVerifier
    try {
        var HostnameVerifier = Java.use("javax.net.ssl.HostnameVerifier");
        var HttpsURLConnection = Java.use("javax.net.ssl.HttpsURLConnection");
        
        HttpsURLConnection.setDefaultHostnameVerifier.implementation = function(hostnameVerifier) {
            console.log("[+] HttpsURLConnection.setDefaultHostnameVerifier() called - bypassing hostname verification");
            
            var EmptyHostnameVerifier = Java.registerClass({
                name: "com.frida.EmptyHostnameVerifier",
                implements: [HostnameVerifier],
                methods: {
                    verify: function(hostname, session) {
                        console.log("[+] HostnameVerifier.verify() called for: " + hostname + " - accepting");
                        return true;
                    }
                }
            });
            
            var emptyHostnameVerifier = EmptyHostnameVerifier.$new();
            this.setDefaultHostnameVerifier(emptyHostnameVerifier);
        };
        
        HttpsURLConnection.setHostnameVerifier.implementation = function(hostnameVerifier) {
            console.log("[+] HttpsURLConnection.setHostnameVerifier() called - bypassing");
            var EmptyHostnameVerifier = Java.registerClass({
                name: "com.frida.EmptyHostnameVerifier2",
                implements: [HostnameVerifier],
                methods: {
                    verify: function(hostname, session) {
                        console.log("[+] HostnameVerifier.verify() called for: " + hostname + " - accepting");
                        return true;
                    }
                }
            });
            var emptyHostnameVerifier = EmptyHostnameVerifier.$new();
            this.setHostnameVerifier(emptyHostnameVerifier);
        };
        console.log("[+] HostnameVerifier hooks installed successfully");
    } catch(e) {
        console.log("[-] Failed to hook HostnameVerifier: " + e);
    }
    
    // Hook OkHttp (very common HTTP client)
    try {
        var OkHttpClient = Java.use("okhttp3.OkHttpClient");
        OkHttpClient.certificatePinner.implementation = function() {
            console.log("[+] OkHttpClient.certificatePinner() called - bypassing certificate pinning");
            return null;
        };
        console.log("[+] OkHttp certificate pinning bypassed");
    } catch(e) {
        console.log("[-] OkHttp not found or failed to hook: " + e);
    }
    
    // Hook OkHttp Builder
    try {
        var OkHttpClientBuilder = Java.use("okhttp3.OkHttpClient$Builder");
        OkHttpClientBuilder.certificatePinner.implementation = function(certificatePinner) {
            console.log("[+] OkHttpClient.Builder.certificatePinner() called - ignoring certificate pinner");
            return this;
        };
        console.log("[+] OkHttp Builder certificate pinning bypassed");
    } catch(e) {
        console.log("[-] OkHttp Builder not found or failed to hook: " + e);
    }
    
    // Hook Apache HTTP Client
    try {
        var DefaultHttpClient = Java.use("org.apache.http.impl.client.DefaultHttpClient");
        DefaultHttpClient.execute.overload("org.apache.http.client.methods.HttpUriRequest").implementation = function(request) {
            console.log("[+] Apache HTTP request intercepted: " + request.getURI());
            return this.execute(request);
        };
        console.log("[+] Apache HTTP Client hooked");
    } catch(e) {
        console.log("[-] Apache HTTP Client not found: " + e);
    }
    
    // Hook TrustManagerFactory
    try {
        var TrustManagerFactory = Java.use("javax.net.ssl.TrustManagerFactory");
        TrustManagerFactory.getTrustManagers.implementation = function() {
            console.log("[+] TrustManagerFactory.getTrustManagers() called - returning custom trust manager");
            
            var TrustManager = Java.use("javax.net.ssl.X509TrustManager");
            var EmptyTrustManager = Java.registerClass({
                name: "com.frida.EmptyTrustManager2",
                implements: [TrustManager],
                methods: {
                    checkClientTrusted: function(chain, authType) {
                        console.log("[+] Custom TrustManager - accepting client certificate");
                    },
                    checkServerTrusted: function(chain, authType) {
                        console.log("[+] Custom TrustManager - accepting server certificate");
                    },
                    getAcceptedIssuers: function() {
                        return [];
                    }
                }
            });
            
            return [EmptyTrustManager.$new()];
        };
        console.log("[+] TrustManagerFactory hooked successfully");
    } catch(e) {
        console.log("[-] Failed to hook TrustManagerFactory: " + e);
    }
    
    // Hook Volley (Google's HTTP library)
    try {
        var HurlStack = Java.use("com.android.volley.toolbox.HurlStack");
        HurlStack.createConnection.implementation = function(url) {
            console.log("[+] Volley HurlStack.createConnection() called for: " + url);
            var connection = this.createConnection(url);
            
            if (connection.getClass().getName().indexOf("HttpsURLConnection") !== -1) {
                console.log("[+] Setting custom HostnameVerifier for Volley HTTPS connection");
                var HostnameVerifier = Java.use("javax.net.ssl.HostnameVerifier");
                var EmptyHostnameVerifier = Java.registerClass({
                    name: "com.frida.VolleyHostnameVerifier",
                    implements: [HostnameVerifier],
                    methods: {
                        verify: function(hostname, session) {
                            console.log("[+] Volley HostnameVerifier - accepting: " + hostname);
                            return true;
                        }
                    }
                });
                connection.setHostnameVerifier(EmptyHostnameVerifier.$new());
            }
            
            return connection;
        };
        console.log("[+] Volley HTTP library hooked");
    } catch(e) {
        console.log("[-] Volley not found: " + e);
    }
    
    // Hook WebView SSL Error Handler
    try {
        var WebViewClient = Java.use("android.webkit.WebViewClient");
        WebViewClient.onReceivedSslError.implementation = function(view, handler, error) {
            console.log("[+] WebView SSL error intercepted - proceeding anyway");
            handler.proceed();
        };
        console.log("[+] WebView SSL error handler bypassed");
    } catch(e) {
        console.log("[-] Failed to hook WebView SSL error handler: " + e);
    }
    
    // Hook Network Security Config (Android 7.0+)
    try {
        var NetworkSecurityPolicy = Java.use("android.security.NetworkSecurityPolicy");
        NetworkSecurityPolicy.getInstance.implementation = function() {
            console.log("[+] NetworkSecurityPolicy.getInstance() called - returning permissive policy");
            
            var policy = this.getInstance();
            
            // Try to hook isCertificateTransparencyVerificationRequired
            try {
                policy.isCertificateTransparencyVerificationRequired.implementation = function(hostname) {
                    console.log("[+] Certificate transparency verification disabled for: " + hostname);
                    return false;
                };
            } catch(e) {
                console.log("[-] Could not hook certificate transparency verification: " + e);
            }
            
            return policy;
        };
        console.log("[+] NetworkSecurityPolicy hooked");
    } catch(e) {
        console.log("[-] NetworkSecurityPolicy not found: " + e);
    }
    
    // Hook common certificate pinning libraries
    
    // Hook Square's CertificatePinner
    try {
        var CertificatePinner = Java.use("okhttp3.CertificatePinner");
        CertificatePinner.check.overload("java.lang.String", "java.util.List").implementation = function(hostname, peerCertificates) {
            console.log("[+] CertificatePinner.check() called for: " + hostname + " - bypassing");
            return;
        };
        console.log("[+] Square CertificatePinner bypassed");
    } catch(e) {
        console.log("[-] Square CertificatePinner not found: " + e);
    }
    
    // Hook TrustKit
    try {
        var TrustKit = Java.use("com.datatheorem.android.trustkit.TrustKit");
        TrustKit.getInstance.implementation = function() {
            console.log("[+] TrustKit.getInstance() called - returning null to disable");
            return null;
        };
        console.log("[+] TrustKit disabled");
    } catch(e) {
        console.log("[-] TrustKit not found: " + e);
    }
    
    console.log("[+] SSL Kill Switch setup complete!");
    console.log("[+] All SSL/TLS certificate validation has been bypassed");
    console.log("[+] HTTPS traffic should now be captured by mitmproxy");
});
