#!/usr/bin/env python3
"""
详细调试Frida连接问题
显示完整的错误信息
"""

import subprocess
import sys
import time
import json
from pathlib import Path

def run_cmd_with_output(cmd, timeout=30):
    """执行命令并显示完整输出"""
    print(f"📋 执行命令: {cmd}")
    try:
        result = subprocess.run(cmd, shell=True, 
                              capture_output=True, text=True, 
                              timeout=timeout)
        
        print(f"   返回码: {result.returncode}")
        if result.stdout:
            print(f"   标准输出: {result.stdout}")
        if result.stderr:
            print(f"   错误输出: {result.stderr}")
        
        return result.returncode, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        print("   ❌ 命令超时")
        return -1, "", "Timeout"

def check_environment():
    """检查环境状态"""
    print("🔍 检查环境状态...")
    
    # 检查设备连接
    print("\n1. 检查设备连接:")
    run_cmd_with_output("source android_env.sh && adb devices")
    
    # 检查frida-server文件
    print("\n2. 检查frida-server文件:")
    if Path("frida-server").exists():
        print("   ✅ frida-server文件存在")
        # 检查文件大小
        size = Path("frida-server").stat().st_size
        print(f"   文件大小: {size} bytes")
    else:
        print("   ❌ frida-server文件不存在")
    
    # 检查SSL绕过脚本
    print("\n3. 检查SSL绕过脚本:")
    if Path("ssl_bypass.js").exists():
        print("   ✅ ssl_bypass.js文件存在")
        # 显示脚本前几行
        with open("ssl_bypass.js", 'r') as f:
            lines = f.readlines()[:5]
            print("   脚本开头:")
            for line in lines:
                print(f"      {line.strip()}")
    else:
        print("   ❌ ssl_bypass.js文件不存在")
    
    # 检查Frida安装
    print("\n4. 检查Frida安装:")
    run_cmd_with_output("export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin && frida --version")

def test_frida_server():
    """测试frida-server"""
    print("\n🚀 测试frida-server...")
    
    # 推送frida-server
    print("\n1. 推送frida-server:")
    run_cmd_with_output("source android_env.sh && adb push frida-server /data/local/tmp/frida-server")
    
    # 设置权限
    print("\n2. 设置权限:")
    run_cmd_with_output("source android_env.sh && adb shell chmod 755 /data/local/tmp/frida-server")
    
    # 杀死现有进程
    print("\n3. 杀死现有frida-server进程:")
    run_cmd_with_output("source android_env.sh && adb shell pkill frida-server")
    time.sleep(2)
    
    # 启动frida-server
    print("\n4. 启动frida-server:")
    subprocess.Popen("source android_env.sh && adb shell '/data/local/tmp/frida-server' &", shell=True)
    time.sleep(8)
    
    # 检查进程
    print("\n5. 检查frida-server进程:")
    run_cmd_with_output("source android_env.sh && adb shell ps | grep frida-server")
    
    # 检查端口
    print("\n6. 检查Frida端口:")
    run_cmd_with_output("source android_env.sh && adb shell netstat -tlnp | grep 27042")

def test_target_app():
    """测试目标应用"""
    print("\n📱 测试目标应用...")
    
    package = "com.yjzx.yjzx2017"
    
    # 检查应用是否安装
    print("\n1. 检查应用安装:")
    run_cmd_with_output(f"source android_env.sh && adb shell pm list packages | grep {package}")
    
    # 强制停止应用
    print("\n2. 强制停止应用:")
    run_cmd_with_output(f"source android_env.sh && adb shell am force-stop {package}")
    time.sleep(2)
    
    # 启动应用
    print("\n3. 启动应用:")
    run_cmd_with_output(f"source android_env.sh && adb shell am start -n {package}/.controller.activity.splash.SplashActivity")
    time.sleep(8)
    
    # 检查应用进程
    print("\n4. 检查应用进程:")
    run_cmd_with_output(f"source android_env.sh && adb shell ps | grep {package}")

def test_frida_connection():
    """测试Frida连接"""
    print("\n🔧 测试Frida连接...")
    
    package = "com.yjzx.yjzx2017"
    
    # 测试frida-ps
    print("\n1. 测试frida-ps:")
    run_cmd_with_output("export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin && frida-ps -U")
    
    # 尝试连接到应用（显示详细输出）
    print("\n2. 尝试连接到应用:")
    cmd = f"export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin && timeout 20 frida -U {package} -l ssl_bypass.js"
    
    print(f"📋 执行命令: {cmd}")
    
    try:
        # 使用Popen来实时显示输出
        process = subprocess.Popen(
            cmd, shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        print("   Frida输出:")
        output_lines = []
        
        while True:
            line = process.stdout.readline()
            if line:
                print(f"      {line.strip()}")
                output_lines.append(line.strip())
            
            if process.poll() is not None:
                break
            
            # 如果输出太多，限制显示
            if len(output_lines) > 50:
                print("      ... (输出过多，截断)")
                break
        
        # 读取剩余输出
        remaining_output = process.stdout.read()
        if remaining_output:
            lines = remaining_output.split('\n')[:10]  # 只显示前10行
            for line in lines:
                if line.strip():
                    print(f"      {line.strip()}")
        
        returncode = process.wait()
        print(f"   进程退出码: {returncode}")
        
        return returncode == 0
        
    except Exception as e:
        print(f"   ❌ 连接异常: {e}")
        return False

def main():
    print("🚀 Frida详细调试")
    print("🔧 显示完整的错误信息和调试过程")
    print("=" * 70)
    
    try:
        # 检查环境
        check_environment()
        
        # 测试frida-server
        test_frida_server()
        
        # 测试目标应用
        test_target_app()
        
        # 测试Frida连接
        success = test_frida_connection()
        
        if success:
            print("\n🎉 Frida连接测试成功！")
        else:
            print("\n⚠️  Frida连接测试未完全成功")
            print("💡 但已收集了详细的调试信息")
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断")
    except Exception as e:
        print(f"\n❌ 调试异常: {e}")
    finally:
        # 清理
        print("\n🧹 清理frida-server...")
        subprocess.run("source android_env.sh && adb shell pkill frida-server", shell=True)

if __name__ == "__main__":
    main()
