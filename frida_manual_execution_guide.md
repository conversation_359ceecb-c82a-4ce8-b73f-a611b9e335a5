
# 🎯 Frida SSL绕过手动执行指南

## 当前状态
✅ frida-server正常运行
✅ 目标应用正常运行  
✅ 设备连接正常
✅ 优化的SSL绕过脚本已准备

## 🔧 手动执行步骤

### 方法1: 直接命令行执行（推荐）
```bash
# 1. 设置环境
source android_env.sh
export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin

# 2. 获取应用PID
adb shell ps | grep yjzx

# 3. 使用PID运行Frida（替换YOUR_PID为实际PID）
frida -U YOUR_PID -l optimized_ssl_bypass.js

# 4. 或者直接使用包名
frida -U com.yjzx.yjzx2017 -l optimized_ssl_bypass.js
```

### 方法2: Spawn模式
```bash
frida -U -f com.yjzx.yjzx2017 -l optimized_ssl_bypass.js
```

### 方法3: 交互式模式
```bash
frida -U com.yjzx.yjzx2017
# 然后在Frida控制台中执行：
%load optimized_ssl_bypass.js
```

## 🔍 验证SSL绕过效果

### 1. 启动mitmproxy（另一个终端）
```bash
mitmdump -s mitm-scripts/capture.py --listen-port 8080
```

### 2. 设置Android代理
```bash
adb shell settings put global http_proxy *************:8080
```

### 3. 触发HTTPS请求
```bash
adb shell am start -a android.intent.action.VIEW -d https://httpbin.org/get
adb shell am start -a android.intent.action.VIEW -d https://www.baidu.com
```

### 4. 检查捕获结果
```bash
cat mitm-logs/realtime_capture.json
```

## 🎉 成功指标
- Frida控制台显示 "[SSL Bypass] SSL bypass initialization completed!"
- mitmproxy捕获到HTTPS请求
- realtime_capture.json文件包含HTTPS流量

## 🔧 故障排除
- 如果遇到"system_server"错误：这是模拟器环境的正常现象，可以忽略
- 如果连接失败：重启应用或使用不同的PID
- 如果没有捕获到HTTPS：检查代理设置和mitmproxy状态

## 💡 提示
- SSL绕过脚本已经优化，兼容性更好
- 可以同时运行多个测试来验证效果
- 系统现在具备完整的APK动态分析能力
