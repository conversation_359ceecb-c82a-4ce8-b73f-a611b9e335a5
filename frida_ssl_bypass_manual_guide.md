
# Frida SSL绕过完整手动指南

## 🎯 目标
实现对易金在线APK的HTTPS流量完全捕获，绕过SSL证书验证。

## 📋 前置条件检查
1. Android模拟器运行正常
2. frida-server文件存在
3. ssl_bypass.js脚本存在
4. Frida工具已安装
5. 目标应用已安装

## 🔧 手动执行步骤

### 步骤1: 启动frida-server
```bash
# 设置环境
source android_env.sh

# 检查设备连接
adb devices

# 推送frida-server（如果需要）
adb push frida-server /data/local/tmp/frida-server
adb shell chmod 755 /data/local/tmp/frida-server

# 启动frida-server（忽略SELinux警告）
adb shell '/data/local/tmp/frida-server &'

# 验证启动
adb shell ps | grep frida-server
```

### 步骤2: 准备目标应用
```bash
# 停止应用
adb shell am force-stop com.yjzx.yjzx2017

# 启动应用
adb shell am start -n com.yjzx.yjzx2017/.controller.activity.splash.SplashActivity

# 检查应用进程
adb shell ps | grep yjzx
```

### 步骤3: 运行Frida SSL绕过
```bash
# 设置PATH
export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin

# 方法1: 直接附加到应用
frida -U com.yjzx.yjzx2017 -l ssl_bypass.js

# 方法2: 使用PID附加（如果知道PID）
frida -U [PID] -l ssl_bypass.js

# 方法3: Spawn模式
frida -U -f com.yjzx.yjzx2017 -l ssl_bypass.js
```

### 步骤4: 测试HTTPS捕获
在另一个终端中执行：
```bash
# 清空捕获文件
echo "[]" > mitm-logs/realtime_capture.json

# 触发HTTPS请求
adb shell am start -a android.intent.action.VIEW -d https://httpbin.org/get
adb shell am start -a android.intent.action.VIEW -d https://www.baidu.com

# 应用内操作
adb shell input tap 540 960

# 等待并检查结果
sleep 20
cat mitm-logs/realtime_capture.json
```

## 🔧 故障排除

### 问题1: "system_server"错误
这是Android模拟器环境的已知问题，可以安全忽略。

### 问题2: "unable to access process"
- 重启frida-server
- 重启应用
- 尝试不同的连接方法

### 问题3: SELinux警告
这些警告不影响功能，可以安全忽略。

### 问题4: 没有捕获到HTTPS请求
- 检查mitmproxy是否运行
- 检查代理设置
- 确认SSL绕过脚本已加载

## 💡 成功指标
1. frida-server进程正在运行
2. Frida成功连接到应用
3. SSL绕过脚本加载成功
4. 在realtime_capture.json中看到HTTPS请求

## 🎉 成功后的能力
- 完整的APK动态分析
- HTTPS流量透明捕获
- SSL证书验证绕过
- 实时网络监控
- API端点发现

## 📞 如果仍有问题
1. 检查所有前置条件
2. 重启模拟器和所有服务
3. 尝试不同的Frida连接方法
4. 查看详细的错误日志
