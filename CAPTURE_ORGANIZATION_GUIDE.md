# 📂 APK动态分析结果组织系统使用指南

## 🎯 系统概述

您的动态分析系统现在具备了**专业级的文件组织能力**！所有捕获结果会自动按APP分类整理，每个APP的每次分析都会创建独立的会话文件夹。

## 📁 目录结构

```
capture_results/
├── CAPTURE_RESULTS_SUMMARY.md     # 📊 总览报告
├── apps_index.json                # 📋 应用索引文件
├── com.iloda.beacon/              # 📱 应用文件夹 (按包名)
│   └── session_20250909_114601/   # 🕐 会话文件夹 (按时间)
│       ├── analysis_reports/      # 📄 分析报告
│       │   ├── real_apk_analysis_report_*.json
│       │   ├── https_blocked_domains_report_*.json
│       │   ├── simple_dynamic_test_report_*.json
│       │   └── production_test_report_*.json
│       ├── network_logs/          # 🌐 网络流量日志
│       │   ├── mitm_real_capture.log
│       │   ├── deep_capture_stream.log
│       │   └── captured_https_urls.json
│       ├── screenshots/           # 📸 应用截图
│       │   └── real_analysis_screenshot_*.png
│       ├── ui_dumps/             # 🖱️  UI结构文件
│       │   ├── real_analysis_ui_dump_*.xml
│       │   └── ui_dump_test_*.xml
│       ├── frida_logs/           # 🔒 SSL绕过日志
│       ├── certificates/         # 🔐 证书文件
│       ├── raw_data/            # 📦 原始数据
│       └── session_metadata.json # 📋 会话元数据
└── com.other.app/               # 🔄 其他应用...
    └── session_*/
        ├── analysis_reports/
        ├── network_logs/
        └── ...
```

## 🚀 使用方法

### 1. 自动整理现有文件

```bash
# 整理当前目录下的所有分析文件
python capture_result_organizer.py
```

### 2. 集成化分析 (推荐)

```python
# 使用集成化分析系统，自动整理结果
python integrated_dynamic_analysis.py

# 或者在代码中使用
from integrated_dynamic_analysis import IntegratedDynamicAnalysis

analyzer = IntegratedDynamicAnalysis("com.example.app", "path/to/app.apk")
result = await analyzer.run_complete_analysis()
```

### 3. 手动整理指定应用

```python
from capture_result_organizer import CaptureResultOrganizer

organizer = CaptureResultOrganizer()
result = organizer.organize_files_for_app("com.target.app")
```

## 📊 当前整理成果

### ✅ 已整理: com.iloda.beacon

**会话**: `20250909_114601`  
**文件总数**: 13个  
**包含内容**:
- ✅ **4个分析报告**: 完整的JSON格式分析结果
- ✅ **4个网络日志**: mitmproxy捕获日志和HTTPS域名分析
- ✅ **2张截图**: 应用运行时的界面截图
- ✅ **3个UI dump**: 完整的界面结构XML文件

**关键发现**:
- 🌐 网络流量已捕获 (19个被阻止的HTTPS域名)
- 📸 UI截图已保存 (应用完整界面)
- 📊 分析报告已生成 (多维度分析数据)

## 🔍 文件说明

### 📄 分析报告 (analysis_reports/)
- **real_apk_analysis_report_*.json**: 真实APK的完整动态分析结果
- **https_blocked_domains_report_*.json**: SSL证书固定域名分析报告
- **simple_dynamic_test_report_*.json**: 基础功能测试报告
- **production_test_report_*.json**: 生产级测试报告

### 🌐 网络日志 (network_logs/)
- **mitm_*.log**: mitmproxy代理服务器日志
- **captured_https_urls.json**: 成功解密的HTTPS请求
- **deep_capture_stream.log**: 深度捕获流数据

### 📸 截图 (screenshots/)
- **real_analysis_screenshot_*.png**: 应用运行时的实际截图
- 包含权限对话框、登录界面等关键界面状态

### 🖱️ UI结构 (ui_dumps/)
- ***.xml**: Android UIAutomator生成的界面结构文件
- 包含所有UI元素的详细信息（位置、属性、可点击性等）

### 📋 会话元数据 (session_metadata.json)
```json
{
  "session_info": {
    "package_name": "com.iloda.beacon",
    "session_id": "20250909_114601",
    "start_time": "2025-09-09T11:46:01.417761",
    "analysis_type": "dynamic_analysis"
  },
  "file_summary": {
    "analysis_reports": 4,
    "network_logs": 4,
    "screenshots": 2,
    "ui_dumps": 3
  },
  "key_findings": [
    "网络流量已捕获",
    "UI截图已保存", 
    "分析报告已生成"
  ]
}
```

## 🛠️ 高级功能

### 📋 应用索引系统
- **apps_index.json**: 包含所有分析过的应用信息
- 自动跟踪每个应用的分析历史
- 提供快速查找和统计功能

### 📊 总览报告
- **CAPTURE_RESULTS_SUMMARY.md**: 人类可读的汇总报告
- 包含所有应用的分析统计
- 显示最近的分析活动

### 🔄 自动化集成
- 新的分析会自动创建时间戳会话文件夹
- 相关文件自动分类到正确的子目录
- 元数据自动生成和维护

## 💡 最佳实践

### 1. 分析新APP时
```bash
# 方法1: 使用集成系统 (推荐)
python integrated_dynamic_analysis.py

# 方法2: 手动分析后整理
# ... 运行你的分析脚本 ...
python capture_result_organizer.py
```

### 2. 查看历史分析
```bash
# 查看总览
cat capture_results/CAPTURE_RESULTS_SUMMARY.md

# 查看应用索引
cat capture_results/apps_index.json | python -m json.tool
```

### 3. 比较不同会话
```bash
# 比较同一应用的不同分析会话
ls capture_results/com.target.app/session_*/
```

## 🎉 优势特性

### ✅ 组织性
- 📱 **按应用分组**: 每个APK有独立的分析空间
- 🕐 **按时间分会话**: 每次分析都有独立的时间标识
- 📂 **按类型分类**: 不同类型的文件分门别类

### ✅ 可追溯性
- 📋 **完整元数据**: 每个会话都有详细的元数据记录
- 🔍 **快速定位**: 通过索引文件快速找到目标分析结果
- 📊 **统计分析**: 自动生成统计和趋势分析

### ✅ 扩展性
- 🔧 **易于扩展**: 可以轻松添加新的文件类型和分析工具
- 🤖 **自动化友好**: API设计便于脚本和工具集成
- 📈 **批量处理**: 支持批量分析和结果管理

---

**🎊 恭喜！您的APK动态分析系统现在具备了专业级的文件组织和管理能力！**




