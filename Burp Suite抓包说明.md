使用 Burp Suite 对模拟器中的 APK 进行 HTTPS 流量抓取，需要完成 Burp 自身配置、模拟器代理设置、证书安装等步骤，以下是详细操作：
一、Burp Suite 基础配置
设置监听端口
打开 Burp Suite，默认进入 Proxy 标签页，切换到 Options 子标签。
在 Proxy Listeners 区域，点击 Add，设置监听地址为 127.0.0.1（本地回环），端口自定义（如 8888，确保未被占用），点击 OK。
确保该监听器状态为 Running（绿色对勾）。
配置 SSL 证书
仍然在 Proxy → Options 中，找到 SSL Certificate 区域，点击 Export CA certificate，选择格式为 DER（后缀 .der），保存到电脑（如 burp_cert.der）。
二、模拟器网络代理设置
获取电脑本地 IP
确保模拟器与电脑在同一网络（如均连接本地局域网）。
在电脑命令行中执行 ipconfig（Windows）或 ifconfig（Mac/Linux），获取本地 IP 地址（如 *************）。
设置模拟器代理
打开 Android 模拟器，进入 设置 → WLAN，长按已连接的 WiFi，选择 修改网络。
勾选 显示高级选项，代理设置选择 手动：
代理服务器主机名：填写电脑本地 IP（如 *************）
代理服务器端口：填写 Burp 监听的端口（如 8888）
点击 保存，确保代理生效。
三、安装 Burp 证书到模拟器（关键：抓取 HTTPS）
Android 对 HTTPS 证书有严格校验，必须将 Burp 证书安装为系统信任证书才能解密流量。
方法 1：通过浏览器下载安装（适用于 Android 7.0 以下）
在模拟器中打开浏览器，访问 http://burp（注意：不是 https）。
页面会显示 Burp 证书下载链接，点击下载（默认文件名可能为 cacert.der）。
下载完成后，进入 设置 → 安全 → 从存储设备安装，找到下载的证书文件，命名（如 BurpCA）并确认安装。
方法 2：手动推送证书到系统目录（适用于 Android 7.0 以上，需模拟器 root）
转换证书格式：
将之前保存的 burp_cert.der 转换为 PEM 格式（Android 系统需要）：
bash
openssl x509 -inform DER -in burp_cert.der -out burp_cert.pem

获取证书哈希值（用于命名系统证书）：
bash
openssl x509 -inform PEM -subject_hash_old -in burp_cert.pem | head -1

假设输出哈希为 9a5ba575，则证书需重命名为 9a5ba575.0。
推送证书到模拟器系统目录：
确保模拟器已 root（如使用 Genymotion 或开启 Android Studio 模拟器的 root 权限）。
通过 ADB 命令推送证书：
bash
# 进入 ADB shell 并获取 root 权限
adb shell
su

# 挂载系统分区为可写（不同模拟器可能路径不同）
mount -o remount,rw /system

# 将电脑上的证书推送到模拟器临时目录
exit  # 退出 root shell
adb push 9a5ba575.0 /sdcard/

# 再次进入 root shell，移动证书到系统信任目录
adb shell
su
cp /sdcard/9a5ba575.0 /system/etc/security/cacerts/
chmod 644 /system/etc/security/cacerts/9a5ba575.0

# 重启模拟器
reboot

四、自动化安装 APK 并启动抓包
可以通过脚本整合 ADB 命令和 Burp 操作，实现自动化：
编写自动化脚本（示例：bash 脚本）
bash
#!/bin/bash
APK_PATH="/path/to/your/app.apk"
BURP_PORT="8888"
COMPUTER_IP="*************"  # 替换为你的电脑 IP

# 1. 启动 ADB 并连接模拟器
adb start-server
adb wait-for-device

# 2. 安装 APK
adb install -r $APK_PATH
if [ $? -ne 0 ]; then
    echo "APK 安装失败"
    exit 1
fi

# 3. 配置模拟器代理（需根据模拟器 WiFi 名称调整，此处以"WLAN"为例）
adb shell settings put global http_proxy $COMPUTER_IP:$BURP_PORT

# 4. 启动应用（需替换为应用的包名和主 Activity）
APP_PACKAGE="com.example.targetapp"
APP_ACTIVITY="com.example.targetapp.MainActivity"
adb shell am start -n $APP_PACKAGE/$APP_ACTIVITY

echo "自动化操作完成，Burp 开始抓包..."

运行脚本并监控流量
给脚本添加执行权限：chmod +x auto_capture.sh
运行脚本：./auto_capture.sh
打开 Burp Suite 的 Proxy → Intercept 标签，开启拦截（Intercept is on），即可看到应用的 HTTP/HTTPS 流量。
五、注意事项
HTTPS 抓包失败排查：
确认证书已正确安装为系统证书（Android 7.0+ 必须在 /system/etc/security/cacerts/ 目录）。
检查应用是否使用了 证书锁定（SSL Pinning），若有，需通过 Frida 等工具绕过（如 frida -U -f com.example.app -l bypass-sslpinning.js --no-pause）。
代理冲突：
确保模拟器仅配置了 Burp 的代理，无其他代理软件干扰。
若使用夜神、雷电等模拟器，可能需要在模拟器设置中关闭 “桥接模式” 或手动指定网络。
通过以上步骤，即可实现用 Burp Suite 自动化抓取模拟器中 APK 的 HTTPS 流量。