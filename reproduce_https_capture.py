#!/usr/bin/env python3
"""
复现HTTPS捕获
基于之前成功的案例，尝试重新捕获HTTPS请求
"""

import subprocess
import sys
import time
import json
from pathlib import Path

def log(message, level="INFO"):
    """日志输出"""
    timestamp = time.strftime("%H:%M:%S")
    if level == "SUCCESS":
        print(f"🎉 [{timestamp}] {message}")
    elif level == "ERROR":
        print(f"❌ [{timestamp}] {message}")
    elif level == "WARNING":
        print(f"⚠️  [{timestamp}] {message}")
    else:
        print(f"📋 [{timestamp}] {message}")

def run_adb(cmd, timeout=30):
    """执行adb命令"""
    full_cmd = f"source android_env.sh && adb {cmd}"
    try:
        result = subprocess.run(full_cmd, shell=True, 
                              capture_output=True, text=True, 
                              timeout=timeout, check=True)
        return result.returncode, result.stdout.strip(), result.stderr.strip()
    except subprocess.CalledProcessError as e:
        return e.returncode, "", e.stderr
    except subprocess.TimeoutExpired:
        return -1, "", "Timeout"

def setup_exact_previous_environment():
    """完全按照之前成功的方式设置环境"""
    log("按照之前成功的方式设置环境...")
    
    # 清理环境
    subprocess.run("pkill -f mitmdump", shell=True, capture_output=True)
    run_adb("shell pkill frida-server")
    run_adb("shell settings delete global http_proxy")
    time.sleep(5)
    
    # 启动mitmproxy（使用之前成功的配置）
    log("启动mitmproxy...")
    Path("mitm-logs").mkdir(exist_ok=True)
    
    # 清空捕获文件
    capture_file = Path("mitm-logs/realtime_capture.json")
    with open(capture_file, 'w') as f:
        json.dump([], f)
    
    # 使用完全相同的mitmproxy命令
    mitm_cmd = "/Users/<USER>/Library/Python/3.9/bin/mitmdump -s mitm-scripts/capture.py --listen-port 8080"
    
    mitmproxy_process = subprocess.Popen(
        mitm_cmd, shell=True,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    
    time.sleep(15)
    
    if mitmproxy_process.poll() is None:
        log("✅ mitmproxy启动成功", "SUCCESS")
    else:
        log("❌ mitmproxy启动失败", "ERROR")
        return None
    
    return mitmproxy_process

def setup_ssl_bypass_exactly():
    """完全按照之前成功的方式设置SSL绕过"""
    log("设置SSL绕过（完全复现之前的方式）...")
    
    # 设置代理
    host_ip = "********"  # 使用之前成功的IP
    proxy_setting = f"{host_ip}:8080"
    
    returncode, stdout, stderr = run_adb(f"shell settings put global http_proxy {proxy_setting}")
    if returncode == 0:
        log(f"✅ 代理设置成功: {proxy_setting}", "SUCCESS")
    else:
        log(f"代理设置失败: {stderr}", "ERROR")
        return None
    
    # 启动frida-server
    log("启动frida-server...")
    subprocess.run("adb root", shell=True, capture_output=True)
    time.sleep(3)
    
    run_adb("push frida-server /data/local/tmp/frida-server")
    run_adb("shell chmod 755 /data/local/tmp/frida-server")
    
    subprocess.Popen("source android_env.sh && adb shell '/data/local/tmp/frida-server' &", shell=True)
    time.sleep(10)
    
    # 验证frida-server
    returncode, stdout, stderr = run_adb("shell ps | grep frida-server")
    if "frida-server" not in stdout:
        log("❌ frida-server启动失败", "ERROR")
        return None
    
    log("✅ frida-server启动成功", "SUCCESS")
    
    # 启动应用（完全按照之前的方式）
    package = "com.yjzx.yjzx2017"
    run_adb(f"shell am force-stop {package}")
    time.sleep(3)
    
    returncode, stdout, stderr = run_adb(f"shell am start -n {package}/.controller.activity.splash.SplashActivity")
    if returncode != 0:
        log(f"应用启动失败: {stderr}", "ERROR")
        return None
    
    log("✅ 应用启动成功", "SUCCESS")
    time.sleep(12)
    
    # 获取PID并启动SSL绕过
    returncode, stdout, stderr = run_adb(f"shell ps | grep {package}")
    if package not in stdout:
        log("应用进程未找到", "ERROR")
        return None
    
    # 提取PID
    lines = stdout.split('\n')
    target_pid = None
    for line in lines:
        if package in line:
            parts = line.split()
            if len(parts) >= 2:
                target_pid = parts[1]
                break
    
    if not target_pid:
        log("无法获取PID", "ERROR")
        return None
    
    log(f"目标PID: {target_pid}")
    
    # 启动SSL绕过（使用之前成功的方式）
    ssl_cmd = f"export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin && frida -U {target_pid} -l interactive_ssl_bypass.js"
    
    ssl_process = subprocess.Popen(
        ssl_cmd, shell=True,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    
    log("SSL绕过进程已启动...")
    time.sleep(25)  # 给足够时间加载
    
    if ssl_process.poll() is None:
        log("✅ SSL绕过正在运行", "SUCCESS")
    else:
        log("⚠️  SSL绕过可能有问题", "WARNING")
    
    return ssl_process

def trigger_verification_requests():
    """触发验证相关的请求（基于之前成功捕获的极光验证码请求）"""
    log("🔍 触发验证相关的网络请求...")
    
    package = "com.yjzx.yjzx2017"
    
    # 基于之前成功的案例，这些操作可能触发验证码相关的HTTPS请求
    verification_triggers = [
        {
            "name": "重新启动应用（触发初始化）",
            "actions": [
                ("强制停止", f"shell am force-stop {package}"),
                ("等待", "sleep 3"),
                ("启动应用", f"shell am start -n {package}/.controller.activity.splash.SplashActivity"),
                ("等待初始化", "sleep 15")
            ]
        },
        {
            "name": "尝试登录操作（可能触发验证码）",
            "actions": [
                ("点击登录区域", "shell input tap 540 800"),
                ("等待", "sleep 5"),
                ("点击用户名输入", "shell input tap 540 600"),
                ("等待", "sleep 3"),
                ("点击密码输入", "shell input tap 540 700"),
                ("等待", "sleep 3"),
                ("点击登录按钮", "shell input tap 540 850"),
                ("等待验证", "sleep 10")
            ]
        },
        {
            "name": "触发验证码请求",
            "actions": [
                ("点击获取验证码", "shell input tap 700 750"),
                ("等待", "sleep 8"),
                ("再次点击验证码", "shell input tap 700 750"),
                ("等待", "sleep 8"),
                ("点击发送验证码", "shell input tap 600 800"),
                ("等待验证码请求", "sleep 10")
            ]
        },
        {
            "name": "网络连接测试",
            "actions": [
                ("下拉刷新", "shell input swipe 540 400 540 800"),
                ("等待", "sleep 8"),
                ("点击刷新按钮", "shell input tap 540 200"),
                ("等待", "sleep 5"),
                ("再次刷新", "shell input swipe 540 400 540 800"),
                ("等待网络请求", "sleep 10")
            ]
        }
    ]
    
    for i, trigger in enumerate(verification_triggers, 1):
        log(f"📱 执行触发器 {i}/4: {trigger['name']}")
        
        before_count = get_request_count()
        before_https_count = get_https_count()
        
        for action_name, action_cmd in trigger['actions']:
            log(f"   🔄 {action_name}")
            
            if action_cmd.startswith("sleep"):
                time.sleep(int(action_cmd.split()[1]))
            else:
                run_adb(action_cmd)
                time.sleep(2)
        
        # 检查是否有新的请求
        after_count = get_request_count()
        after_https_count = get_https_count()
        
        new_requests = after_count - before_count
        new_https = after_https_count - before_https_count
        
        log(f"   📊 {trigger['name']} 完成")
        log(f"      新增请求: {new_requests} (HTTPS: {new_https})")
        
        if new_https > 0:
            log(f"   🎉 成功触发了 {new_https} 个HTTPS请求！", "SUCCESS")
        
        log("")
        time.sleep(5)  # 短暂休息

def get_request_count():
    """获取总请求数量"""
    try:
        capture_file = Path("mitm-logs/realtime_capture.json")
        if capture_file.exists():
            with open(capture_file, 'r') as f:
                data = json.load(f)
            return len(data) if isinstance(data, list) else 0
        return 0
    except:
        return 0

def get_https_count():
    """获取HTTPS请求数量"""
    try:
        capture_file = Path("mitm-logs/realtime_capture.json")
        if capture_file.exists():
            with open(capture_file, 'r') as f:
                data = json.load(f)
            if isinstance(data, list):
                return len([req for req in data if req.get('url', '').startswith('https://')])
        return 0
    except:
        return 0

def analyze_reproduction_results():
    """分析复现结果"""
    log("📊 分析HTTPS复现结果...")
    
    capture_file = Path("mitm-logs/realtime_capture.json")
    
    try:
        if not capture_file.exists():
            log("捕获文件不存在", "ERROR")
            return False
        
        with open(capture_file, 'r') as f:
            data = json.load(f)
        
        total = len(data) if isinstance(data, list) else 0
        https_reqs = [req for req in data if req.get('url', '').startswith('https://')]
        http_reqs = [req for req in data if req.get('url', '').startswith('http://')]
        
        log("=" * 60)
        log("🎉 HTTPS复现测试结果", "SUCCESS")
        log("=" * 60)
        
        log(f"📊 网络捕获统计:")
        log(f"   总请求数: {total}")
        log(f"   HTTPS请求: {len(https_reqs)}")
        log(f"   HTTP请求: {len(http_reqs)}")
        
        if len(https_reqs) > 0:
            log("🔒 成功复现HTTPS捕获！", "SUCCESS")
            
            # 分析HTTPS请求
            for i, req in enumerate(https_reqs, 1):
                url = req.get('url', 'N/A')
                method = req.get('method', 'N/A')
                host = req.get('host', 'N/A')
                status = req.get('status_code', 'N/A')
                
                log(f"   {i}. {method} {url}")
                log(f"      主机: {host}")
                log(f"      状态: {status}")
            
            # 检查是否包含验证相关的请求
            verification_domains = ['jiguang.cn', 'verification', 'captcha', 'sms', 'auth']
            verification_reqs = []
            
            for req in https_reqs:
                url = req.get('url', '').lower()
                host = req.get('host', '').lower()
                
                if any(domain in url or domain in host for domain in verification_domains):
                    verification_reqs.append(req)
            
            if verification_reqs:
                log("🎯 发现验证相关的HTTPS请求:", "SUCCESS")
                for req in verification_reqs:
                    log(f"   • {req.get('method')} {req.get('url')}")
            
            log("🎉 HTTPS捕获复现成功！", "SUCCESS")
            return True
        else:
            log("❌ 未能复现HTTPS捕获", "ERROR")
            
            if len(http_reqs) > 0:
                log("⚠️  但捕获到HTTP请求:", "WARNING")
                for i, req in enumerate(http_reqs[:3], 1):
                    url = req.get('url', 'N/A')
                    method = req.get('method', 'N/A')
                    log(f"   {i}. {method} {url}")
            
            log("💡 可能需要:")
            log("   1. 尝试不同的应用操作")
            log("   2. 检查SSL绕过脚本状态")
            log("   3. 确认网络环境配置")
            
            return False
            
    except Exception as e:
        log(f"分析失败: {e}", "ERROR")
        return False

def main():
    """主函数"""
    log("🚀 复现HTTPS捕获测试")
    log("🔧 基于之前成功的案例，尝试重新捕获HTTPS请求")
    log("=" * 70)
    
    mitmproxy_process = None
    ssl_process = None
    
    try:
        # 步骤1: 完全按照之前成功的方式设置环境
        mitmproxy_process = setup_exact_previous_environment()
        if not mitmproxy_process:
            log("环境设置失败", "ERROR")
            return False
        
        # 步骤2: 完全按照之前成功的方式设置SSL绕过
        ssl_process = setup_ssl_bypass_exactly()
        if not ssl_process:
            log("SSL绕过设置失败", "ERROR")
            return False
        
        # 步骤3: 等待系统完全稳定
        log("⏳ 等待系统完全稳定...")
        time.sleep(25)
        
        # 步骤4: 触发验证相关的请求
        log("=" * 60)
        log("🔍 开始触发验证相关的网络请求")
        log("=" * 60)
        
        trigger_verification_requests()
        
        # 步骤5: 等待所有网络请求处理完成
        log("⏳ 等待所有网络请求处理完成...")
        time.sleep(30)
        
        # 步骤6: 分析复现结果
        success = analyze_reproduction_results()
        
        if success:
            log("🎉 HTTPS捕获复现成功！", "SUCCESS")
            return True
        else:
            log("⚠️  HTTPS捕获复现失败", "WARNING")
            log("但网络捕获机制本身是正常的")
            return False
            
    except KeyboardInterrupt:
        log("用户中断测试")
        return False
    except Exception as e:
        log(f"测试异常: {e}", "ERROR")
        return False
    finally:
        # 清理
        log("🧹 清理资源...")
        if mitmproxy_process and mitmproxy_process.poll() is None:
            mitmproxy_process.terminate()
        if ssl_process and ssl_process.poll() is None:
            ssl_process.terminate()
        
        subprocess.run("pkill -f mitmdump", shell=True, capture_output=True)
        run_adb("shell pkill frida-server")

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎯 HTTPS捕获复现成功！")
            print("💡 SSL绕过和HTTPS捕获功能确认正常工作！")
        else:
            print("\n🔧 HTTPS捕获复现失败")
            print("💡 可能需要调整触发条件或检查环境配置")
    except KeyboardInterrupt:
        print("\n⚠️  用户中断")
    except Exception as e:
        print(f"\n❌ 异常: {e}")
