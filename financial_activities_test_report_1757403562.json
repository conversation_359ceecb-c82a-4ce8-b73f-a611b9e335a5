{"test_time": "2025-09-09 15:39:01", "package": "com.yjzx.yjzx2017", "activities_tested": [{"name": "SplashActivity", "full_path": "com.yjzx.yjzx2017.controller.activity.splash.SplashActivity", "description": "启动页面", "launch_success": true, "launch_output": "Starting: Intent { cmp=com.yjzx.yjzx2017/.controller.activity.splash.SplashActivity }", "ui_captured": true, "network_activity": "等待抓包日志分析", "ui_file": "ui_SplashActivity_1757403549.xml"}, {"name": "AuctionSelfBuyActivity", "full_path": "com.yjzx.yjzx2017.controller.activity.auction.AuctionSelfBuyActivity", "description": "拍卖购买页面", "launch_success": false, "launch_output": "Starting: Intent { cmp=com.yjzx.yjzx2017/.controller.activity.auction.AuctionSelfBuyActivity }", "ui_captured": false, "network_activity": "等待抓包日志分析"}, {"name": "SelfAuctionManageActivity", "full_path": "com.yjzx.yjzx2017.controller.selfAuction.SelfAuctionManageActivity", "description": "拍卖管理页面", "launch_success": false, "launch_output": "Starting: Intent { cmp=com.yjzx.yjzx2017/.controller.selfAuction.SelfAuctionManageActivity }", "ui_captured": false, "network_activity": "等待抓包日志分析"}, {"name": "SelfAuctionConfirmActivity", "full_path": "com.yjzx.yjzx2017.controller.selfAuction.SelfAuctionConfirmActivity", "description": "拍卖确认页面", "launch_success": false, "launch_output": "Starting: Intent { cmp=com.yjzx.yjzx2017/.controller.selfAuction.SelfAuctionConfirmActivity }", "ui_captured": false, "network_activity": "等待抓包日志分析"}, {"name": "AddressCompanyActivity", "full_path": "com.yjzx.yjzx2017.controller.activity.setting.AddressCompanyActivity", "description": "公司地址设置", "launch_success": false, "launch_output": "Starting: Intent { cmp=com.yjzx.yjzx2017/.controller.activity.setting.AddressCompanyActivity }", "ui_captured": false, "network_activity": "等待抓包日志分析"}], "summary": {"total": 5, "success": 1, "failed": 4}}