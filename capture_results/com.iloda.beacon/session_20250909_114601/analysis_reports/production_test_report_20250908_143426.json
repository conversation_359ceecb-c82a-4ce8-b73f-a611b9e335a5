{"test_start_time": "2025-09-08T14:33:05.747179", "system_environment": {"adb_available": true, "aapt_available": true, "java_available": true, "frida_available": true, "mitmproxy_available": true, "emulator_running": true, "disk_space": "247Gi", "memory_available": 0}, "static_analysis": {"apk_files_found": 5, "successful_analyses": 5, "failed_analyses": 0, "analysis_details": [{"file_name": "5577.com.iloda.beacon.apk", "file_size": 12245893, "success": true, "basic_info": {"package": "com.iloda.beacon"}, "permissions": ["com.iloda.beacon.permission.JPUSH_MESSAGE", "android.permission.RECEIVE_USER_PRESENT", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.SYSTEM_ALERT_WINDOW", "android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_WIFI_STATE", "android.permission.CHANGE_WIFI_STATE", "android.permission.READ_LOGS", "android.permission.WAKE_LOCK"], "activities": ["com.iloda.beacon.activity.LoginActivity"], "services": [], "certificate_info": {}, "analysis_time": 1.2615149021148682}, {"file_name": "com.android.vending_289.com.apk", "file_size": 95075184, "success": true, "basic_info": {"package": "com.android.vending"}, "permissions": ["android.permission.READ_DEVICE_CONFIG", "android.permission.WRITE_DEVICE_CONFIG", "com.google.android.finsky.permission.DSE", "com.google.android.finsky.permission.DEVELOPER_GROUP_ID_INFO", "android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN", "android.permission.CHANGE_WIFI_STATE", "android.permission.BLUETOOTH_SCAN", "android.permission.BLUETOOTH_ADVERTISE", "android.permission.BLUETOOTH_CONNECT"], "activities": [], "services": [], "certificate_info": {}, "analysis_time": 2.628535032272339}, {"file_name": "com.yjzx.yjzx2017_v5.5.8_2265.com.apk", "file_size": 120935111, "success": true, "basic_info": {"package": "com.yjzx.yjzx2017"}, "permissions": ["android.permission.INTERNET", "android.permission.KILL_BACKGROUND_PROCESSES", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.MANAGE_EXTERNAL_STORAGE", "android.permission.WRITE_MEDIA_STORAGE", "android.permission.ACCESS_NETWORK_STATE", "com.yjzx.yjzx2017.permission.JPUSH_MESSAGE", "android.permission.CAMERA", "android.permission.FLASHLIGHT"], "activities": ["com.yjzx.yjzx2017.controller.activity.splash.SplashActivity"], "services": [], "certificate_info": {}, "analysis_time": 3.895408868789673}, {"file_name": "test_calculator.apk", "file_size": 9, "success": true, "basic_info": {}, "permissions": [], "activities": [], "services": [], "certificate_info": {}, "analysis_time": 0.5999822616577148}, {"file_name": "5577.com.androidfuture.chrismas.framesfree.apk", "file_size": 29779693, "success": true, "basic_info": {"package": "com.androidfuture.chrismas.framesfree"}, "permissions": ["android.permission.SET_WALLPAPER", "android.permission.ACCESS_NETWORK_STATE", "android.permission.INTERNET", "android.permission.READ_PHONE_STATE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.READ_EXTERNAL_STORAGE"], "activities": ["com.androidfuture.chrismas.framesfree.SplashActivity"], "services": [], "certificate_info": {}, "analysis_time": 0.9973750114440918}]}, "dynamic_analysis": {"emulator_ready": true, "apk_installation": true, "app_launch": true, "network_capture": false, "ui_automation": true, "activity_discovery": 3, "network_requests": 0, "errors_encountered": []}, "multi_apk_test": {"total_apks": 5, "successful_analyses": 5, "failed_analyses": 0, "average_time": 1.7851295948028565, "peak_memory_usage": 0, "concurrent_analyses": 0}, "concurrent_test": {"max_concurrent": 3, "successful_concurrent": 3, "failed_concurrent": 0, "performance_degradation": 0}, "error_handling": {"invalid_apk_handling": false, "missing_file_handling": false, "network_error_handling": true, "emulator_disconnect_handling": false, "recovery_success": false}, "performance_test": {"memory_usage_start": 0, "memory_usage_peak": 0, "memory_usage_end": 0, "cpu_usage_average": 0, "analysis_speed": 0.5498709234201737, "stability_score": 100.0, "successful_operations": 5, "failed_operations": 0}, "stability_test": {}, "overall_assessment": {"environment_score": 100.0, "static_analysis_score": 100.0, "dynamic_analysis_score": 80.0, "multi_apk_score": 100.0, "concurrent_score": 100.0, "error_handling_score": 20.0, "performance_score": 100.0, "overall_score": 85.71428571428571, "production_readiness": "Near Production Ready", "recommendations": ["加强错误处理和恢复机制"]}, "test_end_time": "2025-09-08T14:34:26.664824"}