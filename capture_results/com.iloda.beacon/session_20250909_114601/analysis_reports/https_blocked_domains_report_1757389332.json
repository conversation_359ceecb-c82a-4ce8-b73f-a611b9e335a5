{"analysis_time": "2025-09-09T11:42:12.945887", "summary": {"total_blocked_domains": 19, "total_connection_attempts": 338, "unique_ips": 38, "log_files_analyzed": 4}, "blocked_domains": {"list": ["clientservices.googleapis.com", "www.google.com", "api.sobot.com", "accounts.google.com", "safebrowsing.googleapis.com", "android.googleapis.com", "www.gstatic.com", "update.googleapis.com", "digitalassetlinks.googleapis.com", "h.trace.qq.com", "config.jpush.cn", "config-ipv6.jpush.cn", "bjuser.jpush.cn", "play.googleapis.com", "instantmessaging-pa.googleapis.com", "ce3e75d5.jpush.cn", "bjuser-ipv6.jpush.cn", "ce3e75d5-ipv6.jpush.cn", "android.bugly.qq.com"], "by_frequency": {"digitalassetlinks.googleapis.com": 22, "www.google.com": 21, "instantmessaging-pa.googleapis.com": 14, "config.jpush.cn": 6, "ce3e75d5.jpush.cn": 6, "ce3e75d5-ipv6.jpush.cn": 6, "config-ipv6.jpush.cn": 6, "accounts.google.com": 2, "play.googleapis.com": 1, "api.sobot.com": 1, "android.bugly.qq.com": 1, "h.trace.qq.com": 1, "bjuser.jpush.cn": 1, "bjuser-ipv6.jpush.cn": 1, "www.gstatic.com": 1, "safebrowsing.googleapis.com": 1, "update.googleapis.com": 1, "clientservices.googleapis.com": 1, "android.googleapis.com": 1}}, "domain_resolution": {"clientservices.googleapis.com": {"domain": "clientservices.googleapis.com", "ipv4_addresses": ["************"], "ipv6_addresses": ["::ffff:************"], "resolution_time": 0.008234977722167969, "error": null, "cname": null, "mx_records": [], "txt_records": []}, "www.google.com": {"domain": "www.google.com", "ipv4_addresses": ["***********"], "ipv6_addresses": ["::ffff:***********"], "resolution_time": 0.0017368793487548828, "error": null, "cname": null, "mx_records": [], "txt_records": []}, "api.sobot.com": {"domain": "api.sobot.com", "ipv4_addresses": ["************"], "ipv6_addresses": ["::ffff:************"], "resolution_time": 0.0023801326751708984, "error": null, "cname": null, "mx_records": [], "txt_records": []}, "accounts.google.com": {"domain": "accounts.google.com", "ipv4_addresses": ["************"], "ipv6_addresses": ["::ffff:************"], "resolution_time": 0.0026078224182128906, "error": null, "cname": null, "mx_records": [], "txt_records": []}, "safebrowsing.googleapis.com": {"domain": "safebrowsing.googleapis.com", "ipv4_addresses": ["************"], "ipv6_addresses": ["::ffff:************"], "resolution_time": 0.002318143844604492, "error": null, "cname": null, "mx_records": [], "txt_records": []}, "android.googleapis.com": {"domain": "android.googleapis.com", "ipv4_addresses": ["************"], "ipv6_addresses": ["::ffff:************"], "resolution_time": 0.0018990039825439453, "error": null, "cname": null, "mx_records": [], "txt_records": []}, "www.gstatic.com": {"domain": "www.gstatic.com", "ipv4_addresses": ["************"], "ipv6_addresses": ["::ffff:************"], "resolution_time": 0.0023779869079589844, "error": null, "cname": null, "mx_records": [], "txt_records": []}, "update.googleapis.com": {"domain": "update.googleapis.com", "ipv4_addresses": ["************"], "ipv6_addresses": ["::ffff:************"], "resolution_time": 0.002048015594482422, "error": null, "cname": null, "mx_records": [], "txt_records": []}, "digitalassetlinks.googleapis.com": {"domain": "digitalassetlinks.googleapis.com", "ipv4_addresses": ["************"], "ipv6_addresses": ["::ffff:************"], "resolution_time": 0.0022771358489990234, "error": null, "cname": null, "mx_records": [], "txt_records": []}, "h.trace.qq.com": {"domain": "h.trace.qq.com", "ipv4_addresses": ["************"], "ipv6_addresses": ["::ffff:************"], "resolution_time": 0.003946065902709961, "error": null, "cname": null, "mx_records": [], "txt_records": []}, "config.jpush.cn": {"domain": "config.jpush.cn", "ipv4_addresses": ["************"], "ipv6_addresses": ["::ffff:************"], "resolution_time": 0.005886077880859375, "error": null, "cname": null, "mx_records": [], "txt_records": []}, "config-ipv6.jpush.cn": {"domain": "config-ipv6.jpush.cn", "ipv4_addresses": ["************"], "ipv6_addresses": ["::ffff:************"], "resolution_time": 0.003545999526977539, "error": null, "cname": null, "mx_records": [], "txt_records": []}, "bjuser.jpush.cn": {"domain": "bjuser.jpush.cn", "ipv4_addresses": ["************"], "ipv6_addresses": ["::ffff:************"], "resolution_time": 0.002895832061767578, "error": null, "cname": null, "mx_records": [], "txt_records": []}, "play.googleapis.com": {"domain": "play.googleapis.com", "ipv4_addresses": ["************"], "ipv6_addresses": ["::ffff:************"], "resolution_time": 0.007047891616821289, "error": null, "cname": null, "mx_records": [], "txt_records": []}, "instantmessaging-pa.googleapis.com": {"domain": "instantmessaging-pa.googleapis.com", "ipv4_addresses": ["************"], "ipv6_addresses": ["::ffff:************"], "resolution_time": 0.0036110877990722656, "error": null, "cname": null, "mx_records": [], "txt_records": []}, "ce3e75d5.jpush.cn": {"domain": "ce3e75d5.jpush.cn", "ipv4_addresses": ["************"], "ipv6_addresses": ["::ffff:************"], "resolution_time": 0.0026330947875976562, "error": null, "cname": null, "mx_records": [], "txt_records": []}, "bjuser-ipv6.jpush.cn": {"domain": "bjuser-ipv6.jpush.cn", "ipv4_addresses": ["************"], "ipv6_addresses": ["::ffff:************"], "resolution_time": 0.002312898635864258, "error": null, "cname": null, "mx_records": [], "txt_records": []}, "ce3e75d5-ipv6.jpush.cn": {"domain": "ce3e75d5-ipv6.jpush.cn", "ipv4_addresses": ["************"], "ipv6_addresses": ["::ffff:************"], "resolution_time": 0.*****************, "error": null, "cname": null, "mx_records": [], "txt_records": []}, "android.bugly.qq.com": {"domain": "android.bugly.qq.com", "ipv4_addresses": ["************"], "ipv6_addresses": ["::ffff:************"], "resolution_time": 0.0032792091369628906, "error": null, "cname": null, "mx_records": [], "txt_records": []}}, "domain_patterns": {"google_services": {"count": 10, "domains": ["clientservices.googleapis.com", "www.google.com", "accounts.google.com", "safebrowsing.googleapis.com", "android.googleapis.com", "www.gstatic.com", "update.googleapis.com", "digitalassetlinks.googleapis.com", "play.googleapis.com", "instantmessaging-pa.googleapis.com"]}, "push_services": {"count": 6, "domains": ["config.jpush.cn", "config-ipv6.jpush.cn", "bjuser.jpush.cn", "ce3e75d5.jpush.cn", "bjuser-ipv6.jpush.cn", "ce3e75d5-ipv6.jpush.cn"]}, "cdn_services": {"count": 0, "domains": []}, "analytics_services": {"count": 0, "domains": []}, "ad_services": {"count": 0, "domains": []}, "security_services": {"count": 0, "domains": []}, "social_services": {"count": 0, "domains": []}, "other_services": {"count": 3, "domains": ["api.sobot.com", "h.trace.qq.com", "android.bugly.qq.com"]}}, "ip_geolocation": {"************": {"ip": "************", "type": "ipv4", "is_private": true, "is_reserved": false, "is_multicast": false, "is_loopback": false}, "::ffff:************": {"ip": "::ffff:************", "type": "ipv6", "is_private": true, "is_reserved": true, "is_multicast": false, "is_loopback": false}, "***********": {"ip": "***********", "type": "ipv4", "is_private": true, "is_reserved": false, "is_multicast": false, "is_loopback": false}, "::ffff:***********": {"ip": "::ffff:***********", "type": "ipv6", "is_private": true, "is_reserved": true, "is_multicast": false, "is_loopback": false}, "************": {"ip": "************", "type": "ipv4", "is_private": true, "is_reserved": false, "is_multicast": false, "is_loopback": false}, "::ffff:************": {"ip": "::ffff:************", "type": "ipv6", "is_private": true, "is_reserved": true, "is_multicast": false, "is_loopback": false}, "************": {"ip": "************", "type": "ipv4", "is_private": true, "is_reserved": false, "is_multicast": false, "is_loopback": false}, "::ffff:************": {"ip": "::ffff:************", "type": "ipv6", "is_private": true, "is_reserved": true, "is_multicast": false, "is_loopback": false}, "************": {"ip": "************", "type": "ipv4", "is_private": true, "is_reserved": false, "is_multicast": false, "is_loopback": false}, "::ffff:************": {"ip": "::ffff:************", "type": "ipv6", "is_private": true, "is_reserved": true, "is_multicast": false, "is_loopback": false}, "************": {"ip": "************", "type": "ipv4", "is_private": true, "is_reserved": false, "is_multicast": false, "is_loopback": false}, "::ffff:************": {"ip": "::ffff:************", "type": "ipv6", "is_private": true, "is_reserved": true, "is_multicast": false, "is_loopback": false}, "************": {"ip": "************", "type": "ipv4", "is_private": true, "is_reserved": false, "is_multicast": false, "is_loopback": false}, "::ffff:************": {"ip": "::ffff:************", "type": "ipv6", "is_private": true, "is_reserved": true, "is_multicast": false, "is_loopback": false}, "************": {"ip": "************", "type": "ipv4", "is_private": true, "is_reserved": false, "is_multicast": false, "is_loopback": false}, "::ffff:************": {"ip": "::ffff:************", "type": "ipv6", "is_private": true, "is_reserved": true, "is_multicast": false, "is_loopback": false}, "************": {"ip": "************", "type": "ipv4", "is_private": true, "is_reserved": false, "is_multicast": false, "is_loopback": false}, "::ffff:************": {"ip": "::ffff:************", "type": "ipv6", "is_private": true, "is_reserved": true, "is_multicast": false, "is_loopback": false}, "************": {"ip": "************", "type": "ipv4", "is_private": true, "is_reserved": false, "is_multicast": false, "is_loopback": false}, "::ffff:************": {"ip": "::ffff:************", "type": "ipv6", "is_private": true, "is_reserved": true, "is_multicast": false, "is_loopback": false}, "************": {"ip": "************", "type": "ipv4", "is_private": true, "is_reserved": false, "is_multicast": false, "is_loopback": false}, "::ffff:************": {"ip": "::ffff:************", "type": "ipv6", "is_private": true, "is_reserved": true, "is_multicast": false, "is_loopback": false}, "************": {"ip": "************", "type": "ipv4", "is_private": true, "is_reserved": false, "is_multicast": false, "is_loopback": false}, "::ffff:************": {"ip": "::ffff:************", "type": "ipv6", "is_private": true, "is_reserved": true, "is_multicast": false, "is_loopback": false}, "************": {"ip": "************", "type": "ipv4", "is_private": true, "is_reserved": false, "is_multicast": false, "is_loopback": false}, "::ffff:************": {"ip": "::ffff:************", "type": "ipv6", "is_private": true, "is_reserved": true, "is_multicast": false, "is_loopback": false}, "************": {"ip": "************", "type": "ipv4", "is_private": true, "is_reserved": false, "is_multicast": false, "is_loopback": false}, "::ffff:************": {"ip": "::ffff:************", "type": "ipv6", "is_private": true, "is_reserved": true, "is_multicast": false, "is_loopback": false}, "************": {"ip": "************", "type": "ipv4", "is_private": true, "is_reserved": false, "is_multicast": false, "is_loopback": false}, "::ffff:************": {"ip": "::ffff:************", "type": "ipv6", "is_private": true, "is_reserved": true, "is_multicast": false, "is_loopback": false}, "************": {"ip": "************", "type": "ipv4", "is_private": true, "is_reserved": false, "is_multicast": false, "is_loopback": false}, "::ffff:************": {"ip": "::ffff:************", "type": "ipv6", "is_private": true, "is_reserved": true, "is_multicast": false, "is_loopback": false}, "************": {"ip": "************", "type": "ipv4", "is_private": true, "is_reserved": false, "is_multicast": false, "is_loopback": false}, "::ffff:************": {"ip": "::ffff:************", "type": "ipv6", "is_private": true, "is_reserved": true, "is_multicast": false, "is_loopback": false}, "************": {"ip": "************", "type": "ipv4", "is_private": true, "is_reserved": false, "is_multicast": false, "is_loopback": false}, "::ffff:************": {"ip": "::ffff:************", "type": "ipv6", "is_private": true, "is_reserved": true, "is_multicast": false, "is_loopback": false}, "************": {"ip": "************", "type": "ipv4", "is_private": true, "is_reserved": false, "is_multicast": false, "is_loopback": false}, "::ffff:************": {"ip": "::ffff:************", "type": "ipv6", "is_private": true, "is_reserved": true, "is_multicast": false, "is_loopback": false}}, "connection_attempts": [{"timestamp": "14:52:27.259", "domain": "digitalassetlinks.googleapis.com", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 4}, {"timestamp": "14:52:58.891", "domain": "play.googleapis.com", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 7}, {"timestamp": "14:53:07.853", "domain": "instantmessaging-pa.googleapis.com", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 10}, {"timestamp": "14:53:08.799", "domain": "instantmessaging-pa.googleapis.com", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 13}, {"timestamp": "14:53:10.569", "domain": "instantmessaging-pa.googleapis.com", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 16}, {"timestamp": "14:53:12.864", "domain": "instantmessaging-pa.googleapis.com", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 19}, {"timestamp": "14:53:16.648", "domain": "instantmessaging-pa.googleapis.com", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 22}, {"timestamp": "14:53:22.983", "domain": "instantmessaging-pa.googleapis.com", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 25}, {"timestamp": "14:53:31.994", "domain": "instantmessaging-pa.googleapis.com", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 28}, {"timestamp": "14:53:48.427", "domain": "instantmessaging-pa.googleapis.com", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 31}, {"timestamp": "14:54:17.286", "domain": "instantmessaging-pa.googleapis.com", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 34}, {"timestamp": "14:54:19.185", "domain": "digitalassetlinks.googleapis.com", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 37}, {"timestamp": "14:55:00.342", "domain": "instantmessaging-pa.googleapis.com", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 40}, {"timestamp": "14:55:31.826", "domain": "config.jpush.cn", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 46}, {"timestamp": "14:55:31.829", "domain": "ce3e75d5.jpush.cn", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 48}, {"timestamp": "14:55:31.918", "domain": "config.jpush.cn", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 52}, {"timestamp": "14:55:31.943", "domain": "api.sobot.com", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 55}, {"timestamp": "14:55:31.945", "domain": "ce3e75d5-ipv6.jpush.cn", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 56}, {"timestamp": "14:55:31.955", "domain": "config.jpush.cn", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 59}, {"timestamp": "14:55:32.031", "domain": "ce3e75d5.jpush.cn", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 64}, {"timestamp": "14:55:32.040", "domain": "config.jpush.cn", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 66}, {"timestamp": "14:55:32.084", "domain": "android.bugly.qq.com", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 69}, {"timestamp": "14:55:32.118", "domain": "config.jpush.cn", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 72}, {"timestamp": "14:55:32.127", "domain": "config-ipv6.jpush.cn", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 75}, {"timestamp": "14:55:32.163", "domain": "ce3e75d5-ipv6.jpush.cn", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 79}, {"timestamp": "14:55:32.226", "domain": "config.jpush.cn", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 81}, {"timestamp": "14:55:32.227", "domain": "config-ipv6.jpush.cn", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 82}, {"timestamp": "14:55:32.480", "domain": "config-ipv6.jpush.cn", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 89}, {"timestamp": "14:55:32.483", "domain": "config-ipv6.jpush.cn", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 91}, {"timestamp": "14:55:32.529", "domain": "config-ipv6.jpush.cn", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 97}, {"timestamp": "14:55:32.584", "domain": "config-ipv6.jpush.cn", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 101}, {"timestamp": "14:55:32.759", "domain": "ce3e75d5.jpush.cn", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 109}, {"timestamp": "14:55:32.802", "domain": "ce3e75d5-ipv6.jpush.cn", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 112}, {"timestamp": "14:55:32.875", "domain": "h.trace.qq.com", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 117}, {"timestamp": "14:55:32.890", "domain": "ce3e75d5.jpush.cn", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 119}, {"timestamp": "14:55:32.897", "domain": "ce3e75d5.jpush.cn", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 122}, {"timestamp": "14:55:32.937", "domain": "ce3e75d5-ipv6.jpush.cn", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 125}, {"timestamp": "14:55:32.959", "domain": "ce3e75d5-ipv6.jpush.cn", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 127}, {"timestamp": "14:55:33.021", "domain": "bjuser.jpush.cn", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 130}, {"timestamp": "14:55:33.062", "domain": "bjuser-ipv6.jpush.cn", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 133}, {"timestamp": "14:55:36.045", "domain": "ce3e75d5.jpush.cn", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 140}, {"timestamp": "14:55:36.091", "domain": "ce3e75d5-ipv6.jpush.cn", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 143}, {"timestamp": "14:56:20.252", "domain": "instantmessaging-pa.googleapis.com", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 146}, {"timestamp": "14:56:37.603", "domain": "digitalassetlinks.googleapis.com", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 149}, {"timestamp": "14:57:10.450", "domain": "www.google.com", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 154}, {"timestamp": "14:57:10.451", "domain": "www.google.com", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 155}, {"timestamp": "14:57:10.467", "domain": "www.gstatic.com", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 158}, {"timestamp": "14:57:10.748", "domain": "www.google.com", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 162}, {"timestamp": "14:57:10.752", "domain": "www.google.com", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 164}, {"timestamp": "14:57:13.657", "domain": "safebrowsing.googleapis.com", "status": "ssl_handshake_failed", "reason": "certificate_pinning", "line_number": 167}], "technical_analysis": {"ssl_pinning_domains": ["clientservices.googleapis.com", "www.google.com", "api.sobot.com", "accounts.google.com", "safebrowsing.googleapis.com", "android.googleapis.com", "www.gstatic.com", "update.googleapis.com", "digitalassetlinks.googleapis.com", "h.trace.qq.com", "config.jpush.cn", "config-ipv6.jpush.cn", "bjuser.jpush.cn", "play.googleapis.com", "instantmessaging-pa.googleapis.com", "ce3e75d5.jpush.cn", "bjuser-ipv6.jpush.cn", "ce3e75d5-ipv6.jpush.cn", "android.bugly.qq.com"], "common_services": {"google_services": 10, "push_services": 6, "other_services": 3}}}