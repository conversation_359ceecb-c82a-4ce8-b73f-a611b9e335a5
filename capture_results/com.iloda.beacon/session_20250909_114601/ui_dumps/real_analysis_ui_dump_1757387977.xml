<?xml version='1.0' encoding='UTF-8' standalone='yes' ?><hierarchy rotation="0"><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,2088]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,2088]"><node index="0" text="" resource-id="android:id/content" class="android.widget.FrameLayout" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,66][1080,2088]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,66][1080,2088]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[66,121][1014,259]"><node index="0" text="" resource-id="com.android.permissioncontroller:id/app_icon" class="android.widget.ImageView" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[66,132][165,231]" /><node index="1" text="Choose what to allow Liuwa to access" resource-id="com.android.permissioncontroller:id/permissions_message" class="android.widget.TextView" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[165,121][1014,259]" /></node><node index="1" text="" resource-id="com.android.permissioncontroller:id/preferences_frame" class="android.widget.FrameLayout" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[22,303][1058,1939]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[22,303][1058,1939]"><node index="0" text="" resource-id="android:id/list_container" class="android.widget.FrameLayout" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[22,303][1058,1939]"><node index="0" text="" resource-id="com.android.permissioncontroller:id/recycler_view" class="androidx.recyclerview.widget.RecyclerView" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="true" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[22,303][1058,1939]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[22,303][1058,549]"><node index="0" text="" resource-id="com.android.permissioncontroller:id/icon_frame" class="android.widget.LinearLayout" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[66,382][220,470]"><node index="0" text="" resource-id="android:id/icon" class="android.widget.ImageView" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[66,393][132,459]" /></node><node index="1" text="" resource-id="" class="android.widget.RelativeLayout" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[220,303][838,549]"><node index="0" text="Files and media" resource-id="android:id/title" class="android.widget.TextView" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[220,347][529,406]" /><node index="1" text="access photos, media, and files on your device" resource-id="android:id/summary" class="android.widget.TextView" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[220,406][838,505]" /></node><node index="2" text="" resource-id="android:id/widget_frame" class="android.widget.LinearLayout" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[838,303][1014,549]"><node NAF="true" index="0" text="" resource-id="android:id/switch_widget" class="android.widget.Switch" package="com.google.android.permissioncontroller" content-desc="" checkable="true" checked="true" clickable="true" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[882,360][1014,492]" /></node></node><node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[22,549][1058,749]"><node index="0" text="" resource-id="com.android.permissioncontroller:id/icon_frame" class="android.widget.LinearLayout" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[66,605][220,693]"><node index="0" text="" resource-id="android:id/icon" class="android.widget.ImageView" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[66,616][132,682]" /></node><node index="1" text="" resource-id="" class="android.widget.RelativeLayout" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[220,549][838,749]"><node index="0" text="Location" resource-id="android:id/title" class="android.widget.TextView" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[220,593][390,652]" /><node index="1" text="access this device's location" resource-id="android:id/summary" class="android.widget.TextView" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[220,652][710,705]" /></node><node index="2" text="" resource-id="android:id/widget_frame" class="android.widget.LinearLayout" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[838,549][1014,749]"><node NAF="true" index="0" text="" resource-id="android:id/switch_widget" class="android.widget.Switch" package="com.google.android.permissioncontroller" content-desc="" checkable="true" checked="true" clickable="true" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[882,583][1014,715]" /></node></node><node index="2" text="" resource-id="" class="android.widget.LinearLayout" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[22,749][1058,949]"><node index="0" text="" resource-id="com.android.permissioncontroller:id/icon_frame" class="android.widget.LinearLayout" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[66,805][220,893]"><node index="0" text="" resource-id="android:id/icon" class="android.widget.ImageView" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[66,816][132,882]" /></node><node index="1" text="" resource-id="" class="android.widget.RelativeLayout" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[220,749][838,949]"><node index="0" text="Contacts" resource-id="android:id/title" class="android.widget.TextView" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[220,793][396,852]" /><node index="1" text="access your contacts" resource-id="android:id/summary" class="android.widget.TextView" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[220,852][588,905]" /></node><node index="2" text="" resource-id="android:id/widget_frame" class="android.widget.LinearLayout" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[838,749][1014,949]"><node NAF="true" index="0" text="" resource-id="android:id/switch_widget" class="android.widget.Switch" package="com.google.android.permissioncontroller" content-desc="" checkable="true" checked="true" clickable="true" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[882,783][1014,915]" /></node></node><node index="3" text="" resource-id="" class="android.widget.LinearLayout" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[22,949][1058,1149]"><node index="0" text="" resource-id="com.android.permissioncontroller:id/icon_frame" class="android.widget.LinearLayout" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[66,1005][220,1093]"><node index="0" text="" resource-id="android:id/icon" class="android.widget.ImageView" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[66,1016][132,1082]" /></node><node index="1" text="" resource-id="" class="android.widget.RelativeLayout" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[220,949][838,1149]"><node index="0" text="Camera" resource-id="android:id/title" class="android.widget.TextView" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[220,993][374,1052]" /><node index="1" text="take pictures and record video" resource-id="android:id/summary" class="android.widget.TextView" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[220,1052][743,1105]" /></node><node index="2" text="" resource-id="android:id/widget_frame" class="android.widget.LinearLayout" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[838,949][1014,1149]"><node NAF="true" index="0" text="" resource-id="android:id/switch_widget" class="android.widget.Switch" package="com.google.android.permissioncontroller" content-desc="" checkable="true" checked="true" clickable="true" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[882,983][1014,1115]" /></node></node><node index="4" text="" resource-id="" class="android.widget.LinearLayout" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[22,1149][1058,1349]"><node index="0" text="" resource-id="com.android.permissioncontroller:id/icon_frame" class="android.widget.LinearLayout" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[66,1205][220,1293]"><node index="0" text="" resource-id="android:id/icon" class="android.widget.ImageView" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[66,1216][132,1282]" /></node><node index="1" text="" resource-id="" class="android.widget.RelativeLayout" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[220,1149][838,1349]"><node index="0" text="Phone" resource-id="android:id/title" class="android.widget.TextView" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[220,1193][344,1252]" /><node index="1" text="make and manage phone calls" resource-id="android:id/summary" class="android.widget.TextView" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[220,1252][749,1305]" /></node><node index="2" text="" resource-id="android:id/widget_frame" class="android.widget.LinearLayout" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[838,1149][1014,1349]"><node NAF="true" index="0" text="" resource-id="android:id/switch_widget" class="android.widget.Switch" package="com.google.android.permissioncontroller" content-desc="" checkable="true" checked="true" clickable="true" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[882,1183][1014,1315]" /></node></node></node></node></node></node><node index="2" text="" resource-id="" class="android.widget.LinearLayout" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,1939][1080,2088]"><node index="0" text="" resource-id="com.android.permissioncontroller:id/button_group" class="android.widget.LinearLayout" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[674,1939][1080,2088]"><node index="0" text="Cancel" resource-id="com.android.permissioncontroller:id/cancel_button" class="android.widget.Button" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[674,1939][858,2088]" /><node index="1" text="Continue" resource-id="com.android.permissioncontroller:id/continue_button" class="android.widget.Button" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[858,1939][1080,2088]" /></node></node></node></node></node><node index="1" text="" resource-id="android:id/statusBarBackground" class="android.view.View" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,66]" /><node index="2" text="" resource-id="android:id/navigationBarBackground" class="android.view.View" package="com.google.android.permissioncontroller" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][0,0]" /></node></hierarchy>