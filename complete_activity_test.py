#!/usr/bin/env python3
"""
完整的Activity启动和网络抓包测试
测试所有Activity并记录网络行为
"""

import subprocess
import sys
import time
import json
from pathlib import Path
from datetime import datetime

class CompleteActivityTester:
    def __init__(self):
        self.package = "com.yjzx.yjzx2017"
        self.test_results = {
            "start_time": datetime.now().isoformat(),
            "package": self.package,
            "activities": [],
            "summary": {
                "total": 0,
                "successful": 0,
                "failed": 0,
                "network_captured": 0
            }
        }
        
    def run_adb(self, cmd, timeout=30):
        """执行adb命令"""
        full_cmd = f"adb {cmd}"
        try:
            result = subprocess.run(full_cmd, shell=True, 
                                  capture_output=True, text=True, 
                                  timeout=timeout, check=True)
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            return f"ERROR: {e.stderr}"
        except subprocess.TimeoutExpired:
            return "ERROR: Timeout"
    
    def get_all_activities(self):
        """获取所有Activity列表"""
        print("🔍 获取Activity列表...")
        
        apk_files = list(Path("apk").glob(f"*{self.package.split('.')[-1]}*.apk"))
        if not apk_files:
            print("❌ 未找到APK文件")
            return []
        
        apk_file = apk_files[0]
        print(f"📱 分析APK: {apk_file}")
        
        cmd = f"source android_env.sh && $ANDROID_HOME/build-tools/*/aapt dump xmltree {apk_file} AndroidManifest.xml | grep -A 2 -B 2 'activity' | grep 'android:name'"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        activities = []
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            for line in lines:
                if 'android:name' in line and 'Raw:' in line:
                    try:
                        name_part = line.split('Raw: "')[1].split('"')[0]
                        if 'Activity' in name_part and 'Provider' not in name_part:
                            activities.append(name_part)
                    except IndexError:
                        continue
        
        print(f"📋 找到 {len(activities)} 个Activity")
        return activities
    
    def check_network_activity(self):
        """检查是否有新的网络活动"""
        try:
            # 检查最新的网络日志
            log_files = list(Path("mitm-logs").glob("session_*.json"))
            if log_files:
                latest_log = max(log_files, key=lambda x: x.stat().st_mtime)
                try:
                    with open(latest_log, 'r') as f:
                        data = json.load(f)
                        # 数据是请求列表，直接返回长度
                        if isinstance(data, list):
                            return len(data)
                        # 如果是字典格式，查找requests字段
                        elif isinstance(data, dict):
                            return len(data.get('requests', []))
                        return 0
                except (json.JSONDecodeError, IOError):
                    return 0
            return 0
        except Exception as e:
            print(f"检查网络活动时出错: {e}")
            return 0
    
    def test_single_activity(self, activity, index, total):
        """测试单个Activity"""
        print(f"\n🚀 [{index}/{total}] 测试: {activity}")
        print("-" * 80)
        
        activity_result = {
            "name": activity,
            "index": index,
            "start_time": datetime.now().isoformat(),
            "success": False,
            "error": None,
            "network_requests_before": 0,
            "network_requests_after": 0,
            "ui_confirmed": False
        }
        
        # 记录测试前的网络请求数量
        activity_result["network_requests_before"] = self.check_network_activity()
        
        # 尝试启动Activity
        cmd = f"shell am start -n {self.package}/{activity}"
        result = self.run_adb(cmd)
        
        if result and "ERROR" not in result and "Exception" not in result:
            print(f"✅ Activity启动命令成功")
            activity_result["success"] = True
            
            # 等待Activity加载
            time.sleep(3)
            
            # 检查UI是否显示
            ui_result = self.run_adb("shell uiautomator dump /sdcard/ui_test.xml")
            if "ERROR" not in ui_result:
                ui_content = self.run_adb("shell cat /sdcard/ui_test.xml")
                if ui_content and self.package in ui_content:
                    print(f"✅ UI确认Activity已显示")
                    activity_result["ui_confirmed"] = True
                else:
                    print(f"⚠️  Activity可能立即退出")
            
            # 等待可能的网络请求
            time.sleep(2)
            
            # 记录测试后的网络请求数量
            activity_result["network_requests_after"] = self.check_network_activity()
            
            if activity_result["network_requests_after"] > activity_result["network_requests_before"]:
                print(f"📡 检测到网络活动: +{activity_result['network_requests_after'] - activity_result['network_requests_before']} 请求")
                self.test_results["summary"]["network_captured"] += 1
            
            self.test_results["summary"]["successful"] += 1
            
        else:
            print(f"❌ Activity启动失败")
            activity_result["error"] = result
            self.test_results["summary"]["failed"] += 1
        
        activity_result["end_time"] = datetime.now().isoformat()
        self.test_results["activities"].append(activity_result)
        
        # 短暂休息避免系统过载
        time.sleep(1)
        
        return activity_result["success"]
    
    def run_complete_test(self):
        """运行完整测试"""
        print("🎯 开始完整Activity测试")
        print("=" * 100)
        
        activities = self.get_all_activities()
        if not activities:
            print("❌ 没有找到Activity，测试终止")
            return
        
        self.test_results["summary"]["total"] = len(activities)
        
        print(f"📊 将测试 {len(activities)} 个Activity")
        print(f"🕐 预计耗时: {len(activities) * 6 / 60:.1f} 分钟")
        print("🌐 网络抓包已启动")
        print("\n开始测试...")
        
        # 测试所有Activity
        for i, activity in enumerate(activities, 1):
            try:
                self.test_single_activity(activity, i, len(activities))
                
                # 每10个Activity显示一次进度
                if i % 10 == 0:
                    success_rate = (self.test_results["summary"]["successful"] / i) * 100
                    print(f"\n📈 进度报告 [{i}/{len(activities)}]:")
                    print(f"   成功率: {success_rate:.1f}%")
                    print(f"   网络活动: {self.test_results['summary']['network_captured']} 个Activity")
                    print()
                
            except KeyboardInterrupt:
                print(f"\n⚠️  用户中断测试，已完成 {i-1}/{len(activities)} 个Activity")
                break
            except Exception as e:
                print(f"❌ 测试异常: {e}")
                continue
        
        # 完成测试
        self.test_results["end_time"] = datetime.now().isoformat()
        self.generate_report()
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "=" * 100)
        print("📊 完整测试报告")
        print("=" * 100)
        
        summary = self.test_results["summary"]
        
        print(f"📱 测试应用: {self.package}")
        print(f"🕐 测试时间: {self.test_results['start_time']} - {self.test_results.get('end_time', '进行中')}")
        print(f"📋 总Activity数: {summary['total']}")
        print(f"✅ 成功启动: {summary['successful']}")
        print(f"❌ 启动失败: {summary['failed']}")
        print(f"📡 有网络活动: {summary['network_captured']}")
        
        if summary['total'] > 0:
            success_rate = (summary['successful'] / summary['total']) * 100
            network_rate = (summary['network_captured'] / summary['successful']) * 100 if summary['successful'] > 0 else 0
            print(f"📈 成功率: {success_rate:.1f}%")
            print(f"🌐 网络活动率: {network_rate:.1f}%")
        
        # 保存详细报告
        report_file = f"activity_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, indent=2, ensure_ascii=False)
        
        print(f"📄 详细报告已保存: {report_file}")
        
        # 显示网络活动最多的Activity
        network_activities = [a for a in self.test_results["activities"] 
                            if a.get("network_requests_after", 0) > a.get("network_requests_before", 0)]
        
        if network_activities:
            print(f"\n🌐 网络活动最活跃的Activity (前10个):")
            network_activities.sort(key=lambda x: x.get("network_requests_after", 0) - x.get("network_requests_before", 0), reverse=True)
            for i, activity in enumerate(network_activities[:10], 1):
                requests = activity.get("network_requests_after", 0) - activity.get("network_requests_before", 0)
                print(f"   {i}. {activity['name']}: +{requests} 请求")
        
        # 显示启动失败的Activity
        failed_activities = [a for a in self.test_results["activities"] if not a.get("success", False)]
        if failed_activities:
            print(f"\n❌ 启动失败的Activity (前10个):")
            for i, activity in enumerate(failed_activities[:10], 1):
                print(f"   {i}. {activity['name']}: {activity.get('error', 'Unknown error')}")

def main():
    print("🚀 易金在线APK完整Activity测试")
    print("包含网络抓包和行为分析")
    print("=" * 100)
    
    tester = CompleteActivityTester()
    
    try:
        tester.run_complete_test()
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
        tester.generate_report()
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        tester.generate_report()

if __name__ == "__main__":
    main()
