
🔧 手动执行步骤:

1. 确保Android模拟器运行:
   source android_env.sh && adb devices

2. 推送frida-server到设备:
   adb push frida-server /data/local/tmp/frida-server
   adb shell chmod 755 /data/local/tmp/frida-server

3. 启动frida-server (忽略SELinux警告):
   adb shell '/data/local/tmp/frida-server &'

4. 启动目标应用:
   adb shell am start -n com.yjzx.yjzx2017/.controller.activity.splash.SplashActivity

5. 运行Frida SSL绕过:
   export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin
   frida -U com.yjzx.yjzx2017 -l ssl_bypass.js

6. 测试HTTPS捕获:
   adb shell am start -a android.intent.action.VIEW -d https://httpbin.org/get

7. 检查捕获结果:
   查看 mitm-logs/realtime_capture.json 文件

🔧 故障排除:

• SELinux警告: 可以安全忽略，不影响功能
• 连接失败: 重启frida-server和应用
• 应用崩溃: 使用spawn模式启动
• 无HTTPS捕获: 检查代理设置和证书

💡 成功指标:
• frida-server进程运行
• Frida脚本加载成功
• 捕获到HTTPS请求
