#!/bin/bash
# Android SDK环境变量配置
export ANDROID_HOME="/Users/<USER>/Desktop/project/apk_detect/android-sdk"
export PATH="$ANDROID_HOME/cmdline-tools/latest/bin:$PATH"
export PATH="$ANDROID_HOME/platform-tools:$PATH"
export PATH="$ANDROID_HOME/emulator:$PATH"

# 添加Python用户bin目录到PATH（用于mitmproxy等工具）
export PATH="/Users/<USER>/Library/Python/3.9/bin:$PATH"

echo "Android SDK环境变量已设置"
echo "ANDROID_HOME: $ANDROID_HOME"
echo "模拟器路径: $ANDROID_HOME/emulator/emulator"
