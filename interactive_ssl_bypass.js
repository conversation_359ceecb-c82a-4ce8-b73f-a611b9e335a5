
// 交互式SSL绕过脚本
console.log("\n🚀 开始SSL绕过...");

Java.perform(function() {
    console.log("✅ Java.perform 启动成功");
    
    try {
        // Hook SSLContext
        var SSLContext = Java.use("javax.net.ssl.SSLContext");
        SSLContext.init.overload("[Ljavax.net.ssl.KeyManager;", "[Ljavax.net.ssl.TrustManager;", "java.security.SecureRandom").implementation = function(keyManagers, trustManagers, secureRandom) {
            console.log("🔓 SSLContext.init() 已绕过");
            this.init(keyManagers, null, secureRandom);
        };
        console.log("✅ SSLContext hook 成功");
        
        // Hook HostnameVerifier
        var HostnameVerifier = Java.use("javax.net.ssl.HostnameVerifier");
        HostnameVerifier.verify.overload("java.lang.String", "javax.net.ssl.SSLSession").implementation = function(hostname, session) {
            console.log("🔓 HostnameVerifier 已绕过: " + hostname);
            return true;
        };
        console.log("✅ HostnameVerifier hook 成功");
        
        // Hook X509TrustManager
        var X509TrustManager = Java.use("javax.net.ssl.X509TrustManager");
        X509TrustManager.checkClientTrusted.implementation = function(chain, authType) {
            console.log("🔓 X509TrustManager.checkClientTrusted 已绕过");
        };
        X509TrustManager.checkServerTrusted.implementation = function(chain, authType) {
            console.log("🔓 X509TrustManager.checkServerTrusted 已绕过");
        };
        X509TrustManager.getAcceptedIssuers.implementation = function() {
            console.log("🔓 X509TrustManager.getAcceptedIssuers 已绕过");
            return [];
        };
        console.log("✅ X509TrustManager hook 成功");
        
        console.log("\n🎉 SSL绕过初始化完成！");
        console.log("🔒 HTTPS流量现在可以被捕获");
        
    } catch (e) {
        console.log("❌ SSL绕过错误: " + e);
    }
});

console.log("📋 SSL绕过脚本已加载，等待Java环境...");
