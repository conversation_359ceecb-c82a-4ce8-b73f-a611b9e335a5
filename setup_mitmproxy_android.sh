#!/bin/bash

# mitmproxy Android模拟器配置脚本
# 基于官方文档: https://docs.mitmproxy.org/stable/howto/install-system-trusted-ca-android/

set -e

echo "🚀 开始配置mitmproxy for Android模拟器..."

# 1. 确保模拟器以可写系统模式运行
echo "📱 检查模拟器状态..."
if ! adb get-state >/dev/null 2>&1; then
    echo "❌ 模拟器未连接，请先启动模拟器"
    echo "   使用命令: emulator -avd test_avd -writable-system -no-snapshot"
    exit 1
fi

# 2. 生成mitmproxy证书哈希名称
echo "🔐 准备mitmproxy证书..."
cd ~/.mitmproxy/

if [ ! -f "mitmproxy-ca-cert.cer" ]; then
    echo "❌ mitmproxy证书不存在，请先运行mitmproxy生成证书"
    exit 1
fi

# 生成正确的哈希名称
HASHED_NAME=$(openssl x509 -inform PEM -subject_hash_old -in mitmproxy-ca-cert.cer | head -1)
cp mitmproxy-ca-cert.cer ${HASHED_NAME}.0
echo "✅ 证书重命名为: ${HASHED_NAME}.0"

# 3. 获取root权限并重新挂载系统分区
echo "🔧 配置系统权限..."
adb root
sleep 2
adb remount

# 4. 安装证书到系统证书存储
echo "📋 安装系统证书..."
adb push ${HASHED_NAME}.0 /system/etc/security/cacerts/
adb shell "chmod 644 /system/etc/security/cacerts/${HASHED_NAME}.0"

# 5. 重启模拟器使证书生效
echo "🔄 重启模拟器..."
adb reboot
echo "⏳ 等待模拟器重启..."
adb wait-for-device
sleep 10

# 6. 配置WiFi代理
echo "🌐 配置WiFi代理..."
# 注意：这里使用********，这是Android模拟器访问主机的特殊IP
adb shell "settings put global http_proxy ********:8080"

# 7. 验证配置
echo "✅ 验证配置..."
echo "代理设置: $(adb shell settings get global http_proxy)"
echo "证书安装: $(adb shell ls /system/etc/security/cacerts/${HASHED_NAME}.0 2>/dev/null && echo '已安装' || echo '未找到')"

echo "🎉 mitmproxy Android配置完成！"
echo ""
echo "📝 使用说明:"
echo "1. 启动mitmproxy: mitmdump -s your_script.py --listen-port 8080"
echo "2. 在Android应用中进行网络请求"
echo "3. mitmproxy将捕获所有HTTP/HTTPS流量"
echo ""
echo "⚠️  注意事项:"
echo "- 确保模拟器以 -writable-system 模式启动"
echo "- 某些应用可能使用证书固定，仍然无法拦截"
echo "- 如果仍有问题，可能需要使用透明代理模式"
