#!/usr/bin/env python3
"""
测试mitmproxy证书是否正常工作
验证HTTPS抓包功能
"""

import subprocess
import sys
import time
import json
from pathlib import Path
from datetime import datetime

class CertificateTest:
    def __init__(self):
        self.package = "com.yjzx.yjzx2017"
        
    def run_adb(self, cmd, timeout=30):
        """执行adb命令"""
        full_cmd = f"source android_env.sh && adb {cmd}"
        try:
            result = subprocess.run(full_cmd, shell=True, 
                                  capture_output=True, text=True, 
                                  timeout=timeout, check=True)
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            return f"ERROR: {e.stderr}"
        except subprocess.TimeoutExpired:
            return "ERROR: Timeout"
    
    def clear_capture_file(self):
        """清空捕获文件"""
        realtime_file = Path("mitm-logs/realtime_capture.json")
        if realtime_file.exists():
            with open(realtime_file, 'w') as f:
                json.dump([], f)
        print("🗑️  清空了网络捕获文件")
    
    def get_network_requests(self):
        """获取网络请求"""
        try:
            realtime_file = Path("mitm-logs/realtime_capture.json")
            if realtime_file.exists():
                with open(realtime_file, 'r') as f:
                    data = json.load(f)
                    if isinstance(data, list):
                        return data
            return []
        except Exception as e:
            print(f"⚠️  读取网络请求失败: {e}")
            return []
    
    def test_basic_https(self):
        """测试基本HTTPS连接"""
        print("🔍 测试基本HTTPS连接...")
        
        # 清空捕获文件
        self.clear_capture_file()
        
        # 等待一下让系统稳定
        time.sleep(2)
        
        # 记录初始请求数
        initial_requests = self.get_network_requests()
        initial_count = len(initial_requests)
        print(f"📡 初始网络请求数: {initial_count}")
        
        # 触发一些网络活动
        print("🌐 触发网络连接...")
        
        # 1. 打开浏览器访问HTTPS网站
        self.run_adb("shell am start -a android.intent.action.VIEW -d https://www.google.com")
        time.sleep(5)
        
        # 2. 启动应用
        self.run_adb(f"shell am broadcast -a android.intent.action.MAIN -n {self.package}/{self.package}.controller.activity.MainActivity")
        time.sleep(5)
        
        # 3. 触发更多网络活动
        self.run_adb("shell am start -a android.intent.action.VIEW -d https://httpbin.org/get")
        time.sleep(5)
        
        # 检查捕获的请求
        final_requests = self.get_network_requests()
        final_count = len(final_requests)
        new_requests = final_count - initial_count
        
        print(f"📡 最终网络请求数: {final_count}")
        print(f"📡 新增网络请求: {new_requests}")
        
        if new_requests > 0:
            print("🎉 检测到新的网络请求！")
            
            # 显示新请求的详情
            new_request_list = final_requests[-new_requests:]
            https_count = 0
            http_count = 0
            
            print("\n📋 新捕获的网络请求:")
            for i, req in enumerate(new_request_list, 1):
                url = req.get('url', 'N/A')
                method = req.get('method', 'N/A')
                status = req.get('status_code', 'N/A')
                scheme = req.get('scheme', 'N/A')
                
                if scheme == 'https':
                    https_count += 1
                elif scheme == 'http':
                    http_count += 1
                
                print(f"   {i}. {method} {url}")
                print(f"      状态: {status}")
                print(f"      协议: {scheme}")
                
                if 'response_time' in req:
                    print(f"      响应时间: {req['response_time']:.3f}s")
                print()
            
            print(f"📊 统计:")
            print(f"   HTTPS请求: {https_count}")
            print(f"   HTTP请求: {http_count}")
            
            if https_count > 0:
                print("🎉 证书工作正常！成功捕获HTTPS请求！")
                return True
            else:
                print("⚠️  只捕获到HTTP请求，HTTPS可能仍有问题")
                return False
        else:
            print("❌ 没有检测到新的网络请求")
            return False
    
    def test_app_specific(self):
        """测试应用特定的HTTPS请求"""
        print("\n🎯 测试应用特定的HTTPS请求...")
        
        # 清空捕获文件
        self.clear_capture_file()
        time.sleep(2)
        
        initial_count = len(self.get_network_requests())
        print(f"📡 初始网络请求数: {initial_count}")
        
        # 启动应用的不同Activity
        activities = [
            f"{self.package}.controller.activity.MainActivity",
            f"{self.package}.controller.login.activity.LoginActivity",
            f"{self.package}.controller.activity.setting.SettingActivity"
        ]
        
        for activity in activities:
            print(f"🚀 启动Activity: {activity.split('.')[-1]}")
            self.run_adb(f"shell am broadcast -a android.intent.action.MAIN -n {self.package}/{activity}")
            time.sleep(3)
            
            # 模拟用户交互
            self.run_adb("shell input tap 540 960")
            time.sleep(2)
        
        # 检查结果
        final_count = len(self.get_network_requests())
        new_requests = final_count - initial_count
        
        print(f"📡 最终网络请求数: {final_count}")
        print(f"📡 新增网络请求: {new_requests}")
        
        if new_requests > 0:
            requests = self.get_network_requests()[-new_requests:]
            
            # 分析请求
            app_requests = []
            for req in requests:
                host = req.get('host', '')
                if any(domain in host for domain in ['yjzx.com', 'jpush.cn', 'qq.com', 'sobot.com']):
                    app_requests.append(req)
            
            if app_requests:
                print(f"🎉 捕获到 {len(app_requests)} 个应用相关的网络请求！")
                
                for i, req in enumerate(app_requests, 1):
                    print(f"   {i}. {req.get('method', 'N/A')} {req.get('url', 'N/A')}")
                    print(f"      状态: {req.get('status_code', 'N/A')}")
                    print(f"      主机: {req.get('host', 'N/A')}")
                
                return True
            else:
                print("⚠️  捕获到网络请求，但不是应用相关的")
                return False
        else:
            print("❌ 没有捕获到新的网络请求")
            return False
    
    def run_certificate_test(self):
        """运行完整的证书测试"""
        print("🚀 mitmproxy证书功能测试")
        print("🔧 验证HTTPS抓包是否正常工作")
        print("=" * 60)
        
        # 检查mitmproxy是否运行
        try:
            import requests
            response = requests.get("http://127.0.0.1:8080", timeout=5)
        except:
            print("❌ mitmproxy似乎没有运行，请先启动mitmproxy")
            return False
        
        print("✅ mitmproxy正在运行")
        
        # 检查设备连接和代理设置
        result = self.run_adb("devices")
        if "device" not in result:
            print("❌ Android设备未连接")
            return False
        
        print("✅ Android设备已连接")
        
        # 检查代理设置
        proxy_setting = self.run_adb("shell settings get global http_proxy")
        if "192.168.1.123:8080" not in proxy_setting:
            print("⚠️  代理设置可能不正确，重新设置...")
            self.run_adb("shell settings put global http_proxy 192.168.1.123:8080")
        
        print("✅ 代理设置正确")
        
        # 运行测试
        test_results = []
        
        # 测试1: 基本HTTPS连接
        print("\n" + "="*40)
        print("测试1: 基本HTTPS连接")
        print("="*40)
        result1 = self.test_basic_https()
        test_results.append(("基本HTTPS连接", result1))
        
        # 测试2: 应用特定请求
        print("\n" + "="*40)
        print("测试2: 应用特定HTTPS请求")
        print("="*40)
        result2 = self.test_app_specific()
        test_results.append(("应用特定请求", result2))
        
        # 总结
        print("\n" + "="*60)
        print("📊 证书测试结果总结")
        print("="*60)
        
        passed = 0
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n📈 测试通过率: {passed}/{len(test_results)} ({passed/len(test_results)*100:.1f}%)")
        
        if passed == len(test_results):
            print("🎉 所有测试通过！证书安装成功，HTTPS抓包正常工作！")
            return True
        elif passed > 0:
            print("⚠️  部分测试通过，证书可能部分工作")
            return True
        else:
            print("❌ 所有测试失败，证书安装可能有问题")
            print("\n🔧 故障排除建议:")
            print("   1. 检查证书是否正确安装在Android设备中")
            print("   2. 确认应用信任用户安装的证书")
            print("   3. 尝试重启设备")
            print("   4. 检查网络连接和代理设置")
            return False

def main():
    tester = CertificateTest()
    
    try:
        success = tester.run_certificate_test()
        
        if success:
            print("\n🎯 接下来可以运行完整的网络抓包测试了！")
        else:
            print("\n🔧 需要进一步排查证书问题")
            
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")

if __name__ == "__main__":
    main()
