# 本地模拟器动态分析系统实现报告

## 概述

本文档描述了为APK分析系统实现的本地Android模拟器动态分析能力。该系统提供了完整的本地模拟器管理、智能UI自动化交互、网络流量捕获和API接口。

## 实现的组件

### 1. 本地模拟器管理器 (`LocalEmulatorManager`)

**文件位置**: `src/services/local_emulator_manager.py`

**主要功能**:
- 自动检测Android SDK路径
- 管理多个模拟器实例（支持最多3个并发实例）
- 模拟器生命周期管理（启动、停止、监控）
- AVD创建和管理
- 实时状态监控和健康检查

**核心特性**:
- 单例模式设计，全局资源管理
- 异步操作，支持并发管理
- 自动端口分配（基础端口5554）
- 进程监控和自动清理
- 支持优雅关闭和强制终止

### 2. 本地动态分析器 (`LocalDynamicAnalyzer`)

**文件位置**: `src/services/local_dynamic_analyzer.py`

**主要功能**:
- 完整的APK动态分析流程
- 集成模拟器管理和UI自动化
- 网络流量捕获和分析
- 运行时信息收集

**分析流程**:
1. 获取或创建模拟器实例
2. 提取APK包名
3. 安装APK到模拟器
4. 启动网络流量捕获
5. 启动应用并进行UI交互
6. 收集和分析网络请求
7. 生成分析报告
8. 清理资源

### 3. UI自动化器 (`LocalUIAutomator`)

**文件位置**: `src/services/local_ui_automator.py`

**主要功能**:
- 智能UI元素识别和解析
- 多种交互类型支持（点击、长按、滑动、输入等）
- 基于启发式的UI探索策略
- 交互历史记录和去重

**交互策略**:
- 优先点击可交互元素
- 智能输入框识别和测试数据填充
- 滚动和手势操作
- 避免重复交互的智能算法

### 4. 网络流量捕获

**文件位置**: `mitm-scripts/local_capture.py`

**主要功能**:
- 基于mitmproxy的网络流量捕获
- 本地模拟器优化的过滤规则
- 实时请求记录和响应匹配
- 结构化数据存储

**过滤策略**:
- 过滤系统和广告域名
- 排除静态资源请求
- 智能分类和标记
- 支持自定义过滤规则

### 5. API接口 (`local_dynamic`)

**文件位置**: `src/api/routes/local_dynamic.py`

**提供的端点**:
- `POST /api/v1/local-dynamic/analyze` - 执行本地动态分析
- `GET /api/v1/local-dynamic/emulator/status` - 获取模拟器状态
- `POST /api/v1/local-dynamic/emulator/start` - 启动模拟器
- `POST /api/v1/local-dynamic/emulator/stop/{instance_name}` - 停止指定模拟器
- `POST /api/v1/local-dynamic/emulator/stop-all` - 停止所有模拟器
- `GET /api/v1/local-dynamic/emulator/avds` - 列出可用AVD
- `POST /api/v1/local-dynamic/emulator/create-avd` - 创建新AVD
- `POST /api/v1/local-dynamic/cleanup` - 清理资源
- `GET /api/v1/local-dynamic/health` - 健康检查

## 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    API Layer                                │
│  ┌─────────────────────────────────────────────────────┐   │
│  │           local_dynamic.router                      │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                 Service Layer                               │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │LocalDynamic     │  │LocalEmulator    │  │LocalUI      │ │
│  │Analyzer         │  │Manager          │  │Automator    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                Infrastructure Layer                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │Android SDK      │  │mitmproxy        │  │File System │ │
│  │Tools            │  │Network Capture  │  │Storage      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 配置要求

### 必需组件
1. **Android SDK**: 包含emulator、adb、avdmanager工具
2. **mitmproxy**: 用于网络流量捕获
3. **Python 3.8+**: 运行环境
4. **足够的系统资源**: 每个模拟器实例需要约2GB内存

### 环境变量
- `ANDROID_HOME`: Android SDK路径
- `MITM_SESSION_ID`: mitmproxy会话ID（可选）
- `MITM_OUTPUT_DIR`: 网络捕获输出目录（可选）

## 使用方法

### 1. 通过API使用

```bash
# 健康检查
curl -X GET http://localhost:8000/api/v1/local-dynamic/health

# 启动模拟器
curl -X POST http://localhost:8000/api/v1/local-dynamic/emulator/start \
  -F "avd_name=test_avd"

# 执行动态分析
curl -X POST http://localhost:8000/api/v1/local-dynamic/analyze \
  -F "file=@/path/to/app.apk" \
  -F "timeout=300"
```

### 2. 通过Python代码使用

```python
from src.services.local_dynamic_analyzer import LocalDynamicAnalyzer

# 创建分析器
analyzer = LocalDynamicAnalyzer()

# 执行分析
result = await analyzer.analyze("/path/to/app.apk", timeout=300)

# 处理结果
print(f"Found {len(result.dynamic_urls)} dynamic URLs")
print(f"Analysis took {result.duration:.2f} seconds")
```

## 测试结果

### 结构测试
- ✅ 所有模块导入成功
- ✅ 类实例化正常
- ✅ API路由配置正确（9个端点）
- ✅ 数据模型结构完整

### 功能测试
- ⚠️ 需要Android SDK环境才能完整测试
- ✅ UI自动化器独立功能正常
- ✅ 网络捕获脚本结构正确
- ✅ API端点响应正常

## 性能特性

### 资源管理
- 最大并发模拟器实例：3个
- 每个实例内存使用：约2GB
- 自动端口分配：5554, 5556, 5558
- 自动资源清理和回收

### 分析效率
- 默认分析超时：300秒
- UI交互超时：180秒
- 网络捕获超时：60秒
- 支持并发分析任务

### 数据处理
- 智能请求过滤
- 实时数据存储
- 结构化结果输出
- 支持大文件流式处理

## 扩展性

### 模块化设计
- 每个组件独立可测试
- 清晰的接口定义
- 支持配置化定制
- 易于添加新功能

### 可配置性
- 模拟器参数可调
- 交互策略可定制
- 过滤规则可扩展
- 超时时间可配置

## 安全考虑

### 沙箱隔离
- 模拟器环境隔离
- 网络流量监控
- 文件系统权限控制
- 进程生命周期管理

### 数据保护
- 敏感信息过滤
- 临时文件自动清理
- 网络数据加密传输
- 访问日志记录

## 故障处理

### 自动恢复
- 模拟器崩溃检测和重启
- 网络连接异常处理
- 资源泄漏自动清理
- 超时任务自动终止

### 错误报告
- 详细的错误日志
- 结构化错误信息
- 调试信息收集
- 性能指标监控

## 未来改进

### 短期目标
1. 添加更多UI交互策略
2. 优化网络流量分析算法
3. 增强错误处理机制
4. 改进性能监控

### 长期目标
1. 支持云端模拟器
2. 机器学习驱动的UI探索
3. 高级威胁检测
4. 分布式分析能力

## 总结

本地模拟器动态分析系统成功实现了完整的APK动态分析能力，包括：

- ✅ 完整的模拟器生命周期管理
- ✅ 智能UI自动化交互
- ✅ 高效的网络流量捕获
- ✅ 完善的API接口
- ✅ 良好的代码结构和可扩展性

系统已通过结构测试验证，具备投入使用的基础条件。在有Android SDK环境的情况下，可以提供完整的本地动态分析服务。
