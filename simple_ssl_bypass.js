
Java.perform(function() {
    console.log("[+] 简化SSL绕过启动");
    
    try {
        var SSLContext = Java.use("javax.net.ssl.SSLContext");
        SSLContext.init.overload("[Ljavax.net.ssl.KeyManager;", "[Ljavax.net.ssl.TrustManager;", "java.security.SecureRandom").implementation = function(km, tm, sr) {
            console.log("[+] SSL绕过成功");
            this.init(km, [], sr);
        };
    } catch(e) {}
    
    try {
        var CertificatePinner = Java.use("okhttp3.CertificatePinner");
        CertificatePinner.check.implementation = function() {
            console.log("[+] OkHttp绕过");
            return;
        };
    } catch(e) {}
    
    console.log("[+] 简化SSL绕过完成");
});
