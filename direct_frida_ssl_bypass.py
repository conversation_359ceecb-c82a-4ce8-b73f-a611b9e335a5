#!/usr/bin/env python3
"""
直接Frida SSL绕过
手动启动应用，然后使用Python API附加
"""

import subprocess
import sys
import time
import json
from pathlib import Path

def run_adb(cmd, timeout=30):
    """执行adb命令"""
    full_cmd = f"source android_env.sh && adb {cmd}"
    try:
        result = subprocess.run(full_cmd, shell=True, 
                              capture_output=True, text=True, 
                              timeout=timeout, check=True)
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        return f"ERROR: {e.stderr}"
    except subprocess.TimeoutExpired:
        return "ERROR: Timeout"

def setup_environment():
    """设置环境"""
    print("🔧 设置环境...")
    
    # 检查设备
    result = run_adb("devices")
    if "device" not in result:
        print("❌ 设备未连接")
        return False
    print("✅ 设备已连接")
    
    # 启动frida-server
    print("🚀 启动frida-server...")
    run_adb("shell pkill frida-server")
    time.sleep(2)
    
    subprocess.Popen("source android_env.sh && adb shell '/data/local/tmp/frida-server &'", shell=True)
    time.sleep(8)
    
    result = run_adb("shell ps | grep frida-server")
    if "frida-server" not in result:
        print("❌ frida-server启动失败")
        return False
    print("✅ frida-server启动成功")
    
    return True

def start_target_app():
    """启动目标应用"""
    print("📱 启动目标应用...")
    
    package = "com.yjzx.yjzx2017"
    
    # 强制停止应用
    run_adb(f"shell am force-stop {package}")
    time.sleep(2)
    
    # 启动应用
    result = run_adb(f"shell am start -n {package}/.controller.activity.splash.SplashActivity")
    if "ERROR" in result:
        print(f"❌ 应用启动失败: {result}")
        return False
    
    print("✅ 应用启动命令已发送")
    time.sleep(8)  # 等待应用完全启动
    
    # 检查应用是否运行
    ps_result = run_adb(f"shell ps | grep {package}")
    if package in ps_result:
        # 提取PID
        lines = ps_result.split('\n')
        for line in lines:
            if package in line and 'pushcore' not in line:
                parts = line.split()
                if len(parts) >= 2:
                    pid = parts[1]
                    print(f"✅ 应用正在运行，PID: {pid}")
                    return int(pid)
    
    print("⚠️  应用可能未完全启动，但继续尝试")
    return True

def run_frida_ssl_bypass():
    """运行Frida SSL绕过"""
    print("🔧 运行Frida SSL绕过...")
    
    try:
        import frida
        
        # 连接到设备
        device = frida.get_usb_device()
        print("✅ 连接到设备成功")
        
        # 读取SSL绕过脚本
        if not Path("ssl_bypass.js").exists():
            print("❌ ssl_bypass.js文件不存在")
            return None, None
        
        with open("ssl_bypass.js", 'r') as f:
            script_code = f.read()
        print("✅ SSL绕过脚本读取成功")
        
        # 尝试spawn模式
        package = "com.yjzx.yjzx2017"
        
        print(f"🚀 使用spawn模式启动: {package}")
        
        # 先停止应用
        run_adb(f"shell am force-stop {package}")
        time.sleep(2)
        
        # Spawn应用
        pid = device.spawn([package])
        print(f"✅ 应用已spawn，PID: {pid}")
        
        # 附加到进程
        session = device.attach(pid)
        print("✅ 附加到进程成功")
        
        # 创建脚本
        script = session.create_script(script_code)
        print("✅ 创建SSL绕过脚本成功")
        
        # 设置消息处理器
        def on_message(message, data):
            if message['type'] == 'send':
                print(f"[Frida] {message['payload']}")
            elif message['type'] == 'error':
                print(f"[Frida Error] {message['stack']}")
        
        script.on('message', on_message)
        
        # 加载脚本
        script.load()
        print("✅ SSL绕过脚本加载成功")
        
        # 恢复进程
        device.resume(pid)
        print("✅ 进程已恢复运行")
        
        # 等待脚本初始化
        time.sleep(5)
        
        return script, session
        
    except ImportError:
        print("❌ Frida Python模块未安装")
        return None, None
    except Exception as e:
        print(f"❌ Frida SSL绕过异常: {e}")
        return None, None

def test_https_capture():
    """测试HTTPS捕获"""
    print("🔍 测试HTTPS捕获...")
    
    # 清空捕获文件
    realtime_file = Path("mitm-logs/realtime_capture.json")
    if realtime_file.exists():
        with open(realtime_file, 'w') as f:
            json.dump([], f)
    
    time.sleep(3)
    
    # 触发HTTPS请求
    print("🌐 触发HTTPS请求...")
    
    test_actions = [
        ("访问HTTPS测试站点", "shell am start -a android.intent.action.VIEW -d https://httpbin.org/get"),
        ("访问百度HTTPS", "shell am start -a android.intent.action.VIEW -d https://www.baidu.com"),
        ("应用内操作", "shell input tap 540 960"),
    ]
    
    for action_name, action_cmd in test_actions:
        print(f"   🔄 {action_name}...")
        result = run_adb(action_cmd)
        if "ERROR" in result:
            print(f"      ⚠️  {result}")
        time.sleep(8)
    
    # 等待网络请求
    print("⏳ 等待网络请求处理...")
    time.sleep(15)
    
    # 检查结果
    try:
        with open(realtime_file, 'r') as f:
            data = json.load(f)
            
            if isinstance(data, list) and len(data) > 0:
                https_requests = [req for req in data if req.get('scheme') == 'https']
                http_requests = [req for req in data if req.get('scheme') == 'http']
                
                print(f"📊 捕获结果:")
                print(f"   总请求: {len(data)}")
                print(f"   HTTPS请求: {len(https_requests)}")
                print(f"   HTTP请求: {len(http_requests)}")
                
                if https_requests:
                    print("🎉 SSL绕过成功！捕获到HTTPS请求！")
                    print("📋 HTTPS请求示例:")
                    for i, req in enumerate(https_requests[:3], 1):
                        url = req.get('url', 'N/A')
                        method = req.get('method', 'N/A')
                        status = req.get('status_code', 'N/A')
                        print(f"   {i}. {method} {url}")
                        print(f"      状态: {status}")
                    return True
                elif http_requests:
                    print("⚠️  只捕获到HTTP请求，SSL绕过可能未完全生效")
                    return False
                else:
                    print("⚠️  捕获到请求但协议未识别")
                    return False
            else:
                print("❌ 没有捕获到网络请求")
                return False
                
    except Exception as e:
        print(f"❌ 检查捕获结果失败: {e}")
        return False

def main():
    print("🚀 直接Frida SSL绕过")
    print("🔧 手动启动应用 + Python API附加")
    print("=" * 60)
    
    try:
        # 设置环境
        if not setup_environment():
            return False
        
        # 运行Frida SSL绕过
        script, session = run_frida_ssl_bypass()
        
        if script and session:
            print("\n🎉 Frida SSL绕过脚本运行成功！")
            
            # 测试HTTPS捕获
            print("\n" + "="*50)
            print("🔍 测试HTTPS捕获")
            print("="*50)
            
            success = test_https_capture()
            
            if success:
                print("\n🎉 SSL绕过完全成功！")
                print("✅ HTTPS流量现在可以被mitmproxy完全捕获！")
                print("🔒 SSL证书验证已被完全绕过！")
                
                print("\n📋 系统现在具备完整的HTTPS抓包能力！")
                print("💡 可以进行:")
                print("   • 完整的APK安全分析")
                print("   • HTTPS API逆向工程")
                print("   • 加密通信分析")
                print("   • 隐私数据监控")
                
                print("\n⚠️  Frida脚本正在运行...")
                print("💡 按Ctrl+C停止")
                
                try:
                    # 保持运行
                    while True:
                        time.sleep(30)
                        print("💡 SSL绕过仍在运行...")
                except KeyboardInterrupt:
                    print("\n⚠️  停止Frida脚本...")
                    
            else:
                print("\n⚠️  HTTPS捕获测试未完全成功")
                print("🔧 但SSL绕过脚本可能仍在工作")
                print("💡 可以手动测试更多HTTPS请求")
                
                try:
                    time.sleep(60)  # 运行1分钟
                except KeyboardInterrupt:
                    print("\n⚠️  停止脚本...")
            
            # 清理
            try:
                script.unload()
                session.detach()
                print("✅ Frida脚本已卸载")
            except:
                pass
            
            return success
        else:
            print("❌ Frida SSL绕过失败")
            return False
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断")
        return False
    except Exception as e:
        print(f"\n❌ 异常: {e}")
        return False
    finally:
        # 清理frida-server
        print("🧹 清理frida-server...")
        subprocess.run("source android_env.sh && adb shell pkill frida-server", shell=True)

if __name__ == "__main__":
    main()
