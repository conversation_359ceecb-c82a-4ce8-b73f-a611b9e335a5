#!/usr/bin/env python3
"""
Frida启动问题诊断和修复工具
专门解决Frida连接失败问题
"""

import subprocess
import sys
import time
import json
from pathlib import Path

class FridaStartupFix:
    def __init__(self):
        self.package = "com.yjzx.yjzx2017"
        
    def log(self, message, level="INFO"):
        """日志输出"""
        timestamp = time.strftime("%H:%M:%S")
        if level == "SUCCESS":
            print(f"🎉 [{timestamp}] {message}")
        elif level == "ERROR":
            print(f"❌ [{timestamp}] {message}")
        elif level == "WARNING":
            print(f"⚠️  [{timestamp}] {message}")
        else:
            print(f"📋 [{timestamp}] {message}")
    
    def run_adb(self, cmd, timeout=30):
        """执行adb命令"""
        full_cmd = f"source android_env.sh && adb {cmd}"
        try:
            result = subprocess.run(full_cmd, shell=True, 
                                  capture_output=True, text=True, 
                                  timeout=timeout, check=True)
            return result.returncode, result.stdout.strip(), result.stderr.strip()
        except subprocess.CalledProcessError as e:
            return e.returncode, "", e.stderr
        except subprocess.TimeoutExpired:
            return -1, "", "Timeout"
    
    def diagnose_frida_environment(self):
        """诊断Frida环境"""
        self.log("🔍 诊断Frida环境...")
        
        issues = []
        
        # 1. 检查设备连接
        self.log("检查设备连接...")
        returncode, stdout, stderr = self.run_adb("devices")
        if "device" in stdout and "emulator" in stdout:
            self.log("✅ 设备连接正常")
        else:
            issues.append("设备连接异常")
            self.log("❌ 设备连接异常", "ERROR")
        
        # 2. 检查frida-server状态
        self.log("检查frida-server状态...")
        returncode, stdout, stderr = self.run_adb("shell ps | grep frida-server")
        if "frida-server" in stdout:
            self.log("✅ frida-server正在运行")
            # 显示进程信息
            lines = stdout.split('\n')
            for line in lines:
                if 'frida-server' in line:
                    parts = line.split()
                    if len(parts) >= 2:
                        pid = parts[1]
                        self.log(f"   PID: {pid}")
        else:
            issues.append("frida-server未运行")
            self.log("❌ frida-server未运行", "ERROR")
        
        # 3. 检查Frida端口
        self.log("检查Frida端口...")
        returncode, stdout, stderr = self.run_adb("shell netstat -tlnp | grep 27042")
        if "27042" in stdout:
            self.log("✅ Frida端口27042正在监听")
        else:
            issues.append("Frida端口未监听")
            self.log("❌ Frida端口未监听", "WARNING")
        
        # 4. 检查目标应用
        self.log("检查目标应用...")
        returncode, stdout, stderr = self.run_adb(f"shell ps | grep {self.package}")
        if self.package in stdout:
            self.log("✅ 目标应用正在运行")
            # 显示进程信息
            lines = stdout.split('\n')
            for line in lines:
                if self.package in line:
                    parts = line.split()
                    if len(parts) >= 2:
                        pid = parts[1]
                        process_name = parts[-1] if len(parts) > 8 else "unknown"
                        self.log(f"   PID: {pid}, 进程: {process_name}")
        else:
            issues.append("目标应用未运行")
            self.log("❌ 目标应用未运行", "ERROR")
        
        # 5. 检查Frida客户端
        self.log("检查Frida客户端...")
        try:
            result = subprocess.run("export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin && frida --version", 
                                  shell=True, capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                self.log(f"✅ Frida客户端正常: {result.stdout.strip()}")
            else:
                issues.append("Frida客户端异常")
                self.log("❌ Frida客户端异常", "ERROR")
        except:
            issues.append("Frida客户端无法执行")
            self.log("❌ Frida客户端无法执行", "ERROR")
        
        return issues
    
    def fix_frida_server(self):
        """修复frida-server"""
        self.log("🔧 修复frida-server...")
        
        # 1. 杀死所有frida-server进程
        self.log("杀死现有frida-server进程...")
        self.run_adb("shell pkill -9 frida-server")
        time.sleep(3)
        
        # 2. 重新推送frida-server
        self.log("重新推送frida-server...")
        returncode, stdout, stderr = self.run_adb("push frida-server /data/local/tmp/frida-server")
        if returncode == 0:
            self.log("✅ frida-server推送成功")
        else:
            self.log(f"❌ frida-server推送失败: {stderr}", "ERROR")
            return False
        
        # 3. 设置权限
        self.log("设置frida-server权限...")
        self.run_adb("shell chmod 755 /data/local/tmp/frida-server")
        
        # 4. 启动frida-server
        self.log("启动frida-server...")
        subprocess.Popen("source android_env.sh && adb shell '/data/local/tmp/frida-server' 2>/dev/null &", shell=True)
        
        # 5. 等待启动
        self.log("等待frida-server启动...")
        for i in range(10):
            time.sleep(1)
            returncode, stdout, stderr = self.run_adb("shell ps | grep frida-server")
            if "frida-server" in stdout:
                self.log("✅ frida-server启动成功")
                return True
            self.log(f"   等待中... ({i+1}/10)")
        
        self.log("❌ frida-server启动失败", "ERROR")
        return False
    
    def fix_target_app(self):
        """修复目标应用"""
        self.log("🔧 修复目标应用...")
        
        # 1. 强制停止应用
        self.log("强制停止应用...")
        self.run_adb(f"shell am force-stop {self.package}")
        time.sleep(2)
        
        # 2. 清除应用数据（可选）
        self.log("清除应用缓存...")
        self.run_adb(f"shell pm clear {self.package}")
        time.sleep(2)
        
        # 3. 重新启动应用
        self.log("重新启动应用...")
        returncode, stdout, stderr = self.run_adb(f"shell am start -n {self.package}/.controller.activity.splash.SplashActivity")
        
        if returncode == 0:
            self.log("✅ 应用启动命令执行成功")
            
            # 4. 等待应用完全启动
            self.log("等待应用完全启动...")
            for i in range(15):
                time.sleep(1)
                returncode, stdout, stderr = self.run_adb(f"shell ps | grep {self.package}")
                if self.package in stdout:
                    self.log("✅ 应用启动成功")
                    return True
                self.log(f"   等待中... ({i+1}/15)")
            
            self.log("⚠️  应用可能未完全启动", "WARNING")
            return True  # 继续尝试
        else:
            self.log(f"❌ 应用启动失败: {stderr}", "ERROR")
            return False
    
    def test_frida_connection_methods(self):
        """测试多种Frida连接方法"""
        self.log("🔧 测试多种Frida连接方法...")
        
        # 获取应用进程信息
        returncode, stdout, stderr = self.run_adb(f"shell ps | grep {self.package}")
        
        if self.package not in stdout:
            self.log("❌ 目标应用未运行", "ERROR")
            return None
        
        # 解析进程信息
        processes = []
        lines = stdout.split('\n')
        for line in lines:
            if self.package in line:
                parts = line.split()
                if len(parts) >= 2:
                    pid = parts[1]
                    process_name = parts[-1] if len(parts) > 8 else "unknown"
                    processes.append((pid, process_name))
        
        self.log(f"找到 {len(processes)} 个相关进程:")
        for pid, name in processes:
            self.log(f"   PID: {pid}, 名称: {name}")
        
        # 测试不同的连接方法
        connection_methods = [
            ("包名连接", f"frida -U {self.package} -l ssl_bypass.js"),
            ("主进程PID连接", None),  # 将在下面设置
            ("Spawn模式", f"frida -U -f {self.package} -l ssl_bypass.js"),
        ]
        
        # 设置主进程PID连接
        main_pid = None
        for pid, name in processes:
            if 'pushcore' not in name:
                main_pid = pid
                break
        
        if main_pid:
            connection_methods[1] = ("主进程PID连接", f"frida -U {main_pid} -l ssl_bypass.js")
        else:
            # 使用第一个进程
            if processes:
                connection_methods[1] = ("进程PID连接", f"frida -U {processes[0][0]} -l ssl_bypass.js")
        
        # 逐个测试连接方法
        for method_name, cmd in connection_methods:
            if not cmd:
                continue
                
            self.log(f"测试: {method_name}")
            self.log(f"命令: {cmd}")
            
            try:
                full_cmd = f"export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin && timeout 20 {cmd}"
                
                process = subprocess.Popen(
                    full_cmd, shell=True,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                
                # 等待一段时间
                time.sleep(15)
                
                if process.poll() is None:
                    self.log(f"✅ {method_name} 连接成功！", "SUCCESS")
                    
                    # 读取输出
                    try:
                        import select
                        if hasattr(select, 'select'):
                            ready, _, _ = select.select([process.stdout], [], [], 2)
                            if ready:
                                output = process.stdout.read(1000)
                                if output:
                                    self.log("Frida输出:")
                                    lines = output.split('\n')[:10]
                                    for line in lines:
                                        if line.strip():
                                            self.log(f"   {line.strip()}")
                    except:
                        pass
                    
                    # 终止进程用于测试
                    process.terminate()
                    return method_name, cmd
                else:
                    stdout, stderr = process.communicate()
                    self.log(f"❌ {method_name} 连接失败")
                    
                    # 显示错误信息（过滤system_server）
                    if stderr and 'system_server' not in stderr:
                        error_lines = stderr.split('\n')[:3]
                        for line in error_lines:
                            if line.strip():
                                self.log(f"   错误: {line.strip()}")
                    elif 'system_server' in stderr:
                        self.log("   遇到system_server问题（Android模拟器常见问题）")
                
            except Exception as e:
                self.log(f"❌ {method_name} 测试异常: {e}")
        
        self.log("❌ 所有连接方法都失败", "ERROR")
        return None
    
    def run_working_frida_bypass(self, method_name, cmd):
        """运行可工作的Frida绕过"""
        self.log(f"🚀 使用 {method_name} 运行Frida SSL绕过...")
        
        full_cmd = f"export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin && {cmd}"
        
        try:
            process = subprocess.Popen(
                full_cmd, shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.log("Frida SSL绕过已启动")
            self.log("⏳ 等待SSL绕过脚本加载...")
            time.sleep(20)
            
            if process.poll() is None:
                self.log("✅ Frida SSL绕过正在运行", "SUCCESS")
                
                # 测试SSL绕过效果
                self.test_ssl_bypass_effect()
                
                self.log("💡 Frida SSL绕过正在后台运行...")
                self.log("⚠️  按Ctrl+C停止")
                
                try:
                    # 保持运行
                    while process.poll() is None:
                        time.sleep(30)
                        self.log("💡 SSL绕过仍在运行...")
                except KeyboardInterrupt:
                    self.log("⚠️  用户中断，停止Frida进程...")
                    process.terminate()
                
                return True
            else:
                stdout, stderr = process.communicate()
                self.log("❌ Frida进程意外退出", "ERROR")
                if stderr and 'system_server' not in stderr:
                    self.log(f"错误: {stderr}")
                return False
                
        except Exception as e:
            self.log(f"❌ 运行Frida异常: {e}", "ERROR")
            return False
    
    def test_ssl_bypass_effect(self):
        """测试SSL绕过效果"""
        self.log("🔍 测试SSL绕过效果...")
        
        # 清空捕获文件
        capture_file = Path("mitm-logs/realtime_capture.json")
        if capture_file.exists():
            with open(capture_file, 'w') as f:
                json.dump([], f)
        
        # 触发HTTPS请求
        test_urls = [
            "https://httpbin.org/get",
            "https://www.baidu.com"
        ]
        
        for url in test_urls:
            self.log(f"访问: {url}")
            self.run_adb(f"shell am start -a android.intent.action.VIEW -d {url}")
            time.sleep(8)
        
        # 应用内操作
        self.log("应用内操作...")
        self.run_adb("shell input tap 540 960")
        time.sleep(5)
        
        # 等待处理
        self.log("等待网络请求处理...")
        time.sleep(15)
        
        # 检查结果
        try:
            if capture_file.exists():
                with open(capture_file, 'r') as f:
                    data = json.load(f)
                
                if isinstance(data, list) and len(data) > 0:
                    https_count = len([req for req in data if req.get('scheme') == 'https'])
                    http_count = len([req for req in data if req.get('scheme') == 'http'])
                    
                    self.log(f"捕获结果: 总={len(data)}, HTTPS={https_count}, HTTP={http_count}")
                    
                    if https_count > 0:
                        self.log("🎉 SSL绕过成功！捕获到HTTPS请求！", "SUCCESS")
                    elif http_count > 0:
                        self.log("捕获到HTTP请求，SSL绕过可能部分生效", "WARNING")
                    else:
                        self.log("捕获到请求但协议未识别", "WARNING")
                else:
                    self.log("没有捕获到网络请求", "WARNING")
            else:
                self.log("捕获文件不存在", "WARNING")
                
        except Exception as e:
            self.log(f"检查结果失败: {e}", "ERROR")
    
    def run_complete_fix(self):
        """运行完整的修复流程"""
        self.log("🚀 Frida启动问题完整修复流程")
        self.log("🔧 诊断问题 → 修复环境 → 测试连接 → 运行绕过")
        self.log("=" * 70)
        
        try:
            # 步骤1: 诊断环境
            issues = self.diagnose_frida_environment()
            
            if issues:
                self.log(f"发现 {len(issues)} 个问题，开始修复...", "WARNING")
            else:
                self.log("环境诊断通过", "SUCCESS")
            
            # 步骤2: 修复frida-server
            if not self.fix_frida_server():
                self.log("frida-server修复失败", "ERROR")
                return False
            
            # 步骤3: 修复目标应用
            if not self.fix_target_app():
                self.log("目标应用修复失败", "ERROR")
                return False
            
            # 步骤4: 测试连接方法
            result = self.test_frida_connection_methods()
            
            if result:
                method_name, cmd = result
                self.log(f"找到可工作的连接方法: {method_name}", "SUCCESS")
                
                # 步骤5: 运行SSL绕过
                success = self.run_working_frida_bypass(method_name, cmd)
                
                if success:
                    self.log("🎉 Frida SSL绕过修复并运行成功！", "SUCCESS")
                    return True
                else:
                    self.log("SSL绕过运行失败", "ERROR")
                    return False
            else:
                self.log("所有连接方法都失败", "ERROR")
                self.log("可能的解决方案:")
                self.log("  1. 重启Android模拟器")
                self.log("  2. 重新安装目标应用")
                self.log("  3. 使用不同版本的frida-server")
                self.log("  4. 检查Android系统版本兼容性")
                return False
                
        except KeyboardInterrupt:
            self.log("用户中断修复流程")
            return False
        except Exception as e:
            self.log(f"修复流程异常: {e}", "ERROR")
            return False
        finally:
            # 清理
            self.log("清理资源...")
            self.run_adb("shell pkill frida-server")

def main():
    fixer = FridaStartupFix()
    
    try:
        success = fixer.run_complete_fix()
        
        if success:
            print("\n🎯 Frida启动问题修复成功！")
            print("💡 SSL绕过现在正常工作！")
        else:
            print("\n🔧 Frida启动问题需要进一步调试")
            print("💡 请检查错误信息并尝试建议的解决方案")
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断")
    except Exception as e:
        print(f"\n❌ 异常: {e}")

if __name__ == "__main__":
    main()
