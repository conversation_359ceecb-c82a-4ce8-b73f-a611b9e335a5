[12:45:01.755] HTTP(S) proxy listening at *:8888.
[12:45:07.618][127.0.0.1:53071] client connect
[12:45:07.624][127.0.0.1:53072] client connect
[12:45:07.640][127.0.0.1:53072] server connect connectivitycheck.gstatic.com:443 (198.18.0.78:443)
[12:45:07.641][127.0.0.1:53071] server connect connectivitycheck.gstatic.com:80 (198.18.0.78:80)
127.0.0.1:53071: GET http://connectivitycheck.gstatic.com/generate_204
              << 204 No Content 0b
127.0.0.1:53072: GET https://connectivitycheck.gstatic.com/generate_204
              << 204 No Content 0b
[12:45:08.325][127.0.0.1:53079] client connect
[12:45:08.330][127.0.0.1:53079] server connect android.apis.google.com:443 (198.18.0.79:443)
127.0.0.1:53079: POST https://android.apis.google.com/c2dm/register3
              << 301 Moved Permanently 45b
[12:45:24.776][127.0.0.1:53103] client connect
[12:45:24.793][127.0.0.1:53103] server connect appapi.yjzx.com:443 (198.18.0.80:443)
[12:45:35.286][127.0.0.1:53117] client connect
[12:45:35.290][127.0.0.1:53117] server connect api.sobot.com:443 (198.18.0.81:443)
127.0.0.1:53117: POST https://api.sobot.com/chat-sdk/sdk/user/v2/config.actio…
              << 200  137b
[12:45:35.699][127.0.0.1:53121] client connect
[12:45:35.708][127.0.0.1:53121] server connect h.trace.qq.com:443 (198.18.0.76:443)
[12:45:35.746][127.0.0.1:53124] client connect
[12:45:35.752][127.0.0.1:53124] server connect android.bugly.qq.com:443 (198.18.0.61:443)
127.0.0.1:53121: POST https://h.trace.qq.com/kv
              << 200 OK 2b
127.0.0.1:53124: POST https://android.bugly.qq.com/rqd/async?aid=ed0fa699-39c…
              << 200 OK 0b
127.0.0.1:53121: POST https://h.trace.qq.com/kv
              << 200 OK 2b
[12:45:37.901][127.0.0.1:53127] client connect
[12:45:37.906][127.0.0.1:53127] server connect ali-stats.jpush.cn:443 (198.18.0.77:443)
127.0.0.1:53127: POST https://ali-stats.jpush.cn/v3/report
              << 200 OK 29b
[12:46:00.163][127.0.0.1:53153] client connect
[12:46:00.178][127.0.0.1:53153] server connect digitalassetlinks.googleapis.com:443 (198.18.0.82:443)
[12:46:07.714][127.0.0.1:53121] server disconnect h.trace.qq.com:443 (198.18.0.76:443)
[12:46:17.022][127.0.0.1:53127] client disconnect
[12:46:17.024][127.0.0.1:53127] server disconnect ali-stats.jpush.cn:443 (198.18.0.77:443)
[12:46:17.025][127.0.0.1:53124] client disconnect
[12:46:17.027][127.0.0.1:53124] server disconnect android.bugly.qq.com:443 (198.18.0.61:443)
[12:46:17.027][127.0.0.1:53121] client disconnect
[12:46:17.030][127.0.0.1:53117] client disconnect
[12:46:17.031][127.0.0.1:53117] server disconnect api.sobot.com:443 (198.18.0.81:443)
[12:47:35.845][127.0.0.1:53103] Server TLS handshake failed. connection closed
[12:47:35.846][127.0.0.1:53103] Unable to establish TLS connection with server (connection closed). Trying to establish TLS with client anyway. If you plan to redirect requests away from this server, consider setting `connection_strategy` to `lazy` to suppress early connections.
[12:47:35.854][127.0.0.1:53103] server disconnect appapi.yjzx.com:443 (198.18.0.80:443)
[12:47:35.854][127.0.0.1:53103] client disconnect
[12:49:07.825][127.0.0.1:53071] server disconnect connectivitycheck.gstatic.com:80 (198.18.0.78:80)
[12:49:08.044][127.0.0.1:53072] server disconnect connectivitycheck.gstatic.com:443 (198.18.0.78:443)
[12:49:08.824][127.0.0.1:53079] server disconnect android.apis.google.com:443 (198.18.0.79:443)
[12:50:01.596][127.0.0.1:53153] 198.18.0.82:443: HTTP/2 protocol error: Invalid input ConnectionInputs.RECV_PING in state ConnectionState.CLOSED
[12:50:01.597][127.0.0.1:53153] server disconnect digitalassetlinks.googleapis.com:443 (198.18.0.82:443)
[12:50:07.784][127.0.0.1:53071] client disconnect
[12:50:08.045][127.0.0.1:53072] client disconnect
[12:50:08.846][127.0.0.1:53079] client disconnect
