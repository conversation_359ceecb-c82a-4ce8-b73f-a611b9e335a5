#!/usr/bin/env python3
"""
超简单的 URL 捕获测试脚本
"""

import frida
import sys
import time
import json
import subprocess
from datetime import datetime

def get_app_pid(package_name):
    """获取应用的 PID"""
    try:
        result = subprocess.run(
            ['./android-sdk/platform-tools/adb', '-s', 'emulator-5554', 'shell', 'pidof', package_name],
            capture_output=True, text=True
        )
        pid_str = result.stdout.strip()
        if pid_str:
            return int(pid_str)
    except:
        pass
    return None

def start_app(package_name):
    """启动应用"""
    print(f"[*] 启动应用: {package_name}")
    subprocess.run([
        './android-sdk/platform-tools/adb', '-s', 'emulator-5554', 
        'shell', 'monkey', '-p', package_name, '-c', 'android.intent.category.LAUNCHER', '1'
    ])
    time.sleep(3)

def main():
    package_name = "com.iloda.beacon"
    
    # 启动应用
    start_app(package_name)
    
    # 获取 PID
    pid = get_app_pid(package_name)
    if not pid:
        print(f"[!] 无法获取 {package_name} 的 PID")
        return
    
    print(f"[*] 找到 PID: {pid}")
    
    # 简单的 Hook 脚本
    hook_script = """
    console.log("[*] Hook 脚本开始运行");
    
    var urls = [];
    
    // Hook URL 类
    Java.perform(function() {
        try {
            var URL = Java.use('java.net.URL');
            URL.$init.overload('java.lang.String').implementation = function(url) {
                console.log("[URL] " + url);
                urls.push({
                    type: "URL",
                    url: url,
                    time: new Date().toISOString()
                });
                return this.$init(url);
            };
        } catch(e) {
            console.log("[!] Hook URL 失败: " + e);
        }
        
        try {
            var HttpURLConnection = Java.use('java.net.HttpURLConnection');
            HttpURLConnection.connect.implementation = function() {
                var url = this.getURL().toString();
                console.log("[HttpURLConnection] " + url);
                urls.push({
                    type: "HttpURLConnection",
                    url: url,
                    time: new Date().toISOString()
                });
                return this.connect();
            };
        } catch(e) {
            console.log("[!] Hook HttpURLConnection 失败: " + e);
        }
        
        // Hook OkHttp3
        try {
            var OkHttpClient = Java.use('okhttp3.OkHttpClient');
            var Request = Java.use('okhttp3.Request');
            
            OkHttpClient.newCall.implementation = function(request) {
                var url = request.url().toString();
                console.log("[OkHttp3] " + url);
                urls.push({
                    type: "OkHttp3",
                    url: url,
                    time: new Date().toISOString()
                });
                return this.newCall(request);
            };
        } catch(e) {
            console.log("[!] Hook OkHttp3 失败: " + e);
        }
        
        console.log("[*] Hook 设置完成");
    });
    
    // 定期输出结果
    setInterval(function() {
        if (urls.length > 0) {
            console.log("\\n=== 捕获的 URLs ===");
            console.log(JSON.stringify(urls, null, 2));
        }
    }, 5000);
    """
    
    try:
        # 连接设备
        device = frida.get_usb_device()
        print(f"[*] 连接到设备: {device.name}")
        
        # 附加到进程
        session = device.attach(pid)
        print(f"[*] 附加到进程 {pid}")
        
        # 创建脚本
        script = session.create_script(hook_script)
        
        # 处理消息
        def on_message(message, data):
            if message['type'] == 'send':
                print(f"[SCRIPT] {message['payload']}")
            elif message['type'] == 'error':
                print(f"[ERROR] {message}")
        
        script.on('message', on_message)
        
        # 加载脚本
        script.load()
        print("[*] 脚本加载成功")
        
        # 触发一些 Activity
        print("\n[*] 触发 Activity...")
        activities = [
            "com.iloda.beacon.activity.MainActivity",
            "com.iloda.beacon.activity.GuideActivity"
        ]
        
        for activity in activities:
            print(f"[*] 启动 {activity}")
            subprocess.run([
                './android-sdk/platform-tools/adb', '-s', 'emulator-5554',
                'shell', 'am', 'start', '-n', f'{package_name}/{activity}'
            ])
            time.sleep(2)
        
        # 等待捕获
        print("\n[*] 等待 20 秒捕获 URLs...")
        time.sleep(20)
        
        print("\n[*] 测试完成")
        
    except Exception as e:
        print(f"[!] 错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        try:
            session.detach()
        except:
            pass

if __name__ == "__main__":
    main()




