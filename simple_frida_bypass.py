#!/usr/bin/env python3
"""
简单的Frida SSL绕过脚本
修复命令参数问题
"""

import subprocess
import sys
import time
import json
from pathlib import Path

def run_adb(cmd, timeout=30):
    """执行adb命令"""
    full_cmd = f"source android_env.sh && adb {cmd}"
    try:
        result = subprocess.run(full_cmd, shell=True, 
                              capture_output=True, text=True, 
                              timeout=timeout, check=True)
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        return f"ERROR: {e.stderr}"
    except subprocess.TimeoutExpired:
        return "ERROR: Timeout"

def start_frida_server():
    """启动frida-server"""
    print("🚀 启动frida-server...")
    
    # 先杀死可能存在的进程
    run_adb("shell pkill frida-server")
    time.sleep(2)
    
    # 启动新进程
    subprocess.Popen("source android_env.sh && adb shell '/data/local/tmp/frida-server &'", shell=True)
    
    # 等待启动
    print("⏳ 等待frida-server启动...")
    time.sleep(8)
    
    # 验证启动
    result = run_adb("shell ps | grep frida-server")
    if "frida-server" in result:
        print("✅ frida-server启动成功")
        return True
    else:
        print("❌ frida-server启动失败")
        return False

def run_ssl_bypass():
    """运行SSL绕过"""
    print("🔧 运行SSL绕过...")
    
    package = "com.yjzx.yjzx2017"
    
    # 启动应用
    print("🚀 启动目标应用...")
    run_adb(f"shell am start -n {package}/.controller.activity.MainActivity")
    time.sleep(5)
    
    # 运行Frida脚本 - 使用正确的参数
    print("🔧 注入SSL绕过脚本...")
    
    try:
        # 使用attach模式而不是spawn模式
        cmd = f"frida -U {package} -l ssl_bypass.js"
        print(f"📋 执行命令: {cmd}")
        
        # 启动Frida进程
        process = subprocess.Popen(
            cmd, shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True
        )
        
        print("✅ SSL绕过脚本已启动")
        print("⏳ 等待脚本加载...")
        time.sleep(10)
        
        # 检查进程状态
        if process.poll() is None:
            print("✅ SSL绕过脚本运行中...")
            
            # 读取一些输出
            try:
                # 非阻塞读取
                import select
                import os
                
                if select.select([process.stdout], [], [], 0) == ([process.stdout], [], []):
                    output = process.stdout.read(1000)
                    if output:
                        print("📋 Frida输出:")
                        print(output)
            except:
                pass
            
            return process
        else:
            stdout, stderr = process.communicate()
            print(f"❌ 脚本启动失败")
            print(f"输出: {stdout}")
            return None
            
    except Exception as e:
        print(f"❌ SSL绕过异常: {e}")
        return None

def test_https_capture():
    """测试HTTPS抓包"""
    print("🔍 测试HTTPS抓包...")
    
    package = "com.yjzx.yjzx2017"
    
    # 清空捕获文件
    realtime_file = Path("mitm-logs/realtime_capture.json")
    if realtime_file.exists():
        with open(realtime_file, 'w') as f:
            json.dump([], f)
    
    time.sleep(2)
    
    # 触发网络活动
    print("🌐 触发网络请求...")
    
    test_actions = [
        ("重新启动应用", f"shell am start -n {package}/.controller.activity.MainActivity"),
        ("访问HTTPS网站", "shell am start -a android.intent.action.VIEW -d https://httpbin.org/get"),
        ("用户交互", "shell input tap 540 960"),
        ("访问百度", "shell am start -a android.intent.action.VIEW -d https://www.baidu.com"),
    ]
    
    for action_name, cmd in test_actions:
        print(f"   🔄 {action_name}...")
        run_adb(cmd)
        time.sleep(5)
    
    # 等待网络请求
    print("⏳ 等待网络请求处理...")
    time.sleep(15)
    
    # 检查结果
    try:
        with open(realtime_file, 'r') as f:
            data = json.load(f)
            
            if isinstance(data, list) and len(data) > 0:
                https_requests = [req for req in data if req.get('scheme') == 'https']
                http_requests = [req for req in data if req.get('scheme') == 'http']
                
                print(f"📊 捕获统计:")
                print(f"   总请求: {len(data)}")
                print(f"   HTTPS请求: {len(https_requests)}")
                print(f"   HTTP请求: {len(http_requests)}")
                
                if https_requests:
                    print("🎉 SSL绕过成功！捕获到HTTPS请求！")
                    print("📋 HTTPS请求示例:")
                    for i, req in enumerate(https_requests[:3], 1):
                        url = req.get('url', 'N/A')
                        method = req.get('method', 'N/A')
                        status = req.get('status_code', 'N/A')
                        print(f"   {i}. {method} {url}")
                        print(f"      状态: {status}")
                    return True
                elif http_requests:
                    print("⚠️  捕获到HTTP请求，SSL绕过可能部分生效")
                    return True
                else:
                    print("❌ 没有识别到HTTP/HTTPS请求")
                    return False
            else:
                print("❌ 没有捕获到网络请求")
                return False
                
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    print("🚀 简化版Frida SSL绕过")
    print("🔧 修复命令参数问题")
    print("=" * 50)
    
    try:
        # 检查设备
        result = run_adb("devices")
        if "device" not in result or "ERROR" in result:
            print("❌ Android设备未连接")
            return False
        print("✅ Android设备已连接")
        
        # 启动frida-server
        if not start_frida_server():
            return False
        
        # 运行SSL绕过
        frida_process = run_ssl_bypass()
        if not frida_process:
            return False
        
        # 测试HTTPS抓包
        print("\n" + "="*40)
        print("🔍 测试HTTPS抓包功能")
        print("="*40)
        
        success = test_https_capture()
        
        if success:
            print("\n🎉 Frida SSL绕过成功！")
            print("✅ 网络流量捕获正常工作！")
            
            print("\n📋 Frida进程正在后台运行...")
            print("💡 你可以继续进行网络分析")
            print("⚠️  按Ctrl+C停止")
            
            try:
                # 保持运行
                while True:
                    time.sleep(10)
                    if frida_process.poll() is not None:
                        print("⚠️  Frida进程已退出")
                        break
            except KeyboardInterrupt:
                print("\n⚠️  用户中断，停止Frida进程...")
                frida_process.terminate()
                
            return True
        else:
            print("\n⚠️  测试未完全成功，但基础功能可能已工作")
            
            if frida_process:
                print("⚠️  Frida进程仍在运行")
                try:
                    time.sleep(30)  # 运行30秒
                    frida_process.terminate()
                except KeyboardInterrupt:
                    frida_process.terminate()
            
            return False
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断")
        return False
    except Exception as e:
        print(f"\n❌ 异常: {e}")
        return False
    finally:
        # 清理
        print("🧹 清理frida-server进程...")
        subprocess.run("source android_env.sh && adb shell pkill frida-server", shell=True)

if __name__ == "__main__":
    main()
