#!/usr/bin/env python3
"""
网络诊断
检查为什么Android应用的网络流量没有通过mitmproxy
"""

import subprocess
import sys
import time
import json
from pathlib import Path

def log(message):
    timestamp = time.strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def run_adb(cmd):
    full_cmd = f"source android_env.sh && adb {cmd}"
    result = subprocess.run(full_cmd, shell=True, capture_output=True, text=True)
    return result.returncode, result.stdout.strip(), result.stderr.strip()

def test_proxy_connectivity():
    """测试代理连接性"""
    log("🔍 测试代理连接性")
    
    # 1. 测试主机到mitmproxy的连接
    log("测试主机到mitmproxy连接...")
    result = subprocess.run("curl -x http://127.0.0.1:8080 -I http://httpbin.org/get", 
                          shell=True, capture_output=True, text=True)
    if result.returncode == 0:
        log("✅ 主机可以通过mitmproxy访问网络")
    else:
        log("❌ 主机无法通过mitmproxy访问网络")
        log(f"错误: {result.stderr}")
    
    # 2. 测试Android到主机的连接
    log("测试Android到主机连接...")
    returncode, stdout, stderr = run_adb("shell ping -c 3 ********")
    if returncode == 0:
        log("✅ Android可以ping通主机")
    else:
        log("❌ Android无法ping通主机")
        log(f"错误: {stderr}")
    
    # 3. 测试Android到代理端口的连接
    log("测试Android到代理端口连接...")
    returncode, stdout, stderr = run_adb("shell nc -z ******** 8080")
    if returncode == 0:
        log("✅ Android可以连接到代理端口")
    else:
        log("❌ Android无法连接到代理端口")
        log(f"错误: {stderr}")

def test_android_network_behavior():
    """测试Android网络行为"""
    log("🔍 测试Android网络行为")
    
    # 1. 检查当前网络配置
    log("检查Android网络配置...")
    returncode, stdout, stderr = run_adb("shell settings get global http_proxy")
    log(f"代理设置: {stdout}")
    
    # 2. 检查网络接口
    log("检查网络接口...")
    returncode, stdout, stderr = run_adb("shell ip route")
    log(f"路由信息: {stdout[:200]}...")
    
    # 3. 使用Android的curl测试
    log("使用Android的curl测试...")
    returncode, stdout, stderr = run_adb("shell curl -I http://httpbin.org/get")
    if returncode == 0:
        log("✅ Android curl可以访问HTTP")
        log(f"响应: {stdout[:200]}...")
    else:
        log("❌ Android curl无法访问HTTP")
        log(f"错误: {stderr}")
    
    # 4. 测试HTTPS
    log("测试Android HTTPS...")
    returncode, stdout, stderr = run_adb("shell curl -I https://httpbin.org/get")
    if returncode == 0:
        log("✅ Android curl可以访问HTTPS")
        log(f"响应: {stdout[:200]}...")
    else:
        log("❌ Android curl无法访问HTTPS")
        log(f"错误: {stderr}")

def test_app_network_behavior():
    """测试应用网络行为"""
    log("🔍 测试应用网络行为")
    
    package = "com.yjzx.yjzx2017"
    
    # 1. 启动应用
    log("启动金融应用...")
    run_adb(f"shell am force-stop {package}")
    time.sleep(2)
    returncode, stdout, stderr = run_adb(f"shell am start -n {package}/.controller.activity.splash.SplashActivity")
    
    if returncode == 0:
        log("✅ 应用启动成功")
        time.sleep(8)
    else:
        log("❌ 应用启动失败")
        return
    
    # 2. 监控应用网络连接
    log("监控应用网络连接...")
    returncode, stdout, stderr = run_adb(f"shell netstat -an | grep $(pidof {package})")
    if stdout:
        log("✅ 发现应用网络连接:")
        log(f"连接信息: {stdout}")
    else:
        log("❌ 未发现应用网络连接")
    
    # 3. 检查应用是否使用代理
    log("检查应用网络活动...")
    
    # 触发一些网络操作
    operations = [
        ("下拉刷新", "shell input swipe 540 400 540 800"),
        ("点击中心", "shell input tap 540 960"),
        ("点击刷新", "shell input tap 540 200")
    ]
    
    for op_name, op_cmd in operations:
        log(f"执行: {op_name}")
        run_adb(op_cmd)
        time.sleep(3)
    
    # 再次检查网络连接
    time.sleep(5)
    returncode, stdout, stderr = run_adb(f"shell netstat -an | grep $(pidof {package})")
    if stdout:
        log("✅ 应用网络活动:")
        log(f"连接信息: {stdout}")
    else:
        log("❌ 仍未发现应用网络活动")

def test_alternative_proxy_methods():
    """测试替代代理方法"""
    log("🔍 测试替代代理方法")
    
    # 1. 尝试使用iptables重定向
    log("尝试iptables重定向...")
    iptables_commands = [
        "shell su -c 'iptables -t nat -A OUTPUT -p tcp --dport 80 -j DNAT --to-destination ********:8080'",
        "shell su -c 'iptables -t nat -A OUTPUT -p tcp --dport 443 -j DNAT --to-destination ********:8080'"
    ]
    
    for cmd in iptables_commands:
        returncode, stdout, stderr = run_adb(cmd)
        if returncode == 0:
            log(f"✅ iptables规则添加成功")
        else:
            log(f"❌ iptables规则添加失败: {stderr}")
    
    # 2. 测试重定向后的网络
    log("测试重定向后的网络...")
    time.sleep(3)
    
    returncode, stdout, stderr = run_adb("shell curl -I http://httpbin.org/get")
    if returncode == 0:
        log("✅ 重定向后HTTP访问正常")
    else:
        log("❌ 重定向后HTTP访问失败")

def check_mitmproxy_logs():
    """检查mitmproxy日志"""
    log("🔍 检查mitmproxy捕获情况")
    
    capture_file = Path("mitm-logs/realtime_capture.json")
    
    if capture_file.exists():
        try:
            with open(capture_file, 'r') as f:
                data = json.load(f)
            
            log(f"捕获文件存在，包含 {len(data)} 个请求")
            
            if len(data) > 0:
                log("最近的请求:")
                for i, req in enumerate(data[-3:], 1):
                    url = req.get('url', 'N/A')
                    method = req.get('method', 'N/A')
                    log(f"  {i}. {method} {url}")
            else:
                log("❌ 捕获文件为空")
        except Exception as e:
            log(f"❌ 读取捕获文件失败: {e}")
    else:
        log("❌ 捕获文件不存在")

def main():
    """主函数"""
    log("🚀 网络诊断开始")
    log("=" * 60)
    
    # 1. 测试代理连接性
    test_proxy_connectivity()
    log("")
    
    # 2. 测试Android网络行为
    test_android_network_behavior()
    log("")
    
    # 3. 测试应用网络行为
    test_app_network_behavior()
    log("")
    
    # 4. 检查mitmproxy捕获情况
    check_mitmproxy_logs()
    log("")
    
    # 5. 尝试替代方法
    test_alternative_proxy_methods()
    log("")
    
    # 6. 最终检查
    log("最终检查mitmproxy捕获...")
    time.sleep(10)
    check_mitmproxy_logs()
    
    log("=" * 60)
    log("🎯 网络诊断完成")

if __name__ == "__main__":
    main()
