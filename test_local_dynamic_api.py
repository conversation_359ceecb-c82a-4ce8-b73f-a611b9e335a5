#!/usr/bin/env python3
"""
本地动态分析API测试
测试本地动态分析API端点的功能
"""
import asyncio
import aiohttp
import logging
import sys
from pathlib import Path
import json
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class LocalDynamicAPITester:
    """本地动态分析API测试类"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.api_prefix = "/api/v1"
        self.test_results = {}
        self.test_apk = project_root / "apk" / "com.android.vending_289.com.apk"
        
        # 确保测试APK存在
        if not self.test_apk.exists():
            logger.error(f"Test APK not found: {self.test_apk}")
            raise FileNotFoundError(f"Test APK not found: {self.test_apk}")
    
    async def run_all_tests(self):
        """运行所有API测试"""
        logger.info("🚀 Starting Local Dynamic Analysis API Tests")
        print("=" * 80)
        
        async with aiohttp.ClientSession() as session:
            self.session = session
            
            tests = [
                ("Health Check", self.test_health_check),
                ("Emulator Status", self.test_emulator_status),
                ("List AVDs", self.test_list_avds),
                ("API Analysis (Mock)", self.test_api_analysis_mock),
            ]
            
            for test_name, test_func in tests:
                try:
                    logger.info(f"Running: {test_name}")
                    result = await test_func()
                    self.test_results[test_name] = {
                        'status': 'PASS' if result else 'FAIL',
                        'details': result if isinstance(result, dict) else {}
                    }
                    status = "✅ PASS" if result else "❌ FAIL"
                    logger.info(f"{test_name}: {status}")
                except Exception as e:
                    logger.error(f"{test_name}: ❌ ERROR - {e}")
                    self.test_results[test_name] = {
                        'status': 'ERROR',
                        'error': str(e)
                    }
                
                print("-" * 40)
        
        # 生成测试报告
        self.generate_test_report()
    
    async def test_health_check(self) -> dict:
        """测试健康检查端点"""
        try:
            url = f"{self.base_url}{self.api_prefix}/local-dynamic/health"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"Health check response: {data}")
                    return data
                else:
                    logger.error(f"Health check failed with status: {response.status}")
                    return False
                    
        except Exception as e:
            logger.error(f"Health check request failed: {e}")
            return False
    
    async def test_emulator_status(self) -> dict:
        """测试模拟器状态端点"""
        try:
            url = f"{self.base_url}{self.api_prefix}/local-dynamic/emulator/status"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"Emulator status: {data}")
                    return data
                else:
                    logger.error(f"Emulator status failed with status: {response.status}")
                    return False
                    
        except Exception as e:
            logger.error(f"Emulator status request failed: {e}")
            return False
    
    async def test_list_avds(self) -> dict:
        """测试列出AVD端点"""
        try:
            url = f"{self.base_url}{self.api_prefix}/local-dynamic/emulator/avds"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"Available AVDs: {data}")
                    return data
                else:
                    logger.error(f"List AVDs failed with status: {response.status}")
                    return False
                    
        except Exception as e:
            logger.error(f"List AVDs request failed: {e}")
            return False
    
    async def test_api_analysis_mock(self) -> dict:
        """测试API分析端点（模拟模式）"""
        try:
            url = f"{self.base_url}{self.api_prefix}/local-dynamic/analyze"
            
            # 准备文件上传
            data = aiohttp.FormData()
            data.add_field('file', 
                          open(self.test_apk, 'rb'),
                          filename='test.apk',
                          content_type='application/vnd.android.package-archive')
            data.add_field('timeout', '60')  # 短超时用于测试
            
            logger.info("Uploading APK for analysis...")
            
            async with self.session.post(url, data=data) as response:
                response_text = await response.text()
                
                if response.status == 200:
                    try:
                        result = json.loads(response_text)
                        logger.info(f"Analysis completed: {result.get('status')}")
                        
                        # 检查结果结构
                        expected_fields = ['task_id', 'status', 'analysis_type', 'result']
                        missing_fields = [field for field in expected_fields if field not in result]
                        
                        if missing_fields:
                            logger.warning(f"Missing fields in response: {missing_fields}")
                        
                        return {
                            'response_status': response.status,
                            'analysis_status': result.get('status'),
                            'task_id': result.get('task_id'),
                            'analysis_type': result.get('analysis_type'),
                            'has_result': 'result' in result,
                            'missing_fields': missing_fields
                        }
                    except json.JSONDecodeError as e:
                        logger.error(f"Failed to parse JSON response: {e}")
                        logger.error(f"Response text: {response_text}")
                        return False
                else:
                    logger.error(f"Analysis failed with status: {response.status}")
                    logger.error(f"Response: {response_text}")
                    return False
                    
        except Exception as e:
            logger.error(f"API analysis request failed: {e}")
            return False
    
    def generate_test_report(self):
        """生成测试报告"""
        logger.info("📊 Generating API Test Report")
        print("=" * 80)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result['status'] == 'PASS')
        failed_tests = sum(1 for result in self.test_results.values() if result['status'] == 'FAIL')
        error_tests = sum(1 for result in self.test_results.values() if result['status'] == 'ERROR')
        
        print(f"📈 API Test Summary:")
        print(f"   Total Tests: {total_tests}")
        print(f"   ✅ Passed: {passed_tests}")
        print(f"   ❌ Failed: {failed_tests}")
        print(f"   🚨 Errors: {error_tests}")
        print(f"   📊 Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        print()
        
        print("📋 Detailed Results:")
        for test_name, result in self.test_results.items():
            status_icon = {
                'PASS': '✅',
                'FAIL': '❌',
                'ERROR': '🚨'
            }.get(result['status'], '❓')
            
            print(f"   {status_icon} {test_name}: {result['status']}")
            
            if result['status'] == 'ERROR' and 'error' in result:
                print(f"      Error: {result['error']}")
            elif 'details' in result and result['details']:
                if isinstance(result['details'], dict):
                    for key, value in result['details'].items():
                        if isinstance(value, (list, dict)):
                            print(f"      {key}: {type(value).__name__} with {len(value)} items")
                        else:
                            print(f"      {key}: {value}")
        
        print()
        
        # 保存测试报告到文件
        report_file = project_root / "local_dynamic_api_test_report.json"
        try:
            with open(report_file, 'w') as f:
                json.dump({
                    'timestamp': time.time(),
                    'base_url': self.base_url,
                    'summary': {
                        'total': total_tests,
                        'passed': passed_tests,
                        'failed': failed_tests,
                        'errors': error_tests,
                        'success_rate': (passed_tests/total_tests)*100
                    },
                    'results': self.test_results
                }, f, indent=2)
            
            logger.info(f"📄 API test report saved to: {report_file}")
        except Exception as e:
            logger.error(f"Failed to save test report: {e}")
        
        # 总结
        if passed_tests == total_tests:
            print("🎉 All API tests passed! Local dynamic analysis API is ready.")
        elif passed_tests > 0:
            print("⚠️  Some API tests passed. System partially functional.")
        else:
            print("🚨 All API tests failed. System needs attention.")
        
        print("=" * 80)


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test Local Dynamic Analysis API")
    parser.add_argument("--url", default="http://localhost:8000", 
                       help="Base URL of the API server")
    args = parser.parse_args()
    
    try:
        tester = LocalDynamicAPITester(base_url=args.url)
        await tester.run_all_tests()
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
    except Exception as e:
        logger.error(f"Test execution failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
