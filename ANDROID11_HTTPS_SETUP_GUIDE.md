# Android 11 HTTPS流量捕获完整设置指南

## 📋 概述

本指南提供了在Android 11模拟器上安装系统级证书并进行HTTPS流量捕获的完整解决方案。

## 🎯 解决的问题

- ✅ Android 11 `/system`分区只读限制
- ✅ 系统证书安装权限问题
- ✅ mitmproxy证书自动化安装
- ✅ HTTPS流量捕获和SSL绕过
- ✅ 动态分析环境自动化配置

## 📦 环境要求

### 必需工具

```bash
# macOS安装
brew install android-platform-tools  # adb
brew install mitmproxy               # HTTPS代理
brew install openssl                 # 证书处理

# 可选工具
brew install frida-tools            # SSL绕过
```

### Android模拟器要求

- Android 11 (API 30) 或更高版本
- **必须使用Google APIs镜像**（非Google Play版本）
- 启用Root权限

## 🚀 快速开始

### 1. 创建Android 11 AVD

```bash
# 下载系统镜像
sdkmanager "system-images;android-30;google_apis;x86_64"

# 创建AVD
avdmanager create avd -n Android11_Writable -k "system-images;android-30;google_apis;x86_64"

# 启动模拟器（关键：必须使用-writable-system参数）
emulator -avd Android11_Writable -writable-system -no-snapshot-load
```

### 2. 一键启动HTTPS捕获环境

```bash
# 使用我们提供的自动化脚本
./start_android11_https_capture.sh

# 或指定设备ID
./start_android11_https_capture.sh emulator-5554 8080 Android11_Writable
```

### 3. 验证安装

```bash
# 运行测试验证
python3 test_android11_https_capture.py

# 或指定设备
python3 test_android11_https_capture.py emulator-5554
```

## 🔧 手动安装步骤

如果自动化脚本遇到问题，可以手动执行以下步骤：

### 步骤1: 证书安装

```bash
# 1. 安装证书到系统
python3 android11_advanced_cert_installer.py emulator-5554

# 2. 或使用简化脚本
./android11_install_system_cert.sh
```

### 步骤2: 启动HTTPS代理

```bash
# 启动mitmproxy
mitmdump -p 8080 --set confdir=./mitm-config

# 在另一个终端启动Frida SSL绕过
frida -D emulator-5554 -l ultimate_ssl_bypass.js
```

### 步骤3: 设置代理

```bash
# 设置系统代理
adb shell settings put global http_proxy ********:8080

# 验证代理设置
adb shell settings get global http_proxy
```

## 🔍 核心解决方案详解

### Android 11系统分区只读问题

我们的解决方案采用多策略方法：

1. **Writable System AVD**: 使用`-writable-system`参数启动模拟器
2. **多种Remount策略**: 
   - 标准`adb remount`
   - 手动`mount -o rw,remount`
   - `disable-verity`后重启
3. **自动化检测**: 自动检测最适合的策略

### 证书安装策略

```python
# 我们的证书安装器支持：
1. 自动生成mitmproxy证书
2. 计算正确的证书哈希
3. 安装到系统证书存储
4. 设置正确的文件权限
5. 验证安装结果
```

### SSL绕过方案

提供多种SSL绕过方案：
- **Frida动态绕过**: 运行时修改SSL验证
- **APK重打包**: 修改网络安全配置
- **系统证书**: 安装到受信任的系统存储

## 📊 验证方法

### 1. 证书验证

```bash
# 检查系统证书
adb shell ls -la /system/etc/security/cacerts/ | grep mitm

# Android设置中验证
# 设置 → 安全 → 加密与凭据 → 信任的凭据 → 系统
```

### 2. HTTPS流量测试

```bash
# 在模拟器中访问HTTPS网站
adb shell am start -a android.intent.action.VIEW -d https://www.google.com

# 检查mitmproxy日志确认流量被捕获
```

### 3. 自动化测试

```bash
# 运行完整测试套件
python3 test_android11_https_capture.py

# 查看测试报告
cat android11_https_test_report_*.json
```

## 🛠️ 故障排除

### 常见问题及解决方案

#### 1. "Permission denied"错误

```bash
# 确保获取了root权限
adb root
adb shell id  # 应显示uid=0(root)

# 如果仍失败，重新启动模拟器
emulator -avd AVD_NAME -writable-system -no-snapshot-load
```

#### 2. 系统分区仍然只读

```bash
# 尝试disable-verity
adb disable-verity
adb reboot

# 等待重启后重新执行
adb root
adb remount
```

#### 3. HTTPS请求失败

```bash
# 检查代理设置
adb shell settings get global http_proxy

# 重新设置代理
adb shell settings put global http_proxy ********:8080

# 启动Frida SSL绕过
frida -D emulator-5554 -l ultimate_ssl_bypass.js
```

#### 4. 证书不受信任

```bash
# 重新安装证书
python3 android11_advanced_cert_installer.py

# 重启设备
adb reboot
```

## 📈 性能优化

### 模拟器性能优化

```bash
# 启动时增加内存和CPU核心
emulator -avd Android11_Writable -writable-system \
    -memory 4096 -cores 4 -gpu host
```

### mitmproxy性能优化

```bash
# 使用高性能配置启动mitmproxy
mitmdump -p 8080 \
    --set stream_large_bodies=1m \
    --set max_content_view=100k \
    --set flow_detail=0
```

## 🔐 安全注意事项

⚠️ **重要**: 本解决方案仅用于：
- 安全测试和研究
- 自己拥有的应用分析
- 教育目的

**不得用于**：
- 恶意攻击
- 未授权的应用分析
- 违法活动

## 📚 相关文件说明

| 文件名 | 用途 | 说明 |
|--------|------|------|
| `android11_advanced_cert_installer.py` | 证书安装器 | 主要的证书自动化安装工具 |
| `start_android11_https_capture.sh` | 启动脚本 | 一键启动完整的HTTPS捕获环境 |
| `test_android11_https_capture.py` | 测试脚本 | 验证安装结果和功能 |
| `android11_install_system_cert.sh` | 简化安装 | bash版本的证书安装脚本 |
| `ultimate_ssl_bypass.js` | SSL绕过 | Frida脚本，绕过SSL证书验证 |

## 🆘 技术支持

如果遇到问题：

1. 检查日志文件: `mitm-logs/`目录下的日志
2. 运行诊断测试: `python3 test_android11_https_capture.py`
3. 查看详细错误信息: `android11_https_test_report_*.json`

## 🎯 总结

通过这套完整的解决方案，我们成功解决了Android 11系统级证书安装的技术难题：

✅ **解决了只读系统分区问题**  
✅ **实现了证书自动化安装**  
✅ **提供了多种SSL绕过方案**  
✅ **建立了完整的测试验证流程**  
✅ **创建了一键启动解决方案**  

现在可以在Android 11模拟器上顺利进行HTTPS流量捕获和动态分析了！