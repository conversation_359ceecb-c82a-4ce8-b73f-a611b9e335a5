#!/usr/bin/env python3
"""
APK修改工具 - 将所有Activity设置为exported=true
这样可以直接启动任何Activity进行测试
"""

import os
import sys
import subprocess
import tempfile
import shutil
from pathlib import Path

def run_command(cmd, cwd=None):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, cwd=cwd, 
                              capture_output=True, text=True, check=True)
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {cmd}")
        print(f"错误: {e.stderr}")
        return None

def install_apktool():
    """安装apktool"""
    print("正在安装apktool...")
    
    # 下载apktool
    apktool_url = "https://raw.githubusercontent.com/iBotPeaches/Apktool/master/scripts/osx/apktool"
    apktool_jar_url = "https://bitbucket.org/iBotPeaches/apktool/downloads/apktool_2.8.1.jar"
    
    # 创建apktool目录
    apktool_dir = Path("tools/apktool")
    apktool_dir.mkdir(parents=True, exist_ok=True)
    
    # 下载文件
    run_command(f"curl -L -o {apktool_dir}/apktool {apktool_url}")
    run_command(f"curl -L -o {apktool_dir}/apktool.jar {apktool_jar_url}")
    
    # 设置权限
    run_command(f"chmod +x {apktool_dir}/apktool")
    
    return str(apktool_dir / "apktool")

def modify_manifest(manifest_path):
    """修改AndroidManifest.xml，将所有Activity设为exported=true"""
    print(f"正在修改 {manifest_path}")
    
    with open(manifest_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 简单的文本替换方法
    # 查找所有activity标签，如果没有exported属性，添加exported="true"
    import re
    
    # 匹配activity标签
    activity_pattern = r'<activity([^>]*?)>'
    
    def add_exported(match):
        attrs = match.group(1)
        if 'android:exported' not in attrs:
            # 添加exported="true"
            return f'<activity{attrs} android:exported="true">'
        else:
            # 替换现有的exported值
            attrs = re.sub(r'android:exported="[^"]*"', 'android:exported="true"', attrs)
            return f'<activity{attrs}>'
    
    modified_content = re.sub(activity_pattern, add_exported, content)
    
    with open(manifest_path, 'w', encoding='utf-8') as f:
        f.write(modified_content)
    
    print("AndroidManifest.xml 修改完成")

def modify_apk(apk_path, output_path):
    """修改APK文件"""
    apk_path = Path(apk_path)
    output_path = Path(output_path)
    
    if not apk_path.exists():
        print(f"APK文件不存在: {apk_path}")
        return False
    
    # 检查apktool是否存在
    apktool_path = "tools/apktool/apktool"
    if not Path(apktool_path).exists():
        apktool_path = install_apktool()
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_dir = Path(temp_dir)
        decode_dir = temp_dir / "decoded"
        
        print(f"正在反编译APK: {apk_path}")
        # 反编译APK
        cmd = f"{apktool_path} d -f -o {decode_dir} {apk_path}"
        if not run_command(cmd):
            return False
        
        # 修改AndroidManifest.xml
        manifest_path = decode_dir / "AndroidManifest.xml"
        if manifest_path.exists():
            modify_manifest(manifest_path)
        else:
            print("未找到AndroidManifest.xml")
            return False
        
        print("正在重新打包APK...")
        # 重新打包APK
        cmd = f"{apktool_path} b -f -o {output_path} {decode_dir}"
        if not run_command(cmd):
            return False
        
        print(f"修改后的APK已保存到: {output_path}")
        return True

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("用法: python3 modify_apk_exported.py <input.apk> <output.apk>")
        sys.exit(1)
    
    input_apk = sys.argv[1]
    output_apk = sys.argv[2]
    
    if modify_apk(input_apk, output_apk):
        print("APK修改成功！")
        print(f"现在可以使用以下命令安装修改后的APK:")
        print(f"adb install -r -g {output_apk}")
    else:
        print("APK修改失败！")
        sys.exit(1)
