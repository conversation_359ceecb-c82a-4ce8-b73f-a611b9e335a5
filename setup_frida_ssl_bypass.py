#!/usr/bin/env python3
"""
使用Frida绕过SSL证书验证
解决HTTPS抓包问题的终极方案
"""

import subprocess
import sys
import time
from pathlib import Path

class FridaSSLBypass:
    def __init__(self):
        self.package = "com.yjzx.yjzx2017"
        
    def run_adb(self, cmd, timeout=30):
        """执行adb命令"""
        full_cmd = f"source android_env.sh && adb {cmd}"
        try:
            result = subprocess.run(full_cmd, shell=True, 
                                  capture_output=True, text=True, 
                                  timeout=timeout, check=True)
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            return f"ERROR: {e.stderr}"
        except subprocess.TimeoutExpired:
            return "ERROR: Timeout"
    
    def check_frida_installation(self):
        """检查Frida安装"""
        print("🔍 检查Frida安装...")
        
        # 检查frida命令
        result = subprocess.run("frida --version", shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Frida已安装: {result.stdout.strip()}")
            return True
        else:
            print("❌ Frida未安装")
            return False
    
    def install_frida(self):
        """安装Frida"""
        print("🔧 安装Frida...")
        
        # 安装frida-tools
        print("   安装frida-tools...")
        result = subprocess.run("pip3 install frida-tools", shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ frida-tools安装成功")
        else:
            print(f"❌ frida-tools安装失败: {result.stderr}")
            return False
        
        return True
    
    def setup_frida_server(self):
        """设置Frida服务器"""
        print("🔧 设置Frida服务器...")
        
        # 检查设备架构
        arch = self.run_adb("shell getprop ro.product.cpu.abi")
        print(f"📋 设备架构: {arch}")
        
        # 下载对应的frida-server
        if "x86_64" in arch:
            server_arch = "x86_64"
        elif "x86" in arch:
            server_arch = "x86"
        elif "arm64" in arch:
            server_arch = "arm64"
        else:
            server_arch = "arm"
        
        print(f"📋 需要下载 frida-server-{server_arch}")
        
        # 这里简化处理，假设用户已经有frida-server
        print("⚠️  请手动下载并推送frida-server到设备")
        print("   1. 从 https://github.com/frida/frida/releases 下载对应架构的frida-server")
        print("   2. 重命名为 frida-server")
        print("   3. 推送到设备: adb push frida-server /data/local/tmp/")
        print("   4. 设置权限: adb shell chmod 755 /data/local/tmp/frida-server")
        
        return True
    
    def create_ssl_bypass_script(self):
        """创建SSL绕过脚本"""
        print("📝 创建SSL绕过脚本...")
        
        ssl_bypass_script = '''
// Universal SSL Kill Switch for Android
// Bypasses SSL certificate validation

Java.perform(function() {
    console.log("[+] SSL Kill Switch started");
    
    // Hook SSLContext
    var SSLContext = Java.use("javax.net.ssl.SSLContext");
    SSLContext.init.overload("[Ljavax.net.ssl.KeyManager;", "[Ljavax.net.ssl.TrustManager;", "java.security.SecureRandom").implementation = function(keyManagers, trustManagers, secureRandom) {
        console.log("[+] SSLContext.init() called");
        
        // Create a custom TrustManager that accepts all certificates
        var TrustManager = Java.use("javax.net.ssl.X509TrustManager");
        var EmptyTrustManager = Java.registerClass({
            name: "com.example.EmptyTrustManager",
            implements: [TrustManager],
            methods: {
                checkClientTrusted: function(chain, authType) {
                    console.log("[+] checkClientTrusted called");
                },
                checkServerTrusted: function(chain, authType) {
                    console.log("[+] checkServerTrusted called - bypassing");
                },
                getAcceptedIssuers: function() {
                    console.log("[+] getAcceptedIssuers called");
                    return [];
                }
            }
        });
        
        var emptyTrustManager = EmptyTrustManager.$new();
        this.init(keyManagers, [emptyTrustManager], secureRandom);
    };
    
    // Hook HostnameVerifier
    var HostnameVerifier = Java.use("javax.net.ssl.HostnameVerifier");
    var HttpsURLConnection = Java.use("javax.net.ssl.HttpsURLConnection");
    
    HttpsURLConnection.setDefaultHostnameVerifier.implementation = function(hostnameVerifier) {
        console.log("[+] HttpsURLConnection.setDefaultHostnameVerifier() called");
        
        var EmptyHostnameVerifier = Java.registerClass({
            name: "com.example.EmptyHostnameVerifier",
            implements: [HostnameVerifier],
            methods: {
                verify: function(hostname, session) {
                    console.log("[+] HostnameVerifier.verify() called for: " + hostname);
                    return true;
                }
            }
        });
        
        var emptyHostnameVerifier = EmptyHostnameVerifier.$new();
        this.setDefaultHostnameVerifier(emptyHostnameVerifier);
    };
    
    // Hook OkHttp (commonly used HTTP client)
    try {
        var OkHttpClient = Java.use("okhttp3.OkHttpClient");
        OkHttpClient.certificatePinner.implementation = function() {
            console.log("[+] OkHttpClient.certificatePinner() bypassed");
            return null;
        };
    } catch(e) {
        console.log("[-] OkHttp not found, skipping");
    }
    
    // Hook Apache HTTP Client
    try {
        var DefaultHttpClient = Java.use("org.apache.http.impl.client.DefaultHttpClient");
        DefaultHttpClient.execute.overload("org.apache.http.client.methods.HttpUriRequest").implementation = function(request) {
            console.log("[+] Apache HTTP request: " + request.getURI());
            return this.execute(request);
        };
    } catch(e) {
        console.log("[-] Apache HTTP Client not found, skipping");
    }
    
    console.log("[+] SSL Kill Switch setup complete");
});
'''
        
        script_file = "ssl_bypass.js"
        with open(script_file, 'w') as f:
            f.write(ssl_bypass_script)
        
        print(f"✅ SSL绕过脚本已创建: {script_file}")
        return script_file
    
    def run_frida_bypass(self):
        """运行Frida SSL绕过"""
        print("🚀 运行Frida SSL绕过")
        print("🔧 动态绕过SSL证书验证")
        print("=" * 50)
        
        # 检查Frida安装
        if not self.check_frida_installation():
            print("🔧 安装Frida...")
            if not self.install_frida():
                return False
        
        # 设置Frida服务器
        self.setup_frida_server()
        
        # 创建SSL绕过脚本
        script_file = self.create_ssl_bypass_script()
        
        print("\n📋 使用Frida绕过SSL的步骤:")
        print("1. 确保frida-server在设备上运行:")
        print("   adb shell /data/local/tmp/frida-server &")
        print()
        print("2. 启动应用:")
        print(f"   adb shell am start -n {self.package}/.controller.activity.MainActivity")
        print()
        print("3. 运行Frida脚本:")
        print(f"   frida -U -f {self.package} -l {script_file} --no-pause")
        print()
        print("4. 或者附加到运行中的应用:")
        print(f"   frida -U {self.package} -l {script_file}")
        
        return True
    
    def create_automated_bypass_script(self):
        """创建自动化绕过脚本"""
        print("📝 创建自动化绕过脚本...")
        
        bypass_script = f'''#!/bin/bash
# 自动化SSL绕过脚本

echo "🚀 启动Frida SSL绕过..."

# 启动frida-server
echo "📱 启动frida-server..."
adb shell "/data/local/tmp/frida-server &"
sleep 3

# 启动应用
echo "🚀 启动应用..."
adb shell am start -n {self.package}/.controller.activity.MainActivity
sleep 2

# 运行Frida脚本
echo "🔧 运行SSL绕过脚本..."
frida -U {self.package} -l ssl_bypass.js

echo "✅ SSL绕过完成"
'''
        
        script_file = "run_ssl_bypass.sh"
        with open(script_file, 'w') as f:
            f.write(bypass_script)
        
        # 设置执行权限
        subprocess.run(f"chmod +x {script_file}", shell=True)
        
        print(f"✅ 自动化脚本已创建: {script_file}")
        return script_file

def main():
    bypass = FridaSSLBypass()
    
    try:
        print("🎯 这是解决HTTPS证书问题的终极方案！")
        print("💡 Frida可以在运行时动态绕过SSL证书验证")
        print()
        
        success = bypass.run_frida_bypass()
        
        if success:
            # 创建自动化脚本
            auto_script = bypass.create_automated_bypass_script()
            
            print("\n🎉 Frida SSL绕过设置完成！")
            print("\n🔧 接下来的步骤:")
            print("1. 手动下载并设置frida-server")
            print(f"2. 运行自动化脚本: ./{auto_script}")
            print("3. 或者按照上面的手动步骤执行")
            print("\n💡 一旦Frida绕过生效，所有HTTPS请求都会被mitmproxy捕获！")
        else:
            print("\n❌ Frida设置失败")
            
    except KeyboardInterrupt:
        print("\n⚠️  设置被用户中断")
    except Exception as e:
        print(f"\n❌ 设置异常: {e}")

if __name__ == "__main__":
    main()
'''
