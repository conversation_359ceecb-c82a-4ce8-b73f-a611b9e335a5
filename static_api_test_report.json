{"timestamp": **********.110844, "test_type": "static_api", "summary": {"total": 2, "passed": 2, "failed": 0, "errors": 0, "success_rate": 100.0}, "results": {"Health Check": {"status": "PASS", "details": {"status": "unhealthy", "services": {"database": "up", "redis": "down", "static_analyzer": "up", "dynamic_analyzer": "up"}, "queue_size": 0, "active_tasks": 0}}, "Static Analysis Upload": {"status": "PASS", "details": {"response_status": 200, "analysis_status": "submitted", "task_id": "289c7e48-38b1-4c0b-9801-09693de7fd44", "analysis_type": null, "has_result": false, "package_name": null, "url_count": 0, "has_certificate": false, "missing_fields": ["result"]}}}}