#!/usr/bin/env python3
"""
证书安装后测试HTTPS抓包功能
验证证书是否正确工作
"""

import subprocess
import sys
import time
import json
from pathlib import Path
from datetime import datetime

class HTTPSCaptureTest:
    def __init__(self):
        self.package = "com.yjzx.yjzx2017"
        
    def run_adb(self, cmd, timeout=30):
        """执行adb命令"""
        full_cmd = f"source android_env.sh && adb {cmd}"
        try:
            result = subprocess.run(full_cmd, shell=True, 
                                  capture_output=True, text=True, 
                                  timeout=timeout, check=True)
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            return f"ERROR: {e.stderr}"
        except subprocess.TimeoutExpired:
            return "ERROR: Timeout"
    
    def clear_capture_file(self):
        """清空捕获文件"""
        realtime_file = Path("mitm-logs/realtime_capture.json")
        if realtime_file.exists():
            with open(realtime_file, 'w') as f:
                json.dump([], f)
        print("🗑️  清空了网络捕获文件")
    
    def get_network_requests(self):
        """获取网络请求"""
        try:
            realtime_file = Path("mitm-logs/realtime_capture.json")
            if realtime_file.exists():
                with open(realtime_file, 'r') as f:
                    data = json.load(f)
                    if isinstance(data, list):
                        return data
            return []
        except Exception as e:
            print(f"⚠️  读取网络请求失败: {e}")
            return []
    
    def test_basic_https_sites(self):
        """测试基本HTTPS网站"""
        print("🌐 测试基本HTTPS网站连接...")
        
        self.clear_capture_file()
        time.sleep(2)
        
        initial_count = len(self.get_network_requests())
        print(f"📡 初始网络请求数: {initial_count}")
        
        # 测试多个HTTPS网站
        test_sites = [
            "https://httpbin.org/get",
            "https://www.google.com",
            "https://api.github.com"
        ]
        
        for site in test_sites:
            print(f"   访问: {site}")
            self.run_adb(f"shell am start -a android.intent.action.VIEW -d '{site}'")
            time.sleep(3)
        
        print("⏳ 等待网络请求...")
        time.sleep(8)
        
        final_requests = self.get_network_requests()
        final_count = len(final_requests)
        new_requests = final_count - initial_count
        
        print(f"📡 最终网络请求数: {final_count}")
        print(f"📡 新增网络请求: {new_requests}")
        
        if new_requests > 0:
            https_requests = [req for req in final_requests if req.get('scheme') == 'https']
            http_requests = [req for req in final_requests if req.get('scheme') == 'http']
            
            print(f"📊 请求统计:")
            print(f"   HTTPS请求: {len(https_requests)}")
            print(f"   HTTP请求: {len(http_requests)}")
            
            if https_requests:
                print("🎉 HTTPS证书工作正常！")
                print("📋 捕获的HTTPS请求:")
                for i, req in enumerate(https_requests[:5], 1):
                    print(f"   {i}. {req.get('method', 'N/A')} {req.get('url', 'N/A')}")
                    print(f"      状态: {req.get('status_code', 'N/A')}")
                return True
            else:
                print("⚠️  只捕获到HTTP请求，HTTPS可能仍有问题")
                return False
        else:
            print("❌ 没有捕获到新的网络请求")
            return False
    
    def test_app_https_requests(self):
        """测试应用HTTPS请求"""
        print("🚀 测试易金在线应用HTTPS请求...")
        
        self.clear_capture_file()
        time.sleep(2)
        
        initial_count = len(self.get_network_requests())
        print(f"📡 初始网络请求数: {initial_count}")
        
        # 启动应用的多个Activity
        activities = [
            f"{self.package}.controller.activity.splash.SplashActivity",
            f"{self.package}.controller.activity.MainActivity",
            f"{self.package}.controller.login.activity.LoginActivity",
            f"{self.package}.controller.activity.setting.SettingActivity"
        ]
        
        for activity in activities:
            activity_name = activity.split('.')[-1]
            print(f"   启动: {activity_name}")
            self.run_adb(f"shell am broadcast -a android.intent.action.MAIN -n {self.package}/{activity}")
            time.sleep(3)
            
            # 模拟用户交互
            self.run_adb("shell input tap 540 960")
            time.sleep(2)
        
        print("⏳ 等待应用网络请求...")
        time.sleep(10)
        
        final_requests = self.get_network_requests()
        final_count = len(final_requests)
        new_requests = final_count - initial_count
        
        print(f"📡 最终网络请求数: {final_count}")
        print(f"📡 新增网络请求: {new_requests}")
        
        if new_requests > 0:
            # 分析应用相关请求
            app_requests = []
            https_requests = []
            
            for req in final_requests:
                host = req.get('host', '').lower()
                scheme = req.get('scheme', '')
                
                if scheme == 'https':
                    https_requests.append(req)
                
                # 检查是否是应用相关的请求
                app_domains = ['yjzx.com', 'jpush.cn', 'qq.com', 'sobot.com', 'bugly']
                if any(domain in host for domain in app_domains):
                    app_requests.append(req)
            
            print(f"📊 请求分析:")
            print(f"   总请求数: {len(final_requests)}")
            print(f"   HTTPS请求: {len(https_requests)}")
            print(f"   应用相关: {len(app_requests)}")
            
            if https_requests:
                print("🎉 成功捕获HTTPS请求！")
                print("📋 HTTPS请求详情:")
                for i, req in enumerate(https_requests[:5], 1):
                    print(f"   {i}. {req.get('method', 'N/A')} {req.get('url', 'N/A')}")
                    print(f"      状态: {req.get('status_code', 'N/A')}")
                    print(f"      主机: {req.get('host', 'N/A')}")
            
            if app_requests:
                print("🎯 应用相关请求:")
                for i, req in enumerate(app_requests[:5], 1):
                    print(f"   {i}. {req.get('method', 'N/A')} {req.get('url', 'N/A')}")
                    print(f"      协议: {req.get('scheme', 'N/A')}")
                    print(f"      状态: {req.get('status_code', 'N/A')}")
            
            return len(https_requests) > 0
        else:
            print("❌ 没有捕获到新的网络请求")
            return False
    
    def comprehensive_https_test(self):
        """综合HTTPS测试"""
        print("🚀 证书安装后HTTPS抓包功能测试")
        print("🔧 验证mitmproxy证书是否正确工作")
        print("=" * 60)
        
        # 检查代理设置
        print("🔍 检查代理设置...")
        proxy_setting = self.run_adb("shell settings get global http_proxy")
        if "*************:8080" not in proxy_setting:
            print("🔧 重新设置代理...")
            self.run_adb("shell settings put global http_proxy *************:8080")
        print("✅ 代理设置正确")
        
        test_results = []
        
        # 测试1: 基本HTTPS网站
        print("\n" + "="*50)
        print("测试1: 基本HTTPS网站连接")
        print("="*50)
        result1 = self.test_basic_https_sites()
        test_results.append(("基本HTTPS网站", result1))
        
        # 测试2: 应用HTTPS请求
        print("\n" + "="*50)
        print("测试2: 应用HTTPS请求")
        print("="*50)
        result2 = self.test_app_https_requests()
        test_results.append(("应用HTTPS请求", result2))
        
        # 生成测试报告
        print("\n" + "="*60)
        print("📊 HTTPS抓包测试报告")
        print("="*60)
        
        passed = sum(1 for _, result in test_results if result)
        total = len(test_results)
        
        print(f"📈 测试通过率: {passed}/{total} ({passed/total*100:.1f}%)")
        print()
        
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_name}: {status}")
        
        print()
        
        if passed == total:
            print("🎉 所有测试通过！HTTPS证书完美工作！")
            print("🚀 现在可以进行完整的HTTPS网络抓包分析了！")
            return True
        elif passed > 0:
            print("⚠️  部分测试通过，HTTPS抓包部分工作")
            print("🔧 可能需要进一步调试")
            return True
        else:
            print("❌ 所有测试失败，证书可能仍有问题")
            print("🔧 请检查证书是否正确安装")
            return False

def main():
    tester = HTTPSCaptureTest()
    
    print("⏳ 请确保已在Android设备上安装了mitmproxy证书")
    input("📱 证书安装完成后，按回车键开始测试...")
    
    try:
        success = tester.comprehensive_https_test()
        
        if success:
            print("\n🎯 HTTPS抓包功能测试成功！")
            print("🔧 建议运行完整的网络分析测试")
        else:
            print("\n🔧 需要进一步排查证书问题")
            
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")

if __name__ == "__main__":
    main()
