#!/usr/bin/env python3
"""
静态分析API测试
测试静态分析API端点的功能
"""
import asyncio
import aiohttp
import logging
import sys
from pathlib import Path
import time
import json

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class StaticAnalysisAPITester:
    """静态分析API测试类"""
    
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.test_results = {}
        
        # 查找测试APK
        project_root = Path(__file__).parent
        test_apks = [
            project_root / "apk" / "5577.com.androidfuture.chrismas.framesfree.apk",
            project_root / "apk" / "5577.com.iloda.beacon.apk", 
            project_root / "apk" / "test_calculator.apk",
            project_root / "apk" / "com.android.vending_289.com.apk"
        ]
        
        self.test_apk = None
        for apk in test_apks:
            if apk.exists():
                self.test_apk = apk
                break
        
        if not self.test_apk:
            logger.error("No test APK found")
            raise FileNotFoundError("No test APK found")
        
        logger.info(f"Using test APK: {self.test_apk}")
    
    async def run_tests(self):
        """运行API测试"""
        logger.info("🚀 Starting Static Analysis API Tests")
        print("=" * 80)
        
        tests = [
            ("Health Check", self.test_health_check),
            ("Static Analysis Upload", self.test_static_analysis),
        ]
        
        async with aiohttp.ClientSession() as session:
            self.session = session
            
            for test_name, test_func in tests:
                try:
                    logger.info(f"Running: {test_name}")
                    result = await test_func()
                    self.test_results[test_name] = {
                        'status': 'PASS' if result else 'FAIL',
                        'details': result if isinstance(result, dict) else {}
                    }
                    status = "✅ PASS" if result else "❌ FAIL"
                    logger.info(f"{test_name}: {status}")
                except Exception as e:
                    logger.error(f"{test_name}: ❌ ERROR - {e}")
                    self.test_results[test_name] = {
                        'status': 'ERROR',
                        'error': str(e)
                    }
                
                print("-" * 40)
        
        # 生成测试报告
        self.generate_test_report()
    
    async def test_health_check(self) -> dict:
        """测试健康检查"""
        try:
            async with self.session.get(f"{self.base_url}/api/v1/health") as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"Health check response: {data}")
                    return data
                else:
                    logger.error(f"Health check failed with status: {response.status}")
                    return False
        except Exception as e:
            logger.error(f"Health check request failed: {e}")
            return False
    
    async def test_static_analysis(self) -> dict:
        """测试静态分析API"""
        try:
            logger.info("Uploading APK for static analysis...")

            # 准备文件上传和选项
            data = aiohttp.FormData()
            data.add_field('file',
                          open(self.test_apk, 'rb'),
                          filename=self.test_apk.name,
                          content_type='application/vnd.android.package-archive')

            # 添加分析选项（只进行静态分析）
            options = {
                "static_analysis": True,
                "dynamic_analysis": False
            }
            data.add_field('options', json.dumps(options))

            # 发送分析请求
            async with self.session.post(
                f"{self.base_url}/api/v1/simple/analyze",
                data=data
            ) as response:
                
                if response.status == 200:
                    result = await response.json()
                    logger.info(f"Static analysis completed: {result.get('status', 'unknown')}")
                    
                    return {
                        'response_status': response.status,
                        'analysis_status': result.get('status'),
                        'task_id': result.get('task_id'),
                        'analysis_type': result.get('analysis_type'),
                        'has_result': 'result' in result,
                        'package_name': result.get('result', {}).get('basic_info', {}).get('package_name'),
                        'url_count': len(result.get('result', {}).get('static_urls', [])),
                        'has_certificate': 'certificate' in result.get('result', {}),
                        'missing_fields': [field for field in ['task_id', 'status', 'result'] 
                                         if field not in result]
                    }
                else:
                    error_text = await response.text()
                    logger.error(f"Analysis failed with status: {response.status}")
                    logger.error(f"Response: {error_text}")
                    return False
                    
        except Exception as e:
            logger.error(f"Static analysis request failed: {e}")
            return False
    
    def generate_test_report(self):
        """生成测试报告"""
        logger.info("📊 Generating Static API Test Report")
        print("=" * 80)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result['status'] == 'PASS')
        failed_tests = sum(1 for result in self.test_results.values() if result['status'] == 'FAIL')
        error_tests = sum(1 for result in self.test_results.values() if result['status'] == 'ERROR')
        
        print(f"📈 Static API Test Summary:")
        print(f"   Total Tests: {total_tests}")
        print(f"   ✅ Passed: {passed_tests}")
        print(f"   ❌ Failed: {failed_tests}")
        print(f"   🚨 Errors: {error_tests}")
        print(f"   📊 Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        print()
        
        print("📋 Detailed Results:")
        for test_name, result in self.test_results.items():
            status_icon = {
                'PASS': '✅',
                'FAIL': '❌',
                'ERROR': '🚨'
            }.get(result['status'], '❓')
            
            print(f"   {status_icon} {test_name}: {result['status']}")
            
            if result['status'] == 'ERROR' and 'error' in result:
                print(f"      Error: {result['error']}")
            elif 'details' in result and result['details']:
                if isinstance(result['details'], dict):
                    for key, value in result['details'].items():
                        if isinstance(value, (list, dict)):
                            print(f"      {key}: {type(value).__name__} with {len(value)} items")
                        else:
                            print(f"      {key}: {value}")
        
        print()
        
        # 保存测试报告到文件
        project_root = Path(__file__).parent
        report_file = project_root / "static_api_test_report.json"
        try:
            with open(report_file, 'w') as f:
                json.dump({
                    'timestamp': time.time(),
                    'test_type': 'static_api',
                    'summary': {
                        'total': total_tests,
                        'passed': passed_tests,
                        'failed': failed_tests,
                        'errors': error_tests,
                        'success_rate': (passed_tests/total_tests)*100
                    },
                    'results': self.test_results
                }, f, indent=2)
            
            logger.info(f"📄 Static API test report saved to: {report_file}")
        except Exception as e:
            logger.error(f"Failed to save test report: {e}")
        
        # 总结
        if passed_tests == total_tests:
            print("🎉 All static API tests passed! Static analysis API is ready.")
        elif passed_tests > 0:
            print("⚠️  Some static API tests passed. System partially functional.")
        else:
            print("🚨 All static API tests failed. System needs attention.")
        
        print("=" * 80)


async def main():
    """主函数"""
    try:
        tester = StaticAnalysisAPITester()
        await tester.run_tests()
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
    except Exception as e:
        logger.error(f"Test execution failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
