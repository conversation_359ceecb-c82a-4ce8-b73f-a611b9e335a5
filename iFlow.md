# iFlow CLI Configuration

This project is configured to work with iFlow CLI v0.2.16.

## Project Overview
This is an APK Analysis System that provides both static and dynamic analysis capabilities for Android APK files. The system can extract URLs, analyze certificates, and perform comprehensive security assessments of Android applications. It supports advanced features like SSL/TLS bypass for HTTPS traffic capture and multi-activities testing for comprehensive coverage.

## Project Information
- Working directory: /Users/<USER>/Desktop/project/apk_detect
- iFlow CLI version: 0.2.16

## Architecture
The system follows a microservices architecture with the following components:

1. **API Service** - FastAPI-based REST API for submitting analysis tasks
2. **Database** - PostgreSQL for persistent storage of analysis results
3. **Cache/Queue** - Redis for caching and message queuing
4. **Workers** - Celery workers for processing analysis tasks
5. **Android Sandbox** - Docker container with Android emulator for dynamic analysis
6. **Proxy** - MITM proxy for network traffic capture
7. **Frontend** - Web interface for viewing analysis results

## Key Features
- Static analysis of APK files (manifest parsing, certificate extraction, URL detection)
- Dynamic analysis using Android emulator
- SSL/TLS bypass for HTTPS traffic capture using Frida
- Multi-activities testing for comprehensive coverage
- Batch processing capabilities
- Real-time task status monitoring
- WebSocket support for live updates
- Local dynamic analysis with emulator
- Simplified in-memory analysis without persistent storage

## Development Setup

### Prerequisites
- Python 3.11+
- Docker and Docker Compose
- Android SDK tools (aapt, adb)
- PostgreSQL
- Redis

### Quick Setup
```bash
# Run the setup script
./setup_environment.sh

# Start all services
docker-compose up -d

# Start the API service
python3 -m src.api.main
```

### Manual Setup
1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Set up environment variables in `.env` file

3. Initialize database:
   ```bash
   alembic upgrade head
   ```

## Usage

### Starting iFlow CLI
To start iFlow CLI in this project, simply run:
```bash
iflow
```

iFlow CLI will automatically detect the project context and provide relevant assistance for APK detection and analysis tasks.

### API Development
- Main API entry point: `src/api/main.py`
- Routes are organized in `src/api/routes/`
- Configuration: `src/config/settings.py`
- Models: `src/models/`
- Services: `src/services/`
- Workers: `src/workers/`

### Testing
Run tests with pytest:
```bash
# Run all tests
pytest

# Run unit tests
pytest tests/unit

# Run integration tests
pytest tests/integration
```

### Docker Deployment
```bash
# Start all services
docker-compose up -d

# Start production services
docker-compose -f docker-compose.production.yml up -d
```

## Available Analysis Types

1. **Static Analysis** - Extracts information from APK without execution
2. **Simple Analysis** - In-memory analysis without persistent storage
3. **Local Dynamic Analysis** - Uses local Android emulator for runtime analysis
4. **Frida SSL Bypass Analysis** - Advanced dynamic analysis with SSL certificate bypass
5. **Multiple Activities Analysis** - Tests multiple app activities for comprehensive coverage

## API Endpoints

### Standard Analysis
- `POST /api/v1/analyze` - Submit APK for analysis
- `GET /api/v1/analyze/{task_id}/status` - Check task status
- `GET /api/v1/analyze/{task_id}/result` - Get analysis results
- `POST /api/v1/analyze/batch` - Submit multiple APKs for batch analysis

### Simplified Analysis
- `POST /api/v1/simple/analyze` - Simplified analysis without database
- `GET /api/v1/simple/analyze/{task_id}/status` - Get simplified analysis status
- `GET /api/v1/simple/analyze/{task_id}/result` - Get simplified analysis results

### Local Dynamic Analysis
- `POST /api/v1/local-dynamic/analyze` - Local dynamic analysis with emulator
- `POST /api/v1/local-dynamic/analyze-frida-ssl-bypass` - Local dynamic analysis with Frida SSL bypass
- `POST /api/v1/local-dynamic/analyze-multiple-activities` - Local dynamic analysis with multiple activities testing
- `GET /api/v1/local-dynamic/emulator/status` - Get emulator status
- `POST /api/v1/local-dynamic/emulator/start` - Start emulator instance
- `POST /api/v1/local-dynamic/emulator/stop/{instance_name}` - Stop specific emulator instance
- `POST /api/v1/local-dynamic/emulator/stop-all` - Stop all emulator instances

### Health and Monitoring
- `GET /api/v1/health` - System health check
- `GET /api/v1/local-dynamic/health` - Local dynamic analysis health check

## Development Practices
- Follow PEP 8 coding standards
- Use type hints for all function parameters and return values
- Write unit tests for new functionality
- Use logging instead of print statements
- Document public APIs with docstrings
- Use environment variables for configuration

## Monitoring and Debugging
- Access API documentation at `http://localhost:8000/docs`
- Monitor Celery tasks at `http://localhost:5555` (Flower)
- View captured network traffic at `http://localhost:8081` (MITM Web UI)
- Access Android emulator VNC interface at `http://localhost:6080`

## Advanced Features

### Frida SSL Bypass
The system implements advanced SSL/TLS bypass capabilities using Frida:
- Java layer SSL bypass (HttpsURLConnection, SSLContext, OkHttp)
- Native layer SSL bypass (libssl.so Hook)
- Dynamic class discovery and Hook
- Real-time network traffic monitoring
- Detailed logging and status reporting

### Multi-Activities Testing
Enhanced dynamic analysis that tests multiple application activities:
- Automatic activity discovery
- Comprehensive UI interaction
- Permission handling and testing
- Background/foreground lifecycle testing