#!/bin/bash

echo "===== Android 11 HTTPS捕获环境设置 ====="
echo ""

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Python 3
check_python() {
    print_status "检查Python 3..."
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 未安装"
        echo "请安装Python 3: brew install python3"
        exit 1
    fi
    
    python_version=$(python3 --version 2>&1 | cut -d' ' -f2)
    print_status "✅ Python $python_version 已安装"
}

# 创建虚拟环境
setup_virtual_environment() {
    print_status "设置虚拟环境..."
    
    if [ ! -d .venv ]; then
        print_status "创建虚拟环境..."
        python3 -m venv .venv
        
        if [ $? -ne 0 ]; then
            print_error "虚拟环境创建失败"
            exit 1
        fi
    else
        print_status "虚拟环境已存在"
    fi
    
    # 激活虚拟环境
    source .venv/bin/activate
    
    # 升级pip
    print_status "升级pip..."
    .venv/bin/pip install --upgrade pip
    
    print_status "✅ 虚拟环境设置完成"
}

# 安装Python依赖
install_python_dependencies() {
    print_status "安装Python依赖..."
    
    source .venv/bin/activate
    
    # 使用Android 11专用依赖文件
    local requirements_file="requirements-android11.txt"
    
    if [ ! -f "$requirements_file" ]; then
        print_warning "$requirements_file 不存在，创建基础依赖列表..."
        cat > "$requirements_file" << 'EOF'
# Android 11 HTTPS捕获专用依赖

# HTTPS代理和SSL工具
mitmproxy==10.2.4

# 动态分析工具  
frida-tools==12.3.0

# APK分析工具
lxml==4.9.4
requests==2.31.0

# 异步支持
aiofiles==23.2.1

# 数据处理
pandas==2.1.4

# 测试工具
pytest==7.4.3
pytest-asyncio==0.21.1
EOF
    fi
    
    # 安装依赖
    print_status "安装依赖包..."
    .venv/bin/pip install -r "$requirements_file"
    
    if [ $? -eq 0 ]; then
        print_status "✅ Python依赖安装完成"
    else
        print_error "Python依赖安装失败"
        exit 1
    fi
}

# 检查Android SDK工具
check_android_tools() {
    print_status "检查Android SDK工具..."
    
    if ! command -v adb &> /dev/null; then
        print_warning "ADB 未安装"
        echo "请安装Android SDK工具:"
        echo "  brew install android-platform-tools"
        echo ""
        echo "或下载Android Studio并配置环境变量"
        return 1
    fi
    
    if ! command -v emulator &> /dev/null; then
        print_warning "Android Emulator 未安装"
        echo "请安装Android Emulator (通过Android Studio)"
        return 1
    fi
    
    print_status "✅ Android SDK工具检查通过"
    return 0
}

# 检查系统工具
check_system_tools() {
    print_status "检查系统工具..."
    
    local missing_tools=()
    
    if ! command -v openssl &> /dev/null; then
        missing_tools+=("openssl")
    fi
    
    if [ ${#missing_tools[@]} -gt 0 ]; then
        print_warning "缺少系统工具: ${missing_tools[*]}"
        echo "请安装:"
        echo "  brew install openssl"
        return 1
    fi
    
    print_status "✅ 系统工具检查通过"
    return 0
}

# 创建必要目录
create_directories() {
    print_status "创建必要目录..."
    
    directories=(
        "mitm-logs"
        "mitm-config"
        "screenshots"
        "capture_results"
    )
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            print_status "创建目录: $dir"
        fi
    done
    
    print_status "✅ 目录创建完成"
}

# 验证安装
verify_installation() {
    print_status "验证安装..."
    
    source .venv/bin/activate
    
    # 检查mitmproxy
    if .venv/bin/mitmdump --version >/dev/null 2>&1; then
        mitm_version=$(.venv/bin/mitmdump --version 2>&1 | head -n1)
        print_status "✅ mitmproxy: $mitm_version"
    else
        print_error "mitmproxy 未正确安装"
        return 1
    fi
    
    # 检查frida
    if .venv/bin/frida --version >/dev/null 2>&1; then
        frida_version=$(.venv/bin/frida --version 2>&1)
        print_status "✅ frida: $frida_version"
    else
        print_warning "frida 未正确安装 (可选)"
    fi
    
    print_status "✅ 安装验证完成"
}

# 创建使用说明
create_usage_guide() {
    print_status "创建使用说明..."
    
    cat > ANDROID11_USAGE.md << 'EOF'
# Android 11 HTTPS流量捕获使用指南

## 快速开始

### 1. 激活虚拟环境
```bash
source .venv/bin/activate
```

### 2. 启动Android 11模拟器
```bash
# 创建Android 11 AVD (如果还没有)
avdmanager create avd -n Android11_Test -k "system-images;android-30;google_apis;x86_64"

# 启动模拟器（必须使用-writable-system参数）
emulator -avd Android11_Test -writable-system -no-snapshot-load &
```

### 3. 安装系统证书并启动HTTPS捕获
```bash
# 一键启动（推荐）
./start_android11_https_capture.sh

# 或手动步骤
python android11_advanced_cert_installer.py
./start_https_capture.sh
```

### 4. 验证安装
```bash
python test_android11_https_capture.py
```

## 故障排除

### 虚拟环境问题
```bash
# 重新创建虚拟环境
rm -rf .venv
python3 -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt
```

### 证书安装问题
```bash
# 重新安装证书
python android11_advanced_cert_installer.py emulator-5554
```

### 模拟器问题
```bash
# 确保使用正确的启动参数
emulator -avd YOUR_AVD_NAME -writable-system -no-snapshot-load
```

## 重要提示

1. **必须使用虚拟环境**: 所有Python工具都在.venv中
2. **模拟器启动参数**: 必须使用-writable-system参数
3. **证书安装**: 需要root权限和可写系统分区
4. **端口配置**: 默认使用8080端口，确保端口未被占用

EOF
    
    print_status "✅ 使用说明已创建: ANDROID11_USAGE.md"
}

# 主函数
main() {
    print_status "开始设置Android 11 HTTPS捕获环境"
    echo ""
    
    # 基础检查
    check_python
    
    # 设置虚拟环境
    setup_virtual_environment
    
    # 安装Python依赖
    install_python_dependencies
    
    # 检查外部工具
    android_tools_ok=true
    system_tools_ok=true
    
    if ! check_android_tools; then
        android_tools_ok=false
    fi
    
    if ! check_system_tools; then
        system_tools_ok=false
    fi
    
    # 创建目录
    create_directories
    
    # 验证安装
    if verify_installation; then
        # 创建使用说明
        create_usage_guide
        
        echo ""
        echo "===== 环境设置完成 ====="
        echo ""
        print_status "🎉 Android 11 HTTPS捕获环境设置完成!"
        echo ""
        print_status "📋 下一步:"
        echo "  1. 激活虚拟环境: source .venv/bin/activate"
        
        if $android_tools_ok; then
            echo "  2. 启动Android 11模拟器 (使用-writable-system参数)"
            echo "  3. 运行: ./start_android11_https_capture.sh"
        else
            echo "  2. 安装Android SDK工具: brew install android-platform-tools"
            echo "  3. 安装Android Studio 并配置模拟器"
            echo "  4. 然后运行: ./start_android11_https_capture.sh"
        fi
        
        echo ""
        print_status "📖 详细使用说明: cat ANDROID11_USAGE.md"
        echo ""
    else
        print_error "环境设置过程中遇到问题，请检查上面的错误信息"
        exit 1
    fi
}

# 执行主函数
main "$@"