#!/usr/bin/env python3
"""
自动测试Frida SSL绕过
完全自动化验证SSL绕过是否工作
"""

import subprocess
import sys
import time
import json
import signal
from pathlib import Path

class FridaSSLTester:
    def __init__(self):
        self.package = "com.yjzx.yjzx2017"
        self.frida_process = None
        
    def run_cmd(self, cmd, timeout=30):
        """执行命令"""
        try:
            result = subprocess.run(cmd, shell=True, 
                                  capture_output=True, text=True, 
                                  timeout=timeout)
            return result.returncode, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            return -1, "", "Timeout"
    
    def setup_frida_server(self):
        """设置并启动frida-server"""
        print("🚀 设置frida-server...")
        
        # 杀死现有进程
        self.run_cmd("source android_env.sh && adb shell pkill frida-server")
        time.sleep(2)
        
        # 启动frida-server（后台运行，忽略SELinux警告）
        subprocess.Popen("source android_env.sh && adb shell '/data/local/tmp/frida-server' 2>/dev/null &", shell=True)
        time.sleep(8)
        
        # 验证启动
        returncode, stdout, stderr = self.run_cmd("source android_env.sh && adb shell ps | grep frida-server")
        if "frida-server" in stdout:
            print("✅ frida-server启动成功")
            return True
        else:
            print("❌ frida-server启动失败")
            return False
    
    def start_target_app(self):
        """启动目标应用"""
        print("📱 启动目标应用...")
        
        # 强制停止应用
        self.run_cmd(f"source android_env.sh && adb shell am force-stop {self.package}")
        time.sleep(2)
        
        # 启动应用
        returncode, stdout, stderr = self.run_cmd(f"source android_env.sh && adb shell am start -n {self.package}/.controller.activity.splash.SplashActivity")
        
        if returncode == 0:
            print("✅ 应用启动命令执行成功")
            time.sleep(8)  # 等待应用完全启动
            return True
        else:
            print(f"❌ 应用启动失败: {stderr}")
            return False
    
    def start_frida_ssl_bypass(self):
        """启动Frida SSL绕过"""
        print("🔧 启动Frida SSL绕过...")
        
        # 构建命令
        cmd = f"export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin && frida -U {self.package} -l ssl_bypass.js"
        
        # 启动Frida进程
        try:
            self.frida_process = subprocess.Popen(
                cmd, shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            print("⏳ 等待Frida脚本加载...")
            time.sleep(15)
            
            if self.frida_process.poll() is None:
                print("✅ Frida SSL绕过脚本正在运行")
                return True
            else:
                stdout, stderr = self.frida_process.communicate()
                print("❌ Frida脚本启动失败")
                if stderr and 'SELinux' not in stderr:
                    print(f"错误: {stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Frida启动异常: {e}")
            return False
    
    def test_https_capture(self):
        """测试HTTPS捕获"""
        print("🔍 测试HTTPS捕获...")
        
        # 清空捕获文件
        realtime_file = Path("mitm-logs/realtime_capture.json")
        with open(realtime_file, 'w') as f:
            json.dump([], f)
        
        print("✅ 清空了网络捕获文件")
        time.sleep(3)
        
        # 记录初始请求数
        initial_count = 0
        
        # 触发HTTPS请求
        print("🌐 触发HTTPS请求...")
        
        test_urls = [
            "https://httpbin.org/get",
            "https://www.baidu.com",
            "https://www.google.com"
        ]
        
        for i, url in enumerate(test_urls, 1):
            print(f"   🔄 测试 {i}/{len(test_urls)}: {url}")
            returncode, stdout, stderr = self.run_cmd(f"source android_env.sh && adb shell am start -a android.intent.action.VIEW -d {url}")
            
            if returncode == 0:
                print(f"      ✅ 请求发送成功")
            else:
                print(f"      ⚠️  请求发送警告: {stderr}")
            
            time.sleep(10)  # 等待请求处理
        
        # 应用内操作
        print("   🔄 应用内操作...")
        self.run_cmd("source android_env.sh && adb shell input tap 540 960")
        time.sleep(5)
        
        # 等待网络请求处理
        print("⏳ 等待网络请求处理...")
        time.sleep(20)
        
        # 检查捕获结果
        return self.check_capture_results()
    
    def check_capture_results(self):
        """检查捕获结果"""
        print("📊 检查网络捕获结果...")
        
        realtime_file = Path("mitm-logs/realtime_capture.json")
        
        try:
            with open(realtime_file, 'r') as f:
                data = json.load(f)
            
            if not isinstance(data, list):
                print("❌ 捕获文件格式错误")
                return False
            
            total_requests = len(data)
            https_requests = [req for req in data if req.get('scheme') == 'https']
            http_requests = [req for req in data if req.get('scheme') == 'http']
            
            print(f"📊 捕获统计:")
            print(f"   总请求数: {total_requests}")
            print(f"   HTTPS请求数: {len(https_requests)}")
            print(f"   HTTP请求数: {len(http_requests)}")
            
            if len(https_requests) > 0:
                print("🎉 SSL绕过成功！成功捕获HTTPS请求！")
                print("📋 HTTPS请求示例:")
                
                for i, req in enumerate(https_requests[:5], 1):
                    url = req.get('url', 'N/A')
                    method = req.get('method', 'N/A')
                    status = req.get('status_code', 'N/A')
                    host = req.get('host', 'N/A')
                    
                    print(f"   {i}. {method} {url}")
                    print(f"      主机: {host}")
                    print(f"      状态: {status}")
                
                return True
            elif len(http_requests) > 0:
                print("⚠️  只捕获到HTTP请求，SSL绕过可能未完全生效")
                print("📋 HTTP请求示例:")
                
                for i, req in enumerate(http_requests[:3], 1):
                    url = req.get('url', 'N/A')
                    method = req.get('method', 'N/A')
                    print(f"   {i}. {method} {url}")
                
                return False
            elif total_requests > 0:
                print("⚠️  捕获到请求但协议未识别")
                print("📋 请求示例:")
                
                for i, req in enumerate(data[:3], 1):
                    url = req.get('url', 'N/A')
                    method = req.get('method', 'N/A')
                    print(f"   {i}. {method} {url}")
                
                return False
            else:
                print("❌ 没有捕获到任何网络请求")
                print("🔧 可能的原因:")
                print("   • mitmproxy未运行")
                print("   • 代理设置问题")
                print("   • 网络连接问题")
                return False
                
        except Exception as e:
            print(f"❌ 检查捕获结果失败: {e}")
            return False
    
    def cleanup(self):
        """清理资源"""
        print("🧹 清理资源...")
        
        # 终止Frida进程
        if self.frida_process and self.frida_process.poll() is None:
            print("   停止Frida进程...")
            self.frida_process.terminate()
            try:
                self.frida_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.frida_process.kill()
        
        # 终止frida-server
        print("   停止frida-server...")
        self.run_cmd("source android_env.sh && adb shell pkill frida-server")
        
        print("✅ 清理完成")
    
    def run_complete_test(self):
        """运行完整测试"""
        print("🚀 Frida SSL绕过自动化测试")
        print("🔧 完全自动化验证SSL绕过功能")
        print("=" * 70)
        
        try:
            # 步骤1: 设置frida-server
            if not self.setup_frida_server():
                return False
            
            # 步骤2: 启动目标应用
            if not self.start_target_app():
                return False
            
            # 步骤3: 启动Frida SSL绕过
            if not self.start_frida_ssl_bypass():
                return False
            
            # 步骤4: 测试HTTPS捕获
            print("\n" + "="*60)
            print("🔍 测试HTTPS捕获功能")
            print("="*60)
            
            success = self.test_https_capture()
            
            if success:
                print("\n🎉 Frida SSL绕过测试完全成功！")
                print("✅ HTTPS流量现在可以被mitmproxy完全捕获！")
                print("🔒 SSL证书验证已被完全绕过！")
                
                print("\n📋 系统现在具备:")
                print("   ✅ 完整的APK动态分析环境")
                print("   ✅ HTTPS网络流量捕获")
                print("   ✅ SSL证书验证绕过")
                print("   ✅ 实时网络监控")
                print("   ✅ API端点发现")
                
                print("\n💡 可以用于:")
                print("   • 移动应用安全测试")
                print("   • API逆向工程")
                print("   • 网络行为分析")
                print("   • 隐私泄露检测")
                
                return True
            else:
                print("\n⚠️  HTTPS捕获测试未完全成功")
                print("🔧 可能的原因:")
                print("   • SSL绕过脚本需要更多时间加载")
                print("   • 应用使用了额外的证书固定")
                print("   • 网络代理配置问题")
                
                print("\n💡 但基础Frida连接已成功建立")
                print("🔧 可以手动进行进一步调试")
                
                return False
                
        except KeyboardInterrupt:
            print("\n⚠️  用户中断测试")
            return False
        except Exception as e:
            print(f"\n❌ 测试异常: {e}")
            return False
        finally:
            self.cleanup()

def main():
    tester = FridaSSLTester()
    
    # 设置信号处理
    def signal_handler(sig, frame):
        print("\n⚠️  接收到中断信号...")
        tester.cleanup()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    
    # 运行测试
    success = tester.run_complete_test()
    
    if success:
        print("\n🎯 Frida SSL绕过自动化测试成功完成！")
        print("💡 APK动态分析系统现在完全可用！")
    else:
        print("\n🔧 测试未完全成功，但系统基础功能已建立")
        print("💡 可以进行手动调试和进一步优化")

if __name__ == "__main__":
    main()
