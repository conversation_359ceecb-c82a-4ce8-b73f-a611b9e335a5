{"timestamp": 1756981024.045374, "test_type": "real_emulator", "summary": {"total": 4, "passed": 4, "failed": 0, "errors": 0, "success_rate": 100.0}, "results": {"Test Emulator Connection": {"status": "PASS", "details": {"instance_name": "emulator_test_avd_1756980916", "device_id": "emulator-5554", "status": "running"}}, "Test APK Installation": {"status": "PASS", "details": {"package_name": "com.androidfuture.chrismas.framesfree", "installation_success": true, "emulator_device": "emulator-5554", "cleanup_success": true}}, "Test Basic UI Interaction": {"status": "PASS", "details": {"ui_actions_executed": 5, "interaction_summary": {"total_interactions": 5, "unique_elements_visited": 3, "action_counts": {"click": 3, "scroll": 2}, "last_actions": ["Click android.view.View: Apps list", "Click android.widget.FrameLayout: ", "Click android.widget.FrameLayout: ", "Scroll androidx.viewpager.widget.ViewPager", "Scroll androidx.viewpager.widget.ViewPager"]}, "test_duration": 30}}, "Test Short Dynamic Analysis": {"status": "PASS", "details": {"analysis_duration": 68.48465609550476, "analysis_success": true, "dynamic_urls_found": 0, "network_requests_found": 0, "android_version": "11", "error": null}}}}