{"timestamp": 1756976853.1247842, "test_type": "real_emulator", "summary": {"total": 4, "passed": 4, "failed": 0, "errors": 0, "success_rate": 100.0}, "results": {"Test Emulator Connection": {"status": "PASS", "details": {"instance_name": "emulator_test_avd_1756976744", "device_id": "emulator-5554", "status": "running"}}, "Test APK Installation": {"status": "PASS", "details": {"package_name": "com.androidfuture.chrismas.framesfree", "installation_success": true, "emulator_device": "emulator-5554", "cleanup_success": true}}, "Test Basic UI Interaction": {"status": "PASS", "details": {"ui_actions_executed": 5, "interaction_summary": {"total_interactions": 5, "unique_elements_visited": 5, "action_counts": {"click": 5}, "last_actions": ["Click android.widget.LinearLayout: ", "Click android.widget.Button: Cancel", "Click android.widget.Switch: ", "Click android.widget.<PERSON><PERSON>: Den<PERSON> anyway", "Click android.widget.LinearLayout: "]}, "test_duration": 30}}, "Test Short Dynamic Analysis": {"status": "PASS", "details": {"analysis_duration": 68.93650984764099, "analysis_success": false, "dynamic_urls_found": 0, "network_requests_found": 0, "android_version": "unknown", "error": "1 validation error for DynamicAnalysisResult\nruntime_info\n  value is not a valid dict (type=type_error.dict)"}}}}