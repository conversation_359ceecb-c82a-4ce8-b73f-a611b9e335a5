
# begin build properties
# autogenerated by buildinfo.sh
ro.build.id=NYC
ro.build.display.id=sdk_google_phone_x86_64-userdebug 7.0 NYC 6696031 dev-keys
ro.build.version.incremental=6696031
ro.build.version.sdk=24
ro.build.version.preview_sdk=0
ro.build.version.codename=REL
ro.build.version.all_codenames=REL
ro.build.version.release=7.0
ro.build.version.security_patch=2017-10-05
ro.build.version.base_os=
ro.build.date=Tue Jul 21 06:33:00 UTC 2020
ro.build.date.utc=1595313180
ro.build.type=userdebug
ro.build.user=android-build
ro.build.host=abfarm377
ro.build.tags=dev-keys
ro.build.flavor=sdk_google_phone_x86_64-userdebug
ro.product.model=Android SDK built for x86_64
ro.product.brand=Android
ro.product.name=sdk_google_phone_x86_64
ro.product.device=generic_x86_64
ro.product.board=
# ro.product.cpu.abi and ro.product.cpu.abi2 are obsolete,
# use ro.product.cpu.abilist instead.
ro.product.cpu.abi=x86_64
ro.product.cpu.abilist=x86_64,x86
ro.product.cpu.abilist32=x86
ro.product.cpu.abilist64=x86_64
ro.product.manufacturer=unknown
ro.product.locale=en-US
ro.wifi.channels=
ro.board.platform=
# ro.build.product is obsolete; use ro.product.device
ro.build.product=generic_x86_64
# Do not try to parse description, fingerprint, or thumbprint
ro.build.description=sdk_google_phone_x86_64-userdebug 7.0 NYC 6696031 dev-keys
ro.build.fingerprint=Android/sdk_google_phone_x86_64/generic_x86_64:7.0/NYC/6696031:userdebug/dev-keys
ro.build.characteristics=emulator
# end build properties
#
# from build/target/board/generic_x86_64/system.prop
#
#
# system.prop for generic sdk
#

rild.libpath=/system/lib/libreference-ril.so
rild.libargs=-d /dev/ttyS0

#
# ADDITIONAL_BUILD_PROPERTIES
#
ro.config.notification_sound=OnTheHunt.ogg
ro.config.alarm_alert=Alarm_Classic.ogg
persist.sys.dalvik.vm.lib.2=libart.so
dalvik.vm.isa.x86_64.variant=x86_64
dalvik.vm.isa.x86_64.features=default
dalvik.vm.isa.x86.variant=x86
dalvik.vm.isa.x86.features=default
dalvik.vm.lockprof.threshold=500
xmpp.auto-presence=true
ro.config.nocheckin=yes
net.bt.name=Android
dalvik.vm.stack-trace-file=/data/anr/traces.txt
