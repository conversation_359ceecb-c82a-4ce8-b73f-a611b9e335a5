#!/bin/bash
#
# Copyright (C) 2013 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

function makeTempJar ()
{
  local tempDir=/tmp
  if [ ! -e "${tempDir}" ]; then
    tempDir=.
  fi
  local tempfile="${tempDir}/mainDexClasses-$$.tmp.jar"
  if [ -e "${tempfile}" ]; then
    echo "Failed to create temporary file" >2
    exit 6
  fi
  echo "${tempfile}"
}

function cleanTmp ()
{
  if [ -e "${tmpOut}" ] ; then
    rm "${tmpOut}"
  fi
}


# Set up prog to be the path of this script, including following symlinks,
# and set up progdir to be the fully-qualified pathname of its directory.
prog="$0"

while [ -h "${prog}" ]; do
    newProg=`/bin/ls -ld "${prog}"`
    newProg=`expr "${newProg}" : ".* -> \(.*\)$"`
    if expr "x${newProg}" : 'x/' >/dev/null; then
        prog="${newProg}"
    else
        progdir=`dirname "${prog}"`
        prog="${progdir}/${newProg}"
    fi
done
oldwd=`pwd`
progdir=`dirname "${prog}"`
cd "${progdir}"
progdir=`pwd`
prog="${progdir}"/`basename "${prog}"`
cd "${oldwd}"

baserules="${progdir}"/mainDexClasses.rules
if [ ! -r "${baserules}" ]; then
    echo `basename "$prog"`": can't find mainDexClasses.rules" 1>&2
    exit 1
fi

extrarules="${progdir}"/mainDexClassesNoAapt.rules
if [ ! -r ${extrarules} ]; then
    echo `basename "$prog"`": can't find mainDexClassesNoAapt.rules" 1>&2
    exit 1
fi

jarfile=dx.jar
libdir="$progdir"

if [ ! -r "$libdir/$jarfile" ]; then
    # set dx.jar location for the SDK case
    libdir="$libdir/lib"
fi


if [ ! -r "$libdir/$jarfile" ]; then
    # set dx.jar location for the Android tree case
    libdir=`dirname "$progdir"`/framework
fi

if [ ! -r "$libdir/$jarfile" ]; then
    echo `basename "$prog"`": can't find $jarfile" 1>&2
    exit 1
fi

proguardExec="proguard.sh"
proguard=${PROGUARD_HOME}/bin/${proguardExec}

if [ ! -r "${proguard}" ]; then
  # set proguard location for the SDK case
  proguardBaseDir=`dirname "$progdir"`
  # "${progdir}"/../..
  proguardBaseDir=`dirname "$proguardBaseDir"`
  proguard="${proguardBaseDir}"/tools/proguard/bin/${proguardExec}
fi

if [ ! -r "${proguard}" ]; then
  # set proguard location for the Android tree case
  proguardBaseDir=`dirname "$proguardBaseDir"`
  # "${progdir}"/../../../..
  proguardBaseDir=`dirname "$proguardBaseDir"`
  proguard="${proguardBaseDir}"/external/proguard/bin/${proguardExec}
fi

if [ ! -r "${proguard}" ]; then
  proguard="${ANDROID_BUILD_TOP}"/external/proguard/bin/${proguardExec}
fi

if [ ! -r "${proguard}" ]; then
    proguard="`which proguard`"
fi

if [ -z "${proguard}" -o ! -r "${proguard}" ]; then
    proguard="`which ${proguardExec}`"
fi

if [ -z "${proguard}" -o ! -r "${proguard}" ]; then
    echo `basename "$prog"`": can't find ${proguardExec}" 1>&2
    exit 1
fi

shrinkedAndroidJar="${SHRINKED_ANDROID_JAR}"
if [ -z "${shrinkedAndroidJar}" ]; then
  shrinkedAndroidJar=shrinkedAndroid.jar
fi

if [ ! -r "${shrinkedAndroidJar}" ]; then
  shrinkedAndroidJar=${libdir}/${shrinkedAndroidJar}
fi

if [ ! -r "${shrinkedAndroidJar}" ]; then
    echo `basename "$prog"`": can't find shrinkedAndroid.jar" 1>&2
    exit 1
fi

if [ "$OSTYPE" = "cygwin" ]; then
    # For Cygwin, convert the jarfile path into native Windows style.
    jarpath=`cygpath -w "$libdir/$jarfile"`
  proguard=`cygpath -w "${proguard}"`
  shrinkedAndroidJar=`cygpath -w "${shrinkedAndroidJar}"`
else
    jarpath="$libdir/$jarfile"
fi

disableKeepAnnotated=

while true; do
if expr "x$1" : 'x--output' >/dev/null; then
    exec 1>$2
    shift 2
elif expr "x$1" : 'x--disable-annotation-resolution-workaround' >/dev/null; then
    disableKeepAnnotated=$1
    shift 1
elif expr "x$1" : "x--aapt-rules" >/dev/null; then
    extrarules=$2
    shift 2
else
    break
fi
done

if [ $# -ne 1 ]; then
  echo "Usage : $0 [--output <output file>] <application path>" 1>&2
  exit 2
fi

tmpOut=`makeTempJar`

trap cleanTmp 0

"${proguard}" -injars ${@} -dontwarn -forceprocessing  -outjars "${tmpOut}" \
  -libraryjars "${shrinkedAndroidJar}" -dontoptimize -dontobfuscate -dontpreverify \
  -include "${baserules}" -include "${extrarules}" 1>/dev/null || exit 10

java -cp "$jarpath" com.android.multidex.MainDexListBuilder ${disableKeepAnnotated} "${tmpOut}" ${@} ||  exit 11
