#!/bin/bash
#
# Copyright (C) 2017 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Set up prog to be the path of this script, including following symlinks,
# and set up progdir to be the fully-qualified pathname of its directory.
prog="$0"
while [ -h "${prog}" ]; do
    newProg=`/bin/ls -ld "${prog}"`
    newProg=`expr "${newProg}" : ".* -> \(.*\)$"`
    if expr "x${newProg}" : 'x/' >/dev/null; then
        prog="${newProg}"
    else
        progdir=`dirname "${prog}"`
        prog="${progdir}/${newProg}"
    fi
done
oldwd=`pwd`
progdir=`dirname "${prog}"`
cd "${progdir}"
progdir=`pwd`
prog="${progdir}"/`basename "${prog}"`
cd "${oldwd}"

jarfile=d8.jar
libdir="$progdir"

if [ ! -r "$libdir/$jarfile" ]; then
    # set d8.jar location for the SDK case
    libdir="$libdir/lib"
fi


if [ ! -r "$libdir/$jarfile" ]; then
    # set d8.jar location for the Android tree case
    libdir=`dirname "$progdir"`/framework
fi

if [ ! -r "$libdir/$jarfile" ]; then
    echo `basename "$prog"`": can't find $jarfile"
    exit 1
fi

# By default, give d8 a max heap size of 2 gigs. This can be overridden
# by using a "-J" option (see below).
defaultMx="-Xmx2G"

# The following will extract any initial parameters of the form
# "-J<stuff>" from the command line and pass them to the Java
# invocation (instead of to d8). This makes it possible for you to add
# a command-line parameter such as "-JXmx256M" in your scripts, for
# example. "java" (with no args) and "java -X" give a summary of
# available options.

declare -a javaOpts=()

while expr "x$1" : 'x-J' >/dev/null; do
    opt=`expr "x$1" : 'x-J\(.*\)'`
    javaOpts+=("-${opt}")
    if expr "x${opt}" : "xXmx[0-9]" >/dev/null; then
        defaultMx="no"
    fi
    shift
done

if [ "${defaultMx}" != "no" ]; then
    javaOpts+=("${defaultMx}")
fi

if [ "$OSTYPE" = "cygwin" ]; then
    # For Cygwin, convert the jarfile path into native Windows style.
    jarpath=`cygpath -w "$libdir/$jarfile"`
else
    jarpath="$libdir/$jarfile"
fi

exec java "${javaOpts[@]}" -cp "$jarpath" com.android.tools.r8.D8 "$@"
