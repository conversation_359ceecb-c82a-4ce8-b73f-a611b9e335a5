/*===---- xmmintrin.h - SSE intrinsics -------------------------------------===
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 *
 *===-----------------------------------------------------------------------===
 */

#ifndef __XMMINTRIN_H
#define __XMMINTRIN_H

#include <mmintrin.h>

typedef int __v4si __attribute__((__vector_size__(16)));
typedef float __v4sf __attribute__((__vector_size__(16)));
typedef float __m128 __attribute__((__vector_size__(16)));

/* Unsigned types */
typedef unsigned int __v4su __attribute__((__vector_size__(16)));

/* This header should only be included in a hosted environment as it depends on
 * a standard library to provide allocation routines. */
#if __STDC_HOSTED__
#include <mm_malloc.h>
#endif

/* Define the default attributes for the functions in this file. */
#define __DEFAULT_FN_ATTRS __attribute__((__always_inline__, __nodebug__, __target__("sse")))

/// \brief Adds the 32-bit float values in the low-order bits of the operands.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VADDSS / ADDSS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float] containing one of the source operands.
///    The lower 32 bits of this operand are used in the calculation.
/// \param __b
///    A 128-bit vector of [4 x float] containing one of the source operands.
///    The lower 32 bits of this operand are used in the calculation.
/// \returns A 128-bit vector of [4 x float] whose lower 32 bits contain the sum
///    of the lower 32 bits of both operands. The upper 96 bits are copied from
///    the upper 96 bits of the first source operand.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_add_ss(__m128 __a, __m128 __b)
{
  __a[0] += __b[0];
  return __a;
}

/// \brief Adds two 128-bit vectors of [4 x float], and returns the results of
///    the addition.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VADDPS / ADDPS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float] containing one of the source operands.
/// \param __b
///    A 128-bit vector of [4 x float] containing one of the source operands.
/// \returns A 128-bit vector of [4 x float] containing the sums of both
///    operands.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_add_ps(__m128 __a, __m128 __b)
{
  return (__m128)((__v4sf)__a + (__v4sf)__b);
}

/// \brief Subtracts the 32-bit float value in the low-order bits of the second
///    operand from the corresponding value in the first operand.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VSUBSS / SUBSS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float] containing the minuend. The lower 32 bits
///    of this operand are used in the calculation.
/// \param __b
///    A 128-bit vector of [4 x float] containing the subtrahend. The lower 32
///    bits of this operand are used in the calculation.
/// \returns A 128-bit vector of [4 x float] whose lower 32 bits contain the
///    difference of the lower 32 bits of both operands. The upper 96 bits are
///    copied from the upper 96 bits of the first source operand.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_sub_ss(__m128 __a, __m128 __b)
{
  __a[0] -= __b[0];
  return __a;
}

/// \brief Subtracts each of the values of the second operand from the first
///    operand, both of which are 128-bit vectors of [4 x float] and returns
///    the results of the subtraction.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VSUBPS / SUBPS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float] containing the minuend.
/// \param __b
///    A 128-bit vector of [4 x float] containing the subtrahend.
/// \returns A 128-bit vector of [4 x float] containing the differences between
///    both operands.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_sub_ps(__m128 __a, __m128 __b)
{
  return (__m128)((__v4sf)__a - (__v4sf)__b);
}

/// \brief Multiplies two 32-bit float values in the low-order bits of the
///    operands.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VMULSS / MULSS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float] containing one of the source operands.
///    The lower 32 bits of this operand are used in the calculation.
/// \param __b
///    A 128-bit vector of [4 x float] containing one of the source operands.
///    The lower 32 bits of this operand are used in the calculation.
/// \returns A 128-bit vector of [4 x float] containing the product of the lower
///    32 bits of both operands. The upper 96 bits are copied from the upper 96
///    bits of the first source operand.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_mul_ss(__m128 __a, __m128 __b)
{
  __a[0] *= __b[0];
  return __a;
}

/// \brief Multiplies two 128-bit vectors of [4 x float] and returns the
///    results of the multiplication.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VMULPS / MULPS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float] containing one of the source operands.
/// \param __b
///    A 128-bit vector of [4 x float] containing one of the source operands.
/// \returns A 128-bit vector of [4 x float] containing the products of both
///    operands.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_mul_ps(__m128 __a, __m128 __b)
{
  return (__m128)((__v4sf)__a * (__v4sf)__b);
}

/// \brief Divides the value in the low-order 32 bits of the first operand by
///    the corresponding value in the second operand.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VDIVSS / DIVSS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float] containing the dividend. The lower 32
///    bits of this operand are used in the calculation.
/// \param __b
///    A 128-bit vector of [4 x float] containing the divisor. The lower 32 bits
///    of this operand are used in the calculation.
/// \returns A 128-bit vector of [4 x float] containing the quotients of the
///    lower 32 bits of both operands. The upper 96 bits are copied from the
///    upper 96 bits of the first source operand.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_div_ss(__m128 __a, __m128 __b)
{
  __a[0] /= __b[0];
  return __a;
}

/// \brief Divides two 128-bit vectors of [4 x float].
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VDIVPS / DIVPS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float] containing the dividend.
/// \param __b
///    A 128-bit vector of [4 x float] containing the divisor.
/// \returns A 128-bit vector of [4 x float] containing the quotients of both
///    operands.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_div_ps(__m128 __a, __m128 __b)
{
  return (__m128)((__v4sf)__a / (__v4sf)__b);
}

/// \brief Calculates the square root of the value stored in the low-order bits
///    of a 128-bit vector of [4 x float].
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VSQRTSS / SQRTSS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float]. The lower 32 bits of this operand are
///    used in the calculation.
/// \returns A 128-bit vector of [4 x float] containing the square root of the
///    value in the low-order bits of the operand.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_sqrt_ss(__m128 __a)
{
  __m128 __c = __builtin_ia32_sqrtss((__v4sf)__a);
  return (__m128) { __c[0], __a[1], __a[2], __a[3] };
}

/// \brief Calculates the square roots of the values stored in a 128-bit vector
///    of [4 x float].
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VSQRTPS / SQRTPS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float].
/// \returns A 128-bit vector of [4 x float] containing the square roots of the
///    values in the operand.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_sqrt_ps(__m128 __a)
{
  return __builtin_ia32_sqrtps((__v4sf)__a);
}

/// \brief Calculates the approximate reciprocal of the value stored in the
///    low-order bits of a 128-bit vector of [4 x float].
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VRCPSS / RCPSS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float]. The lower 32 bits of this operand are
///    used in the calculation.
/// \returns A 128-bit vector of [4 x float] containing the approximate
///    reciprocal of the value in the low-order bits of the operand.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_rcp_ss(__m128 __a)
{
  __m128 __c = __builtin_ia32_rcpss((__v4sf)__a);
  return (__m128) { __c[0], __a[1], __a[2], __a[3] };
}

/// \brief Calculates the approximate reciprocals of the values stored in a
///    128-bit vector of [4 x float].
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VRCPPS / RCPPS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float].
/// \returns A 128-bit vector of [4 x float] containing the approximate
///    reciprocals of the values in the operand.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_rcp_ps(__m128 __a)
{
  return __builtin_ia32_rcpps((__v4sf)__a);
}

/// \brief Calculates the approximate reciprocal of the square root of the value
///    stored in the low-order bits of a 128-bit vector of [4 x float].
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VRSQRTSS / RSQRTSS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float]. The lower 32 bits of this operand are
///    used in the calculation.
/// \returns A 128-bit vector of [4 x float] containing the approximate
///    reciprocal of the square root of the value in the low-order bits of the
///    operand.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_rsqrt_ss(__m128 __a)
{
  __m128 __c = __builtin_ia32_rsqrtss((__v4sf)__a);
  return (__m128) { __c[0], __a[1], __a[2], __a[3] };
}

/// \brief Calculates the approximate reciprocals of the square roots of the
///    values stored in a 128-bit vector of [4 x float].
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VRSQRTPS / RSQRTPS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float].
/// \returns A 128-bit vector of [4 x float] containing the approximate
///    reciprocals of the square roots of the values in the operand.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_rsqrt_ps(__m128 __a)
{
  return __builtin_ia32_rsqrtps((__v4sf)__a);
}

/// \brief Compares two 32-bit float values in the low-order bits of both
///    operands and returns the lesser value in the low-order bits of the
///    vector of [4 x float].
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VMINSS / MINSS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float] containing one of the operands. The lower
///    32 bits of this operand are used in the comparison.
/// \param __b
///    A 128-bit vector of [4 x float] containing one of the operands. The lower
///    32 bits of this operand are used in the comparison.
/// \returns A 128-bit vector of [4 x float] whose lower 32 bits contain the
///    minimum value between both operands. The upper 96 bits are copied from
///    the upper 96 bits of the first source operand.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_min_ss(__m128 __a, __m128 __b)
{
  return __builtin_ia32_minss((__v4sf)__a, (__v4sf)__b);
}

/// \brief Compares two 128-bit vectors of [4 x float] and returns the
///    lesser of each pair of values.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VMINPS / MINPS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float] containing one of the operands.
/// \param __b
///    A 128-bit vector of [4 x float] containing one of the operands.
/// \returns A 128-bit vector of [4 x float] containing the minimum values
///    between both operands.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_min_ps(__m128 __a, __m128 __b)
{
  return __builtin_ia32_minps((__v4sf)__a, (__v4sf)__b);
}

/// \brief Compares two 32-bit float values in the low-order bits of both
///    operands and returns the greater value in the low-order bits of
///    a vector [4 x float].
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VMAXSS / MAXSS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float] containing one of the operands. The lower
///    32 bits of this operand are used in the comparison.
/// \param __b
///    A 128-bit vector of [4 x float] containing one of the operands. The lower
///    32 bits of this operand are used in the comparison.
/// \returns A 128-bit vector of [4 x float] whose lower 32 bits contain the
///    maximum value between both operands. The upper 96 bits are copied from
///    the upper 96 bits of the first source operand.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_max_ss(__m128 __a, __m128 __b)
{
  return __builtin_ia32_maxss((__v4sf)__a, (__v4sf)__b);
}

/// \brief Compares two 128-bit vectors of [4 x float] and returns the greater
///    of each pair of values.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VMAXPS / MAXPS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float] containing one of the operands.
/// \param __b
///    A 128-bit vector of [4 x float] containing one of the operands.
/// \returns A 128-bit vector of [4 x float] containing the maximum values
///    between both operands.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_max_ps(__m128 __a, __m128 __b)
{
  return __builtin_ia32_maxps((__v4sf)__a, (__v4sf)__b);
}

/// \brief Performs a bitwise AND of two 128-bit vectors of [4 x float].
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VANDPS / ANDPS instructions.
///
/// \param __a
///    A 128-bit vector containing one of the source operands.
/// \param __b
///    A 128-bit vector containing one of the source operands.
/// \returns A 128-bit vector of [4 x float] containing the bitwise AND of the
///    values between both operands.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_and_ps(__m128 __a, __m128 __b)
{
  return (__m128)((__v4su)__a & (__v4su)__b);
}

/// \brief Performs a bitwise AND of two 128-bit vectors of [4 x float], using
///    the one's complement of the values contained in the first source
///    operand.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VANDNPS / ANDNPS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float] containing the first source operand. The
///    one's complement of this value is used in the bitwise AND.
/// \param __b
///    A 128-bit vector of [4 x float] containing the second source operand.
/// \returns A 128-bit vector of [4 x float] containing the bitwise AND of the
///    one's complement of the first operand and the values in the second
///    operand.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_andnot_ps(__m128 __a, __m128 __b)
{
  return (__m128)(~(__v4su)__a & (__v4su)__b);
}

/// \brief Performs a bitwise OR of two 128-bit vectors of [4 x float].
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VORPS / ORPS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float] containing one of the source operands.
/// \param __b
///    A 128-bit vector of [4 x float] containing one of the source operands.
/// \returns A 128-bit vector of [4 x float] containing the bitwise OR of the
///    values between both operands.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_or_ps(__m128 __a, __m128 __b)
{
  return (__m128)((__v4su)__a | (__v4su)__b);
}

/// \brief Performs a bitwise exclusive OR of two 128-bit vectors of
///    [4 x float].
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VXORPS / XORPS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float] containing one of the source operands.
/// \param __b
///    A 128-bit vector of [4 x float] containing one of the source operands.
/// \returns A 128-bit vector of [4 x float] containing the bitwise exclusive OR
///    of the values between both operands.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_xor_ps(__m128 __a, __m128 __b)
{
  return (__m128)((__v4su)__a ^ (__v4su)__b);
}

/// \brief Compares two 32-bit float values in the low-order bits of both
///    operands for equality and returns the result of the comparison in the
///    low-order bits of a vector [4 x float].
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VCMPEQSS / CMPEQSS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float] containing one of the operands. The lower
///    32 bits of this operand are used in the comparison.
/// \param __b
///    A 128-bit vector of [4 x float] containing one of the operands. The lower
///    32 bits of this operand are used in the comparison.
/// \returns A 128-bit vector of [4 x float] containing the comparison results
///    in the low-order bits.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_cmpeq_ss(__m128 __a, __m128 __b)
{
  return (__m128)__builtin_ia32_cmpeqss((__v4sf)__a, (__v4sf)__b);
}

/// \brief Compares each of the corresponding 32-bit float values of the
///    128-bit vectors of [4 x float] for equality.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VCMPEQPS / CMPEQPS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float].
/// \param __b
///    A 128-bit vector of [4 x float].
/// \returns A 128-bit vector of [4 x float] containing the comparison results.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_cmpeq_ps(__m128 __a, __m128 __b)
{
  return (__m128)__builtin_ia32_cmpeqps((__v4sf)__a, (__v4sf)__b);
}

/// \brief Compares two 32-bit float values in the low-order bits of both
///    operands to determine if the value in the first operand is less than the
///    corresponding value in the second operand and returns the result of the
///    comparison in the low-order bits of a vector of [4 x float].
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VCMPLTSS / CMPLTSS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float] containing one of the operands. The lower
///    32 bits of this operand are used in the comparison.
/// \param __b
///    A 128-bit vector of [4 x float] containing one of the operands. The lower
///    32 bits of this operand are used in the comparison.
/// \returns A 128-bit vector of [4 x float] containing the comparison results
///    in the low-order bits.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_cmplt_ss(__m128 __a, __m128 __b)
{
  return (__m128)__builtin_ia32_cmpltss((__v4sf)__a, (__v4sf)__b);
}

/// \brief Compares each of the corresponding 32-bit float values of the
///    128-bit vectors of [4 x float] to determine if the values in the first
///    operand are less than those in the second operand.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VCMPLTPS / CMPLTPS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float].
/// \param __b
///    A 128-bit vector of [4 x float].
/// \returns A 128-bit vector of [4 x float] containing the comparison results.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_cmplt_ps(__m128 __a, __m128 __b)
{
  return (__m128)__builtin_ia32_cmpltps((__v4sf)__a, (__v4sf)__b);
}

/// \brief Compares two 32-bit float values in the low-order bits of both
///    operands to determine if the value in the first operand is less than or
///    equal to the corresponding value in the second operand and returns the
///    result of the comparison in the low-order bits of a vector of
///    [4 x float].
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VCMPLESS / CMPLESS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float] containing one of the operands. The lower
///    32 bits of this operand are used in the comparison.
/// \param __b
///    A 128-bit vector of [4 x float] containing one of the operands. The lower
///    32 bits of this operand are used in the comparison.
/// \returns A 128-bit vector of [4 x float] containing the comparison results
///    in the low-order bits.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_cmple_ss(__m128 __a, __m128 __b)
{
  return (__m128)__builtin_ia32_cmpless((__v4sf)__a, (__v4sf)__b);
}

/// \brief Compares each of the corresponding 32-bit float values of the
///    128-bit vectors of [4 x float] to determine if the values in the first
///    operand are less than or equal to those in the second operand.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VCMPLEPS / CMPLEPS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float].
/// \param __b
///    A 128-bit vector of [4 x float].
/// \returns A 128-bit vector of [4 x float] containing the comparison results.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_cmple_ps(__m128 __a, __m128 __b)
{
  return (__m128)__builtin_ia32_cmpleps((__v4sf)__a, (__v4sf)__b);
}

/// \brief Compares two 32-bit float values in the low-order bits of both
///    operands to determine if the value in the first operand is greater than
///    the corresponding value in the second operand and returns the result of
///    the comparison in the low-order bits of a vector of [4 x float].
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VCMPLTSS / CMPLTSS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float] containing one of the operands. The lower
///    32 bits of this operand are used in the comparison.
/// \param __b
///    A 128-bit vector of [4 x float] containing one of the operands. The lower
///    32 bits of this operand are used in the comparison.
/// \returns A 128-bit vector of [4 x float] containing the comparison results
///    in the low-order bits.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_cmpgt_ss(__m128 __a, __m128 __b)
{
  return (__m128)__builtin_shufflevector((__v4sf)__a,
                                         (__v4sf)__builtin_ia32_cmpltss((__v4sf)__b, (__v4sf)__a),
                                         4, 1, 2, 3);
}

/// \brief Compares each of the corresponding 32-bit float values of the
///    128-bit vectors of [4 x float] to determine if the values in the first
///    operand are greater than those in the second operand.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VCMPLTPS / CMPLTPS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float].
/// \param __b
///    A 128-bit vector of [4 x float].
/// \returns A 128-bit vector of [4 x float] containing the comparison results.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_cmpgt_ps(__m128 __a, __m128 __b)
{
  return (__m128)__builtin_ia32_cmpltps((__v4sf)__b, (__v4sf)__a);
}

/// \brief Compares two 32-bit float values in the low-order bits of both
///    operands to determine if the value in the first operand is greater than
///    or equal to the corresponding value in the second operand and returns
///    the result of the comparison in the low-order bits of a vector of
///    [4 x float].
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VCMPLESS / CMPLESS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float] containing one of the operands. The lower
///    32 bits of this operand are used in the comparison.
/// \param __b
///    A 128-bit vector of [4 x float] containing one of the operands. The lower
///    32 bits of this operand are used in the comparison.
/// \returns A 128-bit vector of [4 x float] containing the comparison results
///    in the low-order bits.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_cmpge_ss(__m128 __a, __m128 __b)
{
  return (__m128)__builtin_shufflevector((__v4sf)__a,
                                         (__v4sf)__builtin_ia32_cmpless((__v4sf)__b, (__v4sf)__a),
                                         4, 1, 2, 3);
}

/// \brief Compares each of the corresponding 32-bit float values of the
///    128-bit vectors of [4 x float] to determine if the values in the first
///    operand are greater than or equal to those in the second operand.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VCMPLEPS / CMPLEPS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float].
/// \param __b
///    A 128-bit vector of [4 x float].
/// \returns A 128-bit vector of [4 x float] containing the comparison results.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_cmpge_ps(__m128 __a, __m128 __b)
{
  return (__m128)__builtin_ia32_cmpleps((__v4sf)__b, (__v4sf)__a);
}

/// \brief Compares two 32-bit float values in the low-order bits of both
///    operands for inequality and returns the result of the comparison in the
///    low-order bits of a vector of [4 x float].
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VCMPNEQSS / CMPNEQSS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float] containing one of the operands. The lower
///    32 bits of this operand are used in the comparison.
/// \param __b
///    A 128-bit vector of [4 x float] containing one of the operands. The lower
///    32 bits of this operand are used in the comparison.
/// \returns A 128-bit vector of [4 x float] containing the comparison results
///    in the low-order bits.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_cmpneq_ss(__m128 __a, __m128 __b)
{
  return (__m128)__builtin_ia32_cmpneqss((__v4sf)__a, (__v4sf)__b);
}

/// \brief Compares each of the corresponding 32-bit float values of the
///    128-bit vectors of [4 x float] for inequality.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VCMPNEQPS / CMPNEQPS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float].
/// \param __b
///    A 128-bit vector of [4 x float].
/// \returns A 128-bit vector of [4 x float] containing the comparison results.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_cmpneq_ps(__m128 __a, __m128 __b)
{
  return (__m128)__builtin_ia32_cmpneqps((__v4sf)__a, (__v4sf)__b);
}

/// \brief Compares two 32-bit float values in the low-order bits of both
///    operands to determine if the value in the first operand is not less than
///    the corresponding value in the second operand and returns the result of
///    the comparison in the low-order bits of a vector of [4 x float].
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VCMPNLTSS / CMPNLTSS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float] containing one of the operands. The lower
///    32 bits of this operand are used in the comparison.
/// \param __b
///    A 128-bit vector of [4 x float] containing one of the operands. The lower
///    32 bits of this operand are used in the comparison.
/// \returns A 128-bit vector of [4 x float] containing the comparison results
///    in the low-order bits.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_cmpnlt_ss(__m128 __a, __m128 __b)
{
  return (__m128)__builtin_ia32_cmpnltss((__v4sf)__a, (__v4sf)__b);
}

/// \brief Compares each of the corresponding 32-bit float values of the
///    128-bit vectors of [4 x float] to determine if the values in the first
///    operand are not less than those in the second operand.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VCMPNLTPS / CMPNLTPS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float].
/// \param __b
///    A 128-bit vector of [4 x float].
/// \returns A 128-bit vector of [4 x float] containing the comparison results.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_cmpnlt_ps(__m128 __a, __m128 __b)
{
  return (__m128)__builtin_ia32_cmpnltps((__v4sf)__a, (__v4sf)__b);
}

/// \brief Compares two 32-bit float values in the low-order bits of both
///    operands to determine if the value in the first operand is not less than
///    or equal to the corresponding value in the second operand and returns
///    the result of the comparison in the low-order bits of a vector of
///    [4 x float].
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VCMPNLESS / CMPNLESS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float] containing one of the operands. The lower
///    32 bits of this operand are used in the comparison.
/// \param __b
///    A 128-bit vector of [4 x float] containing one of the operands. The lower
///    32 bits of this operand are used in the comparison.
/// \returns A 128-bit vector of [4 x float] containing the comparison results
///    in the low-order bits.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_cmpnle_ss(__m128 __a, __m128 __b)
{
  return (__m128)__builtin_ia32_cmpnless((__v4sf)__a, (__v4sf)__b);
}

/// \brief Compares each of the corresponding 32-bit float values of the
///    128-bit vectors of [4 x float] to determine if the values in the first
///    operand are not less than or equal to those in the second operand.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VCMPNLEPS / CMPNLEPS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float].
/// \param __b
///    A 128-bit vector of [4 x float].
/// \returns A 128-bit vector of [4 x float] containing the comparison results.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_cmpnle_ps(__m128 __a, __m128 __b)
{
  return (__m128)__builtin_ia32_cmpnleps((__v4sf)__a, (__v4sf)__b);
}

/// \brief Compares two 32-bit float values in the low-order bits of both
///    operands to determine if the value in the first operand is not greater
///    than the corresponding value in the second operand and returns the
///    result of the comparison in the low-order bits of a vector of
///    [4 x float].
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VCMPNLTSS / CMPNLTSS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float] containing one of the operands. The lower
///    32 bits of this operand are used in the comparison.
/// \param __b
///    A 128-bit vector of [4 x float] containing one of the operands. The lower
///    32 bits of this operand are used in the comparison.
/// \returns A 128-bit vector of [4 x float] containing the comparison results
///    in the low-order bits.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_cmpngt_ss(__m128 __a, __m128 __b)
{
  return (__m128)__builtin_shufflevector((__v4sf)__a,
                                         (__v4sf)__builtin_ia32_cmpnltss((__v4sf)__b, (__v4sf)__a),
                                         4, 1, 2, 3);
}

/// \brief Compares each of the corresponding 32-bit float values of the
///    128-bit vectors of [4 x float] to determine if the values in the first
///    operand are not greater than those in the second operand.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VCMPNLTPS / CMPNLTPS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float].
/// \param __b
///    A 128-bit vector of [4 x float].
/// \returns A 128-bit vector of [4 x float] containing the comparison results.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_cmpngt_ps(__m128 __a, __m128 __b)
{
  return (__m128)__builtin_ia32_cmpnltps((__v4sf)__b, (__v4sf)__a);
}

/// \brief Compares two 32-bit float values in the low-order bits of both
///    operands to determine if the value in the first operand is not greater
///    than or equal to the corresponding value in the second operand and
///    returns the result of the comparison in the low-order bits of a vector
///    of [4 x float].
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VCMPNLESS / CMPNLESS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float] containing one of the operands. The lower
///    32 bits of this operand are used in the comparison.
/// \param __b
///    A 128-bit vector of [4 x float] containing one of the operands. The lower
///    32 bits of this operand are used in the comparison.
/// \returns A 128-bit vector of [4 x float] containing the comparison results
///    in the low-order bits.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_cmpnge_ss(__m128 __a, __m128 __b)
{
  return (__m128)__builtin_shufflevector((__v4sf)__a,
                                         (__v4sf)__builtin_ia32_cmpnless((__v4sf)__b, (__v4sf)__a),
                                         4, 1, 2, 3);
}

/// \brief Compares each of the corresponding 32-bit float values of the
///    128-bit vectors of [4 x float] to determine if the values in the first
///    operand are not greater than or equal to those in the second operand.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VCMPNLEPS / CMPNLEPS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float].
/// \param __b
///    A 128-bit vector of [4 x float].
/// \returns A 128-bit vector of [4 x float] containing the comparison results.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_cmpnge_ps(__m128 __a, __m128 __b)
{
  return (__m128)__builtin_ia32_cmpnleps((__v4sf)__b, (__v4sf)__a);
}

/// \brief Compares two 32-bit float values in the low-order bits of both
///    operands to determine if the value in the first operand is ordered with
///    respect to the corresponding value in the second operand and returns the
///    result of the comparison in the low-order bits of a vector of
///    [4 x float].
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VCMPORDSS / CMPORDSS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float] containing one of the operands. The lower
///    32 bits of this operand are used in the comparison.
/// \param __b
///    A 128-bit vector of [4 x float] containing one of the operands. The lower
///    32 bits of this operand are used in the comparison.
/// \returns A 128-bit vector of [4 x float] containing the comparison results
///    in the low-order bits.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_cmpord_ss(__m128 __a, __m128 __b)
{
  return (__m128)__builtin_ia32_cmpordss((__v4sf)__a, (__v4sf)__b);
}

/// \brief Compares each of the corresponding 32-bit float values of the
///    128-bit vectors of [4 x float] to determine if the values in the first
///    operand are ordered with respect to those in the second operand.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VCMPORDPS / CMPORDPS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float].
/// \param __b
///    A 128-bit vector of [4 x float].
/// \returns A 128-bit vector of [4 x float] containing the comparison results.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_cmpord_ps(__m128 __a, __m128 __b)
{
  return (__m128)__builtin_ia32_cmpordps((__v4sf)__a, (__v4sf)__b);
}

/// \brief Compares two 32-bit float values in the low-order bits of both
///    operands to determine if the value in the first operand is unordered
///    with respect to the corresponding value in the second operand and
///    returns the result of the comparison in the low-order bits of a vector
///    of [4 x float].
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VCMPUNORDSS / CMPUNORDSS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float] containing one of the operands. The lower
///    32 bits of this operand are used in the comparison.
/// \param __b
///    A 128-bit vector of [4 x float] containing one of the operands. The lower
///    32 bits of this operand are used in the comparison.
/// \returns A 128-bit vector of [4 x float] containing the comparison results
///    in the low-order bits.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_cmpunord_ss(__m128 __a, __m128 __b)
{
  return (__m128)__builtin_ia32_cmpunordss((__v4sf)__a, (__v4sf)__b);
}

/// \brief Compares each of the corresponding 32-bit float values of the
///    128-bit vectors of [4 x float] to determine if the values in the first
///    operand are unordered with respect to those in the second operand.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VCMPUNORDPS / CMPUNORDPS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float].
/// \param __b
///    A 128-bit vector of [4 x float].
/// \returns A 128-bit vector of [4 x float] containing the comparison results.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_cmpunord_ps(__m128 __a, __m128 __b)
{
  return (__m128)__builtin_ia32_cmpunordps((__v4sf)__a, (__v4sf)__b);
}

/// \brief Compares two 32-bit float values in the low-order bits of both
///    operands for equality and returns the result of the comparison.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VCOMISS / COMISS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float]. The lower 32 bits of this operand are
///    used in the comparison.
/// \param __b
///    A 128-bit vector of [4 x float]. The lower 32 bits of this operand are
///    used in the comparison.
/// \returns An integer containing the comparison results.
static __inline__ int __DEFAULT_FN_ATTRS
_mm_comieq_ss(__m128 __a, __m128 __b)
{
  return __builtin_ia32_comieq((__v4sf)__a, (__v4sf)__b);
}

/// \brief Compares two 32-bit float values in the low-order bits of both
///    operands to determine if the first operand is less than the second
///    operand and returns the result of the comparison.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VCOMISS / COMISS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float]. The lower 32 bits of this operand are
///    used in the comparison.
/// \param __b
///    A 128-bit vector of [4 x float]. The lower 32 bits of this operand are
///    used in the comparison.
/// \returns An integer containing the comparison results.
static __inline__ int __DEFAULT_FN_ATTRS
_mm_comilt_ss(__m128 __a, __m128 __b)
{
  return __builtin_ia32_comilt((__v4sf)__a, (__v4sf)__b);
}

/// \brief Compares two 32-bit float values in the low-order bits of both
///    operands to determine if the first operand is less than or equal to the
///    second operand and returns the result of the comparison.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VCOMISS / COMISS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float]. The lower 32 bits of this operand are
///    used in the comparison.
/// \param __b
///    A 128-bit vector of [4 x float]. The lower 32 bits of this operand are
///    used in the comparison.
/// \returns An integer containing the comparison results.
static __inline__ int __DEFAULT_FN_ATTRS
_mm_comile_ss(__m128 __a, __m128 __b)
{
  return __builtin_ia32_comile((__v4sf)__a, (__v4sf)__b);
}

/// \brief Compares two 32-bit float values in the low-order bits of both
///    operands to determine if the first operand is greater than the second
///    operand and returns the result of the comparison.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VCOMISS / COMISS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float]. The lower 32 bits of this operand are
///    used in the comparison.
/// \param __b
///    A 128-bit vector of [4 x float]. The lower 32 bits of this operand are
///    used in the comparison.
/// \returns An integer containing the comparison results.
static __inline__ int __DEFAULT_FN_ATTRS
_mm_comigt_ss(__m128 __a, __m128 __b)
{
  return __builtin_ia32_comigt((__v4sf)__a, (__v4sf)__b);
}

/// \brief Compares two 32-bit float values in the low-order bits of both
///    operands to determine if the first operand is greater than or equal to
///    the second operand and returns the result of the comparison.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VCOMISS / COMISS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float]. The lower 32 bits of this operand are
///    used in the comparison.
/// \param __b
///    A 128-bit vector of [4 x float]. The lower 32 bits of this operand are
///    used in the comparison.
/// \returns An integer containing the comparison results.
static __inline__ int __DEFAULT_FN_ATTRS
_mm_comige_ss(__m128 __a, __m128 __b)
{
  return __builtin_ia32_comige((__v4sf)__a, (__v4sf)__b);
}

/// \brief Compares two 32-bit float values in the low-order bits of both
///    operands to determine if the first operand is not equal to the second
///    operand and returns the result of the comparison.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VCOMISS / COMISS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float]. The lower 32 bits of this operand are
///    used in the comparison.
/// \param __b
///    A 128-bit vector of [4 x float]. The lower 32 bits of this operand are
///    used in the comparison.
/// \returns An integer containing the comparison results.
static __inline__ int __DEFAULT_FN_ATTRS
_mm_comineq_ss(__m128 __a, __m128 __b)
{
  return __builtin_ia32_comineq((__v4sf)__a, (__v4sf)__b);
}

/// \brief Performs an unordered comparison of two 32-bit float values using
///    the low-order bits of both operands to determine equality and returns
///    the result of the comparison.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VUCOMISS / UCOMISS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float]. The lower 32 bits of this operand are
///    used in the comparison.
/// \param __b
///    A 128-bit vector of [4 x float]. The lower 32 bits of this operand are
///    used in the comparison.
/// \returns An integer containing the comparison results.
static __inline__ int __DEFAULT_FN_ATTRS
_mm_ucomieq_ss(__m128 __a, __m128 __b)
{
  return __builtin_ia32_ucomieq((__v4sf)__a, (__v4sf)__b);
}

/// \brief Performs an unordered comparison of two 32-bit float values using
///    the low-order bits of both operands to determine if the first operand is
///    less than the second operand and returns the result of the comparison.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VUCOMISS / UCOMISS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float]. The lower 32 bits of this operand are
///    used in the comparison.
/// \param __b
///    A 128-bit vector of [4 x float]. The lower 32 bits of this operand are
///    used in the comparison.
/// \returns An integer containing the comparison results.
static __inline__ int __DEFAULT_FN_ATTRS
_mm_ucomilt_ss(__m128 __a, __m128 __b)
{
  return __builtin_ia32_ucomilt((__v4sf)__a, (__v4sf)__b);
}

/// \brief Performs an unordered comparison of two 32-bit float values using
///    the low-order bits of both operands to determine if the first operand
///    is less than or equal to the second operand and returns the result of
///    the comparison.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VUCOMISS / UCOMISS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float]. The lower 32 bits of this operand are
///    used in the comparison.
/// \param __b
///    A 128-bit vector of [4 x float]. The lower 32 bits of this operand are
///    used in the comparison.
/// \returns An integer containing the comparison results.
static __inline__ int __DEFAULT_FN_ATTRS
_mm_ucomile_ss(__m128 __a, __m128 __b)
{
  return __builtin_ia32_ucomile((__v4sf)__a, (__v4sf)__b);
}

/// \brief Performs an unordered comparison of two 32-bit float values using
///    the low-order bits of both operands to determine if the first operand
///    is greater than the second operand and returns the result of the
///    comparison.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VUCOMISS / UCOMISS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float]. The lower 32 bits of this operand are
///    used in the comparison.
/// \param __b
///    A 128-bit vector of [4 x float]. The lower 32 bits of this operand are
///    used in the comparison.
/// \returns An integer containing the comparison results.
static __inline__ int __DEFAULT_FN_ATTRS
_mm_ucomigt_ss(__m128 __a, __m128 __b)
{
  return __builtin_ia32_ucomigt((__v4sf)__a, (__v4sf)__b);
}

/// \brief Performs an unordered comparison of two 32-bit float values using
///    the low-order bits of both operands to determine if the first operand is
///    greater than or equal to the second operand and returns the result of
///    the comparison.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VUCOMISS / UCOMISS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float]. The lower 32 bits of this operand are
///    used in the comparison.
/// \param __b
///    A 128-bit vector of [4 x float]. The lower 32 bits of this operand are
///    used in the comparison.
/// \returns An integer containing the comparison results.
static __inline__ int __DEFAULT_FN_ATTRS
_mm_ucomige_ss(__m128 __a, __m128 __b)
{
  return __builtin_ia32_ucomige((__v4sf)__a, (__v4sf)__b);
}

/// \brief Performs an unordered comparison of two 32-bit float values using
///    the low-order bits of both operands to determine inequality and returns
///    the result of the comparison.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VUCOMISS / UCOMISS instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float]. The lower 32 bits of this operand are
///    used in the comparison.
/// \param __b
///    A 128-bit vector of [4 x float]. The lower 32 bits of this operand are
///    used in the comparison.
/// \returns An integer containing the comparison results.
static __inline__ int __DEFAULT_FN_ATTRS
_mm_ucomineq_ss(__m128 __a, __m128 __b)
{
  return __builtin_ia32_ucomineq((__v4sf)__a, (__v4sf)__b);
}

/// \brief Converts a float value contained in the lower 32 bits of a vector of
///    [4 x float] into a 32-bit integer.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VCVTSS2SI / CVTSS2SI instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float]. The lower 32 bits of this operand are
///    used in the conversion.
/// \returns A 32-bit integer containing the converted value.
static __inline__ int __DEFAULT_FN_ATTRS
_mm_cvtss_si32(__m128 __a)
{
  return __builtin_ia32_cvtss2si((__v4sf)__a);
}

/// \brief Converts a float value contained in the lower 32 bits of a vector of
///    [4 x float] into a 32-bit integer.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VCVTSS2SI / CVTSS2SI instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float]. The lower 32 bits of this operand are
///    used in the conversion.
/// \returns A 32-bit integer containing the converted value.
static __inline__ int __DEFAULT_FN_ATTRS
_mm_cvt_ss2si(__m128 __a)
{
  return _mm_cvtss_si32(__a);
}

#ifdef __x86_64__

/// \brief Converts a float value contained in the lower 32 bits of a vector of
///    [4 x float] into a 64-bit integer.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VCVTSS2SI / CVTSS2SI instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float]. The lower 32 bits of this operand are
///    used in the conversion.
/// \returns A 64-bit integer containing the converted value.
static __inline__ long long __DEFAULT_FN_ATTRS
_mm_cvtss_si64(__m128 __a)
{
  return __builtin_ia32_cvtss2si64((__v4sf)__a);
}

#endif

/// \brief Converts two low-order float values in a 128-bit vector of
///    [4 x float] into a 64-bit vector of [2 x i32].
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c CVTPS2PI instruction.
///
/// \param __a
///    A 128-bit vector of [4 x float].
/// \returns A 64-bit integer vector containing the converted values.
static __inline__ __m64 __DEFAULT_FN_ATTRS
_mm_cvtps_pi32(__m128 __a)
{
  return (__m64)__builtin_ia32_cvtps2pi((__v4sf)__a);
}

/// \brief Converts two low-order float values in a 128-bit vector of
///    [4 x float] into a 64-bit vector of [2 x i32].
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c CVTPS2PI instruction.
///
/// \param __a
///    A 128-bit vector of [4 x float].
/// \returns A 64-bit integer vector containing the converted values.
static __inline__ __m64 __DEFAULT_FN_ATTRS
_mm_cvt_ps2pi(__m128 __a)
{
  return _mm_cvtps_pi32(__a);
}

/// \brief Converts a float value contained in the lower 32 bits of a vector of
///    [4 x float] into a 32-bit integer, truncating the result when it is
///    inexact.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VCVTTSS2SI / CVTTSS2SI instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float]. The lower 32 bits of this operand are
///    used in the conversion.
/// \returns A 32-bit integer containing the converted value.
static __inline__ int __DEFAULT_FN_ATTRS
_mm_cvttss_si32(__m128 __a)
{
  return __a[0];
}

/// \brief Converts a float value contained in the lower 32 bits of a vector of
///    [4 x float] into a 32-bit integer, truncating the result when it is
///    inexact.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VCVTTSS2SI / CVTTSS2SI instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float]. The lower 32 bits of this operand are
///    used in the conversion.
/// \returns A 32-bit integer containing the converted value.
static __inline__ int __DEFAULT_FN_ATTRS
_mm_cvtt_ss2si(__m128 __a)
{
  return _mm_cvttss_si32(__a);
}

/// \brief Converts a float value contained in the lower 32 bits of a vector of
///    [4 x float] into a 64-bit integer, truncating the result when it is
///    inexact.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VCVTTSS2SI / CVTTSS2SI instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float]. The lower 32 bits of this operand are
///    used in the conversion.
/// \returns A 64-bit integer containing the converted value.
static __inline__ long long __DEFAULT_FN_ATTRS
_mm_cvttss_si64(__m128 __a)
{
  return __a[0];
}

/// \brief Converts two low-order float values in a 128-bit vector of
///    [4 x float] into a 64-bit vector of [2 x i32], truncating the result
///    when it is inexact.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c CVTTPS2PI / VTTPS2PI instructions.
///
/// \param __a
///    A 128-bit vector of [4 x float].
/// \returns A 64-bit integer vector containing the converted values.
static __inline__ __m64 __DEFAULT_FN_ATTRS
_mm_cvttps_pi32(__m128 __a)
{
  return (__m64)__builtin_ia32_cvttps2pi((__v4sf)__a);
}

/// \brief Converts two low-order float values in a 128-bit vector of [4 x
///    float] into a 64-bit vector of [2 x i32], truncating the result when it
///    is inexact.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c CVTTPS2PI instruction.
///
/// \param __a
///    A 128-bit vector of [4 x float].
/// \returns A 64-bit integer vector containing the converted values.
static __inline__ __m64 __DEFAULT_FN_ATTRS
_mm_cvtt_ps2pi(__m128 __a)
{
  return _mm_cvttps_pi32(__a);
}

/// \brief Converts a 32-bit signed integer value into a floating point value
///    and writes it to the lower 32 bits of the destination. The remaining
///    higher order elements of the destination vector are copied from the
///    corresponding elements in the first operand.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VCVTSI2SS / CVTSI2SS instruction.
///
/// \param __a
///    A 128-bit vector of [4 x float].
/// \param __b
///    A 32-bit signed integer operand containing the value to be converted.
/// \returns A 128-bit vector of [4 x float] whose lower 32 bits contain the
///    converted value of the second operand. The upper 96 bits are copied from
///    the upper 96 bits of the first operand.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_cvtsi32_ss(__m128 __a, int __b)
{
  __a[0] = __b;
  return __a;
}

/// \brief Converts a 32-bit signed integer value into a floating point value
///    and writes it to the lower 32 bits of the destination. The remaining
///    higher order elements of the destination are copied from the
///    corresponding elements in the first operand.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VCVTSI2SS / CVTSI2SS instruction.
///
/// \param __a
///    A 128-bit vector of [4 x float].
/// \param __b
///    A 32-bit signed integer operand containing the value to be converted.
/// \returns A 128-bit vector of [4 x float] whose lower 32 bits contain the
///    converted value of the second operand. The upper 96 bits are copied from
///    the upper 96 bits of the first operand.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_cvt_si2ss(__m128 __a, int __b)
{
  return _mm_cvtsi32_ss(__a, __b);
}

#ifdef __x86_64__

/// \brief Converts a 64-bit signed integer value into a floating point value
///    and writes it to the lower 32 bits of the destination. The remaining
///    higher order elements of the destination are copied from the
///    corresponding elements in the first operand.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VCVTSI2SS / CVTSI2SS instruction.
///
/// \param __a
///    A 128-bit vector of [4 x float].
/// \param __b
///    A 64-bit signed integer operand containing the value to be converted.
/// \returns A 128-bit vector of [4 x float] whose lower 32 bits contain the
///    converted value of the second operand. The upper 96 bits are copied from
///    the upper 96 bits of the first operand.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_cvtsi64_ss(__m128 __a, long long __b)
{
  __a[0] = __b;
  return __a;
}

#endif

/// \brief Converts two elements of a 64-bit vector of [2 x i32] into two
///    floating point values and writes them to the lower 64-bits of the
///    destination. The remaining higher order elements of the destination are
///    copied from the corresponding elements in the first operand.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c CVTPI2PS instruction.
///
/// \param __a
///    A 128-bit vector of [4 x float].
/// \param __b
///    A 64-bit vector of [2 x i32]. The elements in this vector are converted
///    and written to the corresponding low-order elements in the destination.
/// \returns A 128-bit vector of [4 x float] whose lower 64 bits contain the
///    converted value of the second operand. The upper 64 bits are copied from
///    the upper 64 bits of the first operand.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_cvtpi32_ps(__m128 __a, __m64 __b)
{
  return __builtin_ia32_cvtpi2ps((__v4sf)__a, (__v2si)__b);
}

/// \brief Converts two elements of a 64-bit vector of [2 x i32] into two
///    floating point values and writes them to the lower 64-bits of the
///    destination. The remaining higher order elements of the destination are
///    copied from the corresponding elements in the first operand.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c CVTPI2PS instruction.
///
/// \param __a
///    A 128-bit vector of [4 x float].
/// \param __b
///    A 64-bit vector of [2 x i32]. The elements in this vector are converted
///    and written to the corresponding low-order elements in the destination.
/// \returns A 128-bit vector of [4 x float] whose lower 64 bits contain the
///    converted value from the second operand. The upper 64 bits are copied
///    from the upper 64 bits of the first operand.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_cvt_pi2ps(__m128 __a, __m64 __b)
{
  return _mm_cvtpi32_ps(__a, __b);
}

/// \brief Extracts a float value contained in the lower 32 bits of a vector of
///    [4 x float].
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VMOVSS / MOVSS instruction.
///
/// \param __a
///    A 128-bit vector of [4 x float]. The lower 32 bits of this operand are
///    used in the extraction.
/// \returns A 32-bit float containing the extracted value.
static __inline__ float __DEFAULT_FN_ATTRS
_mm_cvtss_f32(__m128 __a)
{
  return __a[0];
}

/// \brief Loads two packed float values from the address __p into the
///     high-order bits of a 128-bit vector of [4 x float]. The low-order bits
///     are copied from the low-order bits of the first operand.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VMOVHPD / MOVHPD instruction.
///
/// \param __a
///    A 128-bit vector of [4 x float]. Bits [63:0] are written to bits [63:0]
///    of the destination.
/// \param __p
///    A pointer to two packed float values. Bits [63:0] are written to bits
///    [127:64] of the destination.
/// \returns A 128-bit vector of [4 x float] containing the moved values.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_loadh_pi(__m128 __a, const __m64 *__p)
{
  typedef float __mm_loadh_pi_v2f32 __attribute__((__vector_size__(8)));
  struct __mm_loadh_pi_struct {
    __mm_loadh_pi_v2f32 __u;
  } __attribute__((__packed__, __may_alias__));
  __mm_loadh_pi_v2f32 __b = ((struct __mm_loadh_pi_struct*)__p)->__u;
  __m128 __bb = __builtin_shufflevector(__b, __b, 0, 1, 0, 1);
  return __builtin_shufflevector(__a, __bb, 0, 1, 4, 5);
}

/// \brief Loads two packed float values from the address __p into the low-order
///    bits of a 128-bit vector of [4 x float]. The high-order bits are copied
///    from the high-order bits of the first operand.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VMOVLPD / MOVLPD instruction.
///
/// \param __a
///    A 128-bit vector of [4 x float]. Bits [127:64] are written to bits
///    [127:64] of the destination.
/// \param __p
///    A pointer to two packed float values. Bits [63:0] are written to bits
///    [63:0] of the destination.
/// \returns A 128-bit vector of [4 x float] containing the moved values.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_loadl_pi(__m128 __a, const __m64 *__p)
{
  typedef float __mm_loadl_pi_v2f32 __attribute__((__vector_size__(8)));
  struct __mm_loadl_pi_struct {
    __mm_loadl_pi_v2f32 __u;
  } __attribute__((__packed__, __may_alias__));
  __mm_loadl_pi_v2f32 __b = ((struct __mm_loadl_pi_struct*)__p)->__u;
  __m128 __bb = __builtin_shufflevector(__b, __b, 0, 1, 0, 1);
  return __builtin_shufflevector(__a, __bb, 4, 5, 2, 3);
}

/// \brief Constructs a 128-bit floating-point vector of [4 x float]. The lower
///    32 bits of the vector are initialized with the single-precision
///    floating-point value loaded from a specified memory location. The upper
///    96 bits are set to zero.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VMOVSS / MOVSS instruction.
///
/// \param __p
///    A pointer to a 32-bit memory location containing a single-precision
///    floating-point value.
/// \returns An initialized 128-bit floating-point vector of [4 x float]. The
///    lower 32 bits contain the value loaded from the memory location. The
///    upper 96 bits are set to zero.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_load_ss(const float *__p)
{
  struct __mm_load_ss_struct {
    float __u;
  } __attribute__((__packed__, __may_alias__));
  float __u = ((struct __mm_load_ss_struct*)__p)->__u;
  return (__m128){ __u, 0, 0, 0 };
}

/// \brief Loads a 32-bit float value and duplicates it to all four vector
///    elements of a 128-bit vector of [4 x float].
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VMOVSS / MOVSS + \c shuffling
///    instruction.
///
/// \param __p
///    A pointer to a float value to be loaded and duplicated.
/// \returns A 128-bit vector of [4 x float] containing the loaded
///    and duplicated values.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_load1_ps(const float *__p)
{
  struct __mm_load1_ps_struct {
    float __u;
  } __attribute__((__packed__, __may_alias__));
  float __u = ((struct __mm_load1_ps_struct*)__p)->__u;
  return (__m128){ __u, __u, __u, __u };
}

#define        _mm_load_ps1(p) _mm_load1_ps(p)

/// \brief Loads a 128-bit floating-point vector of [4 x float] from an aligned
///    memory location.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VMOVAPS / MOVAPS instruction.
///
/// \param __p
///    A pointer to a 128-bit memory location. The address of the memory
///    location has to be 128-bit aligned.
/// \returns A 128-bit vector of [4 x float] containing the loaded valus.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_load_ps(const float *__p)
{
  return *(__m128*)__p;
}

/// \brief Loads a 128-bit floating-point vector of [4 x float] from an
///    unaligned memory location.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VMOVUPS / MOVUPS instruction.
///
/// \param __p
///    A pointer to a 128-bit memory location. The address of the memory
///    location does not have to be aligned.
/// \returns A 128-bit vector of [4 x float] containing the loaded values.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_loadu_ps(const float *__p)
{
  struct __loadu_ps {
    __m128 __v;
  } __attribute__((__packed__, __may_alias__));
  return ((struct __loadu_ps*)__p)->__v;
}

/// \brief Loads four packed float values, in reverse order, from an aligned
///    memory location to 32-bit elements in a 128-bit vector of [4 x float].
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VMOVAPS / MOVAPS + \c shuffling
///    instruction.
///
/// \param __p
///    A pointer to a 128-bit memory location. The address of the memory
///    location has to be 128-bit aligned.
/// \returns A 128-bit vector of [4 x float] containing the moved values, loaded
///    in reverse order.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_loadr_ps(const float *__p)
{
  __m128 __a = _mm_load_ps(__p);
  return __builtin_shufflevector((__v4sf)__a, (__v4sf)__a, 3, 2, 1, 0);
}

/// \brief Create a 128-bit vector of [4 x float] with undefined values.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic has no corresponding instruction.
///
/// \returns A 128-bit vector of [4 x float] containing undefined values.

static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_undefined_ps(void)
{
  return (__m128)__builtin_ia32_undef128();
}

/// \brief Constructs a 128-bit floating-point vector of [4 x float]. The lower
///    32 bits of the vector are initialized with the specified single-precision
///    floating-point value. The upper 96 bits are set to zero.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VMOVSS / MOVSS instruction.
///
/// \param __w
///    A single-precision floating-point value used to initialize the lower 32
///    bits of the result.
/// \returns An initialized 128-bit floating-point vector of [4 x float]. The
///    lower 32 bits contain the value provided in the source operand. The
///    upper 96 bits are set to zero.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_set_ss(float __w)
{
  return (__m128){ __w, 0, 0, 0 };
}

/// \brief Constructs a 128-bit floating-point vector of [4 x float], with each
///    of the four single-precision floating-point vector elements set to the
///    specified single-precision floating-point value.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VPERMILPS / PERMILPS instruction.
///
/// \param __w
///    A single-precision floating-point value used to initialize each vector
///    element of the result.
/// \returns An initialized 128-bit floating-point vector of [4 x float].
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_set1_ps(float __w)
{
  return (__m128){ __w, __w, __w, __w };
}

/* Microsoft specific. */
/// \brief Constructs a 128-bit floating-point vector of [4 x float], with each
///    of the four single-precision floating-point vector elements set to the
///    specified single-precision floating-point value.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VPERMILPS / PERMILPS instruction.
///
/// \param __w
///    A single-precision floating-point value used to initialize each vector
///    element of the result.
/// \returns An initialized 128-bit floating-point vector of [4 x float].
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_set_ps1(float __w)
{
    return _mm_set1_ps(__w);
}

/// \brief Constructs a 128-bit floating-point vector of [4 x float]
///    initialized with the specified single-precision floating-point values.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic is a utility function and does not correspond to a specific
///    instruction.
///
/// \param __z
///    A single-precision floating-point value used to initialize bits [127:96]
///    of the result.
/// \param __y
///    A single-precision floating-point value used to initialize bits [95:64]
///    of the result.
/// \param __x
///    A single-precision floating-point value used to initialize bits [63:32]
///    of the result.
/// \param __w
///    A single-precision floating-point value used to initialize bits [31:0]
///    of the result.
/// \returns An initialized 128-bit floating-point vector of [4 x float].
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_set_ps(float __z, float __y, float __x, float __w)
{
  return (__m128){ __w, __x, __y, __z };
}

/// \brief Constructs a 128-bit floating-point vector of [4 x float],
///    initialized in reverse order with the specified 32-bit single-precision
///    float-point values.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic is a utility function and does not correspond to a specific
///    instruction.
///
/// \param __z
///    A single-precision floating-point value used to initialize bits [31:0]
///    of the result.
/// \param __y
///    A single-precision floating-point value used to initialize bits [63:32]
///    of the result.
/// \param __x
///    A single-precision floating-point value used to initialize bits [95:64]
///    of the result.
/// \param __w
///    A single-precision floating-point value used to initialize bits [127:96]
///    of the result.
/// \returns An initialized 128-bit floating-point vector of [4 x float].
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_setr_ps(float __z, float __y, float __x, float __w)
{
  return (__m128){ __z, __y, __x, __w };
}

/// \brief Constructs a 128-bit floating-point vector of [4 x float] initialized
///    to zero.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VXORPS / XORPS instruction.
///
/// \returns An initialized 128-bit floating-point vector of [4 x float] with
///    all elements set to zero.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_setzero_ps(void)
{
  return (__m128){ 0, 0, 0, 0 };
}

/// \brief Stores the upper 64 bits of a 128-bit vector of [4 x float] to a
///    memory location.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VPEXTRQ / MOVQ instruction.
///
/// \param __p
///    A pointer to a 64-bit memory location.
/// \param __a
///    A 128-bit vector of [4 x float] containing the values to be stored.
static __inline__ void __DEFAULT_FN_ATTRS
_mm_storeh_pi(__m64 *__p, __m128 __a)
{
  __builtin_ia32_storehps((__v2si *)__p, (__v4sf)__a);
}

/// \brief Stores the lower 64 bits of a 128-bit vector of [4 x float] to a
///     memory location.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VMOVLPS / MOVLPS instruction.
///
/// \param __p
///    A pointer to a memory location that will receive the float values.
/// \param __a
///    A 128-bit vector of [4 x float] containing the values to be stored.
static __inline__ void __DEFAULT_FN_ATTRS
_mm_storel_pi(__m64 *__p, __m128 __a)
{
  __builtin_ia32_storelps((__v2si *)__p, (__v4sf)__a);
}

/// \brief Stores the lower 32 bits of a 128-bit vector of [4 x float] to a
///     memory location.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VMOVSS / MOVSS instruction.
///
/// \param __p
///    A pointer to a 32-bit memory location.
/// \param __a
///    A 128-bit vector of [4 x float] containing the value to be stored.
static __inline__ void __DEFAULT_FN_ATTRS
_mm_store_ss(float *__p, __m128 __a)
{
  struct __mm_store_ss_struct {
    float __u;
  } __attribute__((__packed__, __may_alias__));
  ((struct __mm_store_ss_struct*)__p)->__u = __a[0];
}

/// \brief Stores float values from a 128-bit vector of [4 x float] to an
///    unaligned memory location.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VMOVUPS / MOVUPS instruction.
///
/// \param __p
///    A pointer to a 128-bit memory location. The address of the memory
///    location does not have to be aligned.
/// \param __a
///    A 128-bit vector of [4 x float] containing the values to be stored.
static __inline__ void __DEFAULT_FN_ATTRS
_mm_storeu_ps(float *__p, __m128 __a)
{
  struct __storeu_ps {
    __m128 __v;
  } __attribute__((__packed__, __may_alias__));
  ((struct __storeu_ps*)__p)->__v = __a;
}

/// \brief Stores the lower 32 bits of a 128-bit vector of [4 x float] into
///    four contiguous elements in an aligned memory location.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to \c VMOVAPS / MOVAPS + \c shuffling
///    instruction.
///
/// \param __p
///    A pointer to a 128-bit memory location.
/// \param __a
///    A 128-bit vector of [4 x float] whose lower 32 bits are stored to each
///    of the four contiguous elements pointed by __p.
static __inline__ void __DEFAULT_FN_ATTRS
_mm_store_ps(float *__p, __m128 __a)
{
  *(__m128*)__p = __a;
}

/// \brief Stores the lower 32 bits of a 128-bit vector of [4 x float] into
///    four contiguous elements in an aligned memory location.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to \c VMOVAPS / MOVAPS + \c shuffling
///    instruction.
///
/// \param __p
///    A pointer to a 128-bit memory location.
/// \param __a
///    A 128-bit vector of [4 x float] whose lower 32 bits are stored to each
///    of the four contiguous elements pointed by __p.
static __inline__ void __DEFAULT_FN_ATTRS
_mm_store1_ps(float *__p, __m128 __a)
{
  __a = __builtin_shufflevector((__v4sf)__a, (__v4sf)__a, 0, 0, 0, 0);
  _mm_store_ps(__p, __a);
}

/// \brief Stores float values from a 128-bit vector of [4 x float] to an
///    aligned memory location.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VMOVAPS / MOVAPS instruction.
///
/// \param __p
///    A pointer to a 128-bit memory location. The address of the memory
///    location has to be 128-bit aligned.
/// \param __a
///    A 128-bit vector of [4 x float] containing the values to be stored.
static __inline__ void __DEFAULT_FN_ATTRS
_mm_store_ps1(float *__p, __m128 __a)
{
  return _mm_store1_ps(__p, __a);
}

/// \brief Stores float values from a 128-bit vector of [4 x float] to an
///    aligned memory location in reverse order.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VMOVAPS / MOVAPS + \c shuffling
///    instruction.
///
/// \param __p
///    A pointer to a 128-bit memory location. The address of the memory
///    location has to be 128-bit aligned.
/// \param __a
///    A 128-bit vector of [4 x float] containing the values to be stored.
static __inline__ void __DEFAULT_FN_ATTRS
_mm_storer_ps(float *__p, __m128 __a)
{
  __a = __builtin_shufflevector((__v4sf)__a, (__v4sf)__a, 3, 2, 1, 0);
  _mm_store_ps(__p, __a);
}

#define _MM_HINT_T0 3
#define _MM_HINT_T1 2
#define _MM_HINT_T2 1
#define _MM_HINT_NTA 0

#ifndef _MSC_VER
/* FIXME: We have to #define this because "sel" must be a constant integer, and
   Sema doesn't do any form of constant propagation yet. */

/// \brief Loads one cache line of data from the specified address to a location
///    closer to the processor.
///
/// \headerfile <x86intrin.h>
///
/// \code
/// void _mm_prefetch(const void * a, const int sel);
/// \endcode
///
/// This intrinsic corresponds to the \c PREFETCHNTA instruction.
///
/// \param a
///    A pointer to a memory location containing a cache line of data.
/// \param sel
///    A predefined integer constant specifying the type of prefetch operation:
///    _MM_HINT_NTA: Move data using the non-temporal access (NTA) hint.
///    The PREFETCHNTA instruction will be generated.
///    _MM_HINT_T0: Move data using the T0 hint. The PREFETCHT0 instruction will
///    be generated.
///    _MM_HINT_T1: Move data using the T1 hint. The PREFETCHT1 instruction will
///    be generated.
///    _MM_HINT_T2: Move data using the T2 hint. The PREFETCHT2 instruction will
///    be generated.
#define _mm_prefetch(a, sel) (__builtin_prefetch((void *)(a), 0, (sel)))
#endif

/// \brief Stores a 64-bit integer in the specified aligned memory location. To
///    minimize caching, the data is flagged as non-temporal (unlikely to be
///    used again soon).
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c MOVNTQ instruction.
///
/// \param __p
///    A pointer to an aligned memory location used to store the register value.
/// \param __a
///    A 64-bit integer containing the value to be stored.
static __inline__ void __DEFAULT_FN_ATTRS
_mm_stream_pi(__m64 *__p, __m64 __a)
{
  __builtin_ia32_movntq(__p, __a);
}

/// \brief Moves packed float values from a 128-bit vector of [4 x float] to a
///    128-bit aligned memory location. To minimize caching, the data is flagged
///    as non-temporal (unlikely to be used again soon).
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VMOVNTPS / MOVNTPS instruction.
///
/// \param __p
///    A pointer to a 128-bit aligned memory location that will receive the
///    integer values.
/// \param __a
///    A 128-bit vector of [4 x float] containing the values to be moved.
static __inline__ void __DEFAULT_FN_ATTRS
_mm_stream_ps(float *__p, __m128 __a)
{
  __builtin_nontemporal_store((__v4sf)__a, (__v4sf*)__p);
}

/// \brief Forces strong memory ordering (serialization) between store
///    instructions preceding this instruction and store instructions following
///    this instruction, ensuring the system completes all previous stores
///    before executing subsequent stores.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c SFENCE instruction.
///
static __inline__ void __DEFAULT_FN_ATTRS
_mm_sfence(void)
{
  __builtin_ia32_sfence();
}

/// \brief Extracts 16-bit element from a 64-bit vector of [4 x i16] and
///    returns it, as specified by the immediate integer operand.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VPEXTRW / PEXTRW instruction.
///
/// \param __a
///    A 64-bit vector of [4 x i16].
/// \param __n
///    An immediate integer operand that determines which bits are extracted:
///    0: Bits [15:0] are copied to the destination.
///    1: Bits [31:16] are copied to the destination.
///    2: Bits [47:32] are copied to the destination.
///    3: Bits [63:48] are copied to the destination.
/// \returns A 16-bit integer containing the extracted 16 bits of packed data.
#define _mm_extract_pi16(a, n) __extension__ ({ \
  (int)__builtin_ia32_vec_ext_v4hi((__m64)a, (int)n); })

/// \brief Copies data from the 64-bit vector of [4 x i16] to the destination,
///    and inserts the lower 16-bits of an integer operand at the 16-bit offset
///    specified by the immediate operand __n.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VPINSRW / PINSRW instruction.
///
/// \param __a
///    A 64-bit vector of [4 x i16].
/// \param __d
///    An integer. The lower 16-bit value from this operand is written to the
///    destination at the offset specified by operand __n.
/// \param __n
///    An immediate integer operant that determines which the bits to be used
///    in the destination.
///    0: Bits [15:0] are copied to the destination.
///    1: Bits [31:16] are copied to the destination.
///    2: Bits [47:32] are copied to the destination.
///    3: Bits [63:48] are copied to the destination.
///    The remaining bits in the destination are copied from the corresponding
///    bits in operand __a.
/// \returns A 64-bit integer vector containing the copied packed data from the
///    operands.
#define _mm_insert_pi16(a, d, n) __extension__ ({ \
  (__m64)__builtin_ia32_vec_set_v4hi((__m64)a, (int)d, (int)n); })

/// \brief Compares each of the corresponding packed 16-bit integer values of
///    the 64-bit integer vectors, and writes the greater value to the
///    corresponding bits in the destination.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c PMAXSW instruction.
///
/// \param __a
///    A 64-bit integer vector containing one of the source operands.
/// \param __b
///    A 64-bit integer vector containing one of the source operands.
/// \returns A 64-bit integer vector containing the comparison results.
static __inline__ __m64 __DEFAULT_FN_ATTRS
_mm_max_pi16(__m64 __a, __m64 __b)
{
  return (__m64)__builtin_ia32_pmaxsw((__v4hi)__a, (__v4hi)__b);
}

/// \brief Compares each of the corresponding packed 8-bit unsigned integer
///    values of the 64-bit integer vectors, and writes the greater value to the
///    corresponding bits in the destination.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c PMAXUB instruction.
///
/// \param __a
///    A 64-bit integer vector containing one of the source operands.
/// \param __b
///    A 64-bit integer vector containing one of the source operands.
/// \returns A 64-bit integer vector containing the comparison results.
static __inline__ __m64 __DEFAULT_FN_ATTRS
_mm_max_pu8(__m64 __a, __m64 __b)
{
  return (__m64)__builtin_ia32_pmaxub((__v8qi)__a, (__v8qi)__b);
}

/// \brief Compares each of the corresponding packed 16-bit integer values of
///    the 64-bit integer vectors, and writes the lesser value to the
///    corresponding bits in the destination.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c PMINSW instruction.
///
/// \param __a
///    A 64-bit integer vector containing one of the source operands.
/// \param __b
///    A 64-bit integer vector containing one of the source operands.
/// \returns A 64-bit integer vector containing the comparison results.
static __inline__ __m64 __DEFAULT_FN_ATTRS
_mm_min_pi16(__m64 __a, __m64 __b)
{
  return (__m64)__builtin_ia32_pminsw((__v4hi)__a, (__v4hi)__b);
}

/// \brief Compares each of the corresponding packed 8-bit unsigned integer
///    values of the 64-bit integer vectors, and writes the lesser value to the
///    corresponding bits in the destination.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c PMINUB instruction.
///
/// \param __a
///    A 64-bit integer vector containing one of the source operands.
/// \param __b
///    A 64-bit integer vector containing one of the source operands.
/// \returns A 64-bit integer vector containing the comparison results.
static __inline__ __m64 __DEFAULT_FN_ATTRS
_mm_min_pu8(__m64 __a, __m64 __b)
{
  return (__m64)__builtin_ia32_pminub((__v8qi)__a, (__v8qi)__b);
}

/// \brief Takes the most significant bit from each 8-bit element in a 64-bit
///    integer vector to create a 16-bit mask value. Zero-extends the value to
///    32-bit integer and writes it to the destination.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c PMOVMSKB instruction.
///
/// \param __a
///    A 64-bit integer vector containing the values with bits to be extracted.
/// \returns The most significant bit from each 8-bit element in the operand,
///    written to bits [15:0].
static __inline__ int __DEFAULT_FN_ATTRS
_mm_movemask_pi8(__m64 __a)
{
  return __builtin_ia32_pmovmskb((__v8qi)__a);
}

/// \brief Multiplies packed 16-bit unsigned integer values and writes the
///    high-order 16 bits of each 32-bit product to the corresponding bits in
///    the destination.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c PMULHUW instruction.
///
/// \param __a
///    A 64-bit integer vector containing one of the source operands.
/// \param __b
///    A 64-bit integer vector containing one of the source operands.
/// \returns A 64-bit integer vector containing the products of both operands.
static __inline__ __m64 __DEFAULT_FN_ATTRS
_mm_mulhi_pu16(__m64 __a, __m64 __b)
{
  return (__m64)__builtin_ia32_pmulhuw((__v4hi)__a, (__v4hi)__b);
}

/// \brief Shuffles the 4 16-bit integers from a 64-bit integer vector to the
///    destination, as specified by the immediate value operand.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c PSHUFW instruction.
///
/// \code
/// __m64 _mm_shuffle_pi16(__m64 a, const int n);
/// \endcode
///
/// \param a
///    A 64-bit integer vector containing the values to be shuffled.
/// \param n
///    An immediate value containing an 8-bit value specifying which elements to
///    copy from a. The destinations within the 64-bit destination are assigned
///    values as follows:
///    Bits [1:0] are used to assign values to bits [15:0] in the destination.
///    Bits [3:2] are used to assign values to bits [31:16] in the destination.
///    Bits [5:4] are used to assign values to bits [47:32] in the destination.
///    Bits [7:6] are used to assign values to bits [63:48] in the destination.
///    Bit value assignments:
///    00: assigned from bits [15:0] of a.
///    01: assigned from bits [31:16] of a.
///    10: assigned from bits [47:32] of a.
///    11: assigned from bits [63:48] of a.
/// \returns A 64-bit integer vector containing the shuffled values.
#define _mm_shuffle_pi16(a, n) __extension__ ({ \
  (__m64)__builtin_ia32_pshufw((__v4hi)(__m64)(a), (n)); })

/// \brief Conditionally copies the values from each 8-bit element in the first
///    64-bit integer vector operand to the specified memory location, as
///    specified by the most significant bit in the corresponding element in the
///    second 64-bit integer vector operand. To minimize caching, the data is
///    flagged as non-temporal (unlikely to be used again soon).
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c MASKMOVQ instruction.
///
/// \param __d
///    A 64-bit integer vector containing the values with elements to be copied.
/// \param __n
///    A 64-bit integer vector operand. The most significant bit from each 8-bit
///    element determines whether the corresponding element in operand __d is
///    copied. If the most significant bit of a given element is 1, the
///    corresponding element in operand __d is copied.
/// \param __p
///    A pointer to a 64-bit memory location that will receive the conditionally
///    copied integer values. The address of the memory location does not have
///    to be aligned.
static __inline__ void __DEFAULT_FN_ATTRS
_mm_maskmove_si64(__m64 __d, __m64 __n, char *__p)
{
  __builtin_ia32_maskmovq((__v8qi)__d, (__v8qi)__n, __p);
}

/// \brief Computes the rounded averages of the packed unsigned 8-bit integer
///    values and writes the averages to the corresponding bits in the
///    destination.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c PAVGB instruction.
///
/// \param __a
///    A 64-bit integer vector containing one of the source operands.
/// \param __b
///    A 64-bit integer vector containing one of the source operands.
/// \returns A 64-bit integer vector containing the averages of both operands.
static __inline__ __m64 __DEFAULT_FN_ATTRS
_mm_avg_pu8(__m64 __a, __m64 __b)
{
  return (__m64)__builtin_ia32_pavgb((__v8qi)__a, (__v8qi)__b);
}

/// \brief Computes the rounded averages of the packed unsigned 16-bit integer
///    values and writes the averages to the corresponding bits in the
///    destination.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c PAVGW instruction.
///
/// \param __a
///    A 64-bit integer vector containing one of the source operands.
/// \param __b
///    A 64-bit integer vector containing one of the source operands.
/// \returns A 64-bit integer vector containing the averages of both operands.
static __inline__ __m64 __DEFAULT_FN_ATTRS
_mm_avg_pu16(__m64 __a, __m64 __b)
{
  return (__m64)__builtin_ia32_pavgw((__v4hi)__a, (__v4hi)__b);
}

/// \brief Subtracts the corresponding 8-bit unsigned integer values of the two
///    64-bit vector operands and computes the absolute value for each of the
///    difference. Then sum of the 8 absolute differences is written to the
///    bits [15:0] of the destination; the remaining bits [63:16] are cleared.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c PSADBW instruction.
///
/// \param __a
///    A 64-bit integer vector containing one of the source operands.
/// \param __b
///    A 64-bit integer vector containing one of the source operands.
/// \returns A 64-bit integer vector whose lower 16 bits contain the sums of the
///    sets of absolute differences between both operands. The upper bits are
///    cleared.
static __inline__ __m64 __DEFAULT_FN_ATTRS
_mm_sad_pu8(__m64 __a, __m64 __b)
{
  return (__m64)__builtin_ia32_psadbw((__v8qi)__a, (__v8qi)__b);
}

/// \brief Returns the contents of the MXCSR register as a 32-bit unsigned
///    integer value. There are several groups of macros associated with this
///    intrinsic, including:
///    * For checking exception states: _MM_EXCEPT_INVALID, _MM_EXCEPT_DIV_ZERO,
///      _MM_EXCEPT_DENORM, _MM_EXCEPT_OVERFLOW, _MM_EXCEPT_UNDERFLOW,
///      _MM_EXCEPT_INEXACT. There is a convenience wrapper
///      _MM_GET_EXCEPTION_STATE().
///    * For checking exception masks: _MM_MASK_UNDERFLOW, _MM_MASK_OVERFLOW,
///      _MM_MASK_INVALID, _MM_MASK_DENORM, _MM_MASK_DIV_ZERO, _MM_MASK_INEXACT.
///      There is a convenience wrapper _MM_GET_EXCEPTION_MASK().
///    * For checking rounding modes: _MM_ROUND_NEAREST, _MM_ROUND_DOWN,
///      _MM_ROUND_UP, _MM_ROUND_TOWARD_ZERO. There is a convenience wrapper
///      _MM_GET_ROUNDING_MODE(x) where x is one of these macros.
///    * For checking flush-to-zero mode: _MM_FLUSH_ZERO_ON, _MM_FLUSH_ZERO_OFF.
///      There is a convenience wrapper _MM_GET_FLUSH_ZERO_MODE().
///    * For checking denormals-are-zero mode: _MM_DENORMALS_ZERO_ON,
///      _MM_DENORMALS_ZERO_OFF. There is a convenience wrapper
///      _MM_GET_DENORMALS_ZERO_MODE().
///
///    For example, the expression below checks if an overflow exception has
///    occurred:
///      ( _mm_getcsr() & _MM_EXCEPT_OVERFLOW )
///
///    The following example gets the current rounding mode:
///      _MM_GET_ROUNDING_MODE()
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VSTMXCSR / STMXCSR instruction.
///
/// \returns A 32-bit unsigned integer containing the contents of the MXCSR
///    register.
static __inline__ unsigned int __DEFAULT_FN_ATTRS
_mm_getcsr(void)
{
  return __builtin_ia32_stmxcsr();
}

/// \brief Sets the MXCSR register with the 32-bit unsigned integer value. There
///    are several groups of macros associated with this intrinsic, including:
///    * For setting exception states: _MM_EXCEPT_INVALID, _MM_EXCEPT_DIV_ZERO,
///      _MM_EXCEPT_DENORM, _MM_EXCEPT_OVERFLOW, _MM_EXCEPT_UNDERFLOW,
///      _MM_EXCEPT_INEXACT. There is a convenience wrapper
///      _MM_SET_EXCEPTION_STATE(x) where x is one of these macros.
///    * For setting exception masks: _MM_MASK_UNDERFLOW, _MM_MASK_OVERFLOW,
///      _MM_MASK_INVALID, _MM_MASK_DENORM, _MM_MASK_DIV_ZERO, _MM_MASK_INEXACT.
///      There is a convenience wrapper _MM_SET_EXCEPTION_MASK(x) where x is one
///      of these macros.
///    * For setting rounding modes: _MM_ROUND_NEAREST, _MM_ROUND_DOWN,
///      _MM_ROUND_UP, _MM_ROUND_TOWARD_ZERO. There is a convenience wrapper
///      _MM_SET_ROUNDING_MODE(x) where x is one of these macros.
///    * For setting flush-to-zero mode: _MM_FLUSH_ZERO_ON, _MM_FLUSH_ZERO_OFF.
///      There is a convenience wrapper _MM_SET_FLUSH_ZERO_MODE(x) where x is
///      one of these macros.
///    * For setting denormals-are-zero mode: _MM_DENORMALS_ZERO_ON,
///      _MM_DENORMALS_ZERO_OFF. There is a convenience wrapper
///      _MM_SET_DENORMALS_ZERO_MODE(x) where x is one of these macros.
///
///    For example, the following expression causes subsequent floating-point
///    operations to round up:
///      _mm_setcsr(_mm_getcsr() | _MM_ROUND_UP)
///
///    The following example sets the DAZ and FTZ flags:
///      void setFlags() {
///        _MM_SET_FLUSH_ZERO_MODE(_MM_FLUSH_ZERO_ON)
///        _MM_SET_DENORMALS_ZERO_MODE(_MM_DENORMALS_ZERO_ON)
///      }
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VLDMXCSR / LDMXCSR instruction.
///
/// \param __i
///    A 32-bit unsigned integer value to be written to the MXCSR register.
static __inline__ void __DEFAULT_FN_ATTRS
_mm_setcsr(unsigned int __i)
{
  __builtin_ia32_ldmxcsr(__i);
}

/// \brief Selects 4 float values from the 128-bit operands of [4 x float], as
///    specified by the immediate value operand.
///
/// \headerfile <x86intrin.h>
///
/// \code
/// __m128 _mm_shuffle_ps(__m128 a, __m128 b, const int mask);
/// \endcode
///
/// This intrinsic corresponds to the \c VSHUFPS / SHUFPS instruction.
///
/// \param a
///    A 128-bit vector of [4 x float].
/// \param b
///    A 128-bit vector of [4 x float].
/// \param mask
///    An immediate value containing an 8-bit value specifying which elements to
///    copy from a and b.
///    Bits [3:0] specify the values copied from operand a.
///    Bits [7:4] specify the values copied from operand b. The destinations
///    within the 128-bit destination are assigned values as follows:
///    Bits [1:0] are used to assign values to bits [31:0] in the destination.
///    Bits [3:2] are used to assign values to bits [63:32] in the destination.
///    Bits [5:4] are used to assign values to bits [95:64] in the destination.
///    Bits [7:6] are used to assign values to bits [127:96] in the destination.
///    Bit value assignments:
///    00: Bits [31:0] copied from the specified operand.
///    01: Bits [63:32] copied from the specified operand.
///    10: Bits [95:64] copied from the specified operand.
///    11: Bits [127:96] copied from the specified operand.
/// \returns A 128-bit vector of [4 x float] containing the shuffled values.
#define _mm_shuffle_ps(a, b, mask) __extension__ ({ \
  (__m128)__builtin_shufflevector((__v4sf)(__m128)(a), (__v4sf)(__m128)(b), \
                                  0 + (((mask) >> 0) & 0x3), \
                                  0 + (((mask) >> 2) & 0x3), \
                                  4 + (((mask) >> 4) & 0x3), \
                                  4 + (((mask) >> 6) & 0x3)); })

/// \brief Unpacks the high-order (index 2,3) values from two 128-bit vectors of
///    [4 x float] and interleaves them into a 128-bit vector of [4 x
///    float].
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VUNPCKHPS / UNPCKHPS instruction.
///
/// \param __a
///    A 128-bit vector of [4 x float].
///    Bits [95:64] are written to bits [31:0] of the destination.
///    Bits [127:96] are written to bits [95:64] of the destination.
/// \param __b
///    A 128-bit vector of [4 x float].
///    Bits [95:64] are written to bits [63:32] of the destination.
///    Bits [127:96] are written to bits [127:96] of the destination.
/// \returns A 128-bit vector of [4 x float] containing the interleaved values.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_unpackhi_ps(__m128 __a, __m128 __b)
{
  return __builtin_shufflevector((__v4sf)__a, (__v4sf)__b, 2, 6, 3, 7);
}

/// \brief Unpacks the low-order (index 0,1) values from two 128-bit vectors of
///    [4 x float] and interleaves them into a 128-bit vector of [4 x
///    float].
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VUNPCKLPS / UNPCKLPS instruction.
///
/// \param __a
///    A 128-bit vector of [4 x float].
///    Bits [31:0] are written to bits [31:0] of the destination.
///    Bits [63:32] are written to bits [95:64] of the destination.
/// \param __b
///    A 128-bit vector of [4 x float].
///    Bits [31:0] are written to bits [63:32] of the destination.
///    Bits [63:32] are written to bits [127:96] of the destination.
/// \returns A 128-bit vector of [4 x float] containing the interleaved values.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_unpacklo_ps(__m128 __a, __m128 __b)
{
  return __builtin_shufflevector((__v4sf)__a, (__v4sf)__b, 0, 4, 1, 5);
}

/// \brief Constructs a 128-bit floating-point vector of [4 x float]. The lower
///    32 bits are set to the lower 32 bits of the second parameter. The upper
///    96 bits are set to the upper 96 bits of the first parameter.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VMOVSS / MOVSS instruction.
///
/// \param __a
///    A 128-bit floating-point vector of [4 x float]. The upper 96 bits are
///    written to the upper 96 bits of the result.
/// \param __b
///    A 128-bit floating-point vector of [4 x float]. The lower 32 bits are
///    written to the lower 32 bits of the result.
/// \returns A 128-bit floating-point vector of [4 x float].
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_move_ss(__m128 __a, __m128 __b)
{
  return __builtin_shufflevector((__v4sf)__a, (__v4sf)__b, 4, 1, 2, 3);
}

/// \brief Constructs a 128-bit floating-point vector of [4 x float]. The lower
///    64 bits are set to the upper 64 bits of the second parameter. The upper
///    64 bits are set to the upper 64 bits of the first parameter.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VUNPCKHPD / UNPCKHPD instruction.
///
/// \param __a
///    A 128-bit floating-point vector of [4 x float]. The upper 64 bits are
///    written to the upper 64 bits of the result.
/// \param __b
///    A 128-bit floating-point vector of [4 x float]. The upper 64 bits are
///    written to the lower 64 bits of the result.
/// \returns A 128-bit floating-point vector of [4 x float].
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_movehl_ps(__m128 __a, __m128 __b)
{
  return __builtin_shufflevector((__v4sf)__a, (__v4sf)__b, 6, 7, 2, 3);
}

/// \brief Constructs a 128-bit floating-point vector of [4 x float]. The lower
///    64 bits are set to the lower 64 bits of the first parameter. The upper
///    64 bits are set to the lower 64 bits of the second parameter.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VUNPCKLPD / UNPCKLPD instruction.
///
/// \param __a
///    A 128-bit floating-point vector of [4 x float]. The lower 64 bits are
///    written to the lower 64 bits of the result.
/// \param __b
///    A 128-bit floating-point vector of [4 x float]. The lower 64 bits are
///    written to the upper 64 bits of the result.
/// \returns A 128-bit floating-point vector of [4 x float].
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_movelh_ps(__m128 __a, __m128 __b)
{
  return __builtin_shufflevector((__v4sf)__a, (__v4sf)__b, 0, 1, 4, 5);
}

/// \brief Converts a 64-bit vector of [4 x i16] into a 128-bit vector of [4 x
///    float].
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c CVTPI2PS + \c COMPOSITE instruction.
///
/// \param __a
///    A 64-bit vector of [4 x i16]. The elements of the destination are copied
///    from the corresponding elements in this operand.
/// \returns A 128-bit vector of [4 x float] containing the copied and converted
///    values from the operand.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_cvtpi16_ps(__m64 __a)
{
  __m64 __b, __c;
  __m128 __r;

  __b = _mm_setzero_si64();
  __b = _mm_cmpgt_pi16(__b, __a);
  __c = _mm_unpackhi_pi16(__a, __b);
  __r = _mm_setzero_ps();
  __r = _mm_cvtpi32_ps(__r, __c);
  __r = _mm_movelh_ps(__r, __r);
  __c = _mm_unpacklo_pi16(__a, __b);
  __r = _mm_cvtpi32_ps(__r, __c);

  return __r;
}

/// \brief Converts a 64-bit vector of 16-bit unsigned integer values into a
///    128-bit vector of [4 x float].
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c CVTPI2PS + \c COMPOSITE instruction.
///
/// \param __a
///    A 64-bit vector of 16-bit unsigned integer values. The elements of the
///    destination are copied from the corresponding elements in this operand.
/// \returns A 128-bit vector of [4 x float] containing the copied and converted
///    values from the operand.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_cvtpu16_ps(__m64 __a)
{
  __m64 __b, __c;
  __m128 __r;

  __b = _mm_setzero_si64();
  __c = _mm_unpackhi_pi16(__a, __b);
  __r = _mm_setzero_ps();
  __r = _mm_cvtpi32_ps(__r, __c);
  __r = _mm_movelh_ps(__r, __r);
  __c = _mm_unpacklo_pi16(__a, __b);
  __r = _mm_cvtpi32_ps(__r, __c);

  return __r;
}

/// \brief Converts the lower four 8-bit values from a 64-bit vector of [8 x i8]
///    into a 128-bit vector of [4 x float].
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c CVTPI2PS + \c COMPOSITE instruction.
///
/// \param __a
///    A 64-bit vector of [8 x i8]. The elements of the destination are copied
///    from the corresponding lower 4 elements in this operand.
/// \returns A 128-bit vector of [4 x float] containing the copied and converted
///    values from the operand.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_cvtpi8_ps(__m64 __a)
{
  __m64 __b;

  __b = _mm_setzero_si64();
  __b = _mm_cmpgt_pi8(__b, __a);
  __b = _mm_unpacklo_pi8(__a, __b);

  return _mm_cvtpi16_ps(__b);
}

/// \brief Converts the lower four unsigned 8-bit integer values from a 64-bit
///    vector of [8 x u8] into a 128-bit vector of [4 x float].
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c CVTPI2PS + \c COMPOSITE instruction.
///
/// \param __a
///    A 64-bit vector of unsigned 8-bit integer values. The elements of the
///    destination are copied from the corresponding lower 4 elements in this
///    operand.
/// \returns A 128-bit vector of [4 x float] containing the copied and converted
///    values from the source operand.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_cvtpu8_ps(__m64 __a)
{
  __m64 __b;

  __b = _mm_setzero_si64();
  __b = _mm_unpacklo_pi8(__a, __b);

  return _mm_cvtpi16_ps(__b);
}

/// \brief Converts the two 32-bit signed integer values from each 64-bit vector
///    operand of [2 x i32] into a 128-bit vector of [4 x float].
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c CVTPI2PS + \c COMPOSITE instruction.
///
/// \param __a
///    A 64-bit vector of [2 x i32]. The lower elements of the destination are
///    copied from the elements in this operand.
/// \param __b
///    A 64-bit vector of [2 x i32]. The upper elements of the destination are
///    copied from the elements in this operand.
/// \returns A 128-bit vector of [4 x float] whose lower 64 bits contain the
///    copied and converted values from the first operand. The upper 64 bits
///    contain the copied and converted values from the second operand.
static __inline__ __m128 __DEFAULT_FN_ATTRS
_mm_cvtpi32x2_ps(__m64 __a, __m64 __b)
{
  __m128 __c;

  __c = _mm_setzero_ps();
  __c = _mm_cvtpi32_ps(__c, __b);
  __c = _mm_movelh_ps(__c, __c);

  return _mm_cvtpi32_ps(__c, __a);
}

/// \brief Converts each single-precision floating-point element of a 128-bit
///    floating-point vector of [4 x float] into a 16-bit signed integer, and
///    packs the results into a 64-bit integer vector of [4 x i16]. If the
///    floating-point element is NaN or infinity, or if the floating-point
///    element is greater than 0x7FFFFFFF or less than -0x8000, it is converted
///    to 0x8000. Otherwise if the floating-point element is greater
///    than 0x7FFF, it is converted to 0x7FFF.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c CVTPS2PI + \c COMPOSITE instruction.
///
/// \param __a
///    A 128-bit floating-point vector of [4 x float].
/// \returns A 64-bit integer vector of [4 x i16] containing the converted
///    values.
static __inline__ __m64 __DEFAULT_FN_ATTRS
_mm_cvtps_pi16(__m128 __a)
{
  __m64 __b, __c;

  __b = _mm_cvtps_pi32(__a);
  __a = _mm_movehl_ps(__a, __a);
  __c = _mm_cvtps_pi32(__a);

  return _mm_packs_pi32(__b, __c);
}

/// \brief Converts each single-precision floating-point element of a 128-bit
///    floating-point vector of [4 x float] into an 8-bit signed integer, and
///    packs the results into the lower 32 bits of a 64-bit integer vector of
///    [8 x i8]. The upper 32 bits of the vector are set to 0. If the
///    floating-point element is NaN or infinity, or if the floating-point
///    element is greater than 0x7FFFFFFF or less than -0x80, it is converted
///    to 0x80. Otherwise if the floating-point element is greater
///    than 0x7F, it is converted to 0x7F.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c CVTPS2PI + \c COMPOSITE instruction.
///
/// \param __a
///    128-bit floating-point vector of [4 x float].
/// \returns A 64-bit integer vector of [8 x i8]. The lower 32 bits contain the
///    converted values and the uppper 32 bits are set to zero.
static __inline__ __m64 __DEFAULT_FN_ATTRS
_mm_cvtps_pi8(__m128 __a)
{
  __m64 __b, __c;

  __b = _mm_cvtps_pi16(__a);
  __c = _mm_setzero_si64();

  return _mm_packs_pi16(__b, __c);
}

/// \brief Extracts the sign bits from each single-precision floating-point
///    element of a 128-bit floating-point vector of [4 x float] and returns the
///    sign bits in bits [0:3] of the result. Bits [31:4] of the result are set
///    to zero.
///
/// \headerfile <x86intrin.h>
///
/// This intrinsic corresponds to the \c VMOVMSKPS / MOVMSKPS instruction.
///
/// \param __a
///    A 128-bit floating-point vector of [4 x float].
/// \returns A 32-bit integer value. Bits [3:0] contain the sign bits from each
///    single-precision floating-point element of the parameter. Bits [31:4] are
///    set to zero.
static __inline__ int __DEFAULT_FN_ATTRS
_mm_movemask_ps(__m128 __a)
{
  return __builtin_ia32_movmskps((__v4sf)__a);
}


#define _MM_ALIGN16 __attribute__((aligned(16)))

#define _MM_SHUFFLE(z, y, x, w) (((z) << 6) | ((y) << 4) | ((x) << 2) | (w))

#define _MM_EXCEPT_INVALID    (0x0001)
#define _MM_EXCEPT_DENORM     (0x0002)
#define _MM_EXCEPT_DIV_ZERO   (0x0004)
#define _MM_EXCEPT_OVERFLOW   (0x0008)
#define _MM_EXCEPT_UNDERFLOW  (0x0010)
#define _MM_EXCEPT_INEXACT    (0x0020)
#define _MM_EXCEPT_MASK       (0x003f)

#define _MM_MASK_INVALID      (0x0080)
#define _MM_MASK_DENORM       (0x0100)
#define _MM_MASK_DIV_ZERO     (0x0200)
#define _MM_MASK_OVERFLOW     (0x0400)
#define _MM_MASK_UNDERFLOW    (0x0800)
#define _MM_MASK_INEXACT      (0x1000)
#define _MM_MASK_MASK         (0x1f80)

#define _MM_ROUND_NEAREST     (0x0000)
#define _MM_ROUND_DOWN        (0x2000)
#define _MM_ROUND_UP          (0x4000)
#define _MM_ROUND_TOWARD_ZERO (0x6000)
#define _MM_ROUND_MASK        (0x6000)

#define _MM_FLUSH_ZERO_MASK   (0x8000)
#define _MM_FLUSH_ZERO_ON     (0x8000)
#define _MM_FLUSH_ZERO_OFF    (0x0000)

#define _MM_GET_EXCEPTION_MASK() (_mm_getcsr() & _MM_MASK_MASK)
#define _MM_GET_EXCEPTION_STATE() (_mm_getcsr() & _MM_EXCEPT_MASK)
#define _MM_GET_FLUSH_ZERO_MODE() (_mm_getcsr() & _MM_FLUSH_ZERO_MASK)
#define _MM_GET_ROUNDING_MODE() (_mm_getcsr() & _MM_ROUND_MASK)

#define _MM_SET_EXCEPTION_MASK(x) (_mm_setcsr((_mm_getcsr() & ~_MM_MASK_MASK) | (x)))
#define _MM_SET_EXCEPTION_STATE(x) (_mm_setcsr((_mm_getcsr() & ~_MM_EXCEPT_MASK) | (x)))
#define _MM_SET_FLUSH_ZERO_MODE(x) (_mm_setcsr((_mm_getcsr() & ~_MM_FLUSH_ZERO_MASK) | (x)))
#define _MM_SET_ROUNDING_MODE(x) (_mm_setcsr((_mm_getcsr() & ~_MM_ROUND_MASK) | (x)))

#define _MM_TRANSPOSE4_PS(row0, row1, row2, row3) \
do { \
  __m128 tmp3, tmp2, tmp1, tmp0; \
  tmp0 = _mm_unpacklo_ps((row0), (row1)); \
  tmp2 = _mm_unpacklo_ps((row2), (row3)); \
  tmp1 = _mm_unpackhi_ps((row0), (row1)); \
  tmp3 = _mm_unpackhi_ps((row2), (row3)); \
  (row0) = _mm_movelh_ps(tmp0, tmp2); \
  (row1) = _mm_movehl_ps(tmp2, tmp0); \
  (row2) = _mm_movelh_ps(tmp1, tmp3); \
  (row3) = _mm_movehl_ps(tmp3, tmp1); \
} while (0)

/* Aliases for compatibility. */
#define _m_pextrw _mm_extract_pi16
#define _m_pinsrw _mm_insert_pi16
#define _m_pmaxsw _mm_max_pi16
#define _m_pmaxub _mm_max_pu8
#define _m_pminsw _mm_min_pi16
#define _m_pminub _mm_min_pu8
#define _m_pmovmskb _mm_movemask_pi8
#define _m_pmulhuw _mm_mulhi_pu16
#define _m_pshufw _mm_shuffle_pi16
#define _m_maskmovq _mm_maskmove_si64
#define _m_pavgb _mm_avg_pu8
#define _m_pavgw _mm_avg_pu16
#define _m_psadbw _mm_sad_pu8
#define _m_ _mm_
#define _m_ _mm_

#undef __DEFAULT_FN_ATTRS

/* Ugly hack for backwards-compatibility (compatible with gcc) */
#if defined(__SSE2__) && !__building_module(_Builtin_intrinsics)
#include <emmintrin.h>
#endif

#endif /* __XMMINTRIN_H */
