#!/usr/bin/env python3
"""
使用方法：
1. 激活虚拟环境: source .venv/bin/activate
2. 运行安装器: python android11_advanced_cert_installer.py [device_id]
"""
"""
Android 11 高级系统证书安装器
解决Android 11只读系统分区的证书安装问题
支持多种安装策略和自动检测
"""

import os
import sys
import subprocess
import time
import hashlib
import logging
from pathlib import Path
from typing import Optional, Tuple, List

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class Android11CertInstaller:
    """Android 11 系统证书安装器"""
    
    def __init__(self, device_id: str = "emulator-5554"):
        self.device_id = device_id
        self.cert_dir = Path(__file__).parent / "mitm-config"
        self.android_system_cert_dir = "/system/etc/security/cacerts"
        self.temp_cert_path = "/sdcard/mitmproxy_cert.pem"
        
        # 设置ADB路径（优先使用本地路径）
        local_adb = Path(__file__).parent / "platform-tools" / "adb"
        if local_adb.exists():
            self.adb_path = str(local_adb)
        else:
            self.adb_path = "adb"  # 回退到系统路径
        
        # 创建证书目录
        self.cert_dir.mkdir(exist_ok=True)
        
    def run_adb_command(self, command: List[str], timeout: int = 30, check_return_code: bool = True) -> Optional[subprocess.CompletedProcess]:
        """执行ADB命令"""
        try:
            full_command = [self.adb_path, '-s', self.device_id] + command
            logger.debug(f"执行ADB命令: {' '.join(full_command)}")
            
            result = subprocess.run(
                full_command,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            
            if check_return_code and result.returncode != 0:
                logger.warning(f"ADB命令执行失败: {result.stderr.strip()}")
                
            return result
            
        except subprocess.TimeoutExpired:
            logger.error(f"ADB命令超时: {' '.join(command)}")
            return None
        except Exception as e:
            logger.error(f"ADB命令执行异常: {e}")
            return None
    
    def check_device_connection(self) -> bool:
        """检查设备连接状态"""
        logger.info(f"🔍 检查设备连接状态: {self.device_id}")
        
        result = self.run_adb_command(['get-state'], check_return_code=False)
        if result and result.returncode == 0 and 'device' in result.stdout:
            logger.info("✅ 设备连接正常")
            return True
        else:
            logger.error("❌ 设备未连接或连接异常")
            return False
    
    def check_android_version(self) -> Tuple[str, int]:
        """检查Android版本"""
        logger.info("🔍 检查Android版本...")
        
        version_result = self.run_adb_command(['shell', 'getprop', 'ro.build.version.release'])
        sdk_result = self.run_adb_command(['shell', 'getprop', 'ro.build.version.sdk'])
        
        if version_result and sdk_result:
            android_version = version_result.stdout.strip()
            sdk_version = int(sdk_result.stdout.strip() or "0")
            
            logger.info(f"📱 Android版本: {android_version} (SDK {sdk_version})")
            
            if sdk_version >= 30:  # Android 11+
                logger.info("✅ 检测到Android 11+，需要特殊处理")
            else:
                logger.info("ℹ️  Android 10及以下，可使用常规方法")
                
            return android_version, sdk_version
        
        logger.error("❌ 无法获取Android版本信息")
        return "unknown", 0
    
    def check_root_access(self) -> bool:
        """检查Root权限"""
        logger.info("🔍 检查Root权限...")
        
        # 尝试获取root权限
        result = self.run_adb_command(['root'], check_return_code=False)
        if result:
            logger.info("📱 正在获取root权限...")
            time.sleep(2)  # 等待root权限生效
        
        # 验证root权限
        result = self.run_adb_command(['shell', 'id'], check_return_code=False)
        if result and 'uid=0(root)' in result.stdout:
            logger.info("✅ Root权限获取成功")
            return True
        else:
            logger.error("❌ 无法获取Root权限")
            return False
    
    def prepare_mitmproxy_certificate(self) -> Optional[Tuple[Path, str]]:
        """准备mitmproxy证书"""
        logger.info("📜 准备mitmproxy证书...")
        
        # 查找现有证书
        possible_cert_paths = [
            self.cert_dir / "mitmproxy-ca-cert.pem",
            Path.home() / ".mitmproxy" / "mitmproxy-ca-cert.pem",
            Path.cwd() / "mitmproxy-ca-cert.pem",
            Path.cwd() / "mitmproxy-ca-cert.crt"
        ]
        
        cert_path = None
        for path in possible_cert_paths:
            if path.exists():
                cert_path = path
                logger.info(f"✅ 找到现有证书: {cert_path}")
                break
        
        if not cert_path:
            logger.info("⚠️  未找到现有证书，生成新证书...")
            cert_path = self.generate_mitmproxy_certificate()
            
        if not cert_path:
            logger.error("❌ 证书准备失败")
            return None
        
        # 计算证书哈希值
        cert_hash = self.get_certificate_hash(cert_path)
        if not cert_hash:
            logger.error("❌ 证书哈希计算失败")
            return None
            
        logger.info(f"✅ 证书准备完成，哈希: {cert_hash}")
        return cert_path, cert_hash
    
    def generate_mitmproxy_certificate(self) -> Optional[Path]:
        """生成mitmproxy证书"""
        logger.info("🔐 生成mitmproxy证书...")
        
        try:
            # 启动mitmproxy生成证书
            cert_path = self.cert_dir / "mitmproxy-ca-cert.pem"
            
            # 创建临时mitmproxy进程（使用虚拟环境）
            venv_mitmdump = Path('.venv/bin/mitmdump')
            mitmdump_cmd = str(venv_mitmdump) if venv_mitmdump.exists() else 'mitmdump'
            
            proc = subprocess.Popen([
                mitmdump_cmd,
                '--listen-host', '127.0.0.1',
                '--listen-port', '8999',
                '--set', f'confdir={self.cert_dir}'
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            
            # 等待证书生成
            time.sleep(3)
            proc.terminate()
            proc.wait(timeout=10)
            
            if cert_path.exists():
                logger.info(f"✅ 证书生成成功: {cert_path}")
                return cert_path
            else:
                logger.error("❌ 证书文件未生成")
                return None
                
        except Exception as e:
            logger.error(f"证书生成异常: {e}")
            return None
    
    def get_certificate_hash(self, cert_path: Path) -> Optional[str]:
        """获取证书哈希值"""
        try:
            # 使用OpenSSL获取证书哈希
            result = subprocess.run([
                'openssl', 'x509', '-subject_hash_old', '-in', str(cert_path)
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                cert_hash = result.stdout.strip().split('\n')[0]
                return cert_hash
            else:
                logger.error(f"OpenSSL哈希计算失败: {result.stderr}")
                return None
                
        except Exception as e:
            logger.error(f"证书哈希计算异常: {e}")
            return None
    
    def check_system_writable(self) -> bool:
        """检查系统分区是否可写"""
        logger.info("🔍 检查系统分区可写性...")
        
        # 尝试在/system目录创建测试文件
        result = self.run_adb_command([
            'shell', 'touch', '/system/test_write', '2>/dev/null', '&&', 
            'rm', '/system/test_write', '2>/dev/null'
        ], check_return_code=False)
        
        if result and result.returncode == 0:
            logger.info("✅ 系统分区可写")
            return True
        else:
            logger.warning("⚠️  系统分区只读，需要重新挂载")
            return False
    
    def make_system_writable(self) -> bool:
        """使系统分区可写"""
        logger.info("🔧 尝试使系统分区可写...")
        
        strategies = [
            # 策略1: 标准remount
            {
                'name': 'ADB Remount',
                'commands': [['remount']]
            },
            # 策略2: 手动mount
            {
                'name': 'Manual Mount',
                'commands': [
                    ['shell', 'mount', '-o', 'rw,remount', '/system'],
                    ['shell', 'mount', '-o', 'rw,remount', '/'],
                ]
            },
            # 策略3: disable-verity + reboot
            {
                'name': 'Disable Verity',
                'commands': [
                    ['disable-verity'],
                    ['reboot']
                ]
            }
        ]
        
        for strategy in strategies:
            logger.info(f"尝试策略: {strategy['name']}")
            
            try:
                success = True
                for command in strategy['commands']:
                    result = self.run_adb_command(command, check_return_code=False)
                    
                    if 'reboot' in command:
                        logger.info("⏳ 等待设备重启...")
                        time.sleep(10)
                        
                        # 等待设备重新连接
                        for _ in range(30):
                            if self.check_device_connection():
                                break
                            time.sleep(2)
                        else:
                            logger.error("❌ 设备重启后无法连接")
                            success = False
                            break
                            
                        # 重新获取root权限
                        if not self.check_root_access():
                            success = False
                            break
                
                if success and self.check_system_writable():
                    logger.info(f"✅ 策略 {strategy['name']} 成功")
                    return True
                    
            except Exception as e:
                logger.error(f"策略 {strategy['name']} 执行异常: {e}")
                continue
        
        logger.error("❌ 所有策略都失败，无法使系统分区可写")
        return False
    
    def install_system_certificate(self, cert_path: Path, cert_hash: str) -> bool:
        """安装系统证书"""
        logger.info("🔐 安装系统证书...")
        
        try:
            # 1. 推送证书到临时目录
            logger.info("推送证书到设备...")
            result = self.run_adb_command(['push', str(cert_path), self.temp_cert_path])
            if not result or result.returncode != 0:
                logger.error("❌ 证书推送失败")
                return False
            
            # 2. 复制到系统证书目录
            system_cert_name = f"{cert_hash}.0"
            system_cert_path = f"{self.android_system_cert_dir}/{system_cert_name}"
            
            logger.info(f"安装证书到: {system_cert_path}")
            result = self.run_adb_command([
                'shell', 'cp', self.temp_cert_path, system_cert_path
            ])
            if not result or result.returncode != 0:
                logger.error("❌ 证书复制到系统目录失败")
                return False
            
            # 3. 设置正确的权限
            logger.info("设置证书权限...")
            commands = [
                ['shell', 'chmod', '644', system_cert_path],
                ['shell', 'chown', 'root:root', system_cert_path]
            ]
            
            for command in commands:
                result = self.run_adb_command(command)
                if not result or result.returncode != 0:
                    logger.warning(f"⚠️  权限设置命令执行异常: {' '.join(command)}")
            
            # 4. 验证安装
            result = self.run_adb_command([
                'shell', 'ls', '-la', system_cert_path
            ])
            if result and result.returncode == 0:
                logger.info("✅ 系统证书安装成功")
                logger.info(f"证书文件: {result.stdout.strip()}")
                
                # 清理临时文件
                self.run_adb_command(['shell', 'rm', self.temp_cert_path], check_return_code=False)
                
                return True
            else:
                logger.error("❌ 证书安装验证失败")
                return False
                
        except Exception as e:
            logger.error(f"系统证书安装异常: {e}")
            return False
    
    def configure_proxy(self, proxy_host: str = "********", proxy_port: int = 8080) -> bool:
        """配置系统代理"""
        logger.info(f"🌐 配置系统代理: {proxy_host}:{proxy_port}")
        
        try:
            # 设置全局HTTP代理
            result = self.run_adb_command([
                'shell', 'settings', 'put', 'global', 'http_proxy', 
                f"{proxy_host}:{proxy_port}"
            ])
            
            if result and result.returncode == 0:
                # 验证代理设置
                verify_result = self.run_adb_command([
                    'shell', 'settings', 'get', 'global', 'http_proxy'
                ])
                
                if verify_result and f"{proxy_host}:{proxy_port}" in verify_result.stdout:
                    logger.info("✅ 系统代理配置成功")
                    return True
                    
            logger.error("❌ 系统代理配置失败")
            return False
            
        except Exception as e:
            logger.error(f"代理配置异常: {e}")
            return False
    
    def verify_certificate_installation(self, cert_hash: str) -> bool:
        """验证证书安装"""
        logger.info("🔍 验证证书安装...")
        
        try:
            system_cert_path = f"{self.android_system_cert_dir}/{cert_hash}.0"
            
            # 检查证书文件是否存在
            result = self.run_adb_command([
                'shell', 'test', '-f', system_cert_path, '&&', 'echo', 'exists'
            ])
            
            if result and 'exists' in result.stdout:
                logger.info("✅ 系统证书文件存在")
                
                # 检查证书权限
                result = self.run_adb_command([
                    'shell', 'ls', '-la', system_cert_path
                ])
                
                if result and result.returncode == 0:
                    logger.info(f"📋 证书权限: {result.stdout.strip()}")
                    
                    # 建议重启以确保证书生效
                    logger.info("💡 建议重启设备以确保证书完全生效")
                    return True
                    
            logger.error("❌ 证书验证失败")
            return False
            
        except Exception as e:
            logger.error(f"证书验证异常: {e}")
            return False
    
    def run_installation(self) -> bool:
        """运行完整的安装流程"""
        logger.info("=" * 60)
        logger.info("🚀 Android 11 系统证书安装器")
        logger.info("=" * 60)
        
        steps = [
            ("检查设备连接", self.check_device_connection),
            ("检查Android版本", lambda: self.check_android_version()[1] >= 30),
            ("获取Root权限", self.check_root_access),
        ]
        
        # 执行前置检查
        for step_name, step_func in steps:
            logger.info(f"执行步骤: {step_name}")
            if not step_func():
                logger.error(f"❌ 步骤失败: {step_name}")
                return False
        
        # 准备证书
        logger.info("执行步骤: 准备证书")
        cert_info = self.prepare_mitmproxy_certificate()
        if not cert_info:
            logger.error("❌ 证书准备失败")
            return False
        
        cert_path, cert_hash = cert_info
        
        # 检查并处理系统分区
        logger.info("执行步骤: 检查系统分区")
        if not self.check_system_writable():
            logger.info("执行步骤: 使系统分区可写")
            if not self.make_system_writable():
                logger.error("❌ 无法使系统分区可写")
                return False
        
        # 安装系统证书
        logger.info("执行步骤: 安装系统证书")
        if not self.install_system_certificate(cert_path, cert_hash):
            logger.error("❌ 系统证书安装失败")
            return False
        
        # 配置代理
        logger.info("执行步骤: 配置系统代理")
        if not self.configure_proxy():
            logger.warning("⚠️  代理配置失败，可手动设置")
        
        # 验证安装
        logger.info("执行步骤: 验证安装")
        if self.verify_certificate_installation(cert_hash):
            logger.info("\n🎉 Android 11 系统证书安装完成！")
            logger.info("\n📋 后续步骤:")
            logger.info("1. 重启设备: adb reboot")
            logger.info("2. 启动mitmproxy: mitmdump -p 8080")
            logger.info("3. 验证证书: 设置 → 安全 → 信任的凭据 → 系统")
            logger.info("4. 测试HTTPS捕获")
            
            return True
        else:
            logger.error("❌ 证书安装验证失败")
            return False

def main():
    """主函数"""
    if len(sys.argv) > 1:
        device_id = sys.argv[1]
    else:
        device_id = "emulator-5554"
    
    installer = Android11CertInstaller(device_id)
    
    try:
        success = installer.run_installation()
        return 0 if success else 1
    except KeyboardInterrupt:
        logger.info("\n⏹️  用户中断安装")
        return 1
    except Exception as e:
        logger.error(f"安装过程异常: {e}")
        return 1

if __name__ == "__main__":
    exit(main())