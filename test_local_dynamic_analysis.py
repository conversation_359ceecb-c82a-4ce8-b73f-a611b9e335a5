#!/usr/bin/env python3
"""
本地模拟器动态分析测试
验证本地模拟器动态分析功能的完整性
"""
import asyncio
import logging
import sys
from pathlib import Path
import time
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.local_emulator_manager import LocalEmulatorManager
from src.services.local_dynamic_analyzer import LocalDynamicAnalyzer
from src.services.local_ui_automator import LocalUIAutomator
from src.services.static_analyzer import AnalysisTools

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class LocalDynamicAnalysisTester:
    """本地动态分析测试类"""
    
    def __init__(self):
        self.test_results = {}
        self.test_apk = project_root / "apk" / "com.android.vending_289.com.apk"
        
        # 确保测试APK存在
        if not self.test_apk.exists():
            logger.error(f"Test APK not found: {self.test_apk}")
            raise FileNotFoundError(f"Test APK not found: {self.test_apk}")
        
        logger.info(f"Using test APK: {self.test_apk}")
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 Starting Local Dynamic Analysis Tests")
        print("=" * 80)
        
        tests = [
            ("Test Emulator Manager", self.test_emulator_manager),
            ("Test UI Automator", self.test_ui_automator),
            ("Test Local Dynamic Analyzer", self.test_local_dynamic_analyzer),
            ("Test Complete Analysis Flow", self.test_complete_analysis_flow),
        ]
        
        for test_name, test_func in tests:
            try:
                logger.info(f"Running: {test_name}")
                result = await test_func()
                self.test_results[test_name] = {
                    'status': 'PASS' if result else 'FAIL',
                    'details': result if isinstance(result, dict) else {}
                }
                status = "✅ PASS" if result else "❌ FAIL"
                logger.info(f"{test_name}: {status}")
            except Exception as e:
                logger.error(f"{test_name}: ❌ ERROR - {e}")
                self.test_results[test_name] = {
                    'status': 'ERROR',
                    'error': str(e)
                }
            
            print("-" * 40)
        
        # 生成测试报告
        self.generate_test_report()
    
    async def test_emulator_manager(self) -> bool:
        """测试模拟器管理器"""
        try:
            logger.info("Testing LocalEmulatorManager...")
            
            # 创建模拟器管理器
            emulator_manager = LocalEmulatorManager(max_instances=1)
            
            # 测试状态获取
            status = emulator_manager.get_status_summary()
            logger.info(f"Emulator manager status: {status}")
            
            # 测试AVD列表
            avds = await emulator_manager.list_available_avds()
            logger.info(f"Available AVDs: {avds}")
            
            # 如果没有AVD，尝试创建一个
            if not avds:
                logger.info("No AVDs found, creating test AVD...")
                success = await emulator_manager.create_default_avd("test_avd")
                if success:
                    logger.info("Test AVD created successfully")
                else:
                    logger.warning("Failed to create test AVD")
            
            # 测试启动监控
            await emulator_manager.start_monitoring()
            logger.info("Monitoring started")
            
            # 清理
            await emulator_manager.stop_monitoring()
            logger.info("Monitoring stopped")
            
            return True
            
        except Exception as e:
            logger.error(f"Emulator manager test failed: {e}")
            return False
    
    async def test_ui_automator(self) -> bool:
        """测试UI自动化器"""
        try:
            logger.info("Testing LocalUIAutomator...")
            
            # 创建UI自动化器
            ui_automator = LocalUIAutomator()
            
            # 测试基本功能（不需要真实模拟器）
            logger.info("UI automator created successfully")
            
            # 测试状态重置
            ui_automator.reset_state()
            
            # 获取交互摘要
            summary = ui_automator.get_interaction_summary()
            logger.info(f"Interaction summary: {summary}")
            
            return True
            
        except Exception as e:
            logger.error(f"UI automator test failed: {e}")
            return False
    
    async def test_local_dynamic_analyzer(self) -> bool:
        """测试本地动态分析器"""
        try:
            logger.info("Testing LocalDynamicAnalyzer...")
            
            # 创建分析器
            analyzer = LocalDynamicAnalyzer()
            
            # 测试包名提取
            package_name = await analyzer._extract_package_name(str(self.test_apk))
            if package_name:
                logger.info(f"Extracted package name: {package_name}")
            else:
                logger.warning("Failed to extract package name")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Local dynamic analyzer test failed: {e}")
            return False
    
    async def test_complete_analysis_flow(self) -> dict:
        """测试完整分析流程（模拟模式）"""
        try:
            logger.info("Testing complete analysis flow (simulation mode)...")
            
            # 创建分析器
            analyzer = LocalDynamicAnalyzer()
            
            # 模拟分析（不启动真实模拟器）
            start_time = time.time()
            
            # 提取包名
            package_name = await analyzer._extract_package_name(str(self.test_apk))
            if not package_name:
                logger.error("Failed to extract package name")
                return False
            
            # 模拟分析结果
            duration = time.time() - start_time
            
            result_summary = {
                'package_name': package_name,
                'duration': duration,
                'test_mode': 'simulation',
                'components_tested': [
                    'package_extraction',
                    'analyzer_initialization',
                    'ui_automator_integration'
                ]
            }
            
            logger.info(f"Simulation analysis completed: {result_summary}")
            return result_summary
            
        except Exception as e:
            logger.error(f"Complete analysis flow test failed: {e}")
            return False
    
    def generate_test_report(self):
        """生成测试报告"""
        logger.info("📊 Generating Test Report")
        print("=" * 80)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result['status'] == 'PASS')
        failed_tests = sum(1 for result in self.test_results.values() if result['status'] == 'FAIL')
        error_tests = sum(1 for result in self.test_results.values() if result['status'] == 'ERROR')
        
        print(f"📈 Test Summary:")
        print(f"   Total Tests: {total_tests}")
        print(f"   ✅ Passed: {passed_tests}")
        print(f"   ❌ Failed: {failed_tests}")
        print(f"   🚨 Errors: {error_tests}")
        print(f"   📊 Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        print()
        
        print("📋 Detailed Results:")
        for test_name, result in self.test_results.items():
            status_icon = {
                'PASS': '✅',
                'FAIL': '❌',
                'ERROR': '🚨'
            }.get(result['status'], '❓')
            
            print(f"   {status_icon} {test_name}: {result['status']}")
            
            if result['status'] == 'ERROR' and 'error' in result:
                print(f"      Error: {result['error']}")
            elif 'details' in result and result['details']:
                if isinstance(result['details'], dict):
                    for key, value in result['details'].items():
                        print(f"      {key}: {value}")
        
        print()
        
        # 保存测试报告到文件
        report_file = project_root / "local_dynamic_test_report.json"
        try:
            with open(report_file, 'w') as f:
                json.dump({
                    'timestamp': time.time(),
                    'summary': {
                        'total': total_tests,
                        'passed': passed_tests,
                        'failed': failed_tests,
                        'errors': error_tests,
                        'success_rate': (passed_tests/total_tests)*100
                    },
                    'results': self.test_results
                }, f, indent=2)
            
            logger.info(f"📄 Test report saved to: {report_file}")
        except Exception as e:
            logger.error(f"Failed to save test report: {e}")
        
        # 总结
        if passed_tests == total_tests:
            print("🎉 All tests passed! Local dynamic analysis system is ready.")
        elif passed_tests > 0:
            print("⚠️  Some tests passed. System partially functional.")
        else:
            print("🚨 All tests failed. System needs attention.")
        
        print("=" * 80)


async def main():
    """主函数"""
    try:
        tester = LocalDynamicAnalysisTester()
        await tester.run_all_tests()
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
    except Exception as e:
        logger.error(f"Test execution failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
