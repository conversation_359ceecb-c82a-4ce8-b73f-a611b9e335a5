[14:40:34.602] HTTP(S) proxy listening at *:8888.
[14:40:57.492][127.0.0.1:51622] client connect
[14:40:57.501][127.0.0.1:51623] client connect
[14:40:57.507][127.0.0.1:51622] server connect www.google.com:443 (198.18.0.22:443)
[14:40:57.611][127.0.0.1:51623] server connect connectivitycheck.gstatic.com:80 (198.18.0.110:80)
127.0.0.1:51623: GET http://connectivitycheck.gstatic.com/generate_204
              << 204 No Content 0b
[14:40:57.782][127.0.0.1:51623] server disconnect connectivitycheck.gstatic.com:80 (198.18.0.110:80)
[14:40:57.786][127.0.0.1:51623] client disconnect
[14:40:57.787][127.0.0.1:51622] Client TLS handshake failed. The client does not trust the proxy's certificate for www.google.com (OpenSSL Error([('SSL routines', '', 'sslv3 alert certificate unknown')]))
[14:40:57.791][127.0.0.1:51622] client disconnect
[14:40:57.794][127.0.0.1:51622] server disconnect www.google.com:443 (198.18.0.22:443)
[14:40:57.804][127.0.0.1:51628] client connect
[14:40:57.818][127.0.0.1:51628] server connect www.google.com:80 (198.18.0.22:80)
127.0.0.1:51628: GET http://www.google.com/gen_204
              << 204 No Content 0b
[14:40:58.045][127.0.0.1:51628] server disconnect www.google.com:80 (198.18.0.22:80)
[14:40:58.046][127.0.0.1:51628] client disconnect
[14:42:02.131][127.0.0.1:51762] client connect
[14:42:02.146][127.0.0.1:51763] client connect
[14:42:02.151][127.0.0.1:51762] server connect connectivitycheck.gstatic.com:80 (198.18.0.110:80)
[14:42:02.156][127.0.0.1:51763] server connect www.google.com:443 (198.18.0.22:443)
127.0.0.1:51762: GET http://connectivitycheck.gstatic.com/generate_204
              << 204 No Content 0b
[14:42:02.246][127.0.0.1:51762] server disconnect connectivitycheck.gstatic.com:80 (198.18.0.110:80)
[14:42:02.247][127.0.0.1:51762] client disconnect
[14:42:02.378][127.0.0.1:51763] Client TLS handshake failed. The client does not trust the proxy's certificate for www.google.com (OpenSSL Error([('SSL routines', '', 'sslv3 alert certificate unknown')]))
[14:42:02.379][127.0.0.1:51763] client disconnect
[14:42:02.380][127.0.0.1:51763] server disconnect www.google.com:443 (198.18.0.22:443)
[14:42:02.385][127.0.0.1:51768] client connect
[14:42:02.390][127.0.0.1:51768] server connect www.google.com:80 (198.18.0.22:80)
127.0.0.1:51768: GET http://www.google.com/gen_204
              << 204 No Content 0b
[14:42:02.643][127.0.0.1:51768] server disconnect www.google.com:80 (198.18.0.22:80)
[14:42:02.644][127.0.0.1:51768] client disconnect
