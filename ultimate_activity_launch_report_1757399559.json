{"app_info": {"app_name": "Liuwa", "package_name": "com.iloda.beacon", "total_activities": 40, "analysis_timestamp": "2025-09-09T14:32:39.772787"}, "launch_statistics": {"total_attempts": 40, "successful_launches": 40, "success_rate_percent": 100.0, "method_breakdown": {"标准启动": 40}}, "activity_details": [{"activity": ".activity.LoginActivity", "full_name": "com.iloda.beacon.activity.LoginActivity", "exported": false, "permissions": [], "success": true, "method": "标准启动"}, {"activity": ".activity.AddKidGuidlineActivity", "full_name": "com.iloda.beacon.activity.AddKidGuidlineActivity", "exported": false, "permissions": [], "success": true, "method": "标准启动"}, {"activity": ".activity.MainActivity", "full_name": "com.iloda.beacon.activity.MainActivity", "exported": false, "permissions": [], "success": true, "method": "标准启动"}, {"activity": ".activity.AddDeviceGuidlineActivity", "full_name": "com.iloda.beacon.activity.AddDeviceGuidlineActivity", "exported": false, "permissions": [], "success": true, "method": "标准启动"}, {"activity": ".activity.MyKidManagementActivity", "full_name": "com.iloda.beacon.activity.MyKidManagementActivity", "exported": false, "permissions": [], "success": true, "method": "标准启动"}, {"activity": ".activity.SendSOSActivity", "full_name": "com.iloda.beacon.activity.SendSOSActivity", "exported": false, "permissions": [], "success": true, "method": "标准启动"}, {"activity": ".activity.ViewKidStatusOfRealtimeActivity", "full_name": "com.iloda.beacon.activity.ViewKidStatusOfRealtimeActivity", "exported": false, "permissions": [], "success": true, "method": "标准启动"}, {"activity": ".activity.MyKidDetail4KidManagementActivity", "full_name": "com.iloda.beacon.activity.MyKidDetail4KidManagementActivity", "exported": false, "permissions": [], "success": true, "method": "标准启动"}, {"activity": ".activity.AddDeviceActivity", "full_name": "com.iloda.beacon.activity.AddDeviceActivity", "exported": false, "permissions": [], "success": true, "method": "标准启动"}, {"activity": ".activity.ViewDeviceActivity", "full_name": "com.iloda.beacon.activity.ViewDeviceActivity", "exported": false, "permissions": [], "success": true, "method": "标准启动"}, {"activity": ".activity.AddKeeperActivity", "full_name": "com.iloda.beacon.activity.AddKeeperActivity", "exported": false, "permissions": [], "success": true, "method": "标准启动"}, {"activity": ".activity.ViewKeeperActivity", "full_name": "com.iloda.beacon.activity.ViewKeeperActivity", "exported": false, "permissions": [], "success": true, "method": "标准启动"}, {"activity": ".activity.AddKidActivity", "full_name": "com.iloda.beacon.activity.AddKidActivity", "exported": false, "permissions": [], "success": true, "method": "标准启动"}, {"activity": ".activity.MyDeviceManagementActivity", "full_name": "com.iloda.beacon.activity.MyDeviceManagementActivity", "exported": false, "permissions": [], "success": true, "method": "标准启动"}, {"activity": ".activity.PersonalDetailActivity", "full_name": "com.iloda.beacon.activity.PersonalDetailActivity", "exported": false, "permissions": [], "success": true, "method": "标准启动"}, {"activity": ".activity.MoreSettingActivity", "full_name": "com.iloda.beacon.activity.MoreSettingActivity", "exported": false, "permissions": [], "success": true, "method": "标准启动"}, {"activity": ".activity.SensitivitySettingActivity", "full_name": "com.iloda.beacon.activity.SensitivitySettingActivity", "exported": false, "permissions": [], "success": true, "method": "标准启动"}, {"activity": ".activity.TooFarSettingActivity", "full_name": "com.iloda.beacon.activity.TooFarSettingActivity", "exported": false, "permissions": [], "success": true, "method": "标准启动"}, {"activity": ".activity.ChangeLocaltionModeActivity", "full_name": "com.iloda.beacon.activity.ChangeLocaltionModeActivity", "exported": false, "permissions": [], "success": true, "method": "标准启动"}, {"activity": ".activity.AboutUSActivity", "full_name": "com.iloda.beacon.activity.AboutUSActivity", "exported": false, "permissions": [], "success": true, "method": "标准启动"}, {"activity": ".activity.DeviceDetectorActivity", "full_name": "com.iloda.beacon.activity.DeviceDetectorActivity", "exported": false, "permissions": [], "success": true, "method": "标准启动"}, {"activity": ".activity.KidDetail4DeviceDetectorActivity", "full_name": "com.iloda.beacon.activity.KidDetail4DeviceDetectorActivity", "exported": false, "permissions": [], "success": true, "method": "标准启动"}, {"activity": ".activity.ViewKidStatusOfNoWatcherActivity", "full_name": "com.iloda.beacon.activity.ViewKidStatusOfNoWatcherActivity", "exported": false, "permissions": [], "success": true, "method": "标准启动"}, {"activity": ".activity.ViewKidStatusOfSOSActivity", "full_name": "com.iloda.beacon.activity.ViewKidStatusOfSOSActivity", "exported": false, "permissions": [], "success": true, "method": "标准启动"}, {"activity": ".activity.ViewKidStatusOfSOSClueActivity", "full_name": "com.iloda.beacon.activity.ViewKidStatusOfSOSClueActivity", "exported": false, "permissions": [], "success": true, "method": "标准启动"}, {"activity": ".activity.MyKidPersonalData4KidManagementActivity", "full_name": "com.iloda.beacon.activity.MyKidPersonalData4KidManagementActivity", "exported": false, "permissions": [], "success": true, "method": "标准启动"}, {"activity": ".activity.GuideActivity", "full_name": "com.iloda.beacon.activity.GuideActivity", "exported": false, "permissions": [], "success": true, "method": "标准启动"}, {"activity": ".activity.SplashActivity", "full_name": "com.iloda.beacon.activity.SplashActivity", "exported": false, "permissions": [], "success": true, "method": "标准启动"}, {"activity": ".activity.ChangeLanguageActivity", "full_name": "com.iloda.beacon.activity.ChangeLanguageActivity", "exported": false, "permissions": [], "success": true, "method": "标准启动"}, {"activity": ".activity.country.CountryActivity", "full_name": "com.iloda.beacon.activity.country.CountryActivity", "exported": false, "permissions": [], "success": true, "method": "标准启动"}, {"activity": ".wxapi.WXEntryActivity", "full_name": "com.iloda.beacon.wxapi.WXEntryActivity", "exported": true, "permissions": [], "success": true, "method": "标准启动"}, {"activity": ".activity.PrepareDeviceActivity", "full_name": "com.iloda.beacon.activity.PrepareDeviceActivity", "exported": false, "permissions": [], "success": true, "method": "标准启动"}, {"activity": ".activity.PrepareDevice4GuidlineActivity", "full_name": "com.iloda.beacon.activity.PrepareDevice4GuidlineActivity", "exported": false, "permissions": [], "success": true, "method": "标准启动"}, {"activity": ".activity.AfterAddDevicePageActivity", "full_name": "com.iloda.beacon.activity.AfterAddDevicePageActivity", "exported": false, "permissions": [], "success": true, "method": "标准启动"}, {"activity": ".activity.AfterAddKeeperPageActivity", "full_name": "com.iloda.beacon.activity.AfterAddKeeperPageActivity", "exported": false, "permissions": [], "success": true, "method": "标准启动"}, {"activity": ".activity.AfterAddDeviceGuidlinePageActivity", "full_name": "com.iloda.beacon.activity.AfterAddDeviceGuidlinePageActivity", "exported": false, "permissions": [], "success": true, "method": "标准启动"}, {"activity": ".activity.DeclareActivity", "full_name": "com.iloda.beacon.activity.DeclareActivity", "exported": false, "permissions": [], "success": true, "method": "标准启动"}, {"activity": ".activity.RadarActivity", "full_name": "com.iloda.beacon.activity.RadarActivity", "exported": false, "permissions": [], "success": true, "method": "标准启动"}, {"activity": ".activity.BDPropertyActivity", "full_name": "com.iloda.beacon.activity.BDPropertyActivity", "exported": false, "permissions": ["com.tencent.mm.plugin.permission.SEND"], "success": true, "method": "标准启动"}, {"activity": ".PushActivity", "full_name": "cn.jpush.android.ui.PushActivity", "exported": false, "permissions": [], "success": true, "method": "标准启动"}], "recommendations": ["🎉 完美！所有Activity都成功启动了！"]}