"""
mitmproxy script for capturing network traffic from Android apps
"""
import json
import os
try:
    import redis  # optional
except Exception:
    redis = None
import time
from datetime import datetime
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from mitmproxy import http
from typing import Dict, Any
import logging

logger = logging.getLogger(__name__)


class NetworkCapture:
    def __init__(self):
        # Connect to Redis for real-time data sharing (optional)
        self.redis_client = None
        if redis is not None:
            try:
                self.redis_client = redis.Redis(host='redis', port=6379, db=3, decode_responses=True)
                self.redis_client.ping()
            except Exception as e:
                logger.warning(f"Redis connection failed: {e}")
                self.redis_client = None
        
        self.captured_requests = []
        self.session_id = None
        
        # System domains to filter out
        self.system_domains = {
            'google.com', 'googleapis.com', 'gstatic.com', 'googleusercontent.com',
            'doubleclick.net', 'googlesyndication.com', 'googleadservices.com',
            'android.com', 'mozilla.org', 'firefox.com', 'gvt1.com',
            'crashlytics.com', 'fabric.io', 'firebase.com'
        }
        
        # Initialize capture session
        self._initialize_session()
    
    def _initialize_session(self):
        """Initialize a new capture session"""
        self.session_id = f"session_{int(time.time())}"
        logger.info(f"Starting new capture session: {self.session_id}")
        
        if self.redis_client:
            session_info = {
                'session_id': self.session_id,
                'started_at': datetime.now().isoformat(),
                'status': 'active'
            }
            self.redis_client.hset(f"mitm:session:{self.session_id}", mapping=session_info)
    
    def request(self, flow: Any) -> None:
        """Capture HTTP/HTTPS requests"""
        try:
            request_info = self._extract_request_info(flow)
            
            if self._should_capture(flow.request):
                self.captured_requests.append(request_info)
                
                # Save to Redis for real-time access
                if self.redis_client:
                    self._save_request_to_redis(request_info)
                
                logger.debug(f"Captured request: {request_info['method']} {request_info['url']}")
        
        except Exception as e:
            logger.error(f"Failed to capture request: {e}")
    
    def response(self, flow: Any) -> None:
        """Capture HTTP/HTTPS responses"""
        try:
            if self._should_capture(flow.request):
                response_info = self._extract_response_info(flow)
                
                # Update the corresponding request with response data
                self._update_request_with_response(flow.request.pretty_url, response_info)
                
                logger.debug(f"Captured response: {flow.response.status_code} for {flow.request.pretty_url}")
        
        except Exception as e:
            logger.error(f"Failed to capture response: {e}")
    
    def _extract_request_info(self, flow: Any) -> Dict[str, Any]:
        """Extract request information"""
        request = flow.request
        
        return {
            'timestamp': flow.request.timestamp_start,
            'datetime': datetime.fromtimestamp(flow.request.timestamp_start).isoformat(),
            'method': request.method,
            'url': request.pretty_url,
            'host': request.pretty_host,
            'path': request.path,
            'headers': dict(request.headers),
            'content_length': len(request.content) if request.content else 0,
            'content_type': request.headers.get('content-type', ''),
            'session_id': self.session_id
        }
    
    def _extract_response_info(self, flow: Any) -> Dict[str, Any]:
        """Extract response information"""
        response = flow.response
        
        return {
            'status_code': response.status_code,
            'headers': dict(response.headers),
            'content_type': response.headers.get('content-type', ''),
            'content_length': len(response.content) if response.content else 0,
            'response_time': flow.response.timestamp_end - flow.request.timestamp_start if flow.response.timestamp_end else None
        }
    
    def _should_capture(self, request) -> bool:
        """Determine if request should be captured"""
        host = request.pretty_host.lower()
        
        # Filter out system and advertising domains
        for system_domain in self.system_domains:
            if system_domain in host:
                return False
        
        # Filter out common ad/analytics patterns
        ad_patterns = ['ads', 'analytics', 'tracking', 'telemetry', 'crash', 'metrics']
        if any(pattern in host for pattern in ad_patterns):
            return False
        
        # Only capture HTTP/HTTPS
        if request.scheme not in ['http', 'https']:
            return False
        
        return True
    
    def _save_request_to_redis(self, request_info: Dict[str, Any]):
        """Save request to Redis for real-time access"""
        try:
            if not self.redis_client:
                # Fallback to file-based storage
                self._save_request_to_file(request_info)
                return
            
            # Save individual request
            request_key = f"mitm:request:{self.session_id}:{request_info['timestamp']}"
            
            # Convert headers to JSON string for Redis storage
            request_data = request_info.copy()
            if 'headers' in request_data and isinstance(request_data['headers'], dict):
                request_data['headers'] = json.dumps(request_data['headers'])
            
            self.redis_client.hset(request_key, mapping=request_data)
            self.redis_client.expire(request_key, 3600)  # Expire after 1 hour
            
            # Add to session request list
            session_requests_key = f"mitm:session:{self.session_id}:requests"
            self.redis_client.lpush(session_requests_key, request_key)
            self.redis_client.expire(session_requests_key, 3600)
            
            # Update session statistics
            session_stats_key = f"mitm:session:{self.session_id}:stats"
            self.redis_client.hincrby(session_stats_key, 'total_requests', 1)
            self.redis_client.hincrby(session_stats_key, f"method_{request_info['method']}", 1)
            self.redis_client.expire(session_stats_key, 3600)
            
            # Also save to file as backup
            self._save_request_to_file(request_info)
        
        except Exception as e:
            logger.error(f"Failed to save request to Redis: {e}")
            # Fallback to file storage
            self._save_request_to_file(request_info)

    def _save_request_to_file(self, request_info: Dict[str, Any]):
        """Save request to file as backup/fallback"""
        try:
            # Ensure logs directory exists
            log_dir = "/logs" if os.path.exists("/logs") else "mitm-logs"
            os.makedirs(log_dir, exist_ok=True)

            # Save to session-specific file
            log_file = os.path.join(log_dir, f"session_{self.session_id}.json")

            # Load existing data or create new list
            requests_data = []
            if os.path.exists(log_file):
                try:
                    with open(log_file, 'r') as f:
                        requests_data = json.load(f)
                except (json.JSONDecodeError, IOError):
                    requests_data = []

            # Add new request
            requests_data.append(request_info)

            # Keep only last 1000 requests to prevent file from growing too large
            if len(requests_data) > 1000:
                requests_data = requests_data[-1000:]

            # Save back to file
            with open(log_file, 'w') as f:
                json.dump(requests_data, f, indent=2)

            # Also save to general capture file
            general_log_file = os.path.join(log_dir, "captured_requests.json")
            with open(general_log_file, 'w') as f:
                json.dump(requests_data[-100:], f, indent=2)  # Keep last 100 for general access

            # CRITICAL: Save to realtime_capture.json for testing
            realtime_file = os.path.join(log_dir, "realtime_capture.json")
            with open(realtime_file, 'w') as f:
                json.dump(requests_data, f, indent=2)

        except Exception as e:
            logger.error(f"Failed to save request to file: {e}")
    
    def _update_request_with_response(self, url: str, response_info: Dict[str, Any]):
        """Update request record with response information"""
        try:
            # Update in local cache
            for request in self.captured_requests:
                if request['url'] == url:
                    request.update(response_info)
                    break
            
            # Update in Redis
            if self.redis_client:
                session_requests_key = f"mitm:session:{self.session_id}:requests"
                request_keys = self.redis_client.lrange(session_requests_key, 0, -1)
                
                for request_key in request_keys:
                    request_data = self.redis_client.hgetall(request_key)
                    if request_data.get('url') == url:
                        self.redis_client.hset(request_key, mapping=response_info)
                        break
        
        except Exception as e:
            logger.error(f"Failed to update request with response: {e}")
    
    def get_session_requests(self) -> list:
        """Get all requests for current session"""
        return self.captured_requests.copy()
    
    def clear_session(self):
        """Clear current session data"""
        self.captured_requests.clear()
        if self.redis_client and self.session_id:
            # Clean up Redis data
            pattern = f"mitm:*:{self.session_id}:*"
            keys = self.redis_client.keys(pattern)
            if keys:
                self.redis_client.delete(*keys)


# Global capture instance
capture = NetworkCapture()


def request(flow: Any) -> None:
    """mitmproxy request handler"""
    capture.request(flow)


def response(flow: Any) -> None:
    """mitmproxy response handler"""
    capture.response(flow)


def load(loader):
    """mitmproxy addon load handler"""
    logger.info("Network capture script loaded")


def done():
    """mitmproxy addon cleanup handler"""
    logger.info("Network capture script unloaded")
    capture.clear_session()