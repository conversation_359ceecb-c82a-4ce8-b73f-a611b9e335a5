"""
本地模拟器专用的mitmproxy网络捕获脚本
优化了与本地Android模拟器的集成
"""
import json
import os
import time
from datetime import datetime
from typing import Dict, Any, Optional
import logging
from pathlib import Path

logger = logging.getLogger(__name__)


class LocalNetworkCapture:
    """本地网络捕获器"""
    
    def __init__(self):
        self.captured_requests = []
        self.session_id = os.getenv('MITM_SESSION_ID', f"local_session_{int(time.time())}")
        self.output_dir = Path(os.getenv('MITM_OUTPUT_DIR', 'mitm-logs'))
        self.output_dir.mkdir(exist_ok=True)
        
        # 本地模拟器特定的过滤规则
        self.system_domains = {
            'google.com', 'googleapis.com', 'gstatic.com', 'googleusercontent.com',
            'doubleclick.net', 'googlesyndication.com', 'googleadservices.com',
            'android.com', 'mozilla.org', 'firefox.com', 'gvt1.com',
            'crashlytics.com', 'fabric.io', 'firebase.com',
            'localhost', '127.0.0.1', '********'  # 本地地址
        }
        
        # 广告和分析域名模式
        self.ad_patterns = {
            'ads', 'analytics', 'tracking', 'telemetry', 'crash', 'metrics',
            'doubleclick', 'googlesyndication', 'googleadservices',
            'facebook.com', 'fbcdn.net', 'twitter.com', 'linkedin.com'
        }
        
        # 初始化会话
        self._initialize_session()
        
        logger.info(f"LocalNetworkCapture initialized for session: {self.session_id}")
    
    def _initialize_session(self):
        """初始化捕获会话"""
        session_info = {
            'session_id': self.session_id,
            'started_at': datetime.now().isoformat(),
            'status': 'active',
            'capture_type': 'local_emulator'
        }
        
        # 保存会话信息
        session_file = self.output_dir / f"session_{self.session_id}_info.json"
        try:
            with open(session_file, 'w') as f:
                json.dump(session_info, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save session info: {e}")
    
    def request(self, flow) -> None:
        """捕获HTTP/HTTPS请求"""
        try:
            request_info = self._extract_request_info(flow)
            
            if self._should_capture(flow.request):
                self.captured_requests.append(request_info)
                self._save_request_to_file(request_info)
                
                logger.debug(f"Captured request: {request_info['method']} {request_info['url']}")
        
        except Exception as e:
            logger.error(f"Failed to capture request: {e}")
    
    def response(self, flow) -> None:
        """捕获HTTP/HTTPS响应"""
        try:
            if self._should_capture(flow.request):
                response_info = self._extract_response_info(flow)
                
                # 更新对应的请求记录
                self._update_request_with_response(flow.request.pretty_url, response_info)
                
                logger.debug(f"Captured response: {flow.response.status_code} for {flow.request.pretty_url}")
        
        except Exception as e:
            logger.error(f"Failed to capture response: {e}")
    
    def _extract_request_info(self, flow) -> Dict[str, Any]:
        """提取请求信息"""
        request = flow.request
        
        return {
            'timestamp': request.timestamp_start,
            'datetime': datetime.fromtimestamp(request.timestamp_start).isoformat(),
            'method': request.method,
            'url': request.pretty_url,
            'host': request.pretty_host,
            'path': request.path,
            'headers': dict(request.headers),
            'content_length': len(request.content) if request.content else 0,
            'content_type': request.headers.get('content-type', ''),
            'session_id': self.session_id,
            'scheme': request.scheme,
            'port': request.port,
            'query': request.query.decode() if request.query else '',
            'user_agent': request.headers.get('user-agent', ''),
            'referer': request.headers.get('referer', ''),
            'request_body_size': len(request.content) if request.content else 0
        }
    
    def _extract_response_info(self, flow) -> Dict[str, Any]:
        """提取响应信息"""
        response = flow.response
        
        return {
            'status_code': response.status_code,
            'status_message': response.reason,
            'headers': dict(response.headers),
            'content_type': response.headers.get('content-type', ''),
            'content_length': len(response.content) if response.content else 0,
            'response_time': (flow.response.timestamp_end - flow.request.timestamp_start) 
                           if flow.response.timestamp_end else None,
            'response_body_size': len(response.content) if response.content else 0,
            'encoding': response.headers.get('content-encoding', ''),
            'cache_control': response.headers.get('cache-control', ''),
            'server': response.headers.get('server', '')
        }
    
    def _should_capture(self, request) -> bool:
        """判断是否应该捕获请求"""
        host = request.pretty_host.lower()
        url = request.pretty_url.lower()
        
        # 只捕获HTTP/HTTPS
        if request.scheme not in ['http', 'https']:
            return False
        
        # 过滤系统域名
        for system_domain in self.system_domains:
            if system_domain in host:
                return False
        
        # 过滤广告和分析域名
        for ad_pattern in self.ad_patterns:
            if ad_pattern in host or ad_pattern in url:
                return False
        
        # 过滤常见的静态资源（可选）
        static_extensions = {'.css', '.js', '.png', '.jpg', '.jpeg', '.gif', '.ico', '.svg', '.woff', '.woff2'}
        path_lower = request.path.lower()
        if any(path_lower.endswith(ext) for ext in static_extensions):
            return False
        
        # 过滤本地网络请求
        if any(local_addr in host for local_addr in ['localhost', '127.0.0.1', '********', '192.168.']):
            return False
        
        return True
    
    def _save_request_to_file(self, request_info: Dict[str, Any]):
        """保存请求到文件"""
        try:
            # 保存到会话特定文件
            session_file = self.output_dir / f"session_{self.session_id}.json"
            
            # 加载现有数据
            requests_data = []
            if session_file.exists():
                try:
                    with open(session_file, 'r') as f:
                        requests_data = json.load(f)
                except (json.JSONDecodeError, IOError):
                    requests_data = []
            
            # 添加新请求
            requests_data.append(request_info)
            
            # 限制文件大小，保留最新的1000个请求
            if len(requests_data) > 1000:
                requests_data = requests_data[-1000:]
            
            # 保存回文件
            with open(session_file, 'w') as f:
                json.dump(requests_data, f, indent=2)
            
            # 同时保存到实时文件（用于监控）
            realtime_file = self.output_dir / "realtime_capture.json"
            with open(realtime_file, 'w') as f:
                json.dump(requests_data[-100:], f, indent=2)  # 只保留最新100个
                
        except Exception as e:
            logger.error(f"Failed to save request to file: {e}")
    
    def _update_request_with_response(self, url: str, response_info: Dict[str, Any]):
        """更新请求记录的响应信息"""
        try:
            # 更新内存中的记录
            for request in self.captured_requests:
                if request['url'] == url:
                    request.update(response_info)
                    break
            
            # 更新文件中的记录
            session_file = self.output_dir / f"session_{self.session_id}.json"
            if session_file.exists():
                try:
                    with open(session_file, 'r') as f:
                        requests_data = json.load(f)
                    
                    # 找到对应的请求并更新
                    for request in requests_data:
                        if request['url'] == url:
                            request.update(response_info)
                            break
                    
                    # 保存更新后的数据
                    with open(session_file, 'w') as f:
                        json.dump(requests_data, f, indent=2)
                        
                except Exception as e:
                    logger.debug(f"Error updating request file: {e}")
        
        except Exception as e:
            logger.error(f"Failed to update request with response: {e}")
    
    def get_session_requests(self) -> list:
        """获取当前会话的所有请求"""
        return self.captured_requests.copy()
    
    def clear_session(self):
        """清理当前会话数据"""
        self.captured_requests.clear()
        
        # 标记会话结束
        try:
            session_info_file = self.output_dir / f"session_{self.session_id}_info.json"
            if session_info_file.exists():
                with open(session_info_file, 'r') as f:
                    session_info = json.load(f)
                
                session_info['status'] = 'completed'
                session_info['ended_at'] = datetime.now().isoformat()
                session_info['total_requests'] = len(self.captured_requests)
                
                with open(session_info_file, 'w') as f:
                    json.dump(session_info, f, indent=2)
                    
        except Exception as e:
            logger.error(f"Error updating session info: {e}")


# 全局捕获实例
capture = LocalNetworkCapture()


def request(flow) -> None:
    """mitmproxy请求处理器"""
    capture.request(flow)


def response(flow) -> None:
    """mitmproxy响应处理器"""
    capture.response(flow)


def load(loader):
    """mitmproxy插件加载处理器"""
    logger.info("Local network capture script loaded")


def done():
    """mitmproxy插件清理处理器"""
    logger.info("Local network capture script unloaded")
    capture.clear_session()
