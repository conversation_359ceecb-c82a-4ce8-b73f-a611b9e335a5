#!/usr/bin/env python3
"""
诊断网络捕获问题
检查为什么没有捕获到网络请求
"""

import subprocess
import sys
import time
import json
from pathlib import Path

def log(message, level="INFO"):
    """日志输出"""
    timestamp = time.strftime("%H:%M:%S")
    if level == "SUCCESS":
        print(f"🎉 [{timestamp}] {message}")
    elif level == "ERROR":
        print(f"❌ [{timestamp}] {message}")
    elif level == "WARNING":
        print(f"⚠️  [{timestamp}] {message}")
    else:
        print(f"📋 [{timestamp}] {message}")

def run_adb(cmd, timeout=30):
    """执行adb命令"""
    full_cmd = f"source android_env.sh && adb {cmd}"
    try:
        result = subprocess.run(full_cmd, shell=True, 
                              capture_output=True, text=True, 
                              timeout=timeout, check=True)
        return result.returncode, result.stdout.strip(), result.stderr.strip()
    except subprocess.CalledProcessError as e:
        return e.returncode, "", e.stderr
    except subprocess.TimeoutExpired:
        return -1, "", "Timeout"

def diagnose_network_setup():
    """诊断网络设置"""
    log("🔍 诊断网络设置...")
    
    # 检查代理设置
    returncode, stdout, stderr = run_adb("shell settings get global http_proxy")
    if returncode == 0 and stdout:
        log(f"✅ 代理设置: {stdout}")
    else:
        log("❌ 代理设置未找到", "ERROR")
    
    # 检查网络连接
    returncode, stdout, stderr = run_adb("shell ping -c 3 *******")
    if returncode == 0:
        log("✅ 网络连接正常")
    else:
        log("❌ 网络连接异常", "ERROR")
    
    # 检查DNS
    returncode, stdout, stderr = run_adb("shell nslookup www.baidu.com")
    if returncode == 0:
        log("✅ DNS解析正常")
    else:
        log("❌ DNS解析异常", "ERROR")

def diagnose_frida_status():
    """诊断Frida状态"""
    log("🔍 诊断Frida状态...")
    
    # 检查frida-server
    returncode, stdout, stderr = run_adb("shell ps | grep frida-server")
    if "frida-server" in stdout:
        log("✅ frida-server正在运行")
        log(f"   进程信息: {stdout}")
    else:
        log("❌ frida-server未运行", "ERROR")
    
    # 检查应用进程
    package = "com.yjzx.yjzx2017"
    returncode, stdout, stderr = run_adb(f"shell ps | grep {package}")
    if package in stdout:
        log("✅ 应用进程正在运行")
        log(f"   进程信息: {stdout}")
    else:
        log("❌ 应用进程未运行", "ERROR")

def test_direct_network_request():
    """测试直接网络请求"""
    log("🔍 测试直接网络请求...")
    
    # 使用curl测试HTTP请求
    returncode, stdout, stderr = run_adb("shell curl -I http://httpbin.org/get")
    if returncode == 0:
        log("✅ HTTP请求成功")
        log(f"   响应: {stdout[:200]}")
    else:
        log("❌ HTTP请求失败", "ERROR")
        log(f"   错误: {stderr}")
    
    # 测试HTTPS请求
    returncode, stdout, stderr = run_adb("shell curl -I https://httpbin.org/get")
    if returncode == 0:
        log("✅ HTTPS请求成功")
        log(f"   响应: {stdout[:200]}")
    else:
        log("❌ HTTPS请求失败", "ERROR")
        log(f"   错误: {stderr}")

def test_app_network_activity():
    """测试应用网络活动"""
    log("🔍 测试应用网络活动...")
    
    package = "com.yjzx.yjzx2017"
    
    # 监控网络连接
    log("监控应用网络连接...")
    returncode, stdout, stderr = run_adb(f"shell netstat -an | grep $(pidof {package})")
    if stdout:
        log("✅ 发现应用网络连接")
        log(f"   连接信息: {stdout}")
    else:
        log("⚠️  未发现应用网络连接", "WARNING")
    
    # 检查应用权限
    returncode, stdout, stderr = run_adb(f"shell dumpsys package {package} | grep permission")
    if "INTERNET" in stdout:
        log("✅ 应用有网络权限")
    else:
        log("❌ 应用可能没有网络权限", "ERROR")

def test_mitmproxy_functionality():
    """测试mitmproxy功能"""
    log("🔍 测试mitmproxy功能...")
    
    # 检查mitmproxy进程
    result = subprocess.run("ps aux | grep mitmdump | grep -v grep", 
                          shell=True, capture_output=True, text=True)
    if result.stdout:
        log("✅ mitmproxy进程正在运行")
        log(f"   进程信息: {result.stdout}")
    else:
        log("❌ mitmproxy进程未运行", "ERROR")
    
    # 检查端口监听
    result = subprocess.run("lsof -i :8080", shell=True, capture_output=True, text=True)
    if result.stdout:
        log("✅ 端口8080正在监听")
        log(f"   端口信息: {result.stdout}")
    else:
        log("❌ 端口8080未监听", "ERROR")
    
    # 检查捕获脚本
    if Path("mitm-scripts/capture.py").exists():
        log("✅ 捕获脚本存在")
    else:
        log("❌ 捕获脚本不存在", "ERROR")

def suggest_solutions():
    """建议解决方案"""
    log("💡 建议解决方案...")
    
    solutions = [
        "1. 重新安装CA证书到Android系统",
        "2. 检查应用是否使用了证书固定",
        "3. 尝试使用不同的代理端口",
        "4. 验证Frida SSL绕过脚本是否正确加载",
        "5. 检查应用是否有特殊的网络配置",
        "6. 尝试手动触发网络请求",
        "7. 使用tcpdump监控网络流量",
        "8. 检查应用是否使用了自定义的网络库"
    ]
    
    for solution in solutions:
        log(f"   {solution}")

def run_comprehensive_diagnosis():
    """运行综合诊断"""
    log("🚀 网络捕获问题综合诊断")
    log("🔍 检查为什么没有捕获到网络请求")
    log("=" * 70)
    
    try:
        # 诊断1: 网络设置
        diagnose_network_setup()
        log("")
        
        # 诊断2: Frida状态
        diagnose_frida_status()
        log("")
        
        # 诊断3: 直接网络请求
        test_direct_network_request()
        log("")
        
        # 诊断4: 应用网络活动
        test_app_network_activity()
        log("")
        
        # 诊断5: mitmproxy功能
        test_mitmproxy_functionality()
        log("")
        
        # 建议解决方案
        suggest_solutions()
        
        log("=" * 70)
        log("🎯 诊断完成", "SUCCESS")
        log("💡 请根据上述诊断结果调整配置")
        
        return True
        
    except Exception as e:
        log(f"诊断异常: {e}", "ERROR")
        return False

def main():
    """主函数"""
    try:
        success = run_comprehensive_diagnosis()
        
        if success:
            print("\n🎯 网络捕获问题诊断完成！")
            print("💡 请根据诊断结果进行相应调整！")
        else:
            print("\n🔧 诊断过程中遇到问题")
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断")
    except Exception as e:
        print(f"\n❌ 异常: {e}")

if __name__ == "__main__":
    main()
