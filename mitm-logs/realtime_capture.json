[{"timestamp": 1757050391.940021, "datetime": "2025-09-05T13:33:11.940021", "method": "GET", "url": "http://mitm.it/cert/pem", "host": "mitm.it", "path": "/cert/pem", "headers": {"content-type": "application/x-x509-ca-cert", "content-disposition": "attachment; filename=mitmproxy-ca-cert.pem", "content-length": "1172"}, "content_length": 1172, "content_type": "application/x-x509-ca-cert", "session_id": "local_session_1757048713", "scheme": "http", "port": 80, "query": "", "user_agent": "curl/8.7.1", "referer": "", "request_body_size": 0, "status_code": 200, "status_message": "OK", "response_time": 0.009711027145385742, "response_body_size": 1172, "encoding": "", "cache_control": "", "server": ""}, {"timestamp": 1757050680.3782778, "datetime": "2025-09-05T13:38:00.378278", "method": "HEAD", "url": "http://ckegisorwqzee/", "host": "ckegisorwqzee", "path": "/", "headers": {"Host": "ckegisorwqzee", "Proxy-Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Linux; Android 11; sdk_gphone_x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36", "Accept-Encoding": "gzip, deflate"}, "content_length": 0, "content_type": "", "session_id": "local_session_1757048713", "scheme": "http", "port": 80, "query": "", "user_agent": "Mozilla/5.0 (Linux; Android 11; sdk_gphone_x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36", "referer": "", "request_body_size": 0}, {"timestamp": 1757050680.384984, "datetime": "2025-09-05T13:38:00.384984", "method": "HEAD", "url": "http://unsyzpompsmtwk/", "host": "unsyzpompsmtwk", "path": "/", "headers": {"Host": "unsyzpompsmtwk", "Proxy-Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Linux; Android 11; sdk_gphone_x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36", "Accept-Encoding": "gzip, deflate"}, "content_length": 0, "content_type": "", "session_id": "local_session_1757048713", "scheme": "http", "port": 80, "query": "", "user_agent": "Mozilla/5.0 (Linux; Android 11; sdk_gphone_x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36", "referer": "", "request_body_size": 0}, {"timestamp": 1757050680.402974, "datetime": "2025-09-05T13:38:00.402974", "method": "HEAD", "url": "http://cuncyykxgmnfj/", "host": "cuncyykxgmnfj", "path": "/", "headers": {"Host": "cuncyykxgmnfj", "Proxy-Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Linux; Android 11; sdk_gphone_x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36", "Accept-Encoding": "gzip, deflate"}, "content_length": 0, "content_type": "", "session_id": "local_session_1757048713", "scheme": "http", "port": 80, "query": "", "user_agent": "Mozilla/5.0 (Linux; Android 11; sdk_gphone_x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36", "referer": "", "request_body_size": 0}, {"timestamp": 1757051190.083591, "datetime": "2025-09-05T13:46:30.083591", "method": "HEAD", "url": "http://dtetpwmatcwvx/", "host": "dtetpwmatcwvx", "path": "/", "headers": {"Host": "dtetpwmatcwvx", "Proxy-Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Linux; Android 11; sdk_gphone_x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36", "Accept-Encoding": "gzip, deflate"}, "content_length": 0, "content_type": "", "session_id": "local_session_1757048713", "scheme": "http", "port": 80, "query": "", "user_agent": "Mozilla/5.0 (Linux; Android 11; sdk_gphone_x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36", "referer": "", "request_body_size": 0}, {"timestamp": 1757051190.0944, "datetime": "2025-09-05T13:46:30.094400", "method": "HEAD", "url": "http://zjbqnog/", "host": "zjbqnog", "path": "/", "headers": {"Host": "zjbqnog", "Proxy-Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Linux; Android 11; sdk_gphone_x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36", "Accept-Encoding": "gzip, deflate"}, "content_length": 0, "content_type": "", "session_id": "local_session_1757048713", "scheme": "http", "port": 80, "query": "", "user_agent": "Mozilla/5.0 (Linux; Android 11; sdk_gphone_x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36", "referer": "", "request_body_size": 0}, {"timestamp": 1757051190.096382, "datetime": "2025-09-05T13:46:30.096382", "method": "HEAD", "url": "http://gbmrrbgvsor/", "host": "gbmrrbgvsor", "path": "/", "headers": {"Host": "gbmrrbgvsor", "Proxy-Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (Linux; Android 11; sdk_gphone_x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36", "Accept-Encoding": "gzip, deflate"}, "content_length": 0, "content_type": "", "session_id": "local_session_1757048713", "scheme": "http", "port": 80, "query": "", "user_agent": "Mozilla/5.0 (Linux; Android 11; sdk_gphone_x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36", "referer": "", "request_body_size": 0}, {"timestamp": 1757051772.859383, "datetime": "2025-09-05T13:56:12.859383", "method": "GET", "url": "http://mitm.it/cert/pem", "host": "mitm.it", "path": "/cert/pem", "headers": {"Host": "mitm.it", "User-Agent": "curl/8.7.1", "Accept": "*/*", "Proxy-Connection": "Keep-Alive"}, "content_length": 0, "content_type": "", "session_id": "local_session_1757048713", "scheme": "http", "port": 80, "query": "", "user_agent": "curl/8.7.1", "referer": "", "request_body_size": 0}]