#!/bin/bash
# 自动化HTTPS流量捕获启动脚本
# 一键启动mitmproxy + Frida SSL绕过

set -e

echo "🚀 启动HTTPS流量捕获环境..."

# 检查设备连接
if ! adb -s emulator-5554 get-state >/dev/null 2>&1; then
    echo "❌ Android设备未连接"
    exit 1
fi

# 启动mitmproxy (后台)
echo "启动mitmproxy代理服务器..."
source .venv/bin/activate
mitmproxy --listen-host 0.0.0.0 --listen-port 8080 \
    --set confdir=/Users/<USER>/Desktop/project/apk_detect/mitm-config \
    --set flow_detail=1 \
    --showhost \
    --save-stream-file mitm_capture.log &

MITM_PID=$!
echo "mitmproxy PID: $MITM_PID"

# 等待mitmproxy启动
sleep 3

# 启动Frida SSL绕过 (后台)
echo "启动Frida SSL绕过..."
frida -U -l ssl_bypass.js -f com.iloda.beacon --no-pause &
FRIDA_PID=$!
echo "Frida PID: $FRIDA_PID"

echo "✅ HTTPS流量捕获环境已启动"
echo "📊 查看捕获日志: tail -f mitm_capture.log"
echo "🌐 Web界面: http://127.0.0.1:8081"
echo "🛑 停止服务: kill $MITM_PID $FRIDA_PID"

# 保存PID以便后续停止
echo "$MITM_PID $FRIDA_PID" > .https_capture_pids

echo "🎯 环境就绪，开始分析..."
