/**
 * Frida Hook Script - 绕过exported="false"限制
 * 专门用于突破Android Activity启动限制
 */

console.log("[+] ===== exported=\"false\" 绕过Hook启动 =====");

Java.perform(function() {
    console.log("[+] Java运行时已就绪，开始Hook...");
    
    // 1. Hook ActivityManagerService - 绕过exported检查
    try {
        var ActivityManagerService = Java.use("com.android.server.am.ActivityManagerService");
        console.log("[+] 找到ActivityManagerService");
        
        // Hook checkComponentPermission方法
        ActivityManagerService.checkComponentPermission.overload(
            'java.lang.String', 'int', 'int', 'int', 'boolean'
        ).implementation = function(permission, pid, uid, owningUid, exported) {
            console.log("[*] Hook checkComponentPermission - 强制返回允许");
            console.log("    原始exported:", exported);
            // 强制返回PERMISSION_GRANTED (0)
            return 0;
        };
        
        console.log("[+] ActivityManagerService Hook成功");
    } catch (e) {
        console.log("[-] ActivityManagerService Hook失败:", e);
    }
    
    // 2. Hook PackageManager - 绕过Activity信息检查
    try {
        var PackageManager = Java.use("android.content.pm.PackageManager");
        console.log("[+] 找到PackageManager");
        
        // Hook getActivityInfo方法
        PackageManager.getActivityInfo.overload(
            'android.content.ComponentName', 'int'
        ).implementation = function(component, flags) {
            console.log("[*] Hook getActivityInfo for:", component.getClassName());
            
            var result = this.getActivityInfo(component, flags);
            if (result) {
                // 强制设置exported为true
                result.exported.value = true;
                console.log("[+] 强制设置Activity exported=true:", component.getClassName());
            }
            return result;
        };
        
        console.log("[+] PackageManager Hook成功");
    } catch (e) {
        console.log("[-] PackageManager Hook失败:", e);
    }
    
    // 3. Hook ActivityInfo - 直接修改exported属性
    try {
        var ActivityInfo = Java.use("android.content.pm.ActivityInfo");
        console.log("[+] 找到ActivityInfo");
        
        // 获取exported字段并修改
        var exportedField = ActivityInfo.class.getDeclaredField("exported");
        exportedField.setAccessible(true);
        
        // Hook构造函数，强制设置exported=true
        ActivityInfo.$init.overload().implementation = function() {
            this.$init();
            exportedField.set(this, true);
            console.log("[+] ActivityInfo构造时强制设置exported=true");
        };
        
        console.log("[+] ActivityInfo Hook成功");
    } catch (e) {
        console.log("[-] ActivityInfo Hook失败:", e);
    }
    
    // 4. Hook Intent启动相关方法
    try {
        var ContextImpl = Java.use("android.app.ContextImpl");
        console.log("[+] 找到ContextImpl");
        
        // Hook startActivity方法
        ContextImpl.startActivity.overload('android.content.Intent').implementation = function(intent) {
            console.log("[*] Hook startActivity - Intent:", intent.toString());
            
            // 添加FLAG_ACTIVITY_NEW_TASK，有助于启动
            var Intent = Java.use("android.content.Intent");
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK.value);
            
            try {
                return this.startActivity(intent);
            } catch (e) {
                console.log("[!] startActivity异常，尝试绕过:", e);
                
                // 尝试使用startActivityAsUser方法绕过
                try {
                    var UserHandle = Java.use("android.os.UserHandle");
                    var currentUser = UserHandle.CURRENT;
                    return this.startActivityAsUser(intent, currentUser);
                } catch (e2) {
                    console.log("[!] startActivityAsUser也失败:", e2);
                    throw e;
                }
            }
        };
        
        console.log("[+] ContextImpl Hook成功");
    } catch (e) {
        console.log("[-] ContextImpl Hook失败:", e);
    }
    
    // 5. Hook Permission检查 - 最关键的绕过
    try {
        var ActivityTaskManagerService = Java.use("com.android.server.wm.ActivityTaskManagerService");
        console.log("[+] 找到ActivityTaskManagerService");
        
        // Hook startActivityAsUser方法
        ActivityTaskManagerService.startActivityAsUser.implementation = function() {
            console.log("[*] Hook startActivityAsUser - 绕过权限检查");
            
            // 获取所有参数
            var args = Array.prototype.slice.call(arguments);
            console.log("[*] 参数数量:", args.length);
            
            // 调用原始方法，但忽略SecurityException
            try {
                return this.startActivityAsUser.apply(this, arguments);
            } catch (e) {
                if (e.toString().includes("Permission Denial") || 
                    e.toString().includes("SecurityException")) {
                    console.log("[+] 绕过SecurityException，强制启动Activity");
                    // 返回成功码
                    return 0;
                } else {
                    throw e;
                }
            }
        };
        
        console.log("[+] ActivityTaskManagerService Hook成功");
    } catch (e) {
        console.log("[-] ActivityTaskManagerService Hook失败:", e);
    }
    
    // 6. Hook ApplicationPackageManager - 绕过组件权限检查
    try {
        var ApplicationPackageManager = Java.use("android.app.ApplicationPackageManager");
        console.log("[+] 找到ApplicationPackageManager");
        
        // Hook resolveActivity方法
        ApplicationPackageManager.resolveActivity.overload(
            'android.content.Intent', 'int'
        ).implementation = function(intent, flags) {
            console.log("[*] Hook resolveActivity for Intent:", intent.toString());
            
            var result = this.resolveActivity(intent, flags);
            if (result && result.activityInfo) {
                // 强制设置exported为true
                result.activityInfo.exported.value = true;
                console.log("[+] resolveActivity - 强制设置exported=true");
            }
            return result;
        };
        
        console.log("[+] ApplicationPackageManager Hook成功");
    } catch (e) {
        console.log("[-] ApplicationPackageManager Hook失败:", e);
    }
    
    // 7. 通用Activity启动器 - 绕过所有检查
    console.log("[+] 注册通用Activity启动器...");
    
    // 创建强制启动Activity的函数
    globalThis.forceStartActivity = function(packageName, activityName) {
        console.log("[+] 强制启动Activity:", packageName + "/" + activityName);
        
        try {
            var Intent = Java.use("android.content.Intent");
            var ComponentName = Java.use("android.content.ComponentName");
            var ActivityThread = Java.use("android.app.ActivityThread");
            
            // 获取应用上下文
            var currentApp = ActivityThread.currentApplication();
            var context = currentApp.getApplicationContext();
            
            // 创建Intent
            var component = ComponentName.$new(packageName, activityName);
            var intent = Intent.$new();
            intent.setComponent(component);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK.value);
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP.value);
            
            console.log("[*] 创建Intent完成，开始启动...");
            
            // 尝试启动
            context.startActivity(intent);
            console.log("[+] Activity启动成功!");
            return true;
            
        } catch (e) {
            console.log("[!] 强制启动失败:", e);
            
            // 最后的绕过尝试 - 直接调用system方法
            try {
                var Runtime = Java.use("java.lang.Runtime");
                var runtime = Runtime.getRuntime();
                var cmd = "am start -n " + packageName + "/" + activityName + " --activity-clear-task --activity-new-task";
                console.log("[*] 尝试系统命令:", cmd);
                
                var process = runtime.exec(cmd);
                process.waitFor();
                console.log("[+] 系统命令执行完成");
                return true;
            } catch (e2) {
                console.log("[!] 系统命令也失败:", e2);
                return false;
            }
        }
    };
    
    // 8. 自动绕过检查的Activity启动监听
    setInterval(function() {
        // 持续监听和修复exported属性
        Java.choose("android.content.pm.ActivityInfo", {
            onMatch: function(instance) {
                try {
                    if (instance.exported.value === false) {
                        instance.exported.value = true;
                        console.log("[+] 自动修复ActivityInfo.exported属性");
                    }
                } catch (e) {
                    // 忽略错误
                }
            },
            onComplete: function() {}
        });
    }, 1000); // 每秒检查一次
    
    console.log("[+] ===== exported绕过Hook部署完成 =====");
    console.log("[+] 使用方法: forceStartActivity(包名, Activity名)");
    console.log("[+] 示例: forceStartActivity('com.example.app', 'com.example.app.MainActivity')");
});

// 9. 原生层Hook - 绕过更底层的检查
Java.performNow(function() {
    console.log("[+] 尝试原生层Hook...");
    
    // Hook libbinder中的权限检查
    try {
        var libbinder = Module.findBaseAddress("libbinder.so");
        if (libbinder) {
            console.log("[+] 找到libbinder.so");
            
            // Hook checkPermission相关函数
            var checkPermissionAddr = Module.findExportByName("libbinder.so", "_ZN7android14PermissionController15checkPermissionE");
            if (checkPermissionAddr) {
                Interceptor.attach(checkPermissionAddr, {
                    onEnter: function(args) {
                        console.log("[*] Native checkPermission调用");
                    },
                    onLeave: function(retval) {
                        // 强制返回true (1)
                        retval.replace(ptr(1));
                        console.log("[+] Native checkPermission强制返回true");
                    }
                });
            }
        }
    } catch (e) {
        console.log("[-] 原生层Hook失败:", e);
    }
});

console.log("[+] Hook脚本加载完成，exported=\"false\"绕过已部署！");




