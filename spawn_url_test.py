#!/usr/bin/env python3
"""
使用 spawn 模式的 URL 捕获测试脚本
"""

import frida
import sys
import time
import json
import subprocess
from datetime import datetime

def main():
    package_name = "com.iloda.beacon"
    
    # Hook 脚本
    hook_script = """
    console.log("[*] Hook 脚本开始运行");
    
    var urls = [];
    
    Java.perform(function() {
        console.log("[*] Java 环境已准备");
        
        // Hook URL 类
        try {
            var URL = Java.use('java.net.URL');
            URL.$init.overload('java.lang.String').implementation = function(url) {
                console.log("[URL] " + url);
                send({type: "url", data: url});
                return this.$init(url);
            };
            console.log("[✓] Hook URL 成功");
        } catch(e) {
            console.log("[×] Hook URL 失败: " + e.message);
        }
        
        // Hook HttpURLConnection
        try {
            var HttpURLConnection = Java.use('java.net.HttpURLConnection');
            HttpURLConnection.connect.implementation = function() {
                var url = this.getURL().toString();
                console.log("[HttpURLConnection] " + url);
                send({type: "http", data: url});
                return this.connect();
            };
            console.log("[✓] Hook HttpURLConnection 成功");
        } catch(e) {
            console.log("[×] Hook HttpURLConnection 失败: " + e.message);
        }
        
        // Hook HttpsURLConnection
        try {
            var HttpsURLConnection = Java.use('javax.net.ssl.HttpsURLConnection');
            HttpsURLConnection.connect.implementation = function() {
                var url = this.getURL().toString();
                console.log("[HttpsURLConnection] " + url);
                send({type: "https", data: url});
                return this.connect();
            };
            console.log("[✓] Hook HttpsURLConnection 成功");
        } catch(e) {
            console.log("[×] Hook HttpsURLConnection 失败: " + e.message);
        }
        
        // Hook OkHttp3 (如果存在)
        try {
            var OkHttpClient = Java.use('okhttp3.OkHttpClient');
            OkHttpClient.newCall.implementation = function(request) {
                var url = request.url().toString();
                console.log("[OkHttp3] " + url);
                send({type: "okhttp", data: url});
                return this.newCall(request);
            };
            console.log("[✓] Hook OkHttp3 成功");
        } catch(e) {
            console.log("[×] Hook OkHttp3 失败 (可能未使用)");
        }
        
        // Hook WebView
        try {
            var WebView = Java.use('android.webkit.WebView');
            WebView.loadUrl.overload('java.lang.String').implementation = function(url) {
                console.log("[WebView] " + url);
                send({type: "webview", data: url});
                return this.loadUrl(url);
            };
            console.log("[✓] Hook WebView 成功");
        } catch(e) {
            console.log("[×] Hook WebView 失败: " + e.message);
        }
        
        // Hook System.loadLibrary (检测 native 库加载)
        try {
            var System = Java.use('java.lang.System');
            System.loadLibrary.implementation = function(libname) {
                console.log("[LoadLibrary] " + libname);
                return this.loadLibrary(libname);
            };
            console.log("[✓] Hook System.loadLibrary 成功");
        } catch(e) {
            console.log("[×] Hook System.loadLibrary 失败");
        }
        
        console.log("[*] 所有 Hook 设置完成");
    });
    """
    
    try:
        # 连接设备
        device = frida.get_usb_device()
        print(f"[*] 连接到设备: {device.name}")
        
        # 使用 spawn 模式启动应用
        print(f"[*] 使用 spawn 模式启动 {package_name}")
        pid = device.spawn([package_name])
        session = device.attach(pid)
        
        # 创建脚本
        script = session.create_script(hook_script)
        
        # 收集的 URLs
        captured_urls = []
        
        # 处理消息
        def on_message(message, data):
            if message['type'] == 'send':
                payload = message.get('payload', {})
                if isinstance(payload, dict):
                    url_type = payload.get('type', 'unknown')
                    url_data = payload.get('data', '')
                    if url_data:
                        captured_urls.append({
                            'type': url_type,
                            'url': url_data,
                            'time': datetime.now().isoformat()
                        })
                        print(f"[捕获] [{url_type}] {url_data}")
                else:
                    print(f"[SCRIPT] {payload}")
            elif message['type'] == 'error':
                print(f"[ERROR] {message}")
        
        script.on('message', on_message)
        
        # 加载脚本
        script.load()
        print("[*] 脚本加载成功")
        
        # 恢复应用执行
        device.resume(pid)
        print("[*] 应用已恢复执行")
        
        # 等待应用启动
        time.sleep(3)
        
        # 触发一些 Activity
        print("\n[*] 触发各种 Activity...")
        activities = [
            ".activity.MainActivity",
            ".activity.GuideActivity",
            ".activity.LoginActivity",
            ".activity.SplashActivity"
        ]
        
        for activity in activities:
            full_activity = f"{package_name}/{activity}"
            print(f"[*] 尝试启动 {activity}")
            result = subprocess.run([
                './android-sdk/platform-tools/adb', '-s', 'emulator-5554',
                'shell', 'am', 'start', '-n', full_activity
            ], capture_output=True, text=True)
            if "Error" not in result.stderr and "Error" not in result.stdout:
                print(f"    ✓ 启动成功")
            time.sleep(2)
        
        # 模拟一些用户操作
        print("\n[*] 模拟用户操作...")
        # 点击屏幕中心
        subprocess.run([
            './android-sdk/platform-tools/adb', '-s', 'emulator-5554',
            'shell', 'input', 'tap', '540', '960'
        ])
        time.sleep(1)
        
        # 滑动
        subprocess.run([
            './android-sdk/platform-tools/adb', '-s', 'emulator-5554',
            'shell', 'input', 'swipe', '540', '1500', '540', '500', '300'
        ])
        time.sleep(1)
        
        # 返回键
        subprocess.run([
            './android-sdk/platform-tools/adb', '-s', 'emulator-5554',
            'shell', 'input', 'keyevent', '4'
        ])
        time.sleep(1)
        
        # 等待捕获
        print("\n[*] 等待 20 秒捕获 URLs...")
        for i in range(4):
            time.sleep(5)
            print(f"    等待中... ({(i+1)*5}/20 秒)")
            if captured_urls:
                print(f"    已捕获 {len(captured_urls)} 个 URLs")
        
        # 输出结果
        print("\n" + "="*50)
        if captured_urls:
            print(f"[*] 总共捕获 {len(captured_urls)} 个 URLs:")
            for idx, item in enumerate(captured_urls, 1):
                print(f"{idx}. [{item['type']}] {item['url']}")
            
            # 保存到文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"captured_urls_{timestamp}.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(captured_urls, f, indent=2, ensure_ascii=False)
            print(f"\n[*] 结果已保存到: {output_file}")
        else:
            print("[!] 未捕获到任何 URLs")
        
        print("\n[*] 测试完成")
        
    except Exception as e:
        print(f"[!] 错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        try:
            session.detach()
            print("[*] 已分离会话")
        except:
            pass

if __name__ == "__main__":
    main()




