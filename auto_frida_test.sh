#!/bin/bash
# 自动化Frida SSL绕过测试脚本

echo "🚀 自动化Frida SSL绕过测试"
echo "================================"

# 设置环境
source android_env.sh
export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin

# 获取应用PID
echo "📋 获取应用PID..."
PID=$(adb shell ps | grep yjzx | head -1 | awk '{print $2}')

if [ -z "$PID" ]; then
    echo "❌ 未找到应用进程"
    exit 1
fi

echo "✅ 找到应用PID: $PID"

# 启动Frida SSL绕过
echo "🔧 启动Frida SSL绕过..."
echo "💡 如果看到SSL bypass initialization completed，说明成功"
echo "⚠️  按Ctrl+C停止"

frida -U $PID -l optimized_ssl_bypass.js
