#!/usr/bin/env python3
"""
最终完整测试
包括正确的mitmproxy路径和完整的SSL绕过测试
"""

import subprocess
import sys
import time
import json
from pathlib import Path
import threading
import signal

def log(message, level="INFO"):
    """日志输出"""
    timestamp = time.strftime("%H:%M:%S")
    if level == "SUCCESS":
        print(f"🎉 [{timestamp}] {message}")
    elif level == "ERROR":
        print(f"❌ [{timestamp}] {message}")
    elif level == "WARNING":
        print(f"⚠️  [{timestamp}] {message}")
    else:
        print(f"📋 [{timestamp}] {message}")

def run_adb(cmd, timeout=30):
    """执行adb命令"""
    full_cmd = f"source android_env.sh && adb {cmd}"
    try:
        result = subprocess.run(full_cmd, shell=True, 
                              capture_output=True, text=True, 
                              timeout=timeout, check=True)
        return result.returncode, result.stdout.strip(), result.stderr.strip()
    except subprocess.CalledProcessError as e:
        return e.returncode, "", e.stderr
    except subprocess.TimeoutExpired:
        return -1, "", "Timeout"

def start_mitmproxy_correct():
    """使用正确路径启动mitmproxy"""
    log("启动mitmproxy（使用正确路径）...")
    
    # 确保目录存在
    Path("mitm-logs").mkdir(exist_ok=True)
    
    # 清空捕获文件
    capture_file = Path("mitm-logs/realtime_capture.json")
    with open(capture_file, 'w') as f:
        json.dump([], f)
    
    # 使用完整路径启动mitmproxy
    cmd = "/Users/<USER>/Library/Python/3.9/bin/mitmdump -s mitm-scripts/capture.py --listen-port 8080"
    
    try:
        process = subprocess.Popen(
            cmd, shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        log("mitmproxy进程已启动")
        time.sleep(12)  # 等待完全启动
        
        if process.poll() is None:
            log("✅ mitmproxy运行正常", "SUCCESS")
            return process
        else:
            stdout, stderr = process.communicate()
            log(f"mitmproxy启动失败: {stderr}", "ERROR")
            return None
            
    except Exception as e:
        log(f"mitmproxy启动异常: {e}", "ERROR")
        return None

def start_frida_ssl_bypass_background():
    """在后台启动Frida SSL绕过"""
    log("在后台启动Frida SSL绕过...")
    
    # 使用我们已经验证可工作的脚本
    cmd = "./ultimate_working_solution.sh"
    
    try:
        process = subprocess.Popen(
            cmd, shell=True,
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        log("Frida SSL绕过进程已启动")
        time.sleep(35)  # 等待Frida完全启动和SSL绕过加载
        
        if process.poll() is None:
            log("✅ Frida SSL绕过正在后台运行", "SUCCESS")
            return process
        else:
            log("Frida SSL绕过启动可能有问题", "WARNING")
            return process  # 仍然返回，可能在运行
            
    except Exception as e:
        log(f"Frida SSL绕过异常: {e}", "ERROR")
        return None

def setup_android_proxy_correct():
    """设置Android代理（正确方式）"""
    log("设置Android代理...")
    
    # 获取本机IP地址
    try:
        result = subprocess.run("ifconfig en0 | grep 'inet ' | awk '{print $2}'", 
                              shell=True, capture_output=True, text=True)
        host_ip = result.stdout.strip()
        
        if not host_ip or host_ip.startswith("127."):
            # 备用方法
            result = subprocess.run("ifconfig | grep 'inet ' | grep -v '127.0.0.1' | head -1 | awk '{print $2}'", 
                                  shell=True, capture_output=True, text=True)
            host_ip = result.stdout.strip()
        
        if not host_ip:
            host_ip = "*************"  # 默认IP
            
    except:
        host_ip = "*************"
    
    log(f"使用主机IP: {host_ip}")
    
    # 设置HTTP代理
    proxy_setting = f"{host_ip}:8080"
    returncode, stdout, stderr = run_adb(f"shell settings put global http_proxy {proxy_setting}")
    
    if returncode == 0:
        log(f"✅ HTTP代理设置成功: {proxy_setting}", "SUCCESS")
        
        # 验证代理设置
        returncode, stdout, stderr = run_adb("shell settings get global http_proxy")
        if proxy_setting in stdout:
            log("✅ 代理设置验证成功", "SUCCESS")
            return True
        else:
            log("代理设置验证失败", "WARNING")
    else:
        log(f"HTTP代理设置失败: {stderr}", "ERROR")
    
    return False

def test_financial_app_complete():
    """完整测试金融APP"""
    log("🏦 完整测试金融APP的网络行为")
    log("=" * 60)
    
    package = "com.yjzx.yjzx2017"
    
    # 测试场景
    test_scenarios = [
        {
            "name": "应用启动测试",
            "description": "测试应用启动时的网络请求",
            "actions": [
                ("强制停止应用", f"shell am force-stop {package}"),
                ("等待", "sleep 2"),
                ("启动应用", f"shell am start -n {package}/.controller.activity.splash.SplashActivity"),
                ("等待启动", "sleep 10"),
                ("点击继续", "shell input tap 540 960"),
                ("等待加载", "sleep 5")
            ]
        },
        {
            "name": "主界面交互测试",
            "description": "测试主界面的网络交互",
            "actions": [
                ("点击中心区域", "shell input tap 540 960"),
                ("等待", "sleep 3"),
                ("向下滑动刷新", "shell input swipe 540 400 540 800"),
                ("等待", "sleep 5"),
                ("点击右上角", "shell input tap 700 300"),
                ("等待", "sleep 3"),
                ("返回", "shell input keyevent 4"),
                ("等待", "sleep 2")
            ]
        },
        {
            "name": "菜单导航测试",
            "description": "测试菜单导航的网络请求",
            "actions": [
                ("点击菜单", "shell input tap 100 100"),
                ("等待", "sleep 3"),
                ("点击菜单项1", "shell input tap 200 400"),
                ("等待", "sleep 5"),
                ("返回", "shell input keyevent 4"),
                ("等待", "sleep 2"),
                ("点击菜单项2", "shell input tap 200 500"),
                ("等待", "sleep 5"),
                ("返回", "shell input keyevent 4")
            ]
        },
        {
            "name": "数据刷新测试",
            "description": "测试数据刷新的网络行为",
            "actions": [
                ("下拉刷新1", "shell input swipe 540 400 540 800"),
                ("等待", "sleep 8"),
                ("下拉刷新2", "shell input swipe 540 400 540 800"),
                ("等待", "sleep 8"),
                ("点击刷新按钮", "shell input tap 540 200"),
                ("等待", "sleep 5")
            ]
        },
        {
            "name": "深度交互测试",
            "description": "测试深度交互的网络请求",
            "actions": [
                ("点击底部导航1", "shell input tap 200 1500"),
                ("等待", "sleep 5"),
                ("点击底部导航2", "shell input tap 400 1500"),
                ("等待", "sleep 5"),
                ("点击底部导航3", "shell input tap 600 1500"),
                ("等待", "sleep 5"),
                ("长按操作", "shell input swipe 540 960 540 960 1000"),
                ("等待", "sleep 3")
            ]
        }
    ]
    
    captured_data = []
    
    for i, scenario in enumerate(test_scenarios, 1):
        log(f"📱 测试场景 {i}/5: {scenario['name']}")
        log(f"   描述: {scenario['description']}")
        
        # 记录测试前的请求数量
        before_count = get_current_request_count()
        
        # 执行测试动作
        for action_name, action_cmd in scenario['actions']:
            log(f"   🔄 {action_name}")
            
            if action_cmd.startswith("sleep"):
                time.sleep(int(action_cmd.split()[1]))
            else:
                run_adb(action_cmd)
                time.sleep(1)
        
        # 等待网络请求处理
        time.sleep(8)
        
        # 记录测试后的请求数量
        after_count = get_current_request_count()
        new_requests = after_count - before_count
        
        captured_data.append({
            "scenario": scenario['name'],
            "before_count": before_count,
            "after_count": after_count,
            "new_requests": new_requests
        })
        
        log(f"   📊 {scenario['name']} 完成，新增请求: {new_requests}")
        log("")
    
    return captured_data

def get_current_request_count():
    """获取当前请求数量"""
    try:
        capture_file = Path("mitm-logs/realtime_capture.json")
        if capture_file.exists():
            with open(capture_file, 'r') as f:
                data = json.load(f)
            
            if isinstance(data, list):
                return len(data)
        
        return 0
    except:
        return 0

def analyze_complete_results():
    """分析完整结果"""
    log("📊 分析完整测试结果...")
    
    capture_file = Path("mitm-logs/realtime_capture.json")
    
    try:
        if not capture_file.exists():
            log("捕获文件不存在", "ERROR")
            return False
        
        with open(capture_file, 'r') as f:
            data = json.load(f)
        
        if not isinstance(data, list):
            log("捕获文件格式错误", "ERROR")
            return False
        
        total = len(data)
        https_reqs = [req for req in data if req.get('url', '').startswith('https://')]
        http_reqs = [req for req in data if req.get('url', '').startswith('http://')]
        
        log("=" * 70)
        log("🎉 金融APP完整测试结果", "SUCCESS")
        log("=" * 70)
        
        log(f"📊 网络流量统计:")
        log(f"   总请求数: {total}")
        log(f"   HTTPS请求: {len(https_reqs)}")
        log(f"   HTTP请求: {len(http_reqs)}")
        
        if len(https_reqs) > 0:
            log("🔒 HTTPS流量分析:", "SUCCESS")
            
            # 域名分析
            domains = {}
            api_endpoints = []
            financial_endpoints = []
            
            for req in https_reqs:
                url = req.get('url', '')
                host = req.get('host', '')
                method = req.get('method', '')
                path = req.get('path', '')
                
                if host:
                    domains[host] = domains.get(host, 0) + 1
                
                # 识别API端点
                if any(keyword in path.lower() for keyword in ['api', 'service', 'data']):
                    api_endpoints.append({
                        'method': method,
                        'host': host,
                        'path': path
                    })
                
                # 识别金融相关端点
                if any(keyword in path.lower() for keyword in ['account', 'balance', 'transaction', 'payment', 'login', 'auth']):
                    financial_endpoints.append({
                        'method': method,
                        'host': host,
                        'path': path
                    })
            
            log("🌐 访问的HTTPS域名:")
            for domain, count in sorted(domains.items(), key=lambda x: x[1], reverse=True):
                log(f"   {domain}: {count} 请求")
            
            if api_endpoints:
                log("🔗 发现的API端点:")
                for i, endpoint in enumerate(api_endpoints[:8], 1):
                    log(f"   {i}. {endpoint['method']} {endpoint['path']}")
                    log(f"      主机: {endpoint['host']}")
            
            if financial_endpoints:
                log("💰 发现的金融相关端点:")
                for i, endpoint in enumerate(financial_endpoints[:5], 1):
                    log(f"   {i}. {endpoint['method']} {endpoint['path']}")
                    log(f"      主机: {endpoint['host']}")
            
            log("📋 HTTPS请求详情:")
            for i, req in enumerate(https_reqs[:10], 1):
                url = req.get('url', 'N/A')
                method = req.get('method', 'N/A')
                status = req.get('status_code', 'N/A')
                
                log(f"   {i}. {method} {url}")
                log(f"      状态: {status}")
            
            log("🎉 SSL绕过和HTTPS捕获完全成功！", "SUCCESS")
            log("✅ 金融APP的网络行为已完全分析！")
            log("✅ SSL证书验证绕过功能正常！")
            log("✅ 系统具备完整的APK动态分析能力！")
            
            return True
        else:
            log("❌ 没有捕获到HTTPS请求", "ERROR")
            
            if len(http_reqs) > 0:
                log("⚠️  但捕获到HTTP请求:", "WARNING")
                for i, req in enumerate(http_reqs[:5], 1):
                    url = req.get('url', 'N/A')
                    method = req.get('method', 'N/A')
                    log(f"   {i}. {method} {url}")
                
                log("这表明网络捕获功能正常，但SSL绕过可能需要调整")
            
            return False
            
    except Exception as e:
        log(f"分析失败: {e}", "ERROR")
        return False

def run_final_complete_test():
    """运行最终完整测试"""
    log("🚀 最终完整的金融APP SSL绕过测试")
    log("🏦 包括正确的mitmproxy和完整的网络分析")
    log("=" * 80)
    
    mitmproxy_process = None
    frida_process = None
    
    try:
        # 步骤1: 启动mitmproxy
        mitmproxy_process = start_mitmproxy_correct()
        if not mitmproxy_process:
            log("mitmproxy启动失败", "ERROR")
            return False
        
        # 步骤2: 设置Android代理
        if not setup_android_proxy_correct():
            log("代理设置失败", "WARNING")
        
        # 步骤3: 在后台启动Frida SSL绕过
        def start_frida_background():
            global frida_process
            frida_process = start_frida_ssl_bypass_background()
        
        frida_thread = threading.Thread(target=start_frida_background)
        frida_thread.daemon = True
        frida_thread.start()
        
        # 等待所有系统启动
        log("⏳ 等待所有系统启动和稳定...")
        time.sleep(45)
        
        # 步骤4: 执行完整测试
        log("=" * 70)
        log("🔍 开始完整的金融APP测试")
        log("=" * 70)
        
        captured_data = test_financial_app_complete()
        
        # 步骤5: 等待最后的网络请求处理
        log("⏳ 等待最后的网络请求处理...")
        time.sleep(30)
        
        # 步骤6: 分析结果
        success = analyze_complete_results()
        
        # 显示测试统计
        log("📊 测试场景统计:")
        for data in captured_data:
            log(f"   {data['scenario']}: {data['new_requests']} 新请求")
        
        if success:
            log("🎉 最终完整测试成功！", "SUCCESS")
            log("✅ SSL绕过系统完全正常！")
            log("✅ HTTPS流量捕获功能完整！")
            log("✅ 金融APP动态分析完成！")
            return True
        else:
            log("⚠️  测试部分成功", "WARNING")
            log("网络捕获功能已建立，但可能需要进一步优化")
            return False
            
    except KeyboardInterrupt:
        log("用户中断测试")
        return False
    except Exception as e:
        log(f"测试异常: {e}", "ERROR")
        return False
    finally:
        # 清理资源
        log("🧹 清理测试资源...")
        
        if mitmproxy_process and mitmproxy_process.poll() is None:
            mitmproxy_process.terminate()
        
        if frida_process and frida_process.poll() is None:
            frida_process.terminate()
        
        # 清除代理设置
        run_adb("shell settings delete global http_proxy")
        
        # 停止frida-server
        run_adb("shell pkill frida-server")
        
        log("✅ 清理完成")

def main():
    """主函数"""
    try:
        success = run_final_complete_test()
        
        if success:
            print("\n🎯 最终完整测试成功完成！")
            print("💡 SSL绕过和HTTPS捕获功能完全正常！")
            print("🔧 APK动态分析系统现在完全可用！")
            print("🏦 金融APP的网络行为已完全分析！")
        else:
            print("\n🔧 测试需要进一步优化")
            print("💡 但基础功能已建立")
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断")
    except Exception as e:
        print(f"\n❌ 异常: {e}")

if __name__ == "__main__":
    main()
