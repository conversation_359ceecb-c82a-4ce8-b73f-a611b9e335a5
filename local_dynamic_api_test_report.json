{"timestamp": **********.9941251, "base_url": "http://localhost:8000", "summary": {"total": 4, "passed": 4, "failed": 0, "errors": 0, "success_rate": 100.0}, "results": {"Health Check": {"status": "PASS", "details": {"status": "healthy", "emulator_manager": "available", "android_tools": {"adb": "available", "emulator": "available"}, "running_instances": 0, "total_instances": 0}}, "Emulator Status": {"status": "PASS", "details": {"total_instances": 0, "max_instances": 3, "available_ports": 3, "status_counts": {}, "android_home": "/Users/<USER>/Desktop/project/apk_detect/android-sdk", "emulator_path": "/Users/<USER>/Desktop/project/apk_detect/android-sdk/emulator/emulator", "adb_path": "/Users/<USER>/Desktop/project/apk_detect/platform-tools/adb", "instances": {}}}, "List AVDs": {"status": "PASS", "details": {"avds": ["test_avd"], "count": 1}}, "API Analysis (Mock)": {"status": "PASS", "details": {"response_status": 200, "analysis_status": "failed", "task_id": "0b36035e-813f-41d8-bf26-ef5ae69ed4de", "analysis_type": "local_dynamic", "has_result": true, "missing_fields": []}}}}