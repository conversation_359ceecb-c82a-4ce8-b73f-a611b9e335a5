#!/usr/bin/env python3
"""
设置Android模拟器屏幕锁定
为证书安装做准备
"""

import subprocess
import sys
import time

class ScreenLockSetup:
    def __init__(self):
        pass
        
    def run_adb(self, cmd, timeout=30):
        """执行adb命令"""
        full_cmd = f"source android_env.sh && adb {cmd}"
        try:
            result = subprocess.run(full_cmd, shell=True, 
                                  capture_output=True, text=True, 
                                  timeout=timeout, check=True)
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            return f"ERROR: {e.stderr}"
        except subprocess.TimeoutExpired:
            return "ERROR: Timeout"
    
    def check_current_lock_status(self):
        """检查当前锁定状态"""
        print("🔍 检查当前屏幕锁定状态...")
        
        # 检查锁定是否禁用
        disabled = self.run_adb("shell settings get secure lockscreen.disabled")
        print(f"📋 锁定禁用状态: {disabled}")
        
        # 检查锁定类型
        lock_type = self.run_adb("shell settings get secure lockscreen.password_type")
        print(f"📋 锁定类型: {lock_type}")
        
        return disabled, lock_type
    
    def enable_screen_lock_via_settings(self):
        """通过设置启用屏幕锁定"""
        print("🔧 通过设置启用屏幕锁定...")
        
        # 启用锁定屏幕
        self.run_adb("shell settings put secure lockscreen.disabled 0")
        
        # 打开屏幕锁定设置
        self.run_adb("shell am start -a android.settings.SECURITY_SETTINGS")
        time.sleep(2)
        
        print("✅ 已打开安全设置页面")
        return True
    
    def set_pin_lock_programmatically(self):
        """程序化设置PIN锁定"""
        print("🔧 尝试程序化设置PIN锁定...")
        
        # 设置PIN锁定类型
        self.run_adb("shell settings put secure lockscreen.password_type 131072")
        
        # 启用锁定
        self.run_adb("shell settings put secure lockscreen.disabled 0")
        
        # 设置简单的PIN（1234）
        # 注意：这在真实设备上可能不工作，但在模拟器上可能有效
        self.run_adb("shell settings put secure lockscreen.password_salt 1234")
        
        print("✅ PIN锁定设置完成")
        return True
    
    def simulate_lock_setup_interaction(self):
        """模拟锁定设置交互"""
        print("🔧 模拟屏幕锁定设置交互...")
        
        # 打开锁定设置
        self.run_adb("shell am start -a android.app.action.SET_NEW_PASSWORD")
        time.sleep(3)
        
        # 如果上面不工作，尝试直接设置
        self.run_adb("shell am start -a android.settings.SECURITY_SETTINGS")
        time.sleep(2)
        
        print("📱 请在Android设备上完成以下步骤:")
        print("   1. 在安全设置中找到'屏幕锁定'")
        print("   2. 选择'PIN'、'密码'或'图案'")
        print("   3. 设置一个简单的锁定（如PIN: 1234）")
        print("   4. 确认设置")
        
        return True
    
    def verify_lock_setup(self):
        """验证锁定设置"""
        print("🔍 验证屏幕锁定设置...")
        
        disabled, lock_type = self.check_current_lock_status()
        
        if "1" not in disabled and lock_type != "0":
            print("✅ 屏幕锁定已正确设置")
            return True
        else:
            print("⚠️  屏幕锁定可能未正确设置")
            return False
    
    def run_setup(self):
        """运行完整的屏幕锁定设置"""
        print("🚀 Android屏幕锁定设置工具")
        print("🔧 为证书安装准备屏幕锁定")
        print("=" * 50)
        
        # 检查当前状态
        print("\n步骤1: 检查当前状态")
        print("-" * 30)
        disabled, lock_type = self.check_current_lock_status()
        
        if "1" not in disabled and lock_type != "0":
            print("✅ 屏幕锁定已设置，无需操作")
            return True
        
        # 尝试程序化设置
        print("\n步骤2: 程序化设置")
        print("-" * 30)
        self.set_pin_lock_programmatically()
        
        # 验证设置
        time.sleep(2)
        if self.verify_lock_setup():
            print("🎉 屏幕锁定设置成功！")
            return True
        
        # 手动设置
        print("\n步骤3: 手动设置")
        print("-" * 30)
        self.simulate_lock_setup_interaction()
        
        print("\n⏳ 请完成手动设置后按回车键继续...")
        input()
        
        # 最终验证
        if self.verify_lock_setup():
            print("🎉 屏幕锁定设置成功！")
            return True
        else:
            print("❌ 屏幕锁定设置失败")
            return False

def main():
    setup = ScreenLockSetup()
    
    try:
        success = setup.run_setup()
        
        if success:
            print("\n✅ 屏幕锁定设置完成！")
            print("🎯 现在可以安装CA证书了")
            print("🔧 请运行: python3 fix_cert_install_error.py")
        else:
            print("\n❌ 屏幕锁定设置失败")
            print("📋 请手动在Android设备上设置屏幕锁定")
            
    except KeyboardInterrupt:
        print("\n⚠️  设置被用户中断")
    except Exception as e:
        print(f"\n❌ 设置异常: {e}")

if __name__ == "__main__":
    main()
