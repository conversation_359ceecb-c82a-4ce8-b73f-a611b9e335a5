{"start_time": "2025-09-05T13:28:22.066451", "package": "com.yjzx.yjzx2017", "method": "broadcast_trigger", "activities_tested": [{"name": "com.yjzx.yjzx2017.controller.activity.MainActivity", "index": 1, "method": "broadcast_trigger", "start_time": "2025-09-05T13:28:22.067162", "launch_success": true, "network_before": 2, "network_after": 2, "network_increase": 0, "error_message": null, "captured_requests": [], "new_hosts": [], "end_time": "2025-09-05T13:28:39.112111"}, {"name": "com.yjzx.yjzx2017.controller.login.activity.LoginActivity", "index": 2, "method": "broadcast_trigger", "start_time": "2025-09-05T13:28:41.116985", "launch_success": true, "network_before": 2, "network_after": 2, "network_increase": 0, "error_message": null, "captured_requests": [], "new_hosts": [], "end_time": "2025-09-05T13:28:57.751603"}, {"name": "com.yjzx.yjzx2017.controller.login.activity.RegistActivity", "index": 3, "method": "broadcast_trigger", "start_time": "2025-09-05T13:28:59.755344", "launch_success": true, "network_before": 2, "network_after": 2, "network_increase": 0, "error_message": null, "captured_requests": [], "new_hosts": [], "end_time": "2025-09-05T13:29:15.281383"}, {"name": "com.yjzx.yjzx2017.controller.activity.setting.SettingActivity", "index": 4, "method": "broadcast_trigger", "start_time": "2025-09-05T13:29:17.282412", "launch_success": true, "network_before": 2, "network_after": 2, "network_increase": 0, "error_message": null, "captured_requests": [], "new_hosts": [], "end_time": "2025-09-05T13:29:34.501795"}, {"name": "com.yjzx.yjzx2017.controller.camera.CameraNewActivity", "index": 5, "method": "broadcast_trigger", "start_time": "2025-09-05T13:29:36.506027", "launch_success": true, "network_before": 2, "network_after": 2, "network_increase": 0, "error_message": null, "captured_requests": [], "new_hosts": [], "end_time": "2025-09-05T13:29:53.001748"}, {"name": "com.yjzx.yjzx2017.controller.activity.auction.AuctionSelfBuyActivity", "index": 6, "method": "broadcast_trigger", "start_time": "2025-09-05T13:29:55.005936", "launch_success": true, "network_before": 2, "network_after": 2, "network_increase": 0, "error_message": null, "captured_requests": [], "new_hosts": [], "end_time": "2025-09-05T13:30:11.773552"}, {"name": "com.yjzx.yjzx2017.controller.selfAuction.SelfAuctionAddActivity", "index": 7, "method": "broadcast_trigger", "start_time": "2025-09-05T13:30:13.777565", "launch_success": true, "network_before": 2, "network_after": 2, "network_increase": 0, "error_message": null, "captured_requests": [], "new_hosts": [], "end_time": "2025-09-05T13:30:30.023720"}, {"name": "com.yjzx.yjzx2017.controller.login.activity.ForgotPasswordActivity", "index": 8, "method": "broadcast_trigger", "start_time": "2025-09-05T13:30:32.027826", "launch_success": true, "network_before": 2, "network_after": 2, "network_increase": 0, "error_message": null, "captured_requests": [], "new_hosts": [], "end_time": "2025-09-05T13:30:48.521239"}, {"name": "com.yjzx.yjzx2017.controller.activity.setting.AddressCompanyActivity", "index": 9, "method": "broadcast_trigger", "start_time": "2025-09-05T13:30:50.523632", "launch_success": true, "network_before": 2, "network_after": 2, "network_increase": 0, "error_message": null, "captured_requests": [], "new_hosts": [], "end_time": "2025-09-05T13:31:06.791832"}, {"name": "com.yjzx.yjzx2017.controller.selfAuction.SelfAuctionManageActivity", "index": 10, "method": "broadcast_trigger", "start_time": "2025-09-05T13:31:08.795990", "launch_success": true, "network_before": 2, "network_after": 2, "network_increase": 0, "error_message": null, "captured_requests": [], "new_hosts": [], "end_time": "2025-09-05T13:31:25.513928"}], "network_captures": [], "summary": {"total_tested": 10, "successful_launches": 10, "failed_launches": 0, "network_requests_captured": 0, "unique_hosts": [], "request_methods": {}, "status_codes": {}}, "end_time": "2025-09-05T13:31:27.518301", "total_network_increase": 0}