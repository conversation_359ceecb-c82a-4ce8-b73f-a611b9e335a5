#!/usr/bin/env python3
"""
最终版Frida SSL绕过脚本
基于调试经验，使用最可靠的方法
"""

import subprocess
import sys
import time
import json
from pathlib import Path

def run_adb(cmd, timeout=30):
    """执行adb命令"""
    full_cmd = f"source android_env.sh && adb {cmd}"
    try:
        result = subprocess.run(full_cmd, shell=True, 
                              capture_output=True, text=True, 
                              timeout=timeout, check=True)
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        return f"ERROR: {e.stderr}"
    except subprocess.TimeoutExpired:
        return "ERROR: Timeout"

def setup_frida_environment():
    """设置Frida环境"""
    print("🔧 设置Frida环境...")
    
    # 检查设备连接
    result = run_adb("devices")
    if "device" not in result or "ERROR" in result:
        print("❌ Android设备未连接")
        return False
    print("✅ Android设备已连接")
    
    # 推送frida-server
    if not Path("frida-server").exists():
        print("❌ frida-server文件不存在")
        return False
    
    print("📱 推送frida-server...")
    push_result = run_adb("push frida-server /data/local/tmp/frida-server")
    if "ERROR" in push_result:
        print(f"⚠️  推送警告: {push_result}")
    
    # 设置权限
    run_adb("shell chmod 755 /data/local/tmp/frida-server")
    
    # 启动frida-server
    print("🚀 启动frida-server...")
    run_adb("shell pkill frida-server")  # 杀死现有进程
    time.sleep(2)
    
    # 后台启动frida-server
    subprocess.Popen("source android_env.sh && adb shell '/data/local/tmp/frida-server &'", shell=True)
    time.sleep(8)
    
    # 验证启动
    ps_result = run_adb("shell ps | grep frida-server")
    if "frida-server" in ps_result:
        print("✅ frida-server启动成功")
        return True
    else:
        print("❌ frida-server启动失败")
        return False

def prepare_target_app():
    """准备目标应用"""
    print("📱 准备目标应用...")
    
    package = "com.yjzx.yjzx2017"
    
    # 检查应用是否安装
    app_check = run_adb(f"shell pm list packages | grep {package}")
    if package not in app_check:
        print("❌ 目标应用未安装")
        return False
    print("✅ 目标应用已安装")
    
    # 强制停止应用
    print("🔄 重置应用状态...")
    run_adb(f"shell am force-stop {package}")
    time.sleep(2)
    
    return True

def run_frida_ssl_bypass():
    """运行Frida SSL绕过 - 使用spawn模式"""
    print("🔧 运行Frida SSL绕过...")
    
    package = "com.yjzx.yjzx2017"
    
    # 检查SSL绕过脚本
    if not Path("ssl_bypass.js").exists():
        print("❌ ssl_bypass.js文件不存在")
        return None
    
    print("✅ SSL绕过脚本存在")
    
    # 使用spawn模式启动应用并注入脚本
    cmd = f"export PATH=$PATH:/Users/<USER>/Library/Python/3.9/bin && frida -U -f {package} -l ssl_bypass.js"
    print(f"📋 执行命令: {cmd}")
    
    try:
        # 启动Frida进程
        process = subprocess.Popen(
            cmd, shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True
        )
        
        print("⏳ 等待Frida启动和脚本注入...")
        time.sleep(15)
        
        if process.poll() is None:
            print("✅ Frida SSL绕过脚本正在运行！")
            
            # 读取一些输出来确认
            try:
                # 非阻塞读取
                import select
                import os
                
                if hasattr(select, 'select'):
                    ready, _, _ = select.select([process.stdout], [], [], 1)
                    if ready:
                        output = process.stdout.read(2000)
                        if output:
                            print("📋 Frida输出预览:")
                            lines = output.split('\n')
                            for line in lines[:10]:
                                if line.strip() and not line.startswith('    '):
                                    print(f"   {line}")
            except:
                pass
            
            return process
        else:
            stdout, _ = process.communicate()
            print("❌ Frida启动失败")
            if stdout:
                print("错误输出:")
                lines = stdout.split('\n')
                for line in lines[:10]:
                    if line.strip():
                        print(f"   {line}")
            return None
            
    except Exception as e:
        print(f"❌ Frida启动异常: {e}")
        return None

def test_ssl_bypass():
    """测试SSL绕过效果"""
    print("🔍 测试SSL绕过效果...")
    
    # 清空捕获文件
    realtime_file = Path("mitm-logs/realtime_capture.json")
    if realtime_file.exists():
        with open(realtime_file, 'w') as f:
            json.dump([], f)
    
    time.sleep(3)
    
    # 触发HTTPS请求
    print("🌐 触发HTTPS网络请求...")
    
    test_actions = [
        ("访问HTTPS测试站点", "shell am start -a android.intent.action.VIEW -d https://httpbin.org/get"),
        ("等待加载", 8),
        ("访问百度HTTPS", "shell am start -a android.intent.action.VIEW -d https://www.baidu.com"),
        ("等待加载", 8),
        ("访问Google HTTPS", "shell am start -a android.intent.action.VIEW -d https://www.google.com"),
        ("等待加载", 8),
        ("应用内操作", "shell input tap 540 960"),
        ("等待响应", 5),
    ]
    
    for action_name, action in test_actions:
        print(f"   🔄 {action_name}...")
        
        if isinstance(action, int):
            time.sleep(action)
        else:
            result = run_adb(action)
            if "ERROR" in result:
                print(f"      ⚠️  {result}")
            time.sleep(2)
    
    # 等待网络请求处理
    print("⏳ 等待网络请求处理...")
    time.sleep(15)
    
    # 检查捕获结果
    try:
        with open(realtime_file, 'r') as f:
            data = json.load(f)
            
            if isinstance(data, list) and len(data) > 0:
                https_requests = [req for req in data if req.get('scheme') == 'https']
                http_requests = [req for req in data if req.get('scheme') == 'http']
                
                print(f"📊 网络捕获结果:")
                print(f"   总请求数: {len(data)}")
                print(f"   HTTPS请求: {len(https_requests)}")
                print(f"   HTTP请求: {len(http_requests)}")
                
                if https_requests:
                    print("🎉 SSL绕过成功！成功捕获HTTPS请求！")
                    print("📋 HTTPS请求示例:")
                    for i, req in enumerate(https_requests[:5], 1):
                        url = req.get('url', 'N/A')
                        method = req.get('method', 'N/A')
                        status = req.get('status_code', 'N/A')
                        print(f"   {i}. {method} {url}")
                        print(f"      状态码: {status}")
                    return True
                elif http_requests:
                    print("⚠️  只捕获到HTTP请求，SSL绕过可能未完全生效")
                    return False
                else:
                    print("⚠️  捕获到请求但协议未识别")
                    return False
            else:
                print("❌ 没有捕获到网络请求")
                return False
                
    except Exception as e:
        print(f"❌ 检查捕获结果失败: {e}")
        return False

def main():
    print("🚀 最终版Frida SSL绕过")
    print("🔧 解决HTTPS证书验证问题的终极方案")
    print("=" * 70)
    
    try:
        # 步骤1: 设置Frida环境
        if not setup_frida_environment():
            return False
        
        # 步骤2: 准备目标应用
        if not prepare_target_app():
            return False
        
        # 步骤3: 运行Frida SSL绕过
        frida_process = run_frida_ssl_bypass()
        if not frida_process:
            return False
        
        # 步骤4: 测试SSL绕过效果
        print("\n" + "="*50)
        print("🔍 测试SSL绕过效果")
        print("="*50)
        
        success = test_ssl_bypass()
        
        if success:
            print("\n🎉 Frida SSL绕过完全成功！")
            print("✅ HTTPS流量现在可以被mitmproxy完全捕获！")
            print("🔒 SSL证书验证已被完全绕过！")
            
            print("\n📋 系统现在具备以下能力:")
            print("   ✅ 完整的APK动态分析环境")
            print("   ✅ HTTP/HTTPS网络流量捕获")
            print("   ✅ SSL证书验证绕过")
            print("   ✅ 实时网络监控")
            print("   ✅ API端点发现")
            print("   ✅ 数据传输分析")
            
            print("\n💡 可以用于:")
            print("   • 移动应用安全测试")
            print("   • API逆向工程")
            print("   • 网络行为分析")
            print("   • 恶意软件分析")
            print("   • 隐私泄露检测")
            
            print("\n⚠️  Frida进程正在后台运行...")
            print("💡 你可以继续进行其他分析工作")
            print("🔧 按Ctrl+C停止Frida进程")
            
            try:
                # 保持Frida运行
                while True:
                    time.sleep(30)
                    if frida_process.poll() is not None:
                        print("⚠️  Frida进程意外退出")
                        break
                    print("💡 Frida SSL绕过仍在运行...")
            except KeyboardInterrupt:
                print("\n⚠️  用户中断，停止Frida进程...")
                frida_process.terminate()
                
            return True
        else:
            print("\n⚠️  SSL绕过测试未完全成功")
            print("🔧 但Frida脚本可能仍在工作")
            print("💡 可以手动测试HTTPS请求")
            
            if frida_process:
                print("⚠️  Frida进程仍在运行，按Ctrl+C停止")
                try:
                    time.sleep(60)  # 运行1分钟
                    frida_process.terminate()
                except KeyboardInterrupt:
                    frida_process.terminate()
            
            return False
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断")
        return False
    except Exception as e:
        print(f"\n❌ 异常: {e}")
        return False
    finally:
        # 清理资源
        print("🧹 清理frida-server进程...")
        subprocess.run("source android_env.sh && adb shell pkill frida-server", shell=True)

if __name__ == "__main__":
    main()
